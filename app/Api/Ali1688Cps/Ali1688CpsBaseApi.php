<?php

namespace App\Api\Ali1688Cps;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * Class Ali1688BaseApi
 * 1688开放平台API基础类，用于处理API请求、签名生成及响应解析。
 */
class Ali1688CpsBaseApi
{
    // 1688开放平台的基础API URL
    const API_BASE_URL = 'https://gw.open.1688.com/openapi/';
    // 默认的请求超时时间（秒）
    const TIMEOUT = 10;
    // 默认的重试次数
    const DEFAULT_RETRY = 3;

    /**
     * 调用1688开放平台的API接口
     * @param string $client_id 客户端ID（appKey）
     * @param string $client_secret 客户端密钥（appSecret）
     * @param string $accessToken 访问令牌（access_token）
     * @param string $apiInfo API接口信息（不包含 appKey，如param2/1/system/currentTime）
     * @param array $params 请求参数
     * @param string $method 请求方法，'GET' 或 'POST'
     * @param int $retry 重试次数，默认3次
     * @return object                返回API请求结果
     * @throws Exception             抛出异常信息
     */
    public static function callApi($client_id, $client_secret, $accessToken, $apiInfo, $params = [], $method = 'GET', $retry = self::DEFAULT_RETRY)
    {
        // 移除apiInfo末尾的斜杠，避免URL拼接时出现双斜杠
        $apiInfo = rtrim($apiInfo, '/');

        // 构造完整的API URL，将apiInfo和client_id拼接到基础URL后
        $url = self::API_BASE_URL . $apiInfo . '/' . $client_id;

        // 根据API的需求，决定是否需要添加access_token参数
        if (!empty($accessToken)) {
            $params['access_token'] = $accessToken;
        }

        // 固定添加webSite参数
        $params['webSite'] = '1688';

        // 生成签名
        $urlPath = $apiInfo . '/' . $client_id;
        $signature = self::generateSignature($urlPath, $params, $client_secret);

        // 将签名添加到请求参数中
        $params['_aop_signature'] = $signature;

        // 记录请求日志，便于调试
        //        Log::info("Ali1688 API 请求", [
        //            'url'                 => $url,
        //            'method'              => $method,
        //            'params'              => $params,
        //            'signature_string'    => $urlPath . self::getSortedParamString($params),
        //            'generated_signature' => $signature,
        //        ]);
        //
        //dd($client_id, $client_secret, $accessToken);
        // 发送API请求并返回结果
        return self::sendCallApiRequest($url, $params, $method, $retry);
    }

    /**
     * 生成API请求的签名
     * 签名规则：
     * 1. 构造签名因子urlPath。
     * 2. 排序并拼接参数（key+value，无分隔符）。
     * 3. 合并urlPath和拼接后的参数。
     * 4. 生成HMAC-SHA1签名，转为大写十六进制字符串。
     * @param string $urlPath 请求路径部分（如param2/1/system/currentTime/1000000）
     * @param array $parameters 请求参数
     * @param string $secret 客户端密钥
     * @return string            生成的签名
     */
    private static function generateSignature($urlPath, $parameters, $secret)
    {
        // 过滤掉'_aop_signature'参数，避免其参与签名
        $filteredParams = self::filterParameters($parameters);

        // 获取排序后的参数字符串
        $sortedParamString = self::getSortedParamString($filteredParams);

        // 合并urlPath和排序后的参数字符串，形成签名字符串
        $signatureString = $urlPath . $sortedParamString;

        // 使用HMAC-SHA1算法生成签名，并转为大写的十六进制字符串
        $hash = hash_hmac('sha1', $signatureString, $secret, true);
        $signature = strtoupper(bin2hex($hash));

        // 记录签名字符串和生成的签名，便于调试
        Log::debug("签名字符串: " . $signatureString);
        Log::debug("生成的签名: " . $signature);

        return $signature;
    }

    /**
     * 过滤请求参数，排除不参与签名的参数
     * @param array $parameters 请求参数
     * @return array            过滤后的参数
     */
    private static function filterParameters($parameters)
    {
        return array_filter($parameters, function ($key) {
            // 排除'_aop_signature'参数
            return $key !== '_aop_signature';
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * 获取排序并拼接后的参数字符串（key+value，无分隔符）
     * @param array $params 参数数组
     * @return string       排序并拼接后的参数字符串
     */
    private static function getSortedParamString($params)
    {
        // 按照键名进行升序排序
        ksort($params);

        // 拼接参数的键和值，没有分隔符
        $paramString = '';
        foreach ($params as $key => $value) {
            if ($key === 'dataBody') {
                // Treat dataBody as a raw JSON string
                $paramString .= $key . $value;
            } elseif (is_array($value)) {
                // Serialize other arrays to JSON
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                $paramString .= $key . $value;
            } elseif (is_object($value)) {
                // Serialize objects to JSON
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                $paramString .= $key . $value;
            } else {
                // Concatenate scalar values directly
                $paramString .= $key . $value;
            }
        }

        return $paramString;
    }

    /**
     * 发送API请求
     * @param string $url API的完整URL
     * @param array $params 请求参数
     * @param string $method 请求方法，'GET' 或 'POST'
     * @param int $retry 重试次数
     * @return object        API响应数据
     * @throws Exception     抛出异常信息
     */
    public static function sendCallApiRequest($url, $params, $method = 'GET', $retry = self::DEFAULT_RETRY)
    {
        //
        $attempt = 0; // 当前尝试次数
        while ($attempt < $retry) {
            //
            try {
                // 根据请求方法选择发送GET或POST请求
                if (strtoupper($method) === 'GET') {
                    //
                    $response = Http::timeout(self::TIMEOUT)->get($url, $params);
                } elseif (strtoupper($method) === 'POST') {
                    //
                    $response = Http::timeout(self::TIMEOUT)->withHeaders([
                        'User-Agent' => 'Ali1688Client/1.0',
                        'Accept' => 'application/json',
                    ])->asForm()->post($url, $params); // 以表单形式发送POST数据
                } else {
                    // 如果请求方法不支持，则抛出异常
                    throw new Exception("不支持的HTTP方法: $method");
                }

                //                // 记录响应日志
                //                Log::info("Ali1688 API 响应", [
                //                    'status' => $response->status(),
                //                    'body'   => $response->body(),
                //                ]);
                //                //
                //                dd($response->body());
                //
                /** 2.1 命中 gw.ISPInvokeError：直接跳过，不重试 */
                if (str_contains($response->body(), 'ISPInvokeError')) {
                    //
                    return null;
                }

                // 检查响应是否成功（HTTP状态码2xx）
                if ($response->successful()) {
                    // 返回解码后的JSON响应
                    // 返回解码后的JSON响应
                    return json_decode($response->body());
                }
                //else {

                //dd($params, $response->body(), $url);
                // 如果响应不成功，抛出异常，包含状态码和错误信息
                //throw new Exception("API请求失败，状态码 {$response->status()}: {$response->body()}" . json_encode($params));
                //}
            } catch (Exception $e) {
                // 同样检查 gw.ISPInvokeError（极少数情况下可能在异常信息里）
                /** 2.1 命中 gw.ISPInvokeError：直接跳过，不重试 */
                if (str_contains($response->body(), 'ISPInvokeError')) {
                    //
                    return null;
                }
                //
                $attempt++;
                //
                if ($attempt >= $retry) {
                    // 如果超过最大重试次数，抛出异常
                    throw new Exception("API请求在 {$retry} 次尝试后失败: " . $e->getMessage() . json_encode($params));
                }
            }
        }
        // 如果所有尝试都失败，抛出异常
        throw new Exception("API请求在 {$retry} 次尝试后失败。");
    }
}
