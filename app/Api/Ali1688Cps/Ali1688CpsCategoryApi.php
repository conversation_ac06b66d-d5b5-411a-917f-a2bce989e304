<?php

namespace App\Api\Ali1688Cps;

use InvalidArgumentException;

class Ali1688CpsCategoryApi extends Ali1688CpsBaseApi
{
    /**
     * 递归获取所有 1688 类目示例
     * 说明：1688 类目最多三级，若需要获取整个类目树时，可按以下示例递归获取。
     * @param array $Ali1688AccessToken
     * @param int $categoryID
     * @return array
     */
    public static function getAllSubCategories($Ali1688AccessToken, $categoryID = 0)
    {
        $allCategories = [];
        // 获取当前类目的信息与子类目
        $result = self::getCategoryById($Ali1688AccessToken, $categoryID);
        if (empty($result['categoryInfo'])) {
            return $allCategories; // 若无数据或出错，可根据需要处理
        }

        // $result['categoryInfo'] 本身就是数组，可能包含多个类目信息
        // 例如 categoryID=0 时，可能返回所有一级类目信息
        foreach ($result['categoryInfo'] as $category) {
            // 保存当前类目
            $allCategories[] = $category;
            // 若存在子类目（在 1688 中一般会返回 childCategorys 等信息）
            if (!empty($category['childCategorys']) && is_array($category['childCategorys'])) {
                foreach ($category['childCategorys'] as $subCat) {
                    // 继续递归子类目ID
                    $allCategories = array_merge(
                        $allCategories,
                        self::getAllSubCategories($Ali1688AccessToken, $subCat['id'])
                    );
                }
            }
        }

        return $allCategories;
    }

    /**
     * 根据类目ID查询类目
     * 对应 API: com.alibaba.product:alibaba.category.get-1
     * 官方文档:
     * https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.category.get-1
     * 使用说明:
     * 1. 当 categoryID=0 时，查询所有一级类目
     * 2. 若需要获取所有 1688 类目，需要从根类目(0)开始递归查询其子类目(再查询子类目的子类目)，直到叶子节点。
     * @param array $Ali1688AccessToken 授权信息，(若无需授权可不传 access_token)
     * @param int $categoryID 类目ID，大于等于 0。0 表示查询所有一级类目
     * @return mixed 接口返回数据，结构中包含 'categoryInfo' 等字段
     */
    public static function getCategoryById($Ali1688AccessToken, $categoryID)
    {
        // 接口路径 (POST)
        //   https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.category.get/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.category.get/';

        // 必填校验
        if (!is_numeric($categoryID) || $categoryID < 0) {
            throw new InvalidArgumentException('categoryID 必须大于等于 0');
        }

        // 组装请求体参数
        $body_params = [
            'categoryID' => $categoryID,
        ];

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'] ?? null,
            $Ali1688AccessToken['client_secret'] ?? null,
            $Ali1688AccessToken['access_token'] ?? null,
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
