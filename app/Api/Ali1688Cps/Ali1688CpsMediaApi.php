<?php

namespace App\Api\Ali1688Cps;

class Ali1688CpsMediaApi extends Ali1688CpsBaseApi
{
    /**
     * 获取媒体及推广位信息
     * 对应 API: com.alibaba.p4p:alibaba.cps.listMediaInfo-1
     * 官方文档:
     * https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.p4p:alibaba.cps.listMediaInfo-1
     * @param array $Ali1688AccessToken 授权信息，需包含 ['client_id', 'client_secret', 'access_token']
     * @param null|int $mediaId 可选，媒体ID
     * @return mixed
     */
    public static function listMediaInfo($Ali1688AccessToken, $mediaId = null)
    {
        // 接口路径 (POST)
        // 例如：
        //   https://gw.open.1688.com/openapi/param2/1/com.alibaba.p4p/alibaba.cps.listMediaInfo/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.p4p/alibaba.cps.listMediaInfo/';

        // 组装请求体参数，mediaId可选
        $body_params = [];
        if (!empty($mediaId)) {
            $body_params['mediaId'] = $mediaId;
        }

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
