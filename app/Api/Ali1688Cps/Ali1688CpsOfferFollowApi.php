<?php

namespace App\Api\Ali1688Cps;

use InvalidArgumentException;

class Ali1688CpsOfferFollowApi extends Ali1688CpsBaseApi
{
    /**
     * 关注商品（原有示例）
     * 对应 API: com.alibaba.product:alibaba.product.follow-1
     */
    public static function followProduct($Ali1688AccessToken, $productId)
    {
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.product.follow/';
        if (empty($productId)) {
            throw new InvalidArgumentException('productId 不能为空');
        }
        $body_params = ['productId' => $productId];
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }

    /**
     * 解除关注商品
     * 对应 API: com.alibaba.product:alibaba.product.unfollow.crossborder-1
     * 官方文档: https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.unfollow.crossborder-1
     * @param array $Ali1688AccessToken 授权信息，需包含 ['client_id', 'client_secret', 'access_token']
     * @param string $productId 要解除关注的商品ID
     * @return mixed
     */
    public static function unFollowProductCrossborder($Ali1688AccessToken, $productId)
    {
        // 接口路径 (POST)
        // 最终请求地址形如:
        //   https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.unfollow.crossborder/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.product.unfollow.crossborder/';

        // 参数检查
        if (empty($productId)) {
            throw new InvalidArgumentException('productId 不能为空');
        }

        // 组装请求体参数
        $body_params = [
            'productId' => $productId,
        ];

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
