<?php

namespace App\Api\Ali1688Cps;

use InvalidArgumentException;

class Ali1688CpsPromotion<PERSON>pi extends Ali1688CpsBaseApi
{
    /**
     * 批量生成联盟推广URL点击信息
     * 对应 API: com.alibaba.p4p:alibaba.cps.genClickUrl-1
     * 官方文档: https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.p4p:alibaba.cps.genClickUrl-1
     * @param array $Ali1688AccessToken 授权信息，需包含 ['client_id', 'client_secret', 'access_token']
     * @param array $params 调用参数 (type, mediaId, mediaZoneId, objectValueList 均必填)
     *                                         [
     *                                         'type'            => (int)    转链接类型: 0-商品, 1-店铺, 2-活动, 7-优惠券, 12-图搜 (必填),
     *                                         'mediaId'         => (int)    媒体ID(必填),
     *                                         'mediaZoneId'     => (int)    媒体推广位ID(必填),
     *                                         'objectValueList' => (array|string) 推广实体，可批量传入:
     *                                         - 商品：传offerId
     *                                         - 店铺：传sellerId
     *                                         - 活动：传活动ID
     *                                         - 优惠券：传offerId
     *                                         - 图搜：传图片URL
     *                                         'ext'             => (string) 自定义扩展参数(可选)，查询订单会返回该参数
     *                                         ]
     * @return mixed 接口返回值，包含生成好的推广链接等信息
     *                                         [
     *                                         'result' => [
     *                                         [
     *                                         'objectId'      => '推广实体id',
     *                                         'longClickUrl'  => '长链接(图搜结果页)',
     *                                         'shortClickUrl' => '短链接',
     *                                         'alipayUrl'     => '吱口令',
     *                                         'searchCode'    => '搜索码',
     *                                         ],
     *                                         ...
     *                                         ]
     *                                         ]
     */
    public static function genClickUrl($Ali1688AccessToken, array $params = [])
    {
        // 接口路径 (POST)
        // 最终请求地址示例:
        //   https://gw.open.1688.com/openapi/param2/1/com.alibaba.p4p/alibaba.cps.genClickUrl/${APPKEY}
        $apiInfo = 'param2/1/com.alibaba.p4p/alibaba.cps.genClickUrl/';

        // 必填校验
        if (
            !isset($params['type']) ||
            !isset($params['mediaId']) ||
            !isset($params['mediaZoneId']) ||
            !isset($params['objectValueList'])
        ) {
            throw new InvalidArgumentException(
                '必须至少填写：type, mediaId, mediaZoneId, objectValueList'
            );
        }

        // 组装请求体参数
        $body_params = [
            'type' => $params['type'],
            'mediaId' => $params['mediaId'],
            'mediaZoneId' => $params['mediaZoneId'],
            // objectValueList 若为数组，可根据官方文档需求拼接成字符串(逗号分隔或JSON)。此处简单演示直接传入。
            'objectValueList' => is_array($params['objectValueList'])
                ? implode(',', $params['objectValueList'])
                : $params['objectValueList'],
        ];

        // ext为可选
        if (isset($params['ext'])) {
            $body_params['ext'] = $params['ext'];
        }

        // 发起调用
        return Ali1688CpsBaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'POST'
        );
    }
}
