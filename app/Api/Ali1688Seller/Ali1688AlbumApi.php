<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688PhotoAlbumApi 类
 * 此类用于处理与阿里巴巴1688平台图片和相册管理相关的API调用，包括查询相册空间详情、
 * 修改图片信息、创建和修改相册、删除相册和图片、批量删除图片、上传图片、
 * 获取图片和相册列表以及根据图片ID或URL获取图片信息等功能。
 */
class Ali1688AlbumApi extends Ali1688BaseApi
{
    /**
     * 查询相册空间详情
     * 获取相册详情。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.photobank.album.getProfile-1&aopApiCategory=photobank_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string "1688"            站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688），默认为 '1688'
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.photobank.album.getProfile-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.photobank/alibaba.photobank.album.getProfile-1/7587553
     */
    public static function getAlbumProfile(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.photobank.album.getProfile/';
        // 初始化请求参数
        $params = [
            'webSite' => '1688',
        ];
        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取相册列表
     * 获取授权用户的相册列表。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.photobank.album.getList-1&aopApiCategory=photobank_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包含：
     *                                       - webSite (string, 是): 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688）
     *                                       - albumType (string, 否): 相册类型，取值范围:
     *                                       - MY: 我的相册(系统相册)
     *                                       - OFF: 下架相册(系统相册)
     *                                       - AUDTING: 审核中(系统相册)
     *                                       - NOPASS: 审核不通过(系统相册)
     *                                       - CUSTOM: 自定义
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.photobank.album.getList-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.photobank/alibaba.photobank.album.getList-1/7587553
     */
    public static function getAlbumList(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.photobank.album.getList/';
        // 确保 webSite 参数存在
        $params = array_filter([
            'webSite' => '1688',
            'albumType' => null
        ]);
        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 创建相册
     * 创建一个新的相册。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.photobank.album.add-1&aopApiCategory=photobank_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $albumData 相册数据，包含：
     *                                     - name (string, 是): 相册名称，最长30个中文字符
     *                                     - description (string, 否): 相册描述，最长2000个中文字符，国际站无需处理此字段
     *                                     - authority (integer, 是): 相册访问权限，取值范围:0-不公开；1-公开；2-密码访问
     *                                     - password (string, 否): 相册访问密码，仅当 authority 为 2 时必填，4-16位非中文字符
     * @param string "1688"              站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688），默认为 '1688'
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.photobank.album.add-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.photobank/alibaba.photobank.album.add-1/7587553
     */
    public static function addAlbum(array $Ali1688AccessToken, array $albumData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.photobank.album.add/';

        // 参数验证|| !is_string($albumData['name'])
        if (empty($albumData['name'])) {
            throw new InvalidArgumentException('name 是必填参数，且必须为字符串。');
        }

        if (!isset($albumData['authority']) || !in_array($albumData['authority'], [0, 1, 2], true)) {
            throw new InvalidArgumentException('authority 是必填参数，且必须为 0, 1 或 2。');
        }

        if ($albumData['authority'] === 2) {
            if (empty($albumData['password']) || !is_string($albumData['password'])) {
                throw new InvalidArgumentException('当 authority 为 2 时，password 是必填参数，且必须为字符串。');
            }
            if (strlen($albumData['password']) < 4 || strlen($albumData['password']) > 16) {
                throw new InvalidArgumentException('password 长度必须为4-16个字符。');
            }
            if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $albumData['password'])) {
                throw new InvalidArgumentException('password 不能包含中文字符。');
            }
        }


        // 初始化请求参数
        $params = array_merge([
            'webSite' => "1688",
        ], $albumData);

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                   // 请求方法
        );
    }

    /**
     * 修改相册
     * 修改授权用户自身的相册信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.photobank.album.modify-1&aopApiCategory=photobank_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $albumId 相册ID，必须为正整数
     * @param string $albumData 相册数据，包含：
     *                                       - albumInfo (array, 是): 相册信息，包括：
     *                                       - albumID (long, 是): 相册ID
     *                                       - name (string, 是): 相册名称
     *                                       - description (string, 是): 相册描述
     *                                       - authority (integer, 是): 相册访问权限
     *                                       - imageCount (integer, 是): 图片数量
     *                                       - password (string, 否): 相册密码（可选）
     * @param string "1688"                站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688），默认为 '1688'
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.photobank.album.modify-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.photobank/alibaba.photobank.album.modify-1/7587553
     */
    public static function modifyAlbum(array $Ali1688AccessToken, $albumInfo)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.photobank.album.modify/';

        // 确保 $albumInfo 是一个数组
        if (!is_array($albumInfo)) {
            throw new InvalidArgumentException('albumInfo 必须是一个数组。');
        }

        // 将 albumInfo 编码为 JSON 字符串，并保留 Unicode 字符
        $encodedAlbumData = json_encode($albumInfo, JSON_UNESCAPED_UNICODE);

        // 检查 JSON 编码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('albumInfo 无法被编码为有效的 JSON 字符串：' . json_last_error_msg());
        }

        // 初始化请求参数
        $params = [
            'albumInfo' => $encodedAlbumData,
            'webSite' => '1688', // 如果 'webSite' 是固定值，建议使用常量或配置文件管理
        ];

        // 调用 API
        $response = self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端 ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API 路径
            $params,                                 // 请求参数
            'GET'                                   // 请求方法
        );
        // 处理 API 响应（根据需求添加）
        return $response;
    }

    /**
     * 删除相册
     * 删除指定的相册。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.photobank.album.delete-1&aopApiCategory=photobank_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $albumId 相册ID，必须为正整数
     * @param string "1688"            站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688），默认为 '1688'
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.photobank.album.delete-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.photobank/alibaba.photobank.album.delete-1/7587553
     */
    public static function deleteAlbum(array $Ali1688AccessToken, int $albumId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.photobank.album.delete/';

        // 参数验证
        if ($albumId <= 0) {
            //
            throw new InvalidArgumentException('albumId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'albumID' => $albumId,
            'webSite' => "1688",
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                   // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
