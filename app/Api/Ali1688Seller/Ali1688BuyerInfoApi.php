<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688BuyerInfoApi 类
 * 此类用于处理与阿里巴巴1688平台买家信息相关的API调用，包括将 Openuid 解密为旺旺昵称、
 * 将 LoginId 加密转换为 Openuid 以及批量将 LoginId 转换为 Openuid 等功能。
 */
class Ali1688BuyerInfoApi extends Ali1688BaseApi
{
    /**
     * Openuid转换解密为旺旺昵称接口（仅可使用于用户唤起旺旺）
     * 该接口用于将 Openuid 解密为旺旺昵称，仅限用于用户唤起旺旺，不允许自动化批量操作。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:wangwangnick.openuid.decrypt-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $openuid 待解密的 Openuid
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.account:wangwangnick.openuid.decrypt-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.account/alibaba.account.wangwangnick.openuid.decrypt-1/7587553
     */
    public static function decryptOpenUidToWangWangNick(array $Ali1688AccessToken, string $openuid)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.account/alibaba.account.wangwangnick.openuid.decrypt/';


        if (empty($openuid)) {
            throw new InvalidArgumentException('openuid 是必填参数，且不能为空。');
        }

        if (!is_string($openuid)) {
            throw new InvalidArgumentException('openuid 必须为字符串。');
        }

        // 初始化请求参数
        $params = [
            'openUid' => $openuid,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 用户loginId加密转换为Openuid接口
     * 该接口用于将用户的 LoginId 加密转换为 Openuid。该接口进行风控，不允许批量操作，仅允许商家手动触发。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:loginid.openuid.encrypt-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $loginId 用户登录名
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.account:loginid.openuid.encrypt-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.account/alibaba.account.loginid.openuid.encrypt-1/7587553
     */
    public static function encryptLoginIdToOpenUid(array $Ali1688AccessToken, string $loginId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.account/alibaba.account.loginid.openuid.encrypt/';


        if (empty($loginId)) {
            throw new InvalidArgumentException('loginId 是必填参数，且不能为空。');
        }

        if (!is_string($loginId)) {
            throw new InvalidArgumentException('loginId 必须为字符串。');
        }

        // 初始化请求参数
        $params = [
            'loginId' => $loginId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 用户LoginId批量加密转换为Openuid接口（仅用户ERP订单管理场景）
     * 该接口用于批量将用户的 LoginId 加密转换为 Openuid，仅限于 ERP 订单管理场景使用，且登录ID数量不大于30。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.account:batch.loginid.openuid.encrypt-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $loginIds 用户登录名列表，最多30个
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.account:batch.loginid.openuid.encrypt-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.account/alibaba.account.batch.loginid.openuid.encrypt-1/7587553
     */
    public static function batchEncryptLoginIdsToOpenUids(array $Ali1688AccessToken, array $loginIds)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.account/alibaba.account.batch.loginid.openuid.encrypt/';


        if (empty($loginIds)) {
            throw new InvalidArgumentException('loginIds 是必填参数，且不能为空。');
        }

        if (!is_array($loginIds)) {
            throw new InvalidArgumentException('loginIds 必须为数组。');
        }

        if (count($loginIds) > 30) {
            throw new InvalidArgumentException('loginIds 数量不能超过30。');
        }

        foreach ($loginIds as $loginId) {
            if (!is_string($loginId) || empty($loginId)) {
                throw new InvalidArgumentException('loginIds 中的每个 loginId 必须为非空字符串。');
            }
        }

        // 初始化请求参数
        $params = [
            'loginIdList' => implode(',', $loginIds),
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
