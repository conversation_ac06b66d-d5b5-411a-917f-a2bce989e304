<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688ContentPublishApi 类
 * 此类用于处理与阿里巴巴1688平台内容发布相关的API调用，包括发布内容、
 * 查询话题以及轻应用数据回传等功能。
 */
class Ali1688ContentPublishApi extends Ali1688BaseApi
{
    /**
     * 1688内容发布
     * 通过该接口发布1688内容信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:privatedomain.content.publish-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $contentData 内容数据，包括 contentGeneratorParam（必填）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:privatedomain.content.publish-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.privatedomain/alibaba.privatedomain.content.publish-1/7587553
     */
    public static function publishContent(array $Ali1688AccessToken, array $contentData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.privatedomain/alibaba.privatedomain.content.publish/';


        if (empty($contentData)) {
            throw new InvalidArgumentException('contentData 是必填参数，且不能为空。');
        }

        if (!isset($contentData['contentGeneratorParam']) || !is_array($contentData['contentGeneratorParam'])) {
            throw new InvalidArgumentException('contentGeneratorParam 是必填参数，且必须为数组。');
        }

        // 进一步验证 contentGeneratorParam 的结构（根据实际需求添加）

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $contentData,                            // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 话题查询
     * 私域内容话题查询接口，用于查询相关话题信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:privatedomain.topic.query-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token（可选）
     * @param array $params 查询参数，包括 content（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:privatedomain.topic.query-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.privatedomain/alibaba.privatedomain.topic.query-1/7587553
     */
    public static function queryTopics(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.privatedomain/alibaba.privatedomain.topic.query/';

        // 参数验证
        // access_token 是可选参数
        if (!empty($Ali1688AccessToken)) {
            if (empty($Ali1688AccessToken['client_id']) || empty($Ali1688AccessToken['client_secret'])) {
                throw new InvalidArgumentException('Ali1688AccessToken 必须包含 client_id 和 client_secret。');
            }
        }

        if (!empty($params)) {
            if (isset($params['content']) && !is_string($params['content'])) {
                throw new InvalidArgumentException('content 必须为字符串。');
            }
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'] ?? null,        // 客户端ID（可选）
            $Ali1688AccessToken['client_secret'] ?? null,    // 客户端密钥（可选）
            $Ali1688AccessToken['access_token'] ?? null,     // 访问令牌（可选）
            $apiPath,                                       // API路径
            $params,                                        // 请求参数
            'GET'                                           // 请求方法
        );
    }

    /**
     * 轻应用数据回传
     * 通过该接口将轻应用数据回传至1688平台。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:app.userTrace.upload-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $traceData 回传数据，包括 bizTs（必填）、bizKey（必填）、bizData（可选）、jumpFunction（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:app.userTrace.upload-1
     * @URL http://gw.open.1688.com/openapi/param2/1/cn.alibaba.open/alibaba.app.userTrace.upload-1/7587553
     */
    public static function uploadAppUserTrace(array $Ali1688AccessToken, array $traceData)
    {
        // 定义API路径
        $apiPath = 'param2/1/cn.alibaba.open/alibaba.app.userTrace.upload/';


        if (empty($traceData)) {
            throw new InvalidArgumentException('traceData 是必填参数，且不能为空。');
        }

        if (!isset($traceData['bizTs']) || !is_int($traceData['bizTs'])) {
            throw new InvalidArgumentException('bizTs 是必填参数，且必须为整数。');
        }

        if (!isset($traceData['bizKey']) || !is_string($traceData['bizKey'])) {
            throw new InvalidArgumentException('bizKey 是必填参数，且必须为字符串。');
        }

        if (isset($traceData['bizData']) && !is_string($traceData['bizData'])) {
            throw new InvalidArgumentException('bizData 必须为字符串。');
        }

        if (isset($traceData['jumpFunction']) && !is_string($traceData['jumpFunction'])) {
            throw new InvalidArgumentException('jumpFunction 必须为字符串。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $traceData,                              // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
