<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688CustomCategoryApi 类
 * 此类用于处理与阿里巴巴1688平台自定义分类相关的API调用，包括用户自定义类目创建、
 * 检查是否开启自定义分类、获取自定义分类列表、开启或关闭自定义分类、获取商品自定义分类ID、
 * 添加或移除商品的自定义分类等功能。
 */
class Ali1688CustomCategoryApi extends Ali1688BaseApi
{
    /**
     * 用户自定义类目创建（1688）
     * 在1688中文中，创建用户的自定义类目。如果是一级类目，父类目Id传值0。
     * 如果是子类目，父类目传上一级类目的id。创建成功的话，会返回新的类目的id。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.userDefine.category.add-1&aopApiCategory=category_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $categoryData 类目数据，包含：
     *                                  - groupName (string, 是): 要新增的类目的名称
     *                                  - parentID (long, 是): 上一级类目的id。如果要创建的是一级类目，此处值填0
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.userDefine.category.add-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.userDefine.category/alibaba.userDefine.category.add/7587553
     */
    public static function addUserDefineCategory(array $Ali1688AccessToken, array $categoryData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.userDefine.category/alibaba.userDefine.category.add/';

        // 参数验证
        if (empty($categoryData['groupName']) || !is_string($categoryData['groupName'])) {
            throw new InvalidArgumentException('groupName 是必填参数，且必须为字符串。');
        }

        if (!isset($categoryData['parentID']) || !is_int($categoryData['parentID'])) {
            throw new InvalidArgumentException('parentID 是必填参数，且必须为整数。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $categoryData,                           // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 获取用户是否开启自定义分类
     * 获取阿里巴巴中国网站会员是否已经开启自定义分类功能。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:offerGroup.hasOpened-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @return mixed
     * @throws RuntimeException 如果API请求失败
     * @API  cn.alibaba.open:offerGroup.hasOpened-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.offerGroup/offerGroup.hasOpened/7587553
     */
    public static function hasOpenedCustomCategory(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.offerGroup/offerGroup.hasOpened/';

        // 初始化请求参数
        $params = [];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取授权用户的商品自定义分类列表
     * 获取授权用户的1688商品自定义分类列表。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.userCategory.list.get-1&aopApiCategory=product_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @return mixed
     * @throws RuntimeException 如果API请求失败
     * @API  com.alibaba.product:alibaba.userCategory.list.get-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.userCategory/alibaba.userCategory.list.get/7587553
     */
    public static function getUserCategoryList(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.userCategory/alibaba.userCategory.list.get/';

        // 初始化请求参数
        $params = [];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 开启或关闭自定义分类
     * 阿里巴巴中国网站会员开启或关闭自定义分类功能。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:offerGroup.set-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param bool $isOpen 是否开启自定义分类，true为开启，false为关闭
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:offerGroup.set-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.offerGroup/offerGroup.set/7587553
     */
    public static function setCustomCategory(array $Ali1688AccessToken, bool $isOpen)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.offerGroup/offerGroup.set/';

        // 参数验证
        if (!is_bool($isOpen)) {
            throw new InvalidArgumentException('isOpen 必须为布尔值。');
        }

        // 初始化请求参数
        $params = [
            'switchType' => $isOpen ? 'on' : 'off',
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 获取商品自定义分类ID
     * 通过数据接口的形式，批量获取指定产品所属的自定义分类ID。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:userCategory.get.offerIds-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $offerIds 需要查询的产品ID列表，必须为非空数组，且每个ID为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:userCategory.get.offerIds-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.userCategory/userCategory.get.offerIds/7587553
     */
    public static function getUserCategoryOfferIds(array $Ali1688AccessToken, array $offerIds)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.userCategory/userCategory.get.offerIds/';

        // 参数验证
        if (empty($offerIds)) {
            throw new InvalidArgumentException('offerIds 是必填参数，且必须为非空数组。');
        }

        foreach ($offerIds as $id) {
            if (!is_int($id) && !ctype_digit($id)) {
                throw new InvalidArgumentException('offerIds 中的每个ID必须为正整数。');
            }
        }

        // 初始化请求参数
        $params = [
            'offerIds' => implode(';', $offerIds),
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 添加商品的自定义分类
     * 批量添加多个商品到一个自定义分类下，一次最多操作50个商品。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:userCategory.offers.add-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $offerIds 多个商品ID，使用分号分隔，最多50个
     * @param int $categoryId 自定义类目ID，必须为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:userCategory.offers.add-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.userCategory/userCategory.offers.add/7587553
     */
    public static function addUserCategoryOffers(array $Ali1688AccessToken, array $offerIds, int $categoryId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.userCategory/userCategory.offers.add/';

        // 参数验证
        if (empty($offerIds)) {
            throw new InvalidArgumentException('offerIds 是必填参数，且必须为非空数组。');
        }

        if (count($offerIds) > 50) {
            throw new InvalidArgumentException('一次最多只能添加50个商品。');
        }

        foreach ($offerIds as $id) {
            if (!is_int($id) && !ctype_digit($id)) {
                throw new InvalidArgumentException('offerIds 中的每个ID必须为正整数。');
            }
        }

        if ($categoryId <= 0) {
            throw new InvalidArgumentException('categoryId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'offerIds' => implode(';', $offerIds),
            'categoryId' => $categoryId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 移除商品的自定义分类
     * 批量移除自定义分类下的商品，一次最多操作50个商品。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:userCategory.offers.remove-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $offerIds 需要删除的产品序列。多个产品id半角分号分隔
     * @param int $categoryId 要删除的自定义分类ID，必须为正整数
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:userCategory.offers.remove-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.userCategory/userCategory.offers.remove/7587553
     */
    public static function removeUserCategoryOffers(array $Ali1688AccessToken, array $offerIds, int $categoryId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.userCategory/userCategory.offers.remove/';

        // 参数验证
        if (empty($offerIds)) {
            throw new InvalidArgumentException('offerIds 是必填参数，且必须为非空数组。');
        }

        if (count($offerIds) > 50) {
            throw new InvalidArgumentException('一次最多只能移除50个商品。');
        }

        foreach ($offerIds as $id) {
            if (!is_int($id) && !ctype_digit($id)) {
                throw new InvalidArgumentException('offerIds 中的每个ID必须为正整数。');
            }
        }

        if ($categoryId <= 0) {
            throw new InvalidArgumentException('categoryId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'offerIds' => implode(';', $offerIds),
            'categoryId' => $categoryId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    // 其他自定义类目相关的方法...

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
