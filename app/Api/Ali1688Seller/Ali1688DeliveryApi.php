<?php

namespace App\Api\Ali1688Seller;

use Exception;

class Ali1688D<PERSON>veryApi extends Ali1688BaseApi
{
    /**
     * @param $Ali1688AccessToken
     * @param $body_params
     * @return mixed
     * @throws Exception
     */
    public static function GetAli1688LogisticsOpDeliverySendOrderOffline($Ali1688AccessToken, $body_params)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.logistics.OpDeliverySendOrder.offline/';
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }
}
