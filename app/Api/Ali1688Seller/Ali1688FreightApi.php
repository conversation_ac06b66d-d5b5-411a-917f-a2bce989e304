<?php

namespace App\Api\Ali1688Seller;

class Ali1688F<PERSON>ightApi extends Ali1688BaseApi
{
    /**
     * 获取运费模板列表
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.1b55ed6ul41C&ns=com.alibaba.logistics&n=alibaba.logistics.freightTemplate.getList&v=1
     * @param string $retail_ali88_auth_uuid
     * @param int $category_id 商品类目Id，用于官方运费模板支持的筛选
     * @param int $address_id 发货地址Id，用于官方运费模板支持的筛选
     * @return mixed
     * @API  com.alibaba.logistics:alibaba.logistics.freightTemplate.getList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.getList/7587553
     */
    public static function GetAli1688LogisticsFreightTemplateList($Ali1688AccessToken, int $category_id = 0, int $address_id = 0)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.logistics.freightTemplate.getList/';
        //
        $body_params = [
            'categoryId' => $category_id,//商品类目Id，用于官方运费模板支持的筛选
            'addressId' => $address_id,//发货地址Id，用于官方运费模板支持的筛选
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 获取物流模板详情
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.logistics.myFreightTemplate.list.get-1&aopApiCategory=Logistics_NEW
     * @param string $retail_ali88_auth_uuid
     * @param int $template_id 模版id，用于单条查询的场景
     * @return mixed
     * @API  com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/7587553
     */
    public static function GetAli1688FreightTemplate($Ali1688AccessToken, int $template_id)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.list.get/';
        //
        $body_params = [
            'templateId' => $template_id,//模版id，用于单条查询的场景
            'querySubTemplate' => true,//是否查询子模板
            'queryRate' => true,//是否查询子模板费率
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 创建我的运费模板
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.1b55ed6ul41C&ns=com.alibaba.logistics&n=alibaba.logistics.myFreightTemplate.create&v=1
     * @param string $retail_ali88_auth_uuid
     * @param int $member_id 用户ID
     * @return mixed
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.create-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.create/7587553
     */
    public static function CreateAli1688FreightTemplate($Ali1688AccessToken, string $member_id = '')
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.create/';
        //
        $body_params = [
            'mainTemplate' => [],//主运费模板，必填。必填字段：name（模板名称），remark（备注），fromAreaCode（发货区编码），addressCodeText（发货区编码对应文本，以空格分割）
            'expressSubTemplate' => [],//快递子模板基本信息（必填）。必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'expressSubRateList' => [],//快递子模板的费率设置（必填）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
            'cashSubTemplate' => [],//货到付款子模板基本信息（可不填）。若需要则必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'cashSubRateList' => [],//货到付款子模板的费率设置（若cashSubTemplate为空，则此字段亦无效）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }


    /**
     * 修改我的运费模板
     * @link https://open.1688.com/api/api.htm?spm=a260s.26056301.0.0.1b55ed6ul41C&ns=com.alibaba.logistics&n=alibaba.logistics.myFreightTemplate.update&v=1
     * @param string $retail_ali88_auth_uuid
     * @param int $member_id 用户ID
     * @return mixed
     * @API  com.alibaba.logistics:alibaba.logistics.myFreightTemplate.create-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.update/7587553
     */
    public static function UpdateAli1688FreightTemplate($Ali1688AccessToken, string $member_id = '')
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.logistics.myFreightTemplate.update/';
        //
        $body_params = [
            'mainTemplate' => [],//主运费模板，必填。必填字段：name（模板名称），remark（备注），fromAreaCode（发货区编码），addressCodeText（发货区编码对应文本，以空格分割）
            'expressSubTemplate' => [],//快递子模板基本信息（必填）。必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'expressSubRateList' => [],//快递子模板的费率设置（必填）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
            'cashSubTemplate' => [],//货到付款子模板基本信息（可不填）。若需要则必填字段：chargeType（1:按重量计价，1-按件数，2-按体积），serviceChargeType（0-卖家承担运费，1-买家承担运费）
            'cashSubRateList' => [],//货到付款子模板的费率设置（若cashSubTemplate为空，则此字段亦无效）。第一个设置针对全国的费率，后面的看情况针对个别省份。必填字段见示例。
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 获取交易订单的物流跟踪信息(卖家视角)
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.sellerView-1&aopApiCategory=Logistics_NEW
     * @param        $appid
     * @param        $secret
     * @param        $access_token
     * @param string $order_id
     * @return
     * @API  com.alibaba.logistics:alibaba.trade.getLogisticsTraceInfo.sellerView-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.sellerView/4451567
     */
    public static function GetAli1688LogisticsLogisticsTraceInfoSellerView($Ali1688AccessToken, string $order_id, $logistics_id = null)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsTraceInfo.sellerView/';
        //
        $body_params = [
            'orderId' => $order_id,//交易的订单id
            'logisticsId' => $logistics_id ?? null,//该订单下的物流编号
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

    /**
     * 获取交易订单的物流跟踪信息(卖家视角)
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.logistics:alibaba.trade.ec.getLogisticsInfos.sellerView-1&aopApiCategory=Logistics_NEW
     * @param        $appid
     * @param        $secret
     * @param        $access_token
     * @param string $order_id
     * @return
     * @API  com.alibaba.logistics:alibaba.trade.ec.getLogisticsInfos.sellerView-1
     * @URL https://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.trade.ec.getLogisticsInfos.sellerView/4451567
     */
    public static function GetAli1688LogisticsInfosSellerView($Ali1688AccessToken, string $order_id, $logistics_id = null)
    {
        //
        $apiInfo = 'param2/1/com.alibaba.logistics/alibaba.trade.ec.getLogisticsInfos.sellerView/';
        //
        $body_params = [
            'orderId' => $order_id,//交易的订单id
            'logisticsId' => $logistics_id ?? null,//该订单下的物流编号
        ];
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }

}
