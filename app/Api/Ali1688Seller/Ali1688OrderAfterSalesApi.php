<?php

namespace App\Api\Ali1688Seller;

use DateTime;
use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688OrderAfterSalesApi 类
 * 此类用于处理与阿里巴巴1688平台订单售后相关的API调用，包括查询退款单列表、
 * 查询退款操作记录、卖家同意退货以及查询退款单详情等功能。
 */
class Ali1688OrderAfterSalesApi extends Ali1688BaseApi
{
    /**
     * 查询退款单列表(卖家视角)
     * 根据订单号或退款单列表查询退款单列表，有可能有延迟。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.queryOrderRefundList-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包含：
     *                                  - orderId (Long, 否): 订单Id
     *                                  - applyStartTime (Date, 否): 退款申请时间（起始）
     *                                  - applyEndTime (Date, 否): 退款申请时间(截止)
     *                                  - refundStatusSet (String[], 否): 退款状态列表
     *                                  - buyerMemberId (String, 否): 买家memberId或者buyerOpenUid（买家加密ID）
     *                                  - buyerLoginId (String, 否): 买家loginId或者buyerOpenUid（买家加密ID）
     *                                  - currentPageNum (int, 否): 查询页码，起始页码为0
     *                                  - pageSize (int, 否): 页大小
     *                                  - logisticsNo (String, 否): 退货运单号
     *                                  - modifyStartTime (Date, 否): 退款修改时间(起始)
     *                                  - modifyEndTime (Date, 否): 退款修改时间(截止)
     *                                  - dipsuteType (Integer, 否): 1:售中退款，2:售后退款；0:所有退款单
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.queryOrderRefundList-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.queryOrderRefundList/7587553
     */
    public static function queryOrderRefundListSellerView(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.queryOrderRefundList/';

        // 参数验证
        if (isset($params['orderId']) && !is_numeric($params['orderId'])) {
            throw new InvalidArgumentException('orderId 必须为数字。');
        }

        if (isset($params['applyStartTime']) && !self::validateDate($params['applyStartTime'])) {
            throw new InvalidArgumentException('applyStartTime 格式不正确，应为 yyyyMMddHHmmssSSSZ。');
        }

        if (isset($params['applyEndTime']) && !self::validateDate($params['applyEndTime'])) {
            throw new InvalidArgumentException('applyEndTime 格式不正确，应为 yyyyMMddHHmmssSSSZ。');
        }

        if (isset($params['refundStatusSet']) && !is_array($params['refundStatusSet'])) {
            throw new InvalidArgumentException('refundStatusSet 必须为数组。');
        }

        if (isset($params['currentPageNum']) && (!is_int($params['currentPageNum']) || $params['currentPageNum'] < 0)) {
            throw new InvalidArgumentException('currentPageNum 必须为非负整数。');
        }

        if (isset($params['pageSize']) && (!is_int($params['pageSize']) || $params['pageSize'] <= 0)) {
            throw new InvalidArgumentException('pageSize 必须为正整数。');
        }

        if (isset($params['dipsuteType']) && !in_array($params['dipsuteType'], [0, 1, 2], true)) {
            throw new InvalidArgumentException('dipsuteType 必须为 0, 1 或 2。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 校验日期格式
     * 校验日期是否符合 yyyyMMddHHmmssSSSZ 格式。
     * @param string $date 日期字符串
     * @return bool
     */
    private static function validateDate(string $date): bool
    {
        $d = DateTime::createFromFormat('YmdHisuO', $date);
        return $d && $d->format('YmdHisuO') === $date;
    }

    /**
     * 退款单操作记录列表
     * 查询退款单的操作记录列表。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefundOperationList-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $refundId 退款单Id
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefundOperationList-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefundOperationList/7587553
     */
    public static function queryOrderRefundOperationList(array $Ali1688AccessToken, string $refundId)
    {
        // 验证必填参数
        if (empty($refundId)) {
            throw new InvalidArgumentException('refundId 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefundOperationList/';

        // 初始化请求参数
        $params = [
            'refundId' => $refundId, // 退款单Id
            'pageNo' => 1,         // 当前页号
            'pageSize' => 100,       // 页大小
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 退款单操作-卖家同意退货
     * 卖家同意退货，并填写退货地址信息。只有退货的售中或者售后退款单，才可以调用这个接口。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpAgreeReturnGoods-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $refundId 退款单Id
     * @param string $reason 卖家同意退货的理由
     * @param array $addressInfo 退货地址信息，包含以下字段（可选）：
     *                                   - address: string 卖家收货地址
     *                                   - post: string 邮编
     *                                   - phone: string 电话
     *                                   - fullName: string 全名
     *                                   - mobilePhone: string 手机
     *                                   - discription: string 说明
     *                                   - disputeType: int 1表示售中，2表示售后
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpAgreeReturnGoods-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpAgreeReturnGoods/7587553
     */
    public static function agreeReturnGoods(array $Ali1688AccessToken, string $refundId, string $reason, array $addressInfo = [])
    {
        // 验证必填参数
        if (empty($refundId)) {
            throw new InvalidArgumentException('refundId 是必填参数。');
        }

        if (empty($reason)) {
            throw new InvalidArgumentException('reason 是必填参数。');
        }

        // 可选参数验证
        if (!empty($addressInfo)) {
            $requiredFields = ['address', 'post', 'phone', 'fullName', 'mobilePhone', 'discription', 'disputeType'];
            foreach ($requiredFields as $field) {
                if (!array_key_exists($field, $addressInfo)) {
                    throw new InvalidArgumentException("addressInfo 缺少必填字段：{$field}。");
                }
            }

            if (!in_array($addressInfo['disputeType'], [1, 2], true)) {
                throw new InvalidArgumentException('disputeType 必须为 1 或 2。');
            }
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpAgreeReturnGoods/';

        // 初始化请求参数
        $params = [
            'refundId' => $refundId, // 退款单Id
            'reason' => $reason,   // 同意退货的理由
        ];

        // 如果提供了地址信息，则添加到请求参数中
        if (!empty($addressInfo)) {
            $params = array_merge($params, $addressInfo);
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 查询退款单详情-根据订单ID（卖家视角）
     * 根据订单号实时查询退款单列表，目前只能查询到售中的退款单。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $orderId 订单ID
     * @param string $queryType 查询类型，1：活动；3:退款成功（只支持退款中和退款成功）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView/7587553
     */
    public static function queryBatchRefundByOrderIdAndStatusSellerView(array $Ali1688AccessToken, string $orderId, string $queryType)
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        if (empty($queryType) || !in_array($queryType, ['1', '3'], true)) {
            throw new InvalidArgumentException('queryType 必须为 "1" 或 "3"。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus.sellerView/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId,   // 订单ID
            'queryType' => $queryType, // 查询类型
            'payMode' => 'alipay',    // 资金分流（示例值，可根据实际需求调整）
            '7d' => '1',         // 7天无理由订单标记（1表示7天无理由订单）
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 查询退款单详情-根据退款单ID（卖家视角）
     * 查询退款单详情，同时可以查询到退款操作列表。该API需要向阿里巴巴申请权限才能访问。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund.sellerView-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $refundId 退款单业务主键 TQ+ID
     * @param bool $needTimeOutInfo 是否需要退款单的超时信息（可选）
     * @param bool $needOrderRefundOperation 是否需要退款单伴随的所有退款操作信息（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund.sellerView-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund.sellerView/7587553
     */
    public static function queryOrderRefundSellerView(array $Ali1688AccessToken, string $refundId, bool $needTimeOutInfo = false, bool $needOrderRefundOperation = false)
    {
        // 验证必填参数
        if (empty($refundId)) {
            throw new InvalidArgumentException('refundId 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund.sellerView/';

        // 初始化请求参数
        $params = [
            'refundId' => $refundId,                  // 退款单Id
            'needTimeOutInfo' => $needTimeOutInfo ? 'true' : 'false',          // 是否需要退款单的超时信息
            'needOrderRefundOperation' => $needOrderRefundOperation ? 'true' : 'false', // 是否需要退款单伴随的所有退款操作信息
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 查询退款单详情-根据订单ID
     * 根据订单号实时查询退款单列表，目前只能查询到售中的退款单。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $orderId 订单ID
     * @param string $queryType 查询类型，1：活动；3:退款成功（只支持退款中和退款成功）
     * @param int $disputeType 纠纷类型，1表示售中，2表示售后（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus/7587553
     */
    public static function queryBatchRefundByOrderIdAndStatus(array $Ali1688AccessToken, string $orderId, string $queryType, int $disputeType = 1)
    {
        // 验证必填参数
        if (empty($orderId)) {
            throw new InvalidArgumentException('orderId 是必填参数。');
        }

        if (empty($queryType) || !in_array($queryType, ['1', '3'], true)) {
            throw new InvalidArgumentException('queryType 必须为 "1" 或 "3"。');
        }

        if (!in_array($disputeType, [1, 2], true)) {
            throw new InvalidArgumentException('disputeType 必须为 1 或 2。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryBatchRefundByOrderIdAndStatus/';

        // 初始化请求参数
        $params = [
            'orderId' => $orderId,     // 订单ID
            'queryType' => $queryType,   // 查询类型
            'payMode' => 'alipay',     // 资金分流（示例值，可根据实际需求调整）
            '7d' => '1',          // 7天无理由订单标记（1表示7天无理由订单）
            'disputeType' => $disputeType, // 纠纷类型
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 查询退款单详情-根据退款单ID
     * 查询退款单详情。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param string $refundId 退款单业务主键 TQ+ID
     * @param bool $needTimeOutInfo 是否需要退款单的超时信息（可选）
     * @param bool $needOrderRefundOperation 是否需要退款单伴随的所有退款操作信息（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.refund.OpQueryOrderRefund-1
     * @URL  http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund/7587553
     */
    public static function queryOrderRefund(array $Ali1688AccessToken, string $refundId, bool $needTimeOutInfo = false, bool $needOrderRefundOperation = false)
    {
        // 验证必填参数
        if (empty($refundId)) {
            throw new InvalidArgumentException('refundId 是必填参数。');
        }

        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.refund.OpQueryOrderRefund/';

        // 初始化请求参数
        $params = [
            'refundId' => $refundId,                  // 退款单Id
            'needTimeOutInfo' => $needTimeOutInfo ? 'true' : 'false',          // 是否需要退款单的超时信息
            'needOrderRefundOperation' => $needOrderRefundOperation ? 'true' : 'false', // 是否需要退款单伴随的所有退款操作信息
            'payMode' => 'alipay',                    // 资金分流（示例值，可根据实际需求调整）
            '7d' => '1',                         // 7天无理由订单标记（1表示7天无理由订单）
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }
}
