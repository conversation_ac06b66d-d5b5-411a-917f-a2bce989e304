<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688OtherServicesApi 类
 * 此类用于处理与阿里巴巴1688平台其他服务相关的API调用，包括获取商家的授权品牌信息、
 * 批量提交订单评价、查看订单详情和列表、管理推荐商品组合以及查询和获取商品质量星级建议等功能。
 */
class Ali1688OtherServicesApi extends Ali1688BaseApi
{
    /**
     * 获取商家的授权品牌信息
     * 针对实力商家，1688平台上有授权的品牌信息，商家发布商品时只能选择有授权的品牌。
     * 通过指定商品叶子类目，查询该类目下绑定的授权品牌列表信息，如果没有授权，返回信息为空。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.member:alibaba.member.brand.query-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $leafCatIds 需要查询的商品叶子类目id列表，示例：[12306]
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.member:alibaba.member.brand.query-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.member/alibaba.member.brand.query-1/7587553
     */
    public static function queryAuthorizedBrands(array $Ali1688AccessToken, array $leafCatIds)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.member/alibaba.member.brand.query/';

        // 参数验证
        if (empty($leafCatIds)) {
            throw new InvalidArgumentException('leafCatIds 是必填参数，且不能为空。');
        }

        foreach ($leafCatIds as $catId) {
            if (!is_int($catId) || $catId <= 0) {
                throw new InvalidArgumentException('leafCatIds 中的每个类目ID必须为正整数。');
            }
        }

        // 初始化请求参数
        $params = [
            'leafCatIds' => $leafCatIds,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 批量提交订单评价
     * 支持多笔订单（暂定最多10笔每次）同时提交评价，并且只支持卖家向买家的评价。
     * 当某笔订单存在多个商品时，只能为这笔订单的这些商品提交相同的评价内容。
     * @link https://open.1688.com/api/apidocdetail.htm?id=cn.alibaba.open:trade.order.batch.rate-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $ratingsData 订单评价数据，格式为：
     *                                               [
     *                                               'orderId1' => [
     *                                               [
     *                                               'starLevel' => 5,
     *                                               'content'   => '评价内容'
     *                                               ],
     *                                               // ...
     *                                               ],
     *                                               // ...
     *                                               ]
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  cn.alibaba.open:trade.order.batch.rate-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.order.batch.rate-1/7587553
     */
    public static function batchSubmitOrderRatings(array $Ali1688AccessToken, array $ratingsData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.order.batch.rate/';

        // 参数验证
        if (empty($ratingsData)) {
            throw new InvalidArgumentException('ratingsData 是必填参数，且不能为空。');
        }

        if (count($ratingsData) > 10) {
            throw new InvalidArgumentException('ratingsData 中的订单数量不能超过10。');
        }

        foreach ($ratingsData as $orderId => $rateParams) {
            if (!is_string($orderId) && !is_int($orderId)) {
                throw new InvalidArgumentException('订单ID必须为字符串或整数。');
            }
            if (!is_array($rateParams) || empty($rateParams)) {
                throw new InvalidArgumentException("订单ID {$orderId} 的评价内容不能为空。");
            }
            foreach ($rateParams as $rateParam) {
                if (!isset($rateParam['starLevel']) || !is_int($rateParam['starLevel']) || $rateParam['starLevel'] < 1 || $rateParam['starLevel'] > 5) {
                    throw new InvalidArgumentException("订单ID {$orderId} 的starLevel必须为1-5的整数。");
                }
                if ($rateParam['starLevel'] < 4 && empty($rateParam['content'])) {
                    throw new InvalidArgumentException("订单ID {$orderId} 的starLevel小于4时，content是必填参数。");
                }
            }
        }

        // 初始化请求参数
        $params = [
            'orders' => $ratingsData,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 加密场景订单详情查看（卖家视角）
     * 获取单个交易明细信息，仅限卖家调用。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.ec.getOrder.sellerView-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $orderId 交易的订单ID
     * @param array $additionalParams 可选参数，包括 includeFields, needOutChannel, needSendGoodsOverdueRisk, needDeliverGoodsOverdueRisk 等
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.ec.getOrder.sellerView-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.ec.getOrder.sellerView-1/7587553
     */
    public static function getOrderDetailsSellerView(array $Ali1688AccessToken, int $orderId, array $additionalParams = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.ec.getOrder.sellerView/';

        // 参数验证
        if ($orderId <= 0) {
            throw new InvalidArgumentException('orderId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'orderId' => $orderId,
        ];

        // 合并可选参数
        if (!empty($additionalParams)) {
            $params = array_merge($params, $additionalParams);
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 加密场景订单列表查看（卖家视角）
     * 获取卖家订单列表，用户的memberId必须等于订单的sellerMemberId。该接口仅返回订单基本信息，
     * 不包含订单的物流信息和发票信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.ec.getOrderList.sellerView-1&aopApiCategory=trade_new
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包括 createStartTime, createEndTime, modifyStartTime, modifyEndTime, page, pageSize, orderStatus, refundStatus, buyerMemberId, buyerLoginId, tradeType, bizTypes, isHis, productName, needBuyerAddressAndPhone, needMemoInfo, tousuStatus, buyerRateStatus, sellerRateStatus, needCheckSend, needOutChannel, needSendGoodsOverdueRisk, needDeliverGoodsOverdueRisk, needOfficialLogisticOrder 等
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:alibaba.trade.ec.getOrderList.sellerView-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.ec.getOrderList.sellerView-1/7587553
     */
    public static function getOrderListSellerView(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/alibaba.trade.ec.getOrderList.sellerView/';

        // 参数验证（根据需要可添加更详细的验证）
        if (isset($params['page']) && (!is_int($params['page']) || $params['page'] < 1)) {
            throw new InvalidArgumentException('page 必须为大于等于1的整数。');
        }

        if (isset($params['pageSize']) && (!is_int($params['pageSize']) || $params['pageSize'] < 1 || $params['pageSize'] > 20)) {
            throw new InvalidArgumentException('pageSize 必须为1-20之间的整数。');
        }

        if (isset($params['bizTypes']) && is_array($params['bizTypes'])) {
            $params['bizTypes'] = implode(',', $params['bizTypes']);
        }

        if (isset($params['loginIdList']) && is_array($params['loginIdList'])) {
            $params['loginIdList'] = implode(',', $params['loginIdList']);
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据用户查询商品组合信息
     * 根据用户查询其推荐的商品组合信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.recommend.queryList-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productPromoteParam 入参模型，包含 pageNo 和 pageSize
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.recommend.queryList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.recommend.queryList-1/7587553
     */
    public static function queryProductCombinationByUser(array $Ali1688AccessToken, array $productPromoteParam)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.recommend.queryList/';

        // 参数验证
        if (empty($productPromoteParam)) {
            throw new InvalidArgumentException('productPromoteParam 是必填参数，且不能为空。');
        }

        if (!isset($productPromoteParam['pageNo']) || !is_int($productPromoteParam['pageNo'])) {
            throw new InvalidArgumentException('productPromoteParam 中的 pageNo 是必填参数，且必须为整数。');
        }

        if (!isset($productPromoteParam['pageSize']) || !is_int($productPromoteParam['pageSize'])) {
            throw new InvalidArgumentException('productPromoteParam 中的 pageSize 是必填参数，且必须为整数。');
        }

        // 初始化请求参数
        $params = $productPromoteParam;

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据groupId获取商品列表
     * 根据推荐分组ID获取该分组下的商品列表。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.recommend.queryProductListByGroupId-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $groupId 分组ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.recommend.queryProductListByGroupId-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.recommend.queryProductListByGroupId-1/7587553
     */
    public static function getProductsByGroupId(array $Ali1688AccessToken, int $groupId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.recommend.queryProductListByGroupId/';

        // 参数验证
        if ($groupId <= 0) {
            throw new InvalidArgumentException('groupId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'groupId' => $groupId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 保存用户推荐商品组合
     * 保存用户推荐的商品组合信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.recommend.saveProductGroup-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $groupData 分组数据，包含 groupId（可选）、groupType、memo（可选）、offerIds、title
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.recommend.saveProductGroup-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.recommend.saveProductGroup-1/7587553
     */
    public static function saveUserRecommendedProductGroup(array $Ali1688AccessToken, array $groupData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.recommend.saveProductGroup/';

        // 参数验证
        if (empty($groupData)) {
            throw new InvalidArgumentException('groupData 是必填参数，且不能为空。');
        }

        if (!isset($groupData['groupType']) || !in_array($groupData['groupType'], [0, 1])) {
            throw new InvalidArgumentException('groupType 是必填参数，且必须为0（热推）或1（组合推荐）。');
        }

        if (!isset($groupData['offerIds']) || !is_array($groupData['offerIds']) || empty($groupData['offerIds'])) {
            throw new InvalidArgumentException('offerIds 是必填参数，且必须为非空数组。');
        }

        if (!isset($groupData['title']) || empty($groupData['title'])) {
            throw new InvalidArgumentException('title 是必填参数，且不能为空。');
        }

        // 处理可选参数
        $params = [
            'groupType' => $groupData['groupType'],
            'offerIds' => $groupData['offerIds'],
            'title' => $groupData['title'],
        ];

        if (isset($groupData['groupId'])) {
            $params['groupId'] = $groupData['groupId'];
        }

        if (isset($groupData['memo'])) {
            $params['memo'] = $groupData['memo'];
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 修改商家热推分组信息
     * 修改指定的热推分组信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.recommend.editProductGroup-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $groupData 分组数据，包含 groupId、groupType、memo（可选）、offerIds、title
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.recommend.editProductGroup-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.recommend.editProductGroup-1/7587553
     */
    public static function editHotRecommendGroup(array $Ali1688AccessToken, array $groupData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.recommend.editProductGroup/';

        // 参数验证
        if (empty($groupData)) {
            throw new InvalidArgumentException('groupData 是必填参数，且不能为空。');
        }

        if (!isset($groupData['groupId']) || $groupData['groupId'] <= 0) {
            throw new InvalidArgumentException('groupId 是必填参数，且必须为正整数。');
        }

        if (!isset($groupData['groupType']) || !in_array($groupData['groupType'], [0, 1])) {
            throw new InvalidArgumentException('groupType 是必填参数，且必须为0（热推）或1（组合推荐）。');
        }

        if (!isset($groupData['offerIds']) || !is_array($groupData['offerIds']) || empty($groupData['offerIds'])) {
            throw new InvalidArgumentException('offerIds 是必填参数，且必须为非空数组。');
        }

        if (!isset($groupData['title']) || empty($groupData['title'])) {
            throw new InvalidArgumentException('title 是必填参数，且不能为空。');
        }

        // 处理可选参数
        $params = [
            'groupId' => $groupData['groupId'],
            'groupType' => $groupData['groupType'],
            'offerIds' => $groupData['offerIds'],
            'title' => $groupData['title'],
        ];

        if (isset($groupData['memo'])) {
            $params['memo'] = $groupData['memo'];
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 删除一个推荐分组
     * 根据分组ID删除指定的推荐分组。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.product.recommend.deleteProductGroup-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $groupId 分组ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:alibaba.product.recommend.deleteProductGroup-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.recommend.deleteProductGroup-1/7587553
     */
    public static function deleteRecommendedGroup(array $Ali1688AccessToken, int $groupId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.recommend.deleteProductGroup/';

        // 参数验证
        if ($groupId <= 0) {
            throw new InvalidArgumentException('groupId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'groupId' => $groupId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 根据商品ID获取质量星级建议
     * （修改商品场景）根据商品ID获取质量星级建议。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:com.alibaba.cbu.offer.quality.advice.query-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:com.alibaba.cbu.offer.quality.advice.query-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/com.alibaba.cbu.offer.quality.advice.query-1/7587553
     */
    public static function queryQualityStarAdvice(array $Ali1688AccessToken, int $productId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.query/';

        // 参数验证
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'productID' => $productId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 根据商品ID列表获取质量星级优化建议
     * （修改商品场景）根据商品ID列表获取质量星级优化建议。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:com.alibaba.cbu.offer.quality.advice.queryList-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $productIds 商品ID列表，最多查询100个
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:com.alibaba.cbu.offer.quality.advice.queryList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/com.alibaba.cbu.offer.quality.advice.queryList-1/7587553
     */
    public static function queryQualityStarAdviceList(array $Ali1688AccessToken, array $productIds)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.cbu.offer/com.alibaba.cbu.offer.quality.advice.queryList/';

        // 参数验证
        if (empty($productIds)) {
            throw new InvalidArgumentException('productIds 是必填参数，且不能为空。');
        }

        if (count($productIds) > 100) {
            throw new InvalidArgumentException('productIds 数量不能超过100。');
        }

        foreach ($productIds as $id) {
            if (!is_int($id) || $id <= 0) {
                throw new InvalidArgumentException('productIds 中的每个商品ID必须为正整数。');
            }
        }

        // 初始化请求参数
        $params = [
            'productIDs' => implode(',', $productIds),
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取商品质量星级相关信息
     * 获取商品质量星级相关信息，包括质量星级和质量优化建议等。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:product.quality.getInfo-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $productId 商品ID
     * @param array $additionalParams 可选参数，包括 catId, dataBody, offerId, scene, bizParam
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.product:product.quality.getInfo-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.quality.getInfo-1/7587553
     */
    public static function getProductQualityInfo(array $Ali1688AccessToken, int $productId, array $additionalParams = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.product/alibaba.product.quality.getInfo/';

        // 参数验证
        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'offerId' => $productId,
        ];

        // 合并可选参数
        if (!empty($additionalParams)) {
            $params = array_merge($params, $additionalParams);
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
