<?php

namespace App\Api\Ali1688Seller;

class Ali1688SchemaApi extends Ali1688BaseApi
{
    /**
     * 推送商品信息至Ali1688
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.product:alibaba.new.product.getSchema-1
     * @param array $Ali1688AccessToken 条件
     * @param int $catId 商品类目id
     * @return mixed
     * @API  com.alibaba.product / alibaba.product.add - 1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.product:alibaba.new.product.getSchema-1/7587553
     */
    public static function GetSAli1688Schema($Ali1688AccessToken, $catId)
    {
        //
        set_time_limit(0);
        //
        $apiInfo = 'param2/1/com.alibaba.product/alibaba.new.product.getSchema/';
        //
        $body_params = [
            'catId' => $catId,//商品类目id
            'scene' => "popular",//具体场景，默认为cbu
            //'offerId' => "",//商品id
        ];
        //
        //dd($Ali1688AccessToken, $catId);
        //
        // 调用实际的 API 请求方法，传递凭证和参数
        return Ali1688BaseApi::callApi(
            $Ali1688AccessToken['client_id'],
            $Ali1688AccessToken['client_secret'],
            $Ali1688AccessToken['access_token'],
            $apiInfo,
            $body_params,
            'GET'
        );
    }
}
