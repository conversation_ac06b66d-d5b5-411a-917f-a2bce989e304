<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688ServiceInfoApi 类
 * 此类用于处理与阿里巴巴1688平台订单履约相关的API调用，包括获取用户的履约相关数据等功能。
 */
class Ali1688ServiceInfoApi extends Ali1688BaseApi
{
    /**
     * 获取用户的履约相关数据
     * 获取用户的履约相关数据，包括履约诊断数据等。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:order.fulfillment.getList-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包括 fulfillmentMemberRequest
     *                                     fulfillmentMemberRequest 包含：
     *                                     - forceSource (boolean, 必填): 强制跳过缓存，从数据侧读取
     *                                     - needDiagnose (boolean, 必填): 是否需要履约诊断数据
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.trade:order.fulfillment.getList-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/order.fulfillment.getList-1/7587553
     */
    public static function getFulfillmentData(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.trade/order.fulfillment.getList/';


        if (empty($params)) {
            throw new InvalidArgumentException('params 是必填参数，且不能为空。');
        }

        if (!isset($params['fulfillmentMemberRequest']) || !is_array($params['fulfillmentMemberRequest'])) {
            throw new InvalidArgumentException('fulfillmentMemberRequest 是必填参数，且必须为数组。');
        }

        $fulfillmentRequest = $params['fulfillmentMemberRequest'];

        if (!isset($fulfillmentRequest['forceSource']) || !is_bool($fulfillmentRequest['forceSource'])) {
            throw new InvalidArgumentException('fulfillmentMemberRequest 中的 forceSource 是必填参数，且必须为布尔值。');
        }

        if (!isset($fulfillmentRequest['needDiagnose']) || !is_bool($fulfillmentRequest['needDiagnose'])) {
            throw new InvalidArgumentException('fulfillmentMemberRequest 中的 needDiagnose 是必填参数，且必须为布尔值。');
        }

        // 初始化请求参数
        $requestParams = [
            'fulfillmentMemberRequest' => [
                'forceSource' => $fulfillmentRequest['forceSource'],
                'needDiagnose' => $fulfillmentRequest['needDiagnose'],
            ],
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $requestParams,                          // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
