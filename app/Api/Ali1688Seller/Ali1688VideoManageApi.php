<?php

namespace App\Api\Ali1688Seller;

use InvalidArgumentException;
use RuntimeException;

/**
 * Ali1688VideoManageApi 类
 * 此类用于处理与阿里巴巴1688平台视频管理相关的API调用，包括获取单个视频、
 * 列出用户视频、上传视频、检查存储空间、修改视频、删除视频以及管理视频与商品的关联关系等功能。
 */
class Ali1688VideoManageApi extends Ali1688BaseApi
{
    /**
     * 获取单个视频
     * 根据视频ID获取视频的详细信息。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.get-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $videoId 视频ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.get-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.get-1/7587553
     */
    public static function getSingleVideo(array $Ali1688AccessToken, int $videoId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.get/';

        // 参数验证
        if ($videoId <= 0) {
            throw new InvalidArgumentException('videoId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'videoId' => $videoId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 获取用户视频中心的视频列表
     * 分页查询获取用户视频中心的视频列表，默认返回第一页，每页十条记录。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:albaba.video.videocenter.list-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $params 查询参数，包括 page 和 pageSize
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:albaba.video.videocenter.list-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.list-1/7587553
     */
    public static function listUserVideos(array $Ali1688AccessToken, array $params = [])
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.list/';

        // 参数验证
        if (isset($params['page']) && (!is_int($params['page']) || $params['page'] < 1)) {
            throw new InvalidArgumentException('page 必须为大于等于1的整数。');
        }

        if (isset($params['pageSize']) && (!is_int($params['pageSize']) || $params['pageSize'] < 1 || $params['pageSize'] > 50)) {
            throw new InvalidArgumentException('pageSize 必须为1-50之间的整数。');
        }

        // 设置默认参数
        $params = array_merge([
            'page' => 1,
            'pageSize' => 10,
        ], $params);

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 上传视频至视频中心
     * 通过视频地址或视频文件，将视频上传到用户的视频中心。视频最大为50MB。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.upload-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param array $videoData 视频数据，包括 fileName（必填）、fileUrl 或 fileData、description（可选）、isShopAnnounce、isDemandVideo、offerId（可选）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.upload-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.upload-1/7587553
     */
    public static function uploadVideo(array $Ali1688AccessToken, array $videoData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.upload/';

        // 参数验证
        if (empty($videoData)) {
            throw new InvalidArgumentException('videoData 是必填参数，且不能为空。');
        }

        if (empty($videoData['fileName'])) {
            throw new InvalidArgumentException('fileName 是必填参数，且不能为空。');
        }

        if (empty($videoData['fileUrl']) && empty($videoData['fileData'])) {
            throw new InvalidArgumentException('fileUrl 和 fileData 至少填写一个。');
        }

        if (!empty($videoData['fileUrl']) && !filter_var($videoData['fileUrl'], FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException('fileUrl 必须是有效的URL地址。');
        }

        if (!empty($videoData['fileData']) && !is_resource($videoData['fileData'])) {
            throw new InvalidArgumentException('fileData 必须是有效的文件资源。');
        }

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $videoData,                              // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 视频中心存储空间检测
     * 检测用户的视频中心存储空间，目前仅支持视频制作后上传到1688店铺的场景。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.videoCheck-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @return mixed
     * @throws RuntimeException 如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.videoCheck-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.videoCheck-1/7587553
     */
    public static function checkVideoStorageSpace(array $Ali1688AccessToken)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.videoCheck/';

        // 初始化请求参数
        $params = [];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'GET'                                    // 请求方法
        );
    }

    /**
     * 修改视频中心视频
     * 根据视频ID修改视频的相关信息，如名称和描述。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.modify-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $videoId 视频ID
     * @param array $videoData 视频数据，包括 name（必填）、description（必填）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.modify-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.modify-1/7587553
     */
    public static function modifyVideo(array $Ali1688AccessToken, int $videoId, array $videoData)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.modify/';

        // 参数验证
        if ($videoId <= 0) {
            throw new InvalidArgumentException('videoId 必须为正整数。');
        }

        if (empty($videoData)) {
            throw new InvalidArgumentException('videoData 是必填参数，且不能为空。');
        }

        if (empty($videoData['name'])) {
            throw new InvalidArgumentException('name 是必填参数，且不能为空。');
        }

        if (empty($videoData['description'])) {
            throw new InvalidArgumentException('description 是必填参数，且不能为空。');
        }

        // 合并视频ID到请求参数
        $params = array_merge(['videoId' => $videoId], $videoData);

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 删除视频中心视频
     * 根据视频ID删除指定的视频。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.delete-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $videoId 视频ID
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.delete-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.delete-1/7587553
     */
    public static function deleteVideo(array $Ali1688AccessToken, int $videoId)
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.delete/';

        // 参数验证
        if ($videoId <= 0) {
            throw new InvalidArgumentException('videoId 必须为正整数。');
        }

        // 初始化请求参数
        $params = [
            'videoId' => $videoId,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 1688商品关联视频中心视频
     * 将视频中心的一个视频关联到一个1688商品，同时可以设置为商品主图视频或商品详情视频。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.attach-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $videoId 视频ID
     * @param int $productId 商品ID
     * @param string $location 视频关联商品位置，支持: mainPictureVideo（主图视频）、detailVideo（详情视频）、all（所有）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.attach-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.attach-1/7587553
     */
    public static function attachVideoToProduct(array $Ali1688AccessToken, int $videoId, int $productId, string $location = 'all')
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.attach/';

        // 参数验证
        if ($videoId <= 0) {
            throw new InvalidArgumentException('videoId 必须为正整数。');
        }

        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        $allowedLocations = ['mainPictureVideo', 'detailVideo', 'all'];
        if (!in_array($location, $allowedLocations)) {
            throw new InvalidArgumentException('location 必须为 mainPictureVideo、detailVideo 或 all。');
        }

        // 初始化请求参数
        $params = [
            'videoId' => $videoId,
            'productId' => $productId,
            'location' => $location,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 解绑视频和商品的关联关系
     * 根据视频ID和商品ID，解除视频与商品的关联关系。
     * @link https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.multimedia:alibaba.video.videocenter.detach-1
     * @param array $Ali1688AccessToken 用户授权信息，包括 client_id, client_secret, access_token
     * @param int $videoId 视频ID
     * @param int $productId 商品ID
     * @param string $location 视频关联商品位置，支持: mainPictureVideo（主图视频）、detailVideo（详情视频）、all（所有）
     * @return mixed
     * @throws InvalidArgumentException 如果参数不符合要求
     * @throws RuntimeException         如果API请求失败
     * @API  com.alibaba.multimedia:alibaba.video.videocenter.detach-1
     * @URL http://gw.open.1688.com/openapi/param2/1/com.alibaba.video/alibaba.video.videocenter.detach-1/7587553
     */
    public static function detachVideoFromProduct(array $Ali1688AccessToken, int $videoId, int $productId, string $location = 'all')
    {
        // 定义API路径
        $apiPath = 'param2/1/com.alibaba.video/alibaba.video.videocenter.detach/';

        // 参数验证
        if ($videoId <= 0) {
            throw new InvalidArgumentException('videoId 必须为正整数。');
        }

        if ($productId <= 0) {
            throw new InvalidArgumentException('productId 必须为正整数。');
        }

        $allowedLocations = ['mainPictureVideo', 'detailVideo', 'all'];
        if (!in_array($location, $allowedLocations)) {
            throw new InvalidArgumentException('location 必须为 mainPictureVideo、detailVideo 或 all。');
        }

        // 初始化请求参数
        $params = [
            'videoId' => $videoId,
            'productId' => $productId,
            'location' => $location,
        ];

        // 调用API
        return self::callApi(
            $Ali1688AccessToken['client_id'],        // 客户端ID
            $Ali1688AccessToken['client_secret'],    // 客户端密钥
            $Ali1688AccessToken['access_token'],     // 访问令牌
            $apiPath,                                // API路径
            $params,                                 // 请求参数
            'POST'                                   // 请求方法
        );
    }

    /**
     * 通用API调用方法
     * 此方法用于与阿里巴巴1688 API 进行实际的HTTP通信，处理请求的构建、发送、响应解析以及错误处理。
     * 假设该方法已经在 Ali1688BaseApi 类中实现。
     * 如果 Ali1688BaseApi 类中尚未实现该方法，请确保在该基类中实现类似的方法。
     */
}
