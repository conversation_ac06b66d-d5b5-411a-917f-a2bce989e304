<?php

namespace App\Enums;

use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;
use Spatie\Enum\Laravel\Enum;

class BaseEnum extends Enum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    public function color(): string
    {
        //
        $value = $this->value;
        $colors = static::colors();
        return $colors[$value] ?? 'purple';  // 如果找不到对应的颜色，默认返回 'purple'
    }

    // 获取当前枚举值的颜色

    protected static function colors(): array
    {
        //
        return [
            0 => '#708090', // 石板灰
            1 => '#007FFF', // 湛蓝色
            2 => '#FF1493', // 深粉红
            3 => '#DC143C', // 猩红
            4 => '#CC0066', // 亮粉红
            5 => '#BF00FF', // 电紫色
            6 => '#3F00FF', // 电蓝
            7 => '#FF00FF', // 品红色
            8 => '#E25822', // 火焰红
            9 => '#FF007F', // 玫瑰红
            10 => '#FF4500', // 橘红色
            11 => '#99004D', // 粉紫
            12 => '#DE3163', // 覆盆子红
            13 => '#D2691E', // 深南瓜橙
            14 => '#6A5ACD', // 板岩蓝
            15 => '#483D8B', // 青金石色
            16 => '#0047AB', // 钴蓝
            17 => '#006994', // 太平洋蓝
            18 => '#556B2F', // 橄榄绿
            19 => '#6B8E23', // 橄榄褐
            20 => '#7B68EE', // 中亮蓝紫
            21 => '#0066FF', // 天蓝色
            22 => '#1E8FFF', // 天蓝色
            23 => '#1E90FF', // 闪亮蓝
            24 => '#0096FF', // 蔚蓝
            25 => '#00A1FF', // 海岸蓝
            26 => '#0000FF', // 纯蓝
            27 => '#0000CD', // 中蓝
            28 => '#0000B3', // 亮蓝
            29 => '#00008B', // 深蓝
            30 => '#CC0000', // 暗鲜红
            31 => '#FF6347', // 浅红橙
            32 => '#FF4D4D', // 亮番茄红
            33 => '#FF4040', // 珊瑚红
            34 => '#FF0000', // 鲜红色
            35 => '#E60000', // 烈焰红
            36 => '#B30000', // 深红宝石
            37 => '#990000', // 暗红色
            38 => '#660000', // 深红
            39 => '#8000FF', // 电气紫
            40 => '#A020F0', // 亮紫色
            41 => '#9400D3', // 深紫色
            42 => '#993399', // 深紫罗兰
            43 => '#800080', // 深洋红
            44 => '#660066', // 暗紫
            45 => '#8A2BE2', // 深蓝紫
            46 => '#6A0DAD', // 紫水晶
            47 => '#663399', // 重蓝紫
            48 => '#4B0082', // 靛青
            49 => '#CC5500', // 暗橙色
            50 => '#D2691E', // 巧克力色
            51 => '#CC6600', // 亮橙棕
            52 => '#B35900', // 暗琥珀橙
            53 => '#994C00', // 黄褐色
            54 => '#FFA500', // 橙色
            55 => '#FF9933', // 亮橙色
            56 => '#FF8C00', // 深橙色
            57 => '#FF7518', // 南瓜橙
            58 => '#FF6600', // 橙红色
            59 => '#7FFF00', // 翡翠
            60 => '#00CC00', // 嫩绿
            61 => '#00B300', // 亮绿
            62 => '#7CFC00', // 亮草绿
            63 => '#009900', // 亮草绿
            64 => '#228B22', // 森林绿
            65 => '#008B45', // 暗春绿
            66 => '#008000', // 草绿
            67 => '#016936', // 深碧绿
            68 => '#004D00', // 深苔绿
            69 => '#F6FF00', // 荧光黄
            70 => '#E6E600', // 柠檬黄
            71 => '#FFD700', // 金色
            72 => '#FFC125', // 亮琥珀色
            73 => '#FFB347', // 浅橙色
            74 => '#A0522D', // 红杉棕
            75 => '#964B00', // 栗棕色
            76 => '#8B4513', // 马鞍棕
            77 => '#7B3F00', // 棕褐色
            78 => '#704214', // 深咖棕
            79 => '#9B870C', // 深卡其布
            80 => '#808000', // 橄榄色
            81 => '#8B8000', // 深金菊黄
            82 => '#8C7000', // 深金色
            83 => '#806000', // 暗金色
            84 => '#C7EA46', // 青柠绿
            85 => '#B0E313', // 鲜嫩绿
            86 => '#9ACD32', // 黄绿色
            87 => '#8DB600', // 苹果绿
            88 => '#6B8E23', // 黄绿色
            89 => '#009B77', // 热带雨林绿
            90 => '#00B894', // 加勒比海绿
            91 => '#009F6B', // 青竹绿
            92 => '#00A36C', // 孔雀石绿
            93 => '#2E8B57', // 海绿
            94 => '#008B8B', // 暗青绿
            95 => '#01796F', // 松柏绿
            96 => '#006A4E', // 暗海洋绿
            97 => '#007BA7', // 钴蓝
            98 => '#005073', // 深青蓝
            99 => '#00FA9A', // 春绿色
            100 => '#40E0D0', //青绿
        ];
    }
}
