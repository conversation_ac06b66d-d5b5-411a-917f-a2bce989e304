<?php

namespace App\Enums\Client;

use App\Enums\BaseEnum;


/**
 * Class PaymentClientEnum
 * @package App\Enums\Client
 * @method static self default() 默认状态
 * @method static self Web() web状态
 * @method static self miniprogram() 小程序状态
 * @method static self PublicAccount() 公众号状态
 * @method static self App() app状态
 * @method static self QRCodePayment() 扫码支付状态
 * @method static self H5() H5状态
 * @method static self Cashier() 收银台状态
 */
final class PaymentClientEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'Web' => 1,
            'miniprogram' => 2,
            'PublicAccount' => 3,
            'App' => 4,
            'QRCodePayment' => 5,
            'H5' => 6,
            'Cashier' => 7,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'Web' => 'web',
            'miniprogram' => '小程序',
            'PublicAccount' => '公众号',
            'App' => 'app',
            'QRCodePayment' => '扫码支付',
            'H5' => 'H5',
            'Cashier' => '收银台',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',    // default
            1 => 'red',              // Web
            2 => 'orange',           // miniprogram
            3 => 'yellow',           // PublicAccount
            4 => 'green',            // App
            5 => 'purple',           // QRCodePayment
            6 => 'blue',             // H5
            7 => 'pink',             // Cashier
        ];
    }
}
