<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * Class ProductArchivalStateEnum
 * @package App\Enums\State\Product
 * @method static self default() 默认状态
 * @method static self NotArchived() 未归档状态
 * @method static self Archived() 已归档状态（归档产品不能进行修改）
 */
final class IsArchivalEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        return [
            'default' => 0,
            'NotArchived' => 0,
            'Archived' => 1,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'NotArchived' => '未归档',
            'Archived' => '已归档 （归档产品不能进行修改）',
        ];
    }
}
