<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * binding 状态枚举
 * @package App\Enums\Type\User
 * @method static self unbinding()
 * @method static self binding()
 */
final class IsBindingEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    // 定义状态值
    protected static function values(): array
    {
        return [
            'unbinding' => 0,// 未绑定
            'binding' => 1,  // 已绑定
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'unbinding' => '未绑定',  // 未绑定状态
            'binding' => '已绑定',    // 已绑定状态
        ];
    }
}
