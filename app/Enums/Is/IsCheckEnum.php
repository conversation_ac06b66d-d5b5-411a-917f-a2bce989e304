<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;

/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self disabled()
 * @method static self normal()
 * @method static self pending()
 * @method static self review_rejected()
 * @method static self review_ignored()
 */
final class IsCheckEnum extends BaseEnum
{
    protected static function values(): array
    {
        //
        return [
            'disabled' => 0,
            'normal' => 1,
            'pending' => 2,
            'review_rejected' => 3,
            'review_ignored' => 4,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'disabled' => '禁用',
            'normal' => '正常',
            'pending' => '待审',
            'review_rejected' => '审核拒绝',
            'review_ignored' => '审核忽略',
        ];
    }
}
