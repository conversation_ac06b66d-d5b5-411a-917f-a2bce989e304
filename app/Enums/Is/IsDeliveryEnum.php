<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * 是否开通枚举
 * @package App\Enums\Type\User
 * @method static self NotDelivery()
 * @method static self Delivery()
 */
final class IsDeliveryEnum extends BaseEnum
{


    // 定义枚举值
    protected static function values(): array
    {
        return [
            'NotDelivery' => 0,
            'Delivery' => 1,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'NotDelivery' => '未开通',
            'Delivery' => '开通',
        ];
    }
}
