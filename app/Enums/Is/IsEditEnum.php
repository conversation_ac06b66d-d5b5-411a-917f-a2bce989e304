<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;

/**
 * @method static self editable() 可编辑状态
 * @method static self disabled() 不可编辑状态
 */
final class IsEditEnum extends BaseEnum
{
    // Define status values
    protected static function values(): array
    {
        //
        return [
            'editable' => 0, // 可编辑
            'disabled' => 1, // 不可编辑
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'editable' => '可编辑',
            'disabled' => '不可编辑',
        ];
    }
}
