<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 处理状态枚举
 * @package App\Enums\Is
 * @method static self unprocessed()
 * @method static self processed()
 */
final class IsProcessedEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    /**
     * 定义状态值
     * @return array
     */
    protected static function values(): array
    {
        return [
            'unprocessed' => 0, // 未处理
            'processed' => 1, // 已处理
        ];
    }

    /**
     * 定义状态标签
     * @return array
     */
    protected static function labels(): array
    {
        return [
            'unprocessed' => '未处理',
            'processed' => '已处理',
        ];
    }
}
