<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * Class ProductPushStateEnum
 * @package App\Enums\State\Product
 * @method static self default() 默认状态
 * @method static self NotDistributable() 不可铺货状态
 * @method static self Distributable() 可铺货状态
 */
final class IsPushEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'NotDistributable' => 1,
            'Distributable' => 2,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'NotDistributable' => '不可铺货',
            'Distributable' => '可铺货',
        ];
    }
}
