<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self NotRequired()
 * @method static self Required()
 */
final class IsRequiredEnum extends BaseEnum
{
    protected static function values(): array
    {
        //
        return [
            'NotRequired' => 0,
            'Required' => 1,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'NotRequired' => '否',
            'Required' => '是',
        ];
    }
}
