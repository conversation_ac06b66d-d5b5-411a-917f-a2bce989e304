<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * 是否推广枚举
 * @package App\Enums\Type\User
 * @method static self NotSpread()
 * @method static self Spread()
 */
final class IsSpreadEnum extends BaseEnum
{


    // 定义枚举值
    protected static function values(): array
    {
        //
        return [
            'NotSpread' => 0,
            'Spread' => 1,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'NotSpread' => '未推广',
            'Spread' => '已推广',
        ];
    }
}
