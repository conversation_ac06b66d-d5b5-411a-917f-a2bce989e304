<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 */
final class IsTrialEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
        ];
    }
}
