<?php

namespace App\Enums\Is;

use App\Enums\BaseEnum;


/**
 * IsWechatEnum
 * @package App\Enums\Is
 * @method static self Added()
 * @method static self NotAdded()
 */
final class IsWechatEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'Added' => 1,
            'NotAdded' => 0,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'Added' => '已加微信',
            'NotAdded' => '未加微信',
        ];
    }
}
