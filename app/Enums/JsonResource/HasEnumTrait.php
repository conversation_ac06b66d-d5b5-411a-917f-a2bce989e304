<?php

namespace App\Enums\JsonResource;

trait HasEnumTrait
{
    /**
     * Get the key and label of the given object
     * @param mixed $object
     * @return array<string, mixed>
     */
    public function getKeyAndLabel($object): array
    {
        //
        return [
            'key' => $this->getKey($object),
            'label' => $this->getLabel($object)
        ];
    }

    /**
     * Get the key of the given object
     * @param mixed $object
     * @return string|null
     */
    public function getKey($object): ?string
    {
        //
        return $object ? $object->key() : null;
    }

    /**
     * Get the label of the given object
     * @param mixed $object
     * @return string|null
     */
    public function getLabel($object): ?string
    {
        //
        return $object ? $object->label : null;
    }
}
