<?php

namespace App\Enums\Method;

use App\Enums\BaseEnum;

/**
 * Class GETMethodEnum
 * @package App\Enums\Method\GET
 * @method static self default() 默认状态
 * @method static self physical_express() 普通产品-使用物流发货状态
 * @method static self virtual_certificate() 虚拟产品-无需物流与电子交易凭证平台发码状态
 * @method static self virtual_coupon() 虚拟产品-使用电子交易凭证商家发码状态
 * @method static self virtual_charge_service() 虚拟产品-充值直连状态
 */
final class GETMethodEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'physical_express' => 1,
            'virtual_certificate' => 6,
            'virtual_coupon' => 7,
            'virtual_charge_service' => 8,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'physical_express' => '普通产品-使用物流发货',
            'virtual_certificate' => '虚拟产品-无需物流与电子交易凭证平台发码',
            'virtual_coupon' => '虚拟产品-使用电子交易凭证商家发码',
            'virtual_charge_service' => '虚拟产品-充值直连',
        ];
    }
}
