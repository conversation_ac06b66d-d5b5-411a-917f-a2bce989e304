<?php

namespace App\Enums\Method;

use App\Enums\BaseEnum;


/**
 * Class PaymentMethodEnum
 * @package App\Enums\Method\Payment
 * @method static self default() 默认状态
 * @method static self Alipay() 支付宝支付状态
 * @method static self WeChatPay() 微信支付状态
 * @method static self UnionPay() 银联状态
 * @method static self CreditCard() 银行卡状态
 * @method static self Balance() 余额状态
 * @method static self DigitalRMB() 数字人民币状态
 * @method static self InternationalPayment() 国际支付状态
 * @method static self OfflinePayment() 线下支付状态
 * @method static self Credit() 赊账状态
 * @method static self CashOnDelivery() 现金支付状态
 * @method static self LogisticsCollection() 物流代收状态
 * @method static self NoPayment() 无需支付（0元单）状态
 * @method static self DOUInstallment() DOU分期（信用支付）状态
 * @method static self NewCardPayment() 新卡支付状态
 * @method static self PayLater() 先用后付状态
 * @method static self COD() 货到付款状态
 */
final class PaymentMethodEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'Alipay' => 1,
            'WeChatPay' => 2,
            'UnionPay' => 3,
            'CreditCard' => 4,
            'Balance' => 5,
            'DigitalRMB' => 6,
            'InternationalPayment' => 7,
            'OfflinePayment' => 8,
            'Credit' => 9,
            'CashOnDelivery' => 10,
            'LogisticsCollection' => 11,
            'NoPayment' => 12,
            'DOUInstallment' => 13,
            'NewCardPayment' => 14,
            'PayLater' => 15,
            'COD' => 16,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'Alipay' => '支付宝支付',
            'WeChatPay' => '微信支付',
            'UnionPay' => '银联',
            'CreditCard' => '银行卡',
            'Balance' => '余额',
            'DigitalRMB' => '数字人民币',
            'InternationalPayment' => '国际支付',
            'OfflinePayment' => '线下支付',
            'Credit' => '赊账',
            'CashOnDelivery' => '现金支付',
            'LogisticsCollection' => '物流代收',
            'NoPayment' => '无需支付0元单',
            'DOUInstallment' => 'DOU分期信用支付',
            'NewCardPayment' => '新卡支付',
            'PayLater' => '先用后付',
            'COD' => '货到付款',
        ];
    }
}
