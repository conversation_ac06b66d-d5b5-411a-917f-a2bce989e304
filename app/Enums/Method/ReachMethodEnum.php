<?php

namespace App\Enums\Method;

use App\Enums\BaseEnum;


/**
 * Class ReachMethodEnum
 * @package App\Enums\Type\Range
 * @method static self default()
 * @method static self SelfDelivery()
 * @method static self ServiceProvider()
 * @method static self SupplierDelivery()
 * @method static self DropShipping()
 * @method static self CloudWarehouseDelivery()
 * @method static self SupplyChainDelivery()
 * @method static self SellerPickup()
 * @method static self ConsolidationTransit()
 * @method static self WarehouseDelivery()
 * @method static self LocalDelivery()
 * @method static self WarehouseStocking()
 */
final class ReachMethodEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'SelfDelivery' => 1,
            'ServiceProvider' => 2,
            'SupplierDelivery' => 3,
            'CloudWarehouseDelivery' => 4,
            'Factory' => 5,
            'SupplyChainDelivery' => 6,
            'SellerPickup' => 7,
            'ConsolidationTransit' => 8,
            'WarehouseDelivery' => 9,
            'MerchantPickup' => 10,
            'LocalDelivery' => 11,
            'WarehouseStocking' => 12,
            'ConsolidationOrder' => 13,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'SelfDelivery' => '自主发货',
            'ServiceProvider' => '服务商代发',
            'Supplier' => '供应商代发',
            'CloudWarehouse' => '云仓代发',
            'Factory' => '工厂代发',
            'Gift' => '礼品代发',
            'CityAgent' => '城市代理',
            'DistributionAgent' => '分销代理',
            'PreSaleAgent' => '前置代销',
            'MerchantPickup' => '商家自提单',
            'LocalDelivery' => '同城配送单',
            'InventoryOrder' => '库存备货单',
            'ConsolidationOrder' => '集运转运单',
        ];
    }
}
