<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * 推送状态枚举
 * @package App\Enums\State\CareStateEnum
 * @method static self pending()     // 待推送
 * @method static self ready()     // 准备中
 * @method static self pushed()      // 已推送
 * @method static self change()  // 转换中
 * @method static self stand()  // 待处理
 * @method static self progress()  // 处理中
 * @method static self retrying()    // 重试中
 * @method static self online()    // 上架中
 * @method static self completed()   // 铺货完成
 * @method static self delayed()     // 已延迟
 * @method static self canceled()    // 已取消
 * @method static self failed()      // 推送失败
 */
final class CareStateEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    /**
     * 定义推送状态的值
     */
    protected static function values(): array
    {
        //
        return [
            'pending' => 0,  // 待推送
            'ready' => 10,  // 准备中
            'pushed' => 20,  // 已推送
            'change' => 30,  // 转换中
            'stand' => 40,  // 待处理
            'progress' => 50,  // 处理中
            'retrying' => 60,  // 重试中
            'online' => 70,  // 上架中
            'completed' => 80,  // 铺货完成
            'canceled' => 90,  // 已取消
            'failed' => 100,  // 推送失败
            'delayed' => 110,  // 已延迟
        ];
    }

    /**
     * 定义每个推送状态的中文标签
     */
    protected static function labels(): array
    {
        //
        return [
            'pending' => '待推送', // 待推送
            'ready' => '准备中',  // 准备中
            'pushed' => '已推送',  // 已推送
            'change' => '转换中',  // 转换中
            'stand' => '待处理',  // 待处理
            'progress' => '处理中',  // 处理中
            'retrying' => '重试中',  // 重试中
            'online' => '上架中',  // 上架中
            'completed' => '铺货完成',  // 铺货完成
            'delayed' => '已延迟',  // 已延迟
            'canceled' => '已取消',  // 已取消
            'failed' => '推送失败',  // 推送失败
        ];
    }
}
