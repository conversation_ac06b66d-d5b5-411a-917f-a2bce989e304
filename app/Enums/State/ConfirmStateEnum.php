<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class ConfirmStateEnum
 * 用于表示商品确认状态的枚举类。状态包括默认状态、确认状态、非确认状态等。
 * @package App\Enums\State\Confirm
 * @method static self default() 默认状态
 * @method static self confirm() 确认状态
 * @method static self inactive() 非确认状态
 * @method static self pending() 待处理状态
 * @method static self completed() 已完成状态
 * @method static self archived() 已归档状态
 */
final class ConfirmStateEnum extends BaseEnum
{
    /**
     * 定义状态值
     * 在此方法中，我们定义了商品的不同确认状态的数值表示形式。
     * 这些值用于在数据库中存储状态，以便进行查询和管理。
     * 返回一个关联数组，其中键是状态名称（如 'confirm'、'inactive'），
     * 值是该状态对应的数字（如 1、2、3 等）。
     * @return array 状态值数组
     */
    protected static function values(): array
    {
        return [
            'default' => 0,  // 默认状态：当状态未设置时，使用此值表示
            'confirm' => 1,  // 确认状态：表示商品或操作已被确认
            'inactive' => 2,  // 非确认状态：表示商品或操作未被确认
            'pending' => 3,  // 待处理状态：表示商品或操作正在等待确认或处理
            'completed' => 4,  // 已完成状态：表示商品或操作已经完成
            'archived' => 5,  // 已归档状态：表示商品或操作已被归档，不再进行处理
        ];
    }

    /**
     * 定义状态标签
     * 在此方法中，我们为每个状态定义了用户友好的标签，
     * 这些标签用于展示给用户，帮助用户理解当前的状态。
     * 返回一个关联数组，其中键是状态名称（如 'confirm'、'inactive'），
     * 值是状态的中文标签（如 '确认状态'、'非确认状态' 等）。
     * @return array 状态标签数组
     */
    protected static function labels(): array
    {
        return [
            'default' => '默认状态',   // 默认状态：表示该商品或操作尚未设置确认状态
            'confirm' => '确认状态',   // 确认状态：表示该商品或操作已被确认
            'inactive' => '非确认状态', // 非确认状态：表示该商品或操作未被确认，可能需要处理
            'pending' => '待处理状态', // 待处理状态：表示该商品或操作正在等待处理
            'completed' => '已完成状态', // 已完成状态：表示该商品或操作已经完成，不需要进一步操作
            'archived' => '已归档状态', // 已归档状态：表示该商品或操作已被归档，不再参与操作
        ];
    }

    /**
     * 获取状态值的名称
     * 该方法用于获取当前状态的名称。名称是一个字符串，
     * 例如 'confirm'、'inactive' 等，方便开发者对状态进行引用。
     * @return string 状态值的名称
     */
    public function name(): string
    {
        return static::getKey();
    }

    /**
     * 获取状态值对应的数字
     * 该方法返回与当前状态关联的数字值，
     * 例如 `confirm` 状态的数字值是 1，`inactive` 状态的数字值是 2。
     * @return int 状态值对应的数字
     */
    public function value(): int
    {
        return static::getValue();
    }

    /**
     * 获取状态标签
     * 该方法返回与当前状态关联的中文标签，例如 '确认状态' 或 '非确认状态'。
     * @return string 状态标签
     */
    public function label(): string
    {
        return static::getLabel();
    }
}
