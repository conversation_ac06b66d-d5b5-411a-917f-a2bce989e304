<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * Class DeliveryStateEnum
 * 用于表示配送状态的枚举类。该类涵盖了订单配送过程中各个状态，如待分配快递、待打印面单、待打包发货等。
 * @package App\Enums\State\Purchase
 * @method static self default() 默认状态
 * @method static self match() 待分配快递状态
 * @method static self print() 待打印面单状态
 * @method static self shipment() 待打包发货状态
 * @method static self done() 已发货出库状态
 * @method static self cancelled() 取消状态
 */
final class DeliveryStateEnum extends BaseEnum
{
    use KeyedEnumTrait;

    // 使用KeyedEnumTrait，提供按键值查找的功能

    use ColorEnumTrait;

    // 使用ColorEnumTrait，提供颜色映射功能

    /**
     * 定义配送状态的数值
     * 在此方法中，我们为每个配送状态分配了唯一的数字值。数字值用于数据库存储和状态比较。
     * 返回一个关联数组，其中键是状态名称（如 'match'、'print'），值是该状态对应的数字（如 10、40 等）。
     * @return array 状态值数组
     */
    protected static function values(): array
    {
        return [
            'default' => 0,   // 默认状态，表示未设置任何配送状态
            'match' => 10,  // 待分配快递状态，表示订单已创建，等待分配快递
            'print' => 40,  // 待打印面单状态，表示等待打印面单以供配送
            'shipment' => 50,  // 待打包发货状态，表示准备好进行打包发货
            'done' => 100, // 已发货出库状态，表示订单已经完成发货，等待物流运输
            'cancelled' => 110, // 取消状态，表示订单被取消，不能继续配送
        ];
    }

    /**
     * 定义配送状态的标签
     * 在此方法中，我们为每个配送状态定义了中文标签，用于用户界面展示。
     * 返回一个关联数组，其中键是状态名称（如 'match'、'print'），
     * 值是该状态对应的中文标签（如 '待分配快递'、'待打印面单' 等）。
     * @return array 状态标签数组
     */
    protected static function labels(): array
    {
        return [
            'default' => '默认',           // 默认状态，表示无特定状态或待初始化状态
            'match' => '待分配快递',       // 待分配快递，表示订单需要分配物流公司
            'print' => '待打印面单',       // 待打印面单，表示订单需要打印面单供配送使用
            'shipment' => '待打包发货',       // 待打包发货，表示订单准备好进行打包发货
            'done' => '已发货出库',       // 已发货出库，表示订单已经发货并进入物流环节
            'cancelled' => '取消',            // 取消，表示订单被取消，不能继续配送
        ];
    }

    /**
     * 获取当前状态的名称
     * 该方法返回当前配送状态的名称，例如 'match' 或 'print'，用于在程序中进行状态的比较和引用。
     * @return string 当前状态的名称
     */
    public function name(): string
    {
        return static::getKey();
    }

    /**
     * 获取当前状态的数值
     * 该方法返回当前配送状态的数字值，例如 `match` 的值为 `10`，`done` 的值为 `100`。
     * 数字值用于数据库存储或在逻辑中进行比较。
     * @return int 当前状态的数字值
     */
    public function value(): int
    {
        return static::getValue();
    }

    /**
     * 获取当前状态的中文标签
     * 该方法返回当前配送状态的中文标签，例如 '待分配快递' 或 '已发货出库'，用于用户界面展示。
     * @return string 当前状态的标签
     */
    public function label(): string
    {
        return static::getLabel();
    }
}
