<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 */
final class RefundStateEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'NoAfterSales' => 0,   // 无售后
            //
            'BuyerNotShippedRefundOnly' => 1,   // 买家未发货，仅退款
            'BuyerShippedRefundOnly' => 2,   // 买家已发货，仅退款
            'BuyerShippedReturnAndRefund' => 4,   // 买家已发货，退货退款
            //
            'SellerPendingRefundProcessing' => 10,  // 卖家待处理退款申请
            'SellerAgreedToRefund' => 11,  // 卖家同意退款
            'SellerRefusedRefund' => 13,  // 卖家拒绝退款
            'SellerRefusedReturnAndRefund' => 14,  // 卖家拒绝退货退款
            //
            'PlatformRefundOnlyProcessing' => 20,  // 平台仅处理退款
            'PlatformAgreedToRefundOnly' => 21,  // 平台同意仅退款
            //
            'BuyerReturnInProgress' => 30,  // 买家退货进行中
            'BuyerConfirmedReturn' => 31,  // 买家确认退货
            'BuyerExchangedGoodsShipped' => 33,  // 买家换货已发货
            'BuyerGoodsInTransitForReturn' => 34,  // 买家退货在途中
            //
            'SellerProcessingReturn' => 40,  // 卖家处理退货中
            'SellerReceivingGoods' => 41,  // 卖家收到退货
            'SellerRefusedReturn' => 45,  // 卖家拒绝退货
            'SellerReturnApplicationInProgress' => 46,  // 卖家退货申请进行中
            'SellerAgreedToReturn' => 48,  // 卖家同意退货
            //
            'SellerRefusedToReceiveGoods' => 50,  // 卖家拒绝收货
            'SellerExchangingGoods' => 51,  // 卖家正在换货
            'SellerRefusedExchange' => 55,  // 卖家拒绝换货
            'SellerExchangeApplicationInProgress' => 56,  // 卖家换货申请进行中
            'SellerAgreedToExchange' => 58,  // 卖家同意换货
            'SellerProcessingExchangeShipment' => 60,  // 卖家处理换货发货
            //
            'SellerExchangeShipmentInProgress' => 61,  // 卖家换货发货进行中
            'SellerConfirmedExchange' => 63,  // 卖家确认换货
            'SellerExchangedGoodsShipped' => 64,  // 卖家换货已发货
            //
            'BuyerReceivingGoods' => 70,  // 买家收到制品
            'BuyerRefusedToReceiveGoods' => 71,  // 买家拒绝收货
            'BuyerExchangeApplicationInProgress' => 75,  // 买家换货申请进行中
            'BuyerAgreedToExchange' => 76,  // 买家同意换货
            'BuyerRefusedExchange' => 78,  // 买家拒绝换货
            'BuyerReverseTransaction' => 79,  // 买家反向交易
            //
            'RefundProcessing' => 80,  // 退款处理中
            'SellerRefunding' => 81,  // 卖家正在退款
            'SellerRefundFailed' => 83,  // 卖家退款失败
            'SellerOverdueAutomaticRefundInProgress' => 85,  // 卖家逾期自动退款进行中
            //
            'FinanceFinancialRefundInProgress' => 87,  // 财务退款进行中
            'FinanceFinancialRefundFailed' => 89,  // 财务退款失败
            'FinanceFinancialRefundSuccessful' => 90,  // 财务退款成功
            //
            'InsuranceClaimInProgress' => 90,  // 保险理赔进行中
            'InsuranceRefusedClaim' => 91,  // 保险拒绝理赔
            'InsuranceSubmittingClaimAmount' => 93,  // 保险提交理赔金额
            'InsuranceUnderwritingInProgress' => 95,  // 保险核保进行中
            'InsuranceAgreedToClaim' => 96,  // 保险同意理赔
            //
            'PlatformRefusedInsuranceClaim' => 98,  // 平台拒绝保险理赔
            'PlatformInsuranceClaimCompleted' => 99,  // 平台保险理赔完成
            //
            'PlatformRefundCompleted' => 100, // 平台退款完成
            //
            'BuyerInitiatedAfterSalesCancellation' => 110, // 买家发起售后取消
            'BuyerOverdueAfterSalesCancellation' => 120, // 买家逾期售后取消
            'SellerInitiatedAfterSalesCancellation' => 130, // 卖家发起售后取消
            'SellerOverdueAfterSalesCancellation' => 140, // 卖家逾期售后取消
            'PlatformAfterSalesCancellation' => 160, // 平台售后取消
            'PlatformAfterSalesExtension' => 180, // 平台售后延期
        ];
    }

    protected static function labels(): array
    {
        return [
            'NoAfterSales' => '无售后',
            'BuyerNotShippedRefundOnly' => '买家-未发货，仅退款',
            'BuyerShippedRefundOnly' => '买家-已发货，仅退款',
            'BuyerShippedReturnAndRefund' => '买家-已发货，退货退款',
            'SellerPendingRefundProcessing' => '商家-待处理退款',
            'SellerAgreedToRefund' => '商家-同意退款',
            'SellerRefusedRefund' => '商家-拒绝退款',
            'SellerRefusedReturnAndRefund' => '商家-拒绝退货退款',
            'PlatformRefundOnlyProcessing' => '平台-仅退款中',
            'PlatformAgreedToRefundOnly' => '平台-同意仅退款',
            'BuyerReturnInProgress' => '买家-退货中',
            'BuyerConfirmedReturn' => '买家-确认退货',
            'BuyerExchangedGoodsShipped' => '买家-换货回寄',
            'BuyerGoodsInTransitForReturn' => '买家-物流运输中',
            'SellerProcessingReturn' => '商家-处理退货',
            'SellerReceivingGoods' => '商家-收货中',
            'SellerRefusedReturn' => '商家-拒绝退货',
            'SellerReturnApplicationInProgress' => '商家-退货申请中',
            'SellerAgreedToReturn' => '商家-同意退货',
            'SellerRefusedToReceiveGoods' => '商家-拒绝收货',
            'SellerExchangingGoods' => '商家-处理换货',
            'SellerRefusedExchange' => '商家-换货中',
            'SellerExchangeApplicationInProgress' => '商家-拒绝换货',
            'SellerExchangeShipmentInProgress' => '商家-换货申请中',
            'SellerConfirmedExchange' => '商家-同意换货',
            'SellerExchangedGoodsShipped' => '商家-处理换货发货',
            'BuyerReceivingGoods' => '买家-收货中',
            'BuyerRefusedToReceiveGoods' => '买家-拒绝收货',
            'BuyerExchangeApplicationInProgress' => '买家-换货申请中',
            'BuyerAgreedToExchange' => '买家-同意换货',
            'BuyerRefusedExchange' => '买家-拒绝换货',
            'BuyerReverseTransaction' => '买家-逆向交易',
            'RefundProcessing' => '退款处理中',
            'SellerRefunding' => '商家-退款中',
            'SellerRefundFailed' => '商家-退款失败',
            'SellerOverdueAutomaticRefundInProgress' => '商家-逾期，自动退款中',
            'FinanceFinancialRefundInProgress' => '财务-财务退款中',
            'FinanceFinancialRefundFailed' => '财务-财务退款失败',
            'FinanceFinancialRefundSuccessful' => '财务-财务退款成功',
            'InsuranceClaimProcessing' => '保险理赔处理中',
            'InsuranceClaimInProgress' => '保险-保险理赔中',
            'InsuranceRefusedClaim' => '保险-拒绝理赔',
            'InsuranceSubmittingClaimAmount' => '保险-提交保额',
            'InsuranceUnderwritingInProgress' => '保险-保险核保中',
            'InsuranceAgreedToClaim' => '保险-同意理赔',
            'PlatformRefusedInsuranceClaim' => '平台-拒绝理赔',
            'PlatformInsuranceClaimCompleted' => '平台-理赔完成',
            'AfterSalesCompletedAndCanceled' => '售后结束及取消',
            'PlatformRefundCompleted' => '平台-退款结束',
            'BuyerInitiatedAfterSalesCancellation' => '买家-主动取消售后',
            'BuyerOverdueAfterSalesCancellation' => '买家-逾期取消售后',
            'SellerInitiatedAfterSalesCancellation' => '商家-主动取消售后',
            'SellerOverdueAfterSalesCancellation' => '商家-逾期取消售后',
            'PlatformAfterSalesCancellation' => '平台-取消售后',
            'PlatformAfterSalesExtension' => '平台-延期售后',
        ];
    }
}
