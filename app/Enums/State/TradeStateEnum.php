<?php

namespace App\Enums\State;

use App\Enums\BaseEnum;

/**
 * Class MainStateEnum
 * @package App\Enums\State\
 * @method static self default() 默认状态
 * @method static self wait() 等待支付
 * @method static self confirm() 等待接单
 * @method static self work() 商家处理
 * @method static self picker() 商家拣货
 * @method static self take() 商家出货
 * @method static self courier() 物流揽件
 * @method static self shipment() 商家发货
 * @method static self transport() 物流运输
 * @method static self distribution() 物流配送
 * @method static self halt() 订单拦截
 * @method static self done() 订单完成
 * @method static self lock() 订单锁定
 * @method static self cancel() 订单取消
 * @method static self refund() 退款退货
 * @method static self settlement() 账单结算
 * @method static self exception() 订单异常
 */
final class TradeStateEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0, // 默认状态
            //
            'wait' => 10, // 等待支付
            'confirm' => 20, // 等待接单
            'work' => 30, // 商家处理
            'picker' => 40, // 商家拣货
            'take' => 40, // 商家出货
            'courier' => 50, // 物流揽件
            'shipment' => 60, // 商家发货
            'transport' => 70, // 物流运输
            'distribution' => 80, // 物流配送
            'halt' => 90, // 订单拦截
            'done' => 100, // 订单完成
            //
            'lock' => 110, // 订单锁定
            'cancel' => 120, // 订单取消
            'refund' => 130, // 退款退货
            'settlement' => 140, // 账单结算
            'exception' => 150, // 订单异常
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            //
            'wait' => '等待支付',
            'confirm' => '等待接单',
            'work' => '商家处理',
            'picker' => '商家拣货',
            'take' => '商家出货',
            'courier' => '物流揽件',
            'shipment' => '商家发货',
            'transport' => '物流运输',
            'distribution' => '物流配送',
            'halt' => '订单拦截',
            'done' => '订单完成',
            //
            'lock' => '订单锁定',
            'cancel' => '订单取消',
            'refund' => '退款退货',
            'settlement' => '账单结算',
            'exception' => '订单异常',
        ];
    }
}
