<?php

namespace App\Enums\State;

use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self Default()
 */
final class WishStateEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    protected static function values(): array
    {
        return [
            'Default' => 0,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'Default' => '默认状态',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        //
        return [
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
        ];
    }

}
