<?php

namespace App\Enums\Status;

use App\Enums\BaseEnum;

/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self normal()
 * @method static self approved()
 * @method static self draft()
 * @method static self pending()
 * @method static self checked()
 * @method static self refused()
 */
final class StoreDraftStatusEnum extends BaseEnum
{
    //
    protected static function values(): array
    {
        //
        return [
            'default' => 0, // 默认状态
            'normal' => 1, // 人审通过
            'approved' => 20, // 机审通过
            'draft' => 50, // 草稿
            'pending' => 60, // 待审核
            'checked' => 70, // 审核中
            'refused' => 110, // 审核未通过
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'normal' => '审核通过',
            'approved' => '机审通过',
            'draft' => '草稿',
            'pending' => '待审核',
            'checked' => '审核中',
            'refused' => '审核未通过',
        ];
    }
}
