<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class CampaignTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self Single() 单品
 * @method static self Whole() 整单
 * @method static self Group() 整箱
 * @method static self Package() 套餐
 * @method static self Ingredient() 配料
 * @method static self Gift() 赠品
 */
final class CampaignTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'Single' => 1,
            'Whole' => 2,
            'Group' => 3,
            'Package' => 4,
            'Ingredient' => 5,
            'Gift' => 6,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'Single' => '单品',
            'Whole' => '整单',
            'Group' => '整箱',
            'Package' => '套餐',
            'Ingredient' => '配料',
            'Gift' => '赠品',
        ];
    }

    // 定义颜色映射

}
