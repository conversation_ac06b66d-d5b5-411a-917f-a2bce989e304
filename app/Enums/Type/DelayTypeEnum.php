<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class ShipmentDelayTypeEnum
 * @package App\Enums\Type\Shipment
 * @method static self default() 默认
 * @method static self same_day_dispatch() 当日发
 * @method static self next_day_dispatch() 次日发
 */
final class DelayTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'same_day_dispatch' => 1,
            'next_day_dispatch' => 2,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'same_day_dispatch' => '当日发',
            'next_day_dispatch' => '次日发',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 当日发
            2 => 'orange',        // 次日发
        ];
    }
}
