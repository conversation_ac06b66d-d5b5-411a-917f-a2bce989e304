<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class ShipmentDelayTypeEnum
 * @package App\Enums\Type\Shipment
 * @method static self default() 默认
 * @method static self SameDayDispatch() 当日发
 * @method static self NextDayDispatch() 次日发
 */
final class DeliveryTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'SameDayDispatch' => 1,
            'NextDayDispatch' => 2,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'SameDayDispatch' => '当日发',
            'NextDayDispatch' => '次日发',
        ];
    }


}
