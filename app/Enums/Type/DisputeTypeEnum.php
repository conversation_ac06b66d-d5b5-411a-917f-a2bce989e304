<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;
use App\Traits\System\Enum\ColorEnumTrait;
use App\Traits\System\Enum\KeyedEnumTrait;

/**
 * DisputeTypeEnum - 诉讼类型枚举
 * @package App\Enums\Type\DisputeTypeEnum
 * @method static self default()
 * @method static self dispute_lawsuit()
 * @method static self dispute_edge()
 * @method static self dispute_fake()
 * @method static self dispute_sue()
 * @method static self dispute_jail()
 */
final class DisputeTypeEnum extends BaseEnum
{
    use KeyedEnumTrait;
    use ColorEnumTrait;

    // 定义枚举值
    protected static function values(): array
    {
        return [
            'dispute_lawsuit' => 0,       // 无诉讼
            'dispute_edge' => 1,   // 有版权方（擦边）
            'dispute_fake' => 2,   // 有版权方(打假)
            'dispute_sue' => 3,    // 有版权方(起诉)
            'dispute_jail' => 4,   // 有版权方(坐牢)
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'dispute_lawsuit' => '无诉讼',
            'dispute_edge' => '有版权方（擦边）',
            'dispute_fake' => '有版权方(打假)',
            'dispute_sue' => '有版权方(起诉)',
            'dispute_jail' => '有版权方(坐牢)',
        ];
    }
}
