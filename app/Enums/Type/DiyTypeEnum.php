<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class DiyTypeEnum
 * @package App\Enums\Type\Diy
 * @method static self default() 默认
 * @method static self EntryMode() 入驻模式
 * @method static self SupplyMode() 供应模式
 * @method static self PurchaseMode() 采购模式
 * @method static self SelectionMode() 选品模式
 */
final class DiyTypeEnum extends BaseEnum
{


    //
    protected static function values(): array
    {
        return [
            'default' => 0,
            'EntryMode' => 1,
            'SupplyMode' => 2,
            'PurchaseMode' => 3,
            'SelectionMode' => 4,
            'SelectionMod' => 5,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'EntryMode' => '入驻模式',
            'SupplyMode' => '供应模式',
            'PurchaseMode' => '采购模式',
            'SelectionMode' => '选品模式',
            'SelectionMod' => '选品模式2',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
        ];
    }

}
