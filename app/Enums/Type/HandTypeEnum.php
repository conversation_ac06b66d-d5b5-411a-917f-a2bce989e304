<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class ProductHandTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self new_product() 全新产品
 * @method static self used_product() 二手产品
 */
final class HandTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'new_product' => 1,
            'used_product' => 2,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'new_product' => '全新产品',
            'used_product' => '二手产品',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'grey',     // 默认
            1 => 'red',      // 全新产品
            2 => 'orange',   // 二手产品
        ];
    }
}
