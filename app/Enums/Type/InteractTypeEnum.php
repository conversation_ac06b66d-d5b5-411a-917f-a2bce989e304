<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self StoreOwn()
 * @method static self PlatformGlobal()
 * @method static self ProviderUnique()
 * @method static self ProviderOptional()
 */
final class InteractTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'StoreOwn' => 1,
            'PlatformGlobal' => 2,
            'ProviderUnique' => 3,
            'ProviderOptional' => 4,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'StoreOwn' => '店铺自有',
            'PlatformGlobal' => '平台物流',
            'ProviderUnique' => '服务商一对一合作',
            'ProviderOptional' => '服务商自选',
        ];
    }


}
