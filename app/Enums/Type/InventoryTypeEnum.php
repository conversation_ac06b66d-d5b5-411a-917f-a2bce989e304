<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class ProductInventoryTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self NormalInventory() 普通库存
 * @method static self RegionalInventory() 区域库存
 */
final class InventoryTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'NormalInventory' => 1,
            'RegionalInventory' => 2,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'NormalInventory' => '普通库存',
            'RegionalInventory' => '区域库存',
        ];
    }


}
