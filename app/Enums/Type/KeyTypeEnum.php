<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class OrderKeyTypeEnum
 * @package App\Enums\Type\Order
 * @method static self default() 默认
 * @method static self PriorityCustomer() 优先订单
 * @method static self UrgentCustomer() 加急订单
 * @method static self HighCustomer() 高频购买
 * @method static self ShopNewCustomer() 店铺新客
 * @method static self ShopOldCustomer() 店铺老客
 * @method static self PlatformNewCustomer() 平台新客
 * @method static self PremiumCustomer() premium订单
 */
final class KeyTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'PriorityCustomer' => 1,
            'UrgentCustomer' => 2,
            'HighCustomer' => 3,
            'ShopNewCustomer' => 4,
            'ShopOldCustomer' => 5,
            'PlatformNewCustomer' => 6,
            'PremiumCustomer' => 7,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'PriorityCustomer' => '优先订单',
            'UrgentCustomer' => '加急订单',
            'HighCustomer' => '高频购买',
            'ShopNewCustomer' => '店铺新客',
            'ShopOldCustomer' => '店铺老客',
            'PlatformNewCustomer' => '平台新客',
            'PremiumCustomer' => 'premium订单',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',            // default
            1 => 'red',                      // PriorityOrder
            2 => 'orange',                   // UrgentOrder
            3 => 'yellow',                   // HighFrequencyPurchase
            4 => 'green',                    // ShopNewCustomer
            5 => 'purple',                   // ShopOldCustomer
            6 => 'blue',                     // PlatformNewCustomer
            7 => 'pink',                     // PremiumOrder
        ];
    }

}
