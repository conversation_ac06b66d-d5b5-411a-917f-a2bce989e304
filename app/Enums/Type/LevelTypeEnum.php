<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class LevelTypeEnum
 * @package App\Enums\Type\Level
 * @method static self default() 默认
 * @method static self LevelOne() 一级
 * @method static self LevelTwo() 二级
 * @method static self LevelThree() 三级
 * @method static self LevelFour() 四级
 * @method static self LevelFive() 五级
 */
final class LevelTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        //
        return [
            'default' => 0,
            'LevelOne' => 1,
            'LevelTwo' => 2,
            'LevelThree' => 3,
            'LevelFour' => 4,
            'LevelFive' => 5,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'LevelOne' => '一级',
            'LevelTwo' => '二级',
            'LevelThree' => '三级',
            'LevelFour' => '四级',
            'LevelFive' => '五级',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        //
        return [
            0 => 'default_color',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
            5 => 'purple',
        ];
    }

}
