<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Class PackTypeEnum
 * @package App\Enums\Type\Pack
 * @method static self default() 默认
 * @method static self FiveStar() 五星包装（纸盒包装）
 * @method static self FourStar() 四星包装（20丝pp袋）
 * @method static self ThreeStar() 三星包装（10丝pp袋）
 * @method static self TwoStar() 二星包装（4丝pp袋）
 * @method static self OneStar() 一星包装（无包装）
 */
final class PackTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'FiveStar' => 50,
            'FourStar' => 40,
            'ThreeStar' => 30,
            'TwoStar' => 20,
            'OneStar' => 10,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认',
            'FiveStar' => '五星',
            'FourStar' => '四星',
            'ThreeStar' => '三星',
            'TwoStar' => '二星',
            'OneStar' => '一星',
        ];
    }


}
