<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * PriorityTypeEnum
 * @package App\Enums\Type\User
 * @method static self VeryLow()
 * @method static self Low()
 * @method static self Medium()
 * @method static self High()
 * @method static self VeryHigh()
 */
final class PriorityTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'VeryLow' => 1,
            'Low' => 2,
            'Medium' => 3,
            'High' => 4,
            'VeryHigh' => 5,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'VeryLow' => '极低',
            'Low' => '低',
            'Medium' => '中',
            'High' => '高',
            'VeryHigh' => '极高',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            'VeryLow' => 'purple',
            'Low' => 'green',
            'Medium' => 'yellow',
            'High' => 'orange',
            'VeryHigh' => 'red',
        ];
    }
}
