<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self NewUserDiscount() 新用户立减
 * @method static self FullReduction() 满减
 * @method static self Voucher() 抵价券
 * @method static self PackageGift() 套餐赠送
 * @method static self FullGift() 满赠
 * @method static self TimeoutCompensation() 超时赔付
 * @method static self SpecialPrice() 特价款
 * @method static self FirstOrderCoupon() 首单返优惠券
 * @method static self UseRedPacket() 使用红包
 * @method static self AdvanceOrderDiscount() 提前下单减
 * @method static self FullReturnCoupon() 满返优惠券
 * @method static self FullFreeDeliveryFee() 满免配送费
 * @method static self DiscountProduct() 折扣产品
 * @method static self SpecialDeliveryDiscount() 专送再减
 * @method static self ReviewCoupon() 点评券
 * @method static self SecondHalfPrice() 第二份半价
 * @method static self MemberFreeDeliveryFee() 会员免配送费
 * @method static self NewStoreCustomerDiscount() 门店新客立减
 * @method static self BuyGift() 买赠
 * @method static self PlatformNewUserDiscount() 平台新用户立减
 * @method static self FullReductionDeliveryFee() 满减配送费
 * @method static self DesignatedProductFullReduction() 指定产品满减
 * @method static self NewCustomerFullReduction() 新客满减
 * @method static self StepFullReductionDeliveryFee() 阶梯满减配送费
 * @method static self PlatformNewCustomerReducedDeliveryFee() 平台新客减配送费
 * @method static self AddPricePurchase() 加价购
 * @method static self NewCustomerDiscountDishes() 新客折扣菜
 * @method static self RegionalGroupBuying() 区域拼团
 * @method static self RegionalAddPricePurchase() 区域加价购
 * @method static self GroupBuyingReducedDeliveryFee() 拼团减配送费
 * @method static self NewCustomerExclusiveReducedPackagingFee() 新客专享减包装费
 * @method static self NewCustomerExclusiveReducedDeliveryFee() 新客专享减配送费
 * @method static self MemberDayDiscount() 会员日折扣款
 * @method static self FullReturnMerchantVoucher() 满返商家代金券
 * @method static self UseMerchantVoucher() 使用商家代金券
 * @method static self In-StoreCoupon() 进店领券
 * @method static self ProductCoupon() 产品券
 * @method static self ProductDiscountCoupon() 产品折扣券
 * @method static self AllowanceAlliance() 津贴联盟
 * @method static self ImmediateReduction() 立减金
 * @method static self PackageRecommendationAllowance() 套餐推荐津贴
 * @method static self QualityFlashSale() 品质秒杀
 */
final class PromotionTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'NewUserDiscount' => 1,
            'FullReduction' => 2,
            'Voucher' => 3,
            'PackageGift' => 4,
            'FullGift' => 5,
            'TimeoutCompensation' => 6,
            'SpecialPrice' => 7,
            'FirstOrderCoupon' => 8,
            'UseRedPacket' => 9,
            'AdvanceOrderDiscount' => 11,
            'FullReturnCoupon' => 12,
            'FullFreeDeliveryFee' => 16,
            'DiscountProduct' => 17,
            'SpecialDeliveryDiscount' => 18,
            'ReviewCoupon' => 19,
            'SecondHalfPrice' => 20,
            'MemberFreeDeliveryFee' => 21,
            'NewStoreCustomerDiscount' => 22,
            'BuyGift' => 23,
            'PlatformNewUserDiscount' => 24,
            'FullReductionDeliveryFee' => 25,
            'DesignatedProductFullReduction' => 27,
            'NewCustomerFullReduction' => 28,
            'StepFullReductionDeliveryFee' => 30,
            'PlatformNewCustomerReducedDeliveryFee' => 36,
            'AddPricePurchase' => 40,
            'NewCustomerDiscountDishes' => 41,
            'RegionalGroupBuying' => 45,
            'RegionalAddPricePurchase' => 46,
            'GroupBuyingReducedDeliveryFee' => 48,
            'NewCustomerExclusiveReducedPackagingFee' => 53,
            'NewCustomerExclusiveReducedDeliveryFee' => 54,
            'MemberDayDiscount' => 65,
            'FullReturnMerchantVoucher' => 1100,
            'UseMerchantVoucher' => 101,
            'In-StoreCoupon' => 103,
            'ProductCoupon' => 117,
            'ProductDiscountCoupon' => 118,
            'AllowanceAlliance' => 305,
            'ImmediateReduction' => 306,
            'PackageRecommendationAllowance' => 307,
            'QualityFlashSale' => 76,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'NewUserDiscount' => '新用户立减',
            'FullReduction' => '满减',
            'Voucher' => '抵价券',
            'PackageGift' => '套餐赠送',
            'FullGift' => '满赠',
            'TimeoutCompensation' => '超时赔付',
            'SpecialPrice' => '特价款',
            'FirstOrderCoupon' => '首单返优惠券',
            'UseRedPacket' => '使用红包',
            'AdvanceOrderDiscount' => '提前下单减',
            'FullReturnCoupon' => '满返优惠券',
            'FullFreeDeliveryFee' => '满免配送费',
            'DiscountProduct' => '折扣产品',
            'SpecialDeliveryDiscount' => '专送再减',
            'ReviewCoupon' => '点评券',
            'SecondHalfPrice' => '第二份半价',
            'MemberFreeDeliveryFee' => '会员免配送费',
            'NewStoreCustomerDiscount' => '门店新客立减',
            'BuyGift' => '买赠',
            'PlatformNewUserDiscount' => '平台新用户立减',
            'FullReductionDeliveryFee' => '满减配送费',
            'DesignatedProductFullReduction' => '指定产品满减',
            'NewCustomerFullReduction' => '新客满减',
            'StepFullReductionDeliveryFee' => '阶梯满减配送费',
            'PlatformNewCustomerReducedDeliveryFee' => '平台新客减配送费',
            'AddPricePurchase' => '加价购',
            'NewCustomerDiscountDishes' => '新客折扣菜',
            'RegionalGroupBuying' => '区域拼团',
            'RegionalAddPricePurchase' => '区域加价购',
            'GroupBuyingReducedDeliveryFee' => '拼团减配送费',
            'NewCustomerExclusiveReducedPackagingFee' => '新客专享减包装费',
            'NewCustomerExclusiveReducedDeliveryFee' => '新客专享减配送费',
            'MemberDayDiscount' => '会员日折扣款',
            'FullReturnMerchantVoucher' => '满返商家代金券',
            'UseMerchantVoucher' => '使用商家代金券',
            'In-StoreCoupon' => '进店领券',
            'ProductCoupon' => '产品券',
            'ProductDiscountCoupon' => '产品折扣券',
            'AllowanceAlliance' => '津贴联盟',
            'ImmediateReduction' => '立减金',
            'PackageRecommendationAllowance' => '套餐推荐津贴',
            'QualityFlashSale' => '品质秒杀',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        //
        return [
            0 => 'default_color',        // pending
            1 => 'red',                  // NewUserDiscount
            2 => 'orange',               // FullReduction
            3 => 'yellow',               // Voucher
            4 => 'green',                // PackageGift
            5 => 'purple',               // FullGift
            6 => 'default_color',        // TimeoutCompensation
            7 => 'red',                  // SpecialPrice
            8 => 'orange',               // FirstOrderCoupon
            9 => 'yellow',               // UseRedPacket
            11 => 'green',               // AdvanceOrderDiscount
            12 => 'purple',              // FullReturnCoupon
            16 => 'default_color',       // FullFreeDeliveryFee
            17 => 'red',                 // DiscountProduct
            18 => 'orange',              // SpecialDeliveryDiscount
            19 => 'yellow',              // ReviewCoupon
            20 => 'green',               // SecondHalfPrice
            21 => 'purple',              // MemberFreeDeliveryFee
            22 => 'default_color',       // NewStoreCustomerDiscount
            23 => 'red',                 // BuyGift
            24 => 'orange',              // PlatformNewUserDiscount
            25 => 'yellow',              // FullReductionDeliveryFee
            27 => 'green',               // DesignatedProductFullReduction
            28 => 'purple',              // NewCustomerFullReduction
            30 => 'default_color',       // StepFullReductionDeliveryFee
            36 => 'red',                 // PlatformNewCustomerReducedDeliveryFee
            40 => 'orange',              // AddPricePurchase
            41 => 'yellow',              // NewCustomerDiscountDishes
            45 => 'green',               // RegionalGroupBuying
            46 => 'purple',              // RegionalAddPricePurchase
            48 => 'default_color',       // GroupBuyingReducedDeliveryFee
            53 => 'red',                 // NewCustomerExclusiveReducedPackagingFee
            54 => 'orange',              // NewCustomerExclusiveReducedDeliveryFee
            65 => 'yellow',              // MemberDayDiscount
            1100 => 'green',             // FullReturnMerchantVoucher
            101 => 'purple',             // UseMerchantVoucher
            103 => 'default_color',      // In-StoreCoupon
            117 => 'red',                // ProductCoupon
            118 => 'orange',             // ProductDiscountCoupon
            305 => 'yellow',             // AllowanceAlliance
            306 => 'green',              // ImmediateReduction
            307 => 'purple',             // PackageRecommendationAllowance
            76 => 'default_color',       // QualityFlashSale
        ];
    }

}
