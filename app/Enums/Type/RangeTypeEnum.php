<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class RangeTypeEnum
 * @package App\Enums\Type\Range
 * @method static self default()
 * @method static self country()
 * @method static self province()
 * @method static self city()
 * @method static self district()
 * @method static self street()
 * @method static self park()
 * @method static self building()
 * @method static self floor()
 * @method static self zone()
 * @method static self group()
 */
final class RangeTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'country' => 1,
            'province' => 2,
            'city' => 3,
            'district' => 4,
            'street' => 5,
            'park' => 6,
            'building' => 7,
            'floor' => 8,
            'zone' => 9,
            'group' => 10,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'country' => '国家',
            'province' => '省级',
            'city' => '市级',
            'district' => '区（县）域',
            'street' => '街道',
            'park' => '园区（小区）',
            'building' => '楼宇',
            'floor' => '楼层',
            'zone' => '层域',
            'group' => '组',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 国家
            2 => 'orange',        // 省级
            3 => 'yellow',        // 市级
            4 => 'green',         // 区（县）域
            5 => 'purple',        // 街道
            6 => 'blue',          // 园区（小区）
            7 => 'indigo',        // 楼宇
            8 => 'cyan',          // 楼层
            9 => 'teal',          // 层域
            10 => 'pink',          // 组
        ];
    }
}
