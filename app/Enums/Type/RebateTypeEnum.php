<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class RebateTypeEnum
 * @package App\Enums\Type\Rebate
 * @method static self default() 默认
 * @method static self normal() 普通
 * @method static self group_buying() 拼团
 * @method static self deposit_presale() 定金预售
 * @method static self deposit_find_goods() 订金找货
 * @method static self auction() 拍卖
 * @method static self zero_cost_single() 0元单
 * @method static self recycle() 回收
 * @method static self consign() 寄卖
 * @method static self send_sample() 寄样
 * @method static self zero_cost_lottery() 0元抽奖(超级福袋)
 * @method static self buy_sample() 买样
 * @method static self normal_customization() 普通定制
 * @method static self public_auction() 大众竞拍
 * @method static self express_hour() 小时达
 * @method static self deposit_presale_gift() 定金预售的赠品单
 * @method static self receipt() 收款
 */
final class RebateTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'normal' => 1,
            'group_buying' => 2,
            'deposit_presale' => 3,
            'deposit_find_goods' => 4,
            'auction' => 5,
            'zero_cost_single' => 6,
            'recycle' => 7,
            'consign' => 8,
            'send_sample' => 10,
            'zero_cost_lottery' => 11,
            'buy_sample' => 12,
            'normal_customization' => 13,
            'public_auction' => 16,
            'express_hour' => 18,
            'deposit_presale_gift' => 21,
            'receipt' => 103,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'normal' => '普通',
            'group_buying' => '拼团',
            'deposit_presale' => '定金预售',
            'deposit_find_goods' => '订金找货',
            'auction' => '拍卖',
            'zero_cost_single' => '0元单',
            'recycle' => '回收',
            'consign' => '寄卖',
            'send_sample' => '寄样',
            'zero_cost_lottery' => '0元抽奖(超级福袋)',
            'buy_sample' => '买样',
            'normal_customization' => '普通定制',
            'public_auction' => '大众竞拍',
            'express_hour' => '小时达',
            'deposit_presale_gift' => '定金预售的赠品单',
            'receipt' => '收款',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',  // 默认
            1 => 'red',            // 普通
            2 => 'orange',         // 拼团
            3 => 'yellow',         // 定金预售
            4 => 'green',          // 订金找货
            5 => 'purple',         // 拍卖
            6 => 'blue',           // 0元单
            7 => 'pink',           // 回收
            8 => 'brown',          // 寄卖
            10 => 'cyan',           // 寄样
            11 => 'teal',           // 0元抽奖(超级福袋)
            12 => 'indigo',         // 买样
            13 => 'lime',           // 普通定制
            16 => 'amber',          // 大众竞拍
            18 => 'deep-purple',    // 小时达
            21 => 'light-green',    // 定金预售的赠品单
            103 => 'light-blue',     // 收款
        ];
    }
}
