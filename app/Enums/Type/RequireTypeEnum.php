<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * Enum representing requirement types.
 * @package App\Enums\Type
 * @method static self default()
 * @method static self MandatoryRequired()
 * @method static self SingleRequire()
 * @method static self MultipleRequire()
 * @method static self OptionalRequire()
 * @method static self Required ()
 */
final class RequireTypeEnum extends BaseEnum
{


    protected static function values(): array
    {
        return [
            'default' => 0,
            'MandatoryRequired' => 1,
            'SingleRequire' => 2,
            'MultipleRequire' => 3,
            'OptionalRequire' => 4,
            'Required' => 5,
        ];
    }

    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'MandatoryRequired' => '必选',
            'SingleRequire' => '单选',
            'MultipleRequire' => '多选',
            'OptionalRequire' => '可选',
            'Required' => '默选',
        ];
    }

    protected static function colors(): array
    {
        //
        return [
            0 => 'gray',
            1 => 'red',
            2 => 'orange',
            3 => 'yellow',
            4 => 'green',
        ];
    }
}
