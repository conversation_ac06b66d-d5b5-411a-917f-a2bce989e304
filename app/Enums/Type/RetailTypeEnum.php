<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class RetailTypeEnum
 * @package App\Enums\Type\Retail
 * @method static self default() 默认
 * @method static self multi_channel_sale() 全渠道售卖
 * @method static self specified_channel() 仅指定渠道
 */
final class RetailTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'multi_channel_sale' => 1,
            'specified_channel' => 2,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'multi_channel_sale' => '全渠道售卖',
            'specified_channel' => '仅指定渠道',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 全渠道售卖
            2 => 'orange',        // 仅指定渠道
        ];
    }
}
