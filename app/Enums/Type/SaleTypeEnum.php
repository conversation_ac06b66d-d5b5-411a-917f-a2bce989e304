<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;

/**
 * Class ProductSaleTypeEnum
 * @package App\Enums\Type\Product
 * @method static self default() 默认
 * @method static self online_only() 纯电商
 * @method static self same_as_offline() 专柜同款
 * @method static self customized() 云零售商家定制款
 */
final class SaleTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'online_only' => 1,
            'same_as_offline' => 2,
            'customized' => 3,
        ];
    }

    // Define status labels
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'online_only' => '纯电商',
            'same_as_offline' => '专柜同款',
            'customized' => '云零售商家定制款',
        ];
    }

    // Define color mapping
    protected static function colors(): array
    {
        return [
            0 => 'default_color', // 默认
            1 => 'red',           // 纯电商
            2 => 'orange',        // 专柜同款
            3 => 'yellow',        // 云零售商家定制款
        ];
    }
}
