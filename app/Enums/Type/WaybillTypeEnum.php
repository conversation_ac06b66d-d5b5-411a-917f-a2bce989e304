<?php

namespace App\Enums\Type;

use App\Enums\BaseEnum;


/**
 * DummyDocBlock
 * @package App\Enums\Type\User
 * @method static self default()
 * @method static self ownWaybill()
 * @method static self sharedWaybill()
 */
final class WaybillTypeEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'ownWaybill' => 1,
            'sharedWaybill' => 2,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        //
        return [
            'default' => '默认状态',
            'ownWaybill' => '自有面单',
            'sharedWaybill' => '共享面单',
        ];
    }


}
