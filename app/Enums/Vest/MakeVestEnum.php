<?php

namespace App\Enums\Vest;

use App\Enums\BaseEnum;


/**
 * Class MakeVestEnum
 * @package App\Enums\Vest\Make
 * @method static self default() 默认
 * @method static self Seller() 卖家供应商
 * @method static self ServiceProvider() 服务商仓配服务商
 * @method static self Supplier() 供应商
 * @method static self Buyer() 买家采购商含渠道
 * @method static self PickupStaff() 揽件员揽件员服务商
 * @method static self LogisticsProvider() 物流商
 * @method static self ThirdPartyUser() 第三方使用者
 * @method static self EnterpriseWeChat() 企业微信
 * @method static self Platform() 平台
 */
final class MakeVestEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'Seller' => 1,
            'ServiceProvider' => 2,
            'Supplier' => 3,
            'Buyer' => 4,
            'PickupStaff' => 5,
            'LogisticsProvider' => 6,
            'ThirdPartyUser' => 7,
            'EnterpriseWeChat' => 8,
            'Platform' => 9,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '默认',
            'Seller' => '卖家(供应商)',
            'ServiceProvider' => '服务商(仓配服务商)',
            'Supplier' => '供应商',
            'Buyer' => '买家(采购商含渠道)',
            'PickupStaff' => '揽件员(揽件员服务商)',
            'LogisticsProvider' => '物流商',
            'ThirdPartyUser' => '第三方使用者',
            'EnterpriseWeChat' => '企业微信',
            'Platform' => '平台',
        ];
    }

    // 定义颜色映射
    protected static function colors(): array
    {
        return [
            0 => 'default_color',        // default
            1 => 'red',                  // Seller
            2 => 'orange',               // ServiceProvider
            3 => 'yellow',               // Supplier
            4 => 'green',                // Buyer
            5 => 'purple',               // PickupStaff
            6 => 'blue',                 // LogisticsProvider
            7 => 'pink',                 // ThirdPartyUser
            8 => 'brown',                // EnterpriseWeChat
            9 => 'teal',                 // Platform
        ];
    }
}
