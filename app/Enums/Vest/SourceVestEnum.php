<?php

namespace App\Enums\Vest;

use App\Enums\BaseEnum;

/**
 * Class SourceVestEnum
 * @package App\Enums\Vest\Source
 * @method static self default() 未知状态
 * @method static self pc_platform() PC平台状态
 * @method static self mini_program() 小程序状态
 * @method static self mobile() APP状态
 * @method static self taobao() 淘宝/天猫状态
 * @method static self ali_1688() 1688状态
 * @method static self pinduoduo() 拼多多状态
 * @method static self jd() 京东状态
 * @method static self vipshop() 唯品会状态
 * @method static self aliexpress() 速卖通状态
 * @method static self shopee() Shopee状态
 * @method static self amazon() 亚马逊状态
 * @method static self douyin() 抖音小店状态
 * @method static self kuaishou() 快手状态
 * @method static self xiaohongshu() 小红书状态
 * @method static self wechat() 微信状态
 * @method static self moments() 朋友圈状态
 * @method static self alliance() 联盟状态
 * @method static self recommended() 推荐官推荐状态
 * @method static self flash_sale() 闪电购状态
 * @method static self lazada() Lazada状态
 * @method static self alibaba() 阿里国际状态
 * @method static self self_channel() 自有渠道状态
 */
final class SourceVestEnum extends BaseEnum
{
    protected static function values(): array
    {
        return [
            'default' => 0,
            'pc_platform' => 1,
            'mini_program' => 2,
            'mobile' => 3,
            'taobao' => 4,
            'ali_1688' => 5,
            'pinduoduo' => 6,
            'jd' => 7,
            'vipshop' => 8,
            'aliexpress' => 9,
            'shopee' => 10,
            'amazon' => 11,
            'douyin' => 12,
            'kuaishou' => 13,
            'xiaohongshu' => 14,
            'wechat' => 15,
            'moments' => 16,
            'alliance' => 17,
            'recommended' => 18,
            'flash_sale' => 19,
            'lazada' => 20,
            'alibaba' => 21,
            'self_channel' => 22,
        ];
    }

    // 定义状态标签
    protected static function labels(): array
    {
        return [
            'default' => '未知',
            'pc_platform' => 'PC平台',
            'mini_program' => '小程序',
            'mobile' => 'APP',
            'taobao' => '淘宝/天猫',
            'ali_1688' => '1688',
            'pinduoduo' => '拼多多',
            'jd' => '京东',
            'vipshop' => '唯品会',
            'aliexpress' => '速卖通',
            'shopee' => 'Shopee',
            'amazon' => '亚马逊',
            'douyin' => '抖音小店',
            'kuaishou' => '快手',
            'xiaohongshu' => '小红书',
            'wechat' => '微信',
            'moments' => '朋友圈',
            'alliance' => '联盟',
            'recommended' => '推荐官推荐',
            'flash_sale' => '闪电购',
            'lazada' => 'Lazada',
            'alibaba' => '阿里国际',
            'self_channel' => '自有渠道',
        ];
    }
}
