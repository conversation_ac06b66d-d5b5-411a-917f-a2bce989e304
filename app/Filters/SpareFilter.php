<?php

namespace App\Filters;

/**
 * Class SpareFilter
 * 提供 Spare 模型的过滤逻辑。
 */
class SpareFilter extends ModelFilter
{
    /**
     * 相关模型及其对应的过滤输入键。
     * 例如：['relationMethod' => ['input_key1', 'input_key2']]
     * @var array
     */
    public $relations = [];

    /**
     * 智能多关键词搜索（and/or分词、字段白名单安全）
     * @param string $keywords 关键词（如"iPhone 盖板"）
     * @param string $mode and/or，默认and
     * @param array|null $fields 可自定义字段，默认['spare_name','spare_sn','short_name']
     * @return $this
     */
    public function keyword($keywords, $mode = 'and', $fields = null)
    {
        $keywords = trim($keywords);
        if (!$keywords)
            return $this;

        // 支持中英文空格、逗号分词
        $words = preg_split('/[\s,，]+/u', $keywords);

        // 字段白名单（根据实际业务可以自行调整/扩展）
        $safeFields = ['spare_name', 'spare_sn', 'short_name'];
        if (is_array($fields) && !empty($fields)) {
            $fields = array_intersect($fields, $safeFields);
            if (empty($fields))
                $fields = $safeFields;
        } else {
            $fields = $safeFields;
        }

        $mode = strtolower($mode) === 'or' ? 'or' : 'and';

        return $this->where(function ($query) use ($words, $fields, $mode) {
            if ($mode === 'and') {
                foreach ($words as $word) {
                    $word = trim($word);
                    if ($word === '')
                        continue;
                    $query->where(function ($q) use ($word, $fields) {
                        foreach ($fields as $field) {
                            $q->orWhere($field, 'like', "%$word%");
                        }
                    });
                }
            } else {
                $query->where(function ($q) use ($words, $fields) {
                    foreach ($words as $word) {
                        $word = trim($word);
                        if ($word === '')
                            continue;
                        foreach ($fields as $field) {
                            $q->orWhere($field, 'like', "%$word%");
                        }
                    }
                });
            }
        });
    }

    /**
     * 按创建日期起始范围过滤。
     * @param string $dateFrom 起始日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_from($dateFrom)
    {
        if ($dateFrom) {
            return $this->whereDate('created_at', '>=', $dateFrom);
        }
        return $this;
    }

    /**
     * 按创建日期结束范围过滤。
     * @param string $dateTo 结束日期（格式：YYYY-MM-DD）
     * @return $this
     */
    public function date_to($dateTo)
    {
        if ($dateTo) {
            return $this->whereDate('created_at', '<=', $dateTo);
        }
        return $this;
    }

    /**
     * 按状态过滤备件（支持单个或数组）。
     * @param mixed $state 备件状态
     * @return $this
     */
    public function state($state)
    {
        if (is_array($state)) {
            return $this->whereIn('spare_state', $state);
        }
        if ($state !== null && $state !== '') {
            return $this->where('spare_state', $state);
        }
        return $this;
    }
}
