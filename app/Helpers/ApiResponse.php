<?php

namespace App\Helpers;

use App\Exceptions\BusinessException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * API响应统一处理 Trait
 *
 * 提供统一的API响应格式，包括：
 * - 成功响应
 * - 失败响应
 * - 业务异常处理
 * - 响应格式标准化
 */
trait ApiResponse
{
    /**
     * 成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 自定义消息
     * @param array $codeResponse 响应码配置
     * @param int $httpCode HTTP状态码
     * @return JsonResponse
     */
    public function success($data = null, string $message = '', array $codeResponse = ResponseEnum::HTTP_SUCCESS, int $httpCode = 200): JsonResponse
    {
        return $this->jsonResponse('success', $codeResponse, $data, $message, $httpCode);
    }

    /**
     * 生成统一的JSON响应格式
     *
     * @param string $status 响应状态 (success|fail)
     * @param array $codeResponse 响应码配置 [code, message]
     * @param mixed $data 响应数据
     * @param string $customMessage 自定义消息
     * @param int $httpCode HTTP状态码
     * @return JsonResponse
     */
    private function jsonResponse(string $status, array $codeResponse, mixed $data, string $customMessage, int $httpCode = 200): JsonResponse
    {
        [$code, $defaultMessage] = $codeResponse;

        $response = [
            'success' => $status === 'success',
            'status' => $status,
            'code' => $code,
            'message' => $customMessage ?: $defaultMessage,
            'data' => $data ?? [],
            'timestamp' => now()->toISOString(),
        ];

        // 在开发环境下添加调试信息
        if (config('app.debug') && $status === 'fail') {
            $response['debug'] = [
                'request_id' => request()->header('X-Request-ID', uniqid()),
                'trace_id' => request()->header('X-Trace-ID', uniqid()),
            ];
        }

        return response()->json($response, $httpCode);
    }

    /**
     * 失败响应
     *
     * @param string $message 自定义错误消息
     * @param mixed $data 响应数据
     * @param array $codeResponse 响应码配置
     * @param int $httpCode HTTP状态码
     * @return JsonResponse
     */
    public function fail(string $message = '', $data = null, array $codeResponse = ResponseEnum::HTTP_ERROR, int $httpCode = 400): JsonResponse
    {
        // 记录失败日志
        if ($message) {
            Log::warning('API请求失败: ' . $message, [
                'data' => $data,
                'code' => $codeResponse[0] ?? 'unknown'
            ]);
        }

        return $this->jsonResponse('fail', $codeResponse, $data, $message, $httpCode);
    }

    /**
     * 验证错误响应
     *
     * @param array $errors 验证错误信息
     * @param string $message 自定义消息
     * @return JsonResponse
     */
    public function validationError(array $errors, string $message = '数据验证失败'): JsonResponse
    {
        return $this->jsonResponse('fail', ResponseEnum::CLIENT_PARAMETER_ERROR, $errors, $message, 422);
    }

    /**
     * 未授权响应
     *
     * @param string $message 自定义消息
     * @return JsonResponse
     */
    public function unauthorized(string $message = '未授权访问'): JsonResponse
    {
        return $this->jsonResponse('fail', ResponseEnum::CLIENT_HTTP_UNAUTHORIZED, null, $message, 401);
    }

    /**
     * 禁止访问响应
     *
     * @param string $message 自定义消息
     * @return JsonResponse
     */
    public function forbidden(string $message = '禁止访问'): JsonResponse
    {
        return $this->jsonResponse('fail', [403001, '禁止访问'], null, $message, 403);
    }

    /**
     * 资源未找到响应
     *
     * @param string $message 自定义消息
     * @return JsonResponse
     */
    public function notFound(string $message = '资源未找到'): JsonResponse
    {
        return $this->jsonResponse('fail', ResponseEnum::CLIENT_NOT_FOUND_ERROR, null, $message, 404);
    }

    /**
     * 服务器错误响应
     *
     * @param string $message 自定义消息
     * @param mixed $data 错误数据
     * @return JsonResponse
     */
    public function serverError(string $message = '服务器内部错误', $data = null): JsonResponse
    {
        Log::error('服务器错误: ' . $message, ['data' => $data]);
        return $this->jsonResponse('fail', ResponseEnum::SYSTEM_ERROR, $data, $message, 500);
    }

    /**
     * 抛出业务异常
     *
     * @param array $codeResponse 响应码配置
     * @param string $message 自定义消息
     * @throws BusinessException
     */
    public function throwBusinessException(array $codeResponse = ResponseEnum::HTTP_ERROR, string $message = ''): void
    {
        throw new BusinessException($codeResponse, $message);
    }
}
