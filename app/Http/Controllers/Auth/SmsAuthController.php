<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\SmsVerificationRequest;
use App\Models\UserCenter\User;
use App\Services\Auth\AuthService;
use App\Services\Sms\SmsCodeService;
use App\Services\Sms\SmsService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

/**
 * 短信验证码认证控制器
 *
 * 负责处理基于手机号短信验证码的用户认证功能，包括：
 * - 发送短信验证码
 * - 验证码登录
 * - 验证码注册
 * - 手机号绑定
 * - 频率限制管理
 *
 * @package App\Http\Controllers\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class SmsAuthController extends Controller
{
    /**
     * 短信服务实例
     *
     * @var SmsService
     */
    protected SmsService $smsService;

    /**
     * 短信验证码服务实例
     *
     * @var SmsCodeService
     */
    protected SmsCodeService $smsCodeService;

    /**
     * 认证服务实例
     *
     * @var AuthService
     */
    protected AuthService $authService;

    /**
     * 构造函数
     *
     * 注入所需的服务依赖
     *
     * @param SmsService $smsService 短信发送服务
     * @param SmsCodeService $smsCodeService 验证码管理服务
     * @param AuthService $authService 认证服务
     */
    public function __construct(
        SmsService     $smsService,
        SmsCodeService $smsCodeService,
        AuthService    $authService
    )
    {
        $this->smsService = $smsService;
        $this->smsCodeService = $smsCodeService;
        $this->authService = $authService;
    }

    /**
     * 显示手机号登录页面
     *
     * 渲染手机号短信验证码登录表单
     *
     * @return View|RedirectResponse 返回登录视图或重定向响应
     */
    public function showLoginForm(): View|RedirectResponse
    {
        // 如果用户已经登录，重定向到仪表板
        if (Auth::check()) {
            Log::info('已登录用户尝试访问手机号登录页面', [
                'user_id' => Auth::id(),
                'ip' => request()->ip()
            ]);

            return redirect()->intended(route('dashboard', absolute: false));
        }

        return view('auth.sms-login', [
            'title' => '手机号登录',
            'description' => '使用手机号和短信验证码快速登录'
        ]);
    }

    /**
     * 显示手机号注册页面
     *
     * 渲染手机号短信验证码注册表单
     *
     * @return View|RedirectResponse 返回注册视图或重定向响应
     */
    public function showRegisterForm(): View|RedirectResponse
    {
        // 如果用户已经登录，重定向到仪表板
        if (Auth::check()) {
            Log::info('已登录用户尝试访问手机号注册页面', [
                'user_id' => Auth::id(),
                'ip' => request()->ip()
            ]);

            return redirect()->intended(route('dashboard', absolute: false));
        }

        return view('auth.sms-register', [
            'title' => '手机号注册',
            'description' => '使用手机号快速注册账户'
        ]);
    }

    /**
     * 发送短信验证码
     *
     * 向指定手机号发送验证码，支持登录和注册场景
     *
     * @param Request $request HTTP请求对象
     * @return JsonResponse 返回JSON响应
     */
    public function sendSmsCode(Request $request): JsonResponse
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'mobile' => [
                    'required',
                    'string',
                    'regex:/^1[3-9]\d{9}$/', // 中国大陆手机号格式
                ],
                'type' => [
                    'required',
                    'string',
                    'in:login,register,bind', // 验证码类型：登录、注册、绑定
                ],
                'captcha' => [
                    'sometimes',
                    'string',
                    'min:4',
                    'max:6'
                ]
            ], [
                'mobile.required' => '手机号是必填项',
                'mobile.regex' => '请输入有效的中国大陆手机号',
                'type.required' => '验证码类型是必填项',
                'type.in' => '无效的验证码类型',
                'captcha.min' => '图形验证码至少4位',
                'captcha.max' => '图形验证码最多6位'
            ]);

            $mobile = $validated['mobile'];
            $type = $validated['type'];

            // 检查发送频率限制
            $this->checkSmsRateLimit($mobile);

            // 根据类型进行业务逻辑检查
            $this->validateSmsCodeType($mobile, $type);

            // 生成并发送验证码
            $code = $this->smsCodeService->generateCode($mobile, $type);
            $result = $this->smsService->sendVerificationCode($mobile, $code, $type);

            if (!$result['success']) {
                throw new \Exception($result['message'] ?? '短信发送失败');
            }

            // 记录发送成功日志
            Log::info('短信验证码发送成功', [
                'mobile' => $mobile,
                'type' => $type,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // 增加频率限制计数
            RateLimiter::hit($this->getSmsRateLimitKey($mobile));

            return response()->json([
                'success' => true,
                'message' => '验证码已发送，请注意查收',
                'data' => [
                    'mobile' => $mobile,
                    'expires_in' => config('sms.code_expires_minutes', 5) * 60, // 秒
                    'can_resend_in' => config('sms.resend_interval_seconds', 60) // 秒
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '请求参数验证失败',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            // 记录发送失败日志
            Log::error('短信验证码发送失败', [
                'mobile' => $request->input('mobile'),
                'type' => $request->input('type'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查短信发送频率限制
     *
     * 防止短信验证码被恶意频繁发送
     *
     * @param string $mobile 手机号
     * @throws ValidationException 当超过频率限制时抛出异常
     */
    private function checkSmsRateLimit(string $mobile): void
    {
        $key = $this->getSmsRateLimitKey($mobile);
        $maxAttempts = config('sms.max_attempts_per_hour', 10);
        $decayMinutes = config('sms.rate_limit_decay_minutes', 60);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            $minutes = ceil($seconds / 60);

            Log::warning('短信发送频率限制触发', [
                'mobile' => $mobile,
                'attempts' => RateLimiter::attempts($key),
                'available_in_seconds' => $seconds,
                'ip' => request()->ip()
            ]);

            throw ValidationException::withMessages([
                'mobile' => "发送过于频繁，请在 {$minutes} 分钟后再试"
            ]);
        }
    }

    /**
     * 获取短信频率限制的键名
     *
     * @param string $mobile 手机号
     * @return string 返回频率限制键名
     */
    private function getSmsRateLimitKey(string $mobile): string
    {
        return 'sms_rate_limit:' . $mobile . '|' . request()->ip();
    }

    /**
     * 验证短信验证码类型的业务逻辑
     *
     * 根据不同的验证码类型进行相应的业务检查
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @throws ValidationException 当业务逻辑验证失败时抛出异常
     */
    private function validateSmsCodeType(string $mobile, string $type): void
    {
        switch ($type) {
            case 'register':
                // 注册时检查手机号是否已存在
                if (User::where('mobile', $mobile)->exists()) {
                    throw ValidationException::withMessages([
                        'mobile' => '该手机号已注册，请直接登录'
                    ]);
                }
                break;

            case 'login':
                // 登录时检查手机号是否已注册
                if (!User::where('mobile', $mobile)->exists()) {
                    throw ValidationException::withMessages([
                        'mobile' => '该手机号尚未注册，请先注册'
                    ]);
                }
                break;

            case 'bind':
                // 绑定时检查用户是否已登录
                if (!Auth::check()) {
                    throw ValidationException::withMessages([
                        'mobile' => '请先登录后再绑定手机号'
                    ]);
                }

                // 检查手机号是否已被其他用户绑定
                $existingUser = User::where('mobile', $mobile)
                    ->where('id', '!=', Auth::id())
                    ->first();

                if ($existingUser) {
                    throw ValidationException::withMessages([
                        'mobile' => '该手机号已被其他用户绑定'
                    ]);
                }
                break;
        }
    }

    /**
     * 手机号验证码注册
     *
     * 使用手机号和短信验证码进行用户注册
     *
     * @param SmsVerificationRequest $request 短信验证请求对象
     * @return JsonResponse|RedirectResponse 返回JSON响应或重定向响应
     */
    public function register(SmsVerificationRequest $request): JsonResponse|RedirectResponse
    {
        try {
            $mobile = $request->input('mobile');
            $code = $request->input('code');
            $name = $request->input('name', '');

            // 验证短信验证码
            if (!$this->smsCodeService->verifyCode($mobile, $code, 'register')) {
                throw ValidationException::withMessages([
                    'code' => '验证码错误或已过期，请重新获取'
                ]);
            }

            // 检查手机号是否已注册
            if (User::where('mobile', $mobile)->exists()) {
                throw ValidationException::withMessages([
                    'mobile' => '该手机号已注册，请直接登录'
                ]);
            }

            // 使用数据库事务创建用户
            $user = DB::transaction(function () use ($mobile, $name) {
                return User::create([
                    'name' => $name ?: '手机用户' . substr($mobile, -4),
                    'mobile' => $mobile,
                    'password' => Hash::make(Str::random(32)), // 随机密码
                    'mobile_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            });

            // 触发注册事件
            event(new Registered($user));

            // 自动登录新用户
            Auth::login($user);
            $request->session()->regenerate();

            // 清除验证码
            $this->smsCodeService->clearCode($mobile, 'register');

            // 记录注册成功日志
            Log::info('手机号验证码注册成功', [
                'user_id' => $user->id,
                'mobile' => $mobile,
                'name' => $user->name,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // 根据请求类型返回响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '注册成功，欢迎加入！',
                    'redirect_url' => route('dashboard', absolute: false),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'mobile' => $user->mobile,
                    ]
                ]);
            }

            return redirect()->intended(route('dashboard', absolute: false))
                ->with('success', '注册成功，欢迎加入我们的平台！');

        } catch (ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '注册失败',
                    'errors' => $e->errors()
                ], 422);
            }

            return back()->withErrors($e->errors())->withInput();

        } catch (\Exception $e) {
            Log::error('手机号验证码注册失败', [
                'mobile' => $request->input('mobile'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '注册过程中发生错误，请稍后重试'
                ], 500);
            }

            return back()->with('error', '注册过程中发生错误，请稍后重试');
        }
    }

    /**
     * 手机号验证码登录
     *
     * 使用手机号和短信验证码进行用户登录
     *
     * @param SmsVerificationRequest $request 短信验证请求对象
     * @return JsonResponse|RedirectResponse 返回JSON响应或重定向响应
     */
    public function login(SmsVerificationRequest $request): JsonResponse|RedirectResponse
    {
        try {
            $mobile = $request->input('mobile');
            $code = $request->input('code');
            $remember = $request->boolean('remember');

            // 验证短信验证码
            if (!$this->smsCodeService->verifyCode($mobile, $code, 'login')) {
                throw ValidationException::withMessages([
                    'code' => '验证码错误或已过期，请重新获取'
                ]);
            }

            // 查找用户
            $user = User::where('mobile', $mobile)->first();

            if (!$user) {
                throw ValidationException::withMessages([
                    'mobile' => '该手机号尚未注册，请先注册账户'
                ]);
            }

            // 检查用户状态
            if (isset($user->status) && $user->status !== 'active') {
                throw ValidationException::withMessages([
                    'mobile' => '账户已被禁用，请联系客服'
                ]);
            }

            // 执行登录
            Auth::login($user, $remember);
            $request->session()->regenerate();

            // 清除验证码
            $this->smsCodeService->clearCode($mobile, 'login');

            // 记录登录成功日志
            Log::info('手机号验证码登录成功', [
                'user_id' => $user->id,
                'mobile' => $mobile,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'remember' => $remember
            ]);

            // 根据请求类型返回响应
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '登录成功，欢迎回来！',
                    'redirect_url' => route('dashboard', absolute: false),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name ?? $user->user_name ?? '',
                        'mobile' => $user->mobile,
                    ]
                ]);
            }

            return redirect()->intended(route('dashboard', absolute: false))
                ->with('success', '登录成功，欢迎回来！');

        } catch (ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '登录失败',
                    'errors' => $e->errors()
                ], 422);
            }

            return back()->withErrors($e->errors())->withInput();

        } catch (\Exception $e) {
            Log::error('手机号验证码登录失败', [
                'mobile' => $request->input('mobile'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '登录过程中发生错误，请稍后重试'
                ], 500);
            }

            return back()->with('error', '登录过程中发生错误，请稍后重试');
        }
    }
}
