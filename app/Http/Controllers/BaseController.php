<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 基础控制器类
 *
 * 提供通用的控制器功能，包括：
 * - 统一的响应格式
 * - 通用的 CRUD 操作模板
 * - 错误处理和异常管理
 * - 数据验证
 * - 分页和过滤
 * - 权限检查
 */
abstract class BaseController extends Controller
{
    /**
     * 关联的模型类名
     */
    protected string $modelClass;

    /**
     * 视图文件前缀
     */
    protected string $viewPrefix;

    /**
     * 路由名称前缀
     */
    protected string $routePrefix;

    /**
     * 每页显示数量
     */
    protected int $perPage = 15;

    /**
     * 允许的排序字段
     */
    protected array $allowedSortFields = ['id', 'created_at', 'updated_at'];

    /**
     * 默认排序字段
     */
    protected string $defaultSortField = 'created_at';

    /**
     * 默认排序方向
     */
    protected string $defaultSortDirection = 'desc';

    /**
     * 是否启用软删除
     */
    protected bool $enableSoftDeletes = false;

    /**
     * 查找模型实例
     *
     * @param mixed $id
     * @return \Illuminate\Database\Eloquent\Model
     * @throws ModelNotFoundException
     */
    protected function findModel($id)
    {
        return $this->getModelClass()::findOrFail($id);
    }

    /**
     * 获取模型类
     *
     * @return string
     * @throws Exception
     */
    protected function getModelClass(): string
    {
        if (!isset($this->modelClass)) {
            throw new Exception('模型类未定义，请在子类中设置 $modelClass 属性');
        }

        if (!class_exists($this->modelClass)) {
            throw new Exception("模型类 {$this->modelClass} 不存在");
        }

        return $this->modelClass;
    }

    /**
     * 应用过滤条件
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Request $request
     * @return void
     */
    protected function applyFilters($query, Request $request): void
    {
        // 通用搜索
        if ($search = $request->get('search')) {
            $this->applySearch($query, $search);
        }

        // 状态过滤
        if ($status = $request->get('status')) {
            $query->where('status', $status);
        }

        // 日期范围过滤
        if ($startDate = $request->get('start_date')) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate = $request->get('end_date')) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // 子类可以重写此方法来实现特定的过滤逻辑
    }

    /**
     * 应用搜索条件
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return void
     */
    protected function applySearch($query, string $search): void
    {
        // 子类应该重写此方法来实现具体的搜索逻辑
        // 例如：$query->where('name', 'like', "%{$search}%");
    }

    /**
     * 应用排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Request $request
     * @return void
     */
    protected function applySorting($query, Request $request): void
    {
        $sortField = $request->get('sort', $this->defaultSortField);
        $sortDirection = $request->get('direction', $this->defaultSortDirection);

        // 验证排序字段
        if (in_array($sortField, $this->allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection === 'desc' ? 'desc' : 'asc');
        } else {
            $query->orderBy($this->defaultSortField, $this->defaultSortDirection);
        }
    }

    /**
     * 获取分页数量
     *
     * @param Request $request
     * @return int
     */
    protected function getPerPage(Request $request): int
    {
        $perPage = (int)$request->get('per_page', $this->perPage);
        return min(max($perPage, 1), 100); // 限制在1-100之间
    }

    /**
     * 验证存储请求
     *
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    protected function validateStoreRequest(Request $request): array
    {
        $rules = $this->getStoreValidationRules($request);
        $messages = $this->getValidationMessages();
        $attributes = $this->getValidationAttributes();

        return $request->validate($rules, $messages, $attributes);
    }

    /**
     * 获取存储验证规则
     *
     * @param Request $request
     * @return array
     */
    protected function getStoreValidationRules(Request $request): array
    {
        // 子类应该重写此方法
        return [];
    }

    /**
     * 获取验证错误消息
     *
     * @return array
     */
    protected function getValidationMessages(): array
    {
        return [];
    }

    /**
     * 获取验证属性名称
     *
     * @return array
     */
    protected function getValidationAttributes(): array
    {
        return [];
    }

    /**
     * 验证更新请求
     *
     * @param Request $request
     * @param mixed $item
     * @return array
     * @throws ValidationException
     */
    protected function validateUpdateRequest(Request $request, $item): array
    {
        $rules = $this->getUpdateValidationRules($request, $item);
        $messages = $this->getValidationMessages();
        $attributes = $this->getValidationAttributes();

        return $request->validate($rules, $messages, $attributes);
    }

    /**
     * 获取更新验证规则
     *
     * @param Request $request
     * @param mixed $item
     * @return array
     */
    protected function getUpdateValidationRules(Request $request, $item): array
    {
        // 默认使用存储规则，子类可以重写
        return $this->getStoreValidationRules($request);
    }
    // ==================== 数据处理钩子方法 ====================

    /**
     * 预处理存储数据
     *
     * @param array $data
     * @param Request $request
     * @return array
     */
    protected function preprocessStoreData(array $data, Request $request): array
    {
        return $data;
    }

    /**
     * 预处理更新数据
     *
     * @param array $data
     * @param mixed $item
     * @param Request $request
     * @return array
     */
    protected function preprocessUpdateData(array $data, $item, Request $request): array
    {
        return $data;
    }

    /**
     * 存储后处理
     *
     * @param mixed $item
     * @param Request $request
     * @return void
     */
    protected function afterStore($item, Request $request): void
    {
        // 子类可以重写此方法
    }

    /**
     * 更新后处理
     *
     * @param mixed $item
     * @param Request $request
     * @return void
     */
    protected function afterUpdate($item, Request $request): void
    {
        // 子类可以重写此方法
    }

    /**
     * 删除前检查
     *
     * @param mixed $item
     * @param Request $request
     * @return void
     * @throws Exception
     */
    protected function beforeDestroy($item, Request $request): void
    {
        // 子类可以重写此方法进行删除前检查
    }

    /**
     * 删除后处理
     *
     * @param mixed $item
     * @param Request $request
     * @return void
     */
    protected function afterDestroy($item, Request $request): void
    {
        // 子类可以重写此方法
    }

    // ==================== 视图数据方法 ====================

    /**
     * 获取创建表单数据
     *
     * @param Request $request
     * @return array
     */
    protected function getCreateFormData(Request $request): array
    {
        return [];
    }

    /**
     * 获取编辑表单数据
     *
     * @param mixed $item
     * @param Request $request
     * @return array
     */
    protected function getEditFormData($item, Request $request): array
    {
        return [];
    }

    /**
     * 获取详情页面数据
     *
     * @param mixed $item
     * @param Request $request
     * @return array
     */
    protected function getShowViewData($item, Request $request): array
    {
        return [];
    }

    // ==================== 响应方法（兼容旧版本） ====================

    /**
     * 成功响应（兼容旧版本）
     *
     * @deprecated 请使用 success() 方法
     */
    protected function successResponse($data = [], string $message = '操作成功', int $code = 200): JsonResponse
    {
        return $this->success($data, $message, [], $code);
    }

    /**
     * 错误响应（兼容旧版本）
     *
     * @deprecated 请使用 fail() 方法
     */
    protected function errorResponse(string $message = '操作失败', int $code = 400, $errors = null): JsonResponse
    {
        return $this->fail($message, $errors, [], $code);
    }

    /**
     * 处理异常
     *
     * @param Exception $e
     * @param string $defaultMessage
     * @return JsonResponse|\Illuminate\Http\RedirectResponse
     */
    protected function handleException(Exception $e, string $defaultMessage = '操作失败')
    {
        // 记录异常日志
        Log::error($defaultMessage . ': ' . $e->getMessage(), [
            'exception' => $e,
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'request' => request()->all()
        ]);

        if (request()->expectsJson()) {
            // 根据异常类型返回不同的响应
            if ($e instanceof ModelNotFoundException) {
                return $this->notFound('资源不存在');
            } elseif ($e instanceof ValidationException) {
                return $this->validationError($e->errors(), '数据验证失败');
            } else {
                return $this->serverError($defaultMessage);
            }
        }

        return redirect()->back()
            ->withErrors($defaultMessage)
            ->withInput();
    }
}
