<?php

namespace App\Http\Controllers\Chain;

use App\Http\Controllers\BaseController;
use Exception;
use Illuminate\Http\Request;

/**
 * 链式商店管理控制器
 * 管理链式商店的 CRUD 操作，包括：
 * - 商店列表查看
 * - 商店详情管理
 * - 商店状态更新
 * - 商店配置管理
 */
class ShopController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->viewPrefix = 'chain.shop';
        $this->routePrefix = 'chain.shop';
        $this->perPage = 15;
    }

    /**
     * 显示商店列表
     */
    public function index(Request $request)
    {
        try {
            $shops = $this->getMockShops();

            if ($request->expectsJson()) {
                return $this->successResponse($shops, '获取商店列表成功');
            }

            return view('chain', [
                'title' => '链式商店管理',
                'description' => '管理链式商店的配置和状态',
                'shops' => $shops,
                'menuActive' => 'chain.shop'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取商店列表失败');
        }
    }

    /**
     * 获取模拟商店数据
     */
    private function getMockShops(): array
    {
        return [
            'data' => [
                [
                    'id' => 1,
                    'name' => '链式旗舰店',
                    'code' => 'CHAIN001',
                    'status' => 'active',
                    'type' => 'flagship',
                    'address' => '北京市朝阳区建国门外大街1号',
                    'manager' => '张经理',
                    'phone' => '010-12345678',
                    'created_at' => '2024-01-15 09:00:00'
                ],
                [
                    'id' => 2,
                    'name' => '链式体验店',
                    'code' => 'CHAIN002',
                    'status' => 'active',
                    'type' => 'experience',
                    'address' => '上海市浦东新区陆家嘴环路1000号',
                    'manager' => '李经理',
                    'phone' => '021-87654321',
                    'created_at' => '2024-02-20 10:30:00'
                ]
            ]
        ];
    }
}
