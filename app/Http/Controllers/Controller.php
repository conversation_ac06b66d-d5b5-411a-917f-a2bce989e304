<?php

namespace App\Http\Controllers;

use App\Helpers\ApiResponse;
use App\Models\ProviderCenter\Provider;
use App\Models\UserCenter\User;
use Exception;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

// 注意：以下类需要根据实际项目情况添加正确的导入
// use App\Models\Hub;
// use EasyWeChat\Factory;
// use App\Models\Store;
// use App\Http\Resources\StoreResource;
// use App\Http\Resources\ProviderResource;
// use Guanguans\LaravelExceptionNotify\ExceptionNotifier;

abstract class Controller
{
    use AuthorizesRequests, ValidatesRequests;

    // API接口响应
    use ApiResponse;

    /**
     * 兼容旧方法名
     * @deprecated 请使用 maskSensitiveData 方法
     */
    public static function substr_cut($user_name)
    {
        return self::maskSensitiveData($user_name);
    }

    /**
     * 加密敏感信息（手机号、支付账号、银行卡等）
     * 只保留字符串首尾字符，隐藏中间用*代替
     *
     * @param string $sensitiveData 敏感数据
     * @param int $prefixLength 前缀保留长度，默认3
     * @param int $suffixLength 后缀保留长度，默认3
     * @return string 格式化后的数据
     */
    public static function maskSensitiveData(string $sensitiveData, int $prefixLength = 3, int $suffixLength = 3): string
    {
        if (empty($sensitiveData)) {
            return '';
        }

        $length = mb_strlen($sensitiveData, 'utf-8');

        // 如果字符串长度小于等于前缀+后缀长度，只显示第一个字符
        if ($length <= $prefixLength + $suffixLength) {
            return mb_substr($sensitiveData, 0, 1, 'utf-8') . str_repeat('*', $length - 1);
        }

        $prefix = mb_substr($sensitiveData, 0, $prefixLength, 'utf-8');
        $suffix = mb_substr($sensitiveData, -$suffixLength, null, 'utf-8');
        $maskLength = $length - $prefixLength - $suffixLength;

        return $prefix . str_repeat('*', $maskLength) . $suffix;
    }

    /**
     * 获取微信小程序实例
     *
     * @return mixed
     * @throws Exception
     */
    public function getminiProgram()
    {
        try {
            $hubId = config('app.hub_id');
            if (!$hubId) {
                throw new Exception('Hub ID 未配置');
            }

            // 注意：需要根据实际项目导入 Hub 模型
            // $Hub = Hub::find($hubId);
            // if (!$Hub) {
            //     throw new Exception('Hub 配置未找到');
            // }

            // $config = [
            //     'app_id' => $Hub->mini_appid,
            //     'secret' => $Hub->mini_secret,
            // ];

            // return Factory::miniProgram($config);

            // 临时返回，需要根据实际项目调整
            throw new Exception('miniProgram 方法需要根据实际项目配置');
        } catch (Exception $e) {
            Log::error('获取微信小程序实例失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取微信公众号实例
     *
     * @return mixed
     * @throws Exception
     */
    public function getofficialAccount()
    {
        try {
            $hubId = config('app.hub_id');
            if (!$hubId) {
                throw new Exception('Hub ID 未配置');
            }

            // 注意：需要根据实际项目导入 Hub 模型
            // $Hub = Hub::find($hubId);
            // if (!$Hub) {
            //     throw new Exception('Hub 配置未找到');
            // }

            // $config = [
            //     'app_id' => $Hub->office_appid,
            //     'secret' => $Hub->office_secret,
            //     'token' => $Hub->office_token,
            //     'aes_key' => $Hub->office_encodingaeskey,
            // ];

            // return Factory::officialAccount($config);

            // 临时返回，需要根据实际项目调整
            throw new Exception('officialAccount 方法需要根据实际项目配置');
        } catch (Exception $e) {
            Log::error('获取微信公众号实例失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取用户令牌
     *
     * @return string
     * @throws Exception
     * @deprecated 建议使用 getUserToken() 方法（小写开头）
     */
    public function getUserToken(): string
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new Exception('用户未登录');
            }

            $platformId = config('app.hub_id');
            if (!$platformId) {
                throw new Exception('平台ID未配置');
            }

            $userModel = User::where('platform_id', $platformId)->find($user->id);
            if (!$userModel || !$userModel->uuid) {
                throw new Exception('用户信息不完整');
            }

            return md5($userModel->uuid . csrf_token());
        } catch (Exception $e) {
            Log::error('获取用户令牌失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取商店令牌
     *
     * @param int $Store_id 商店ID
     * @return string
     * @throws Exception
     * @deprecated 建议使用 getStoreToken() 方法（小写开头）
     */
    public function getStoreToken($Store_id): string
    {
        try {
            $platformId = config('app.hub_id');
            if (!$platformId) {
                throw new Exception('平台ID未配置');
            }

            // 注意：需要根据实际项目导入 Store 模型和 StoreResource
            // $store = Store::where('platform_id', $platformId)->find($Store_id);
            // if (!$store || !$store->uuid) {
            //     throw new Exception('商店信息不存在或不完整');
            // }

            // return md5($store->uuid . csrf_token());

            // 临时返回，需要根据实际项目调整
            throw new Exception('GetStoreToken 方法需要根据实际项目配置');
        } catch (Exception $e) {
            Log::error('获取商店令牌失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取供应商令牌
     *
     * @param int $Provider_id 供应商ID
     * @return string
     * @throws Exception
     * @deprecated 建议使用 getProviderToken() 方法（小写开头）
     */
    public function getProviderToken($Provider_id): string
    {
        try {
            $platformId = config('app.hub_id');
            if (!$platformId) {
                throw new Exception('平台ID未配置');
            }

            $provider = Provider::where('platform_id', $platformId)->find($Provider_id);
            if (!$provider || !$provider->uuid) {
                throw new Exception('供应商信息不存在或不完整');
            }

            return md5($provider->uuid . csrf_token());
        } catch (Exception $e) {
            Log::error('获取供应商令牌失败: ' . $e->getMessage());
            throw $e;
        }
    }


    /**
     * 报告异常
     *
     * @param Exception $exception 异常实例
     * @return void
     */
    public function reportException(Exception $exception): void
    {
        try {
            // 记录异常日志
            Log::error('异常报告: ' . $exception->getMessage(), [
                'exception' => $exception,
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ]);

            // 注意：需要根据实际项目导入 ExceptionNotifier
            // ExceptionNotifier::onChannel('xiZhi', '')->reportIf(true, $exception);
        } catch (Exception $e) {
            // 防止异常报告本身出现异常
            Log::critical('异常报告失败: ' . $e->getMessage());
        }
    }


}
