<?php

namespace App\Http\Controllers\Lesson\Home;

use App\Http\Controllers\BaseController;
use Exception;
use Illuminate\Http\Request;

/**
 * 课程首页控制器
 * 管理课程模块的首页功能，包括：
 * - 课程概览
 * - 统计数据
 * - 快速操作
 */
class IndexController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 设置视图前缀
        $this->viewPrefix = 'lesson.home';

        // 设置路由前缀
        $this->routePrefix = 'lesson.home';
    }

    /**
     * 显示课程首页
     */
    public function index(Request $request)
    {
        try {
            // 获取统计数据
            $stats = $this->getStatistics();

            // 获取最近活动
            $recentActivities = $this->getRecentActivities();

            // 获取快速链接
            $quickLinks = $this->getQuickLinks();

            if ($request->expectsJson()) {
                return $this->successResponse([
                    'stats' => $stats,
                    'recent_activities' => $recentActivities,
                    'quick_links' => $quickLinks
                ], '获取首页数据成功');
            }

            return view('lesson', [
                'title' => '课程管理首页',
                'description' => '课程管理系统首页，提供课程概览和快速操作',
                'stats' => $stats,
                'recent_activities' => $recentActivities,
                'quick_links' => $quickLinks,
                'menuActive' => 'lesson'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取首页数据失败');
        }
    }

    /**
     * 获取统计数据
     */
    private function getStatistics(): array
    {
        return [
            'total_courses' => 0, // 总课程数
            'active_courses' => 0, // 活跃课程数
            'total_students' => 0, // 总学生数
            'total_orders' => 0, // 总订单数
            'revenue_today' => 0, // 今日收入
            'revenue_month' => 0, // 本月收入
        ];
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities(): array
    {
        return [
            // 这里可以添加最近的课程活动、订单等
        ];
    }

    /**
     * 获取快速链接
     */
    private function getQuickLinks(): array
    {
        return [
            [
                'title' => '课程管理',
                'url' => route('lesson.product.index'),
                'icon' => 'fas fa-book',
                'description' => '管理所有课程产品'
            ],
            [
                'title' => '订单管理',
                'url' => route('lesson.order.index'),
                'icon' => 'fas fa-shopping-cart',
                'description' => '查看和管理订单'
            ],
            [
                'title' => '客户管理',
                'url' => route('lesson.customer.index'),
                'icon' => 'fas fa-users',
                'description' => '管理客户信息'
            ],
            [
                'title' => '分类管理',
                'url' => route('lesson.category.index'),
                'icon' => 'fas fa-tags',
                'description' => '管理课程分类'
            ]
        ];
    }

    /**
     * 显示创建表单（不适用于首页）
     */
    public function create(Request $request)
    {
        abort(404);
    }

    /**
     * 存储新资源（不适用于首页）
     */
    public function store(Request $request)
    {
        abort(404);
    }

    /**
     * 显示指定资源（不适用于首页）
     */
    public function show(Request $request, $id)
    {
        abort(404);
    }

    /**
     * 显示编辑表单（不适用于首页）
     */
    public function edit(Request $request, $id)
    {
        abort(404);
    }

    /**
     * 更新指定资源（不适用于首页）
     */
    public function update(Request $request, $id)
    {
        abort(404);
    }

    /**
     * 删除指定资源（不适用于首页）
     */
    public function destroy(Request $request, $id)
    {
        abort(404);
    }
}
