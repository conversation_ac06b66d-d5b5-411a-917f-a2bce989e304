<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\BaseController;
use Exception;
use Illuminate\Http\Request;

/**
 * 商店监控控制器
 * 管理商店监控功能，包括：
 * - 商店状态监控
 * - 性能指标查看
 * - 异常报警处理
 */
class ShopController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->viewPrefix = 'monitor.shop';
        $this->routePrefix = 'monitor.shop';
        $this->perPage = 15;
    }

    /**
     * 显示商店监控列表
     */
    public function index(Request $request)
    {
        try {
            $shops = $this->getMockShops();

            if ($request->expectsJson()) {
                return $this->successResponse($shops, '获取商店监控数据成功');
            }

            return view('lesson', [
                'title' => '商店监控',
                'description' => '实时监控商店运营状态',
                'shops' => $shops,
                'menuActive' => 'monitor.shop'
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '获取商店监控数据失败');
        }
    }

    /**
     * 获取模拟商店数据
     */
    private function getMockShops(): array
    {
        return [
            'data' => [
                [
                    'id' => 1,
                    'name' => '旗舰店',
                    'status' => 'online',
                    'orders_today' => 156,
                    'revenue_today' => 25680.50,
                    'cpu_usage' => 45.2,
                    'memory_usage' => 62.8,
                    'response_time' => 120,
                    'last_check' => now()->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'name' => '体验店',
                    'status' => 'warning',
                    'orders_today' => 89,
                    'revenue_today' => 12340.00,
                    'cpu_usage' => 78.5,
                    'memory_usage' => 85.2,
                    'response_time' => 350,
                    'last_check' => now()->format('Y-m-d H:i:s')
                ]
            ]
        ];
    }
}
