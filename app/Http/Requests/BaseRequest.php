<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\ValidationException;

class BaseRequest extends FormRequest
{
    /**
     * 自定义验证失败处理逻辑，区分 JSON 请求和表单提交。
     * @param Validator $validator
     * @return void
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        // 获取验证失败的错误信息
        $errors = $validator->errors();

        // 检查错误是否为 MessageBag 实例
        if (!($errors instanceof MessageBag)) {
            $errors = new MessageBag((array)$errors); // 确保 $errors 为 MessageBag
        }

        // 构建汇总的错误消息
        $summaryMessage = $this->getSummaryMessage($errors);

        // 判断请求是否为 JSON 请求
        if ($this->expectsJson()) {
            // 获取自定义的状态码
            $statusCode = $this->getStatusCode($validator);

            // 构建自定义的 JSON 响应结构，包含详细错误信息
            $response = [
                'success' => false,
                'message' => $summaryMessage, // 汇总的错误消息
                'data' => $errors, // 验证器生成的具体字段错误信息
                'code' => $statusCode, // 自定义状态码
                'status' => 200002,
            ];

            // 抛出 HttpResponseException 并返回自定义 JSON 响应
            throw new HttpResponseException(response()->json($response, $statusCode));
        } else {
            // 非 JSON 请求，使用 Laravel 默认的表单提交处理逻辑
            $response = redirect()
                ->back()
                ->withErrors($errors) // 错误信息传递给视图
                ->with('message', $summaryMessage) // 汇总错误消息
                ->withInput($this->input()); // 保留用户提交的数据

            // 抛出 ValidationException 维持默认表单处理逻辑
            throw new ValidationException($validator, $response);
        }
    }

    /**
     * 构建汇总的错误消息，支持多语言处理
     * @param MessageBag|array $errors
     * @return string 汇总的错误提示
     */
    protected function getSummaryMessage($errors)
    {
        // 获取客户端请求的语言
        //$locale = $this->header('Accept-Language', 'en-US');
        //app()->setLocale($locale); // 设置应用的语言环境

        // 确保错误为数组或 MessageBag 对象
        if ($errors instanceof MessageBag) {
            $allErrors = $errors->all(); // 从 MessageBag 中获取所有错误
        } else {
            $allErrors = (array)$errors; // 如果是数组，直接使用
        }

        // 将所有错误合并为一条字符串消息
        return implode(' ', $allErrors); // 将错误消息用空格连接起来
    }

    /**
     * 获取 HTTP 状态码，依据不同的错误类型自定义状态码
     * @param Validator $validator
     * @return int
     */
    protected function getStatusCode(Validator $validator)
    {
        // 如果存在 email 字段的错误，返回 409 状态码
        if ($validator->errors()->has('email')) {
            return 409; // 冲突，例如电子邮件已存在
        }

        return 202; // 默认返回 202 Unprocessable Entity
    }
}
