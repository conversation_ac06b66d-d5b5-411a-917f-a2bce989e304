<?php

namespace App\Http\Requests\Lesson;

use App\Enums\Type\ProductEditTypeEnum;
use App\Http\Requests\BaseRequest;
use Spatie\Enum\Laravel\Rules\EnumRule;

class ProductDraftRequest extends BaseRequest
{
    /**
     * 确定用户是否有权限发起这个请求。
     * @return bool
     */
    public function authorize()
    {
        // 此处可以添加逻辑来检查当前用户是否有权限执行此操作。
        // 例如，可以检查用户是否登录或者是否有特定的角色或权限。
        // 示例：使用 Gate 来检查用户是否有权限
        // return Gate::allows('edit_store');
        // 默认返回 true，表示不做任何权限检查。
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     * 获取应用于请求的验证规则
     * @return string[]
     */
    public function rules(): array
    {
        // 通过检查请求方法来确定当前是创建操作还是更新操作
        $isUpdate = $this->isMethod('patch') || $this->isMethod('put');
        // 定义基本验证规则
        $rules = [
            // 基本产品信息
            'form.store_uuid' => 'required|uuid|exists:store_center.stores,uuid',  // 确保 store_uuid 存在于 stores 表中
            'form.schema_uuid' => 'required|uuid|exists:produce_brace.produce_schemas,uuid',  // 确保 schema_uuid 存在于 schemas 表中
            'form.product_name' => 'required|string|max:255',  // 产品名称，必须是字符串，最大长度 255
            'form.product_title' => 'required|string|max:1000',  // 产品标题，必须是字符串，最大长度 1000
            'form.product_sn' => 'required|string|max:255',  // 产品编号，必须是字符串，最大长度 255
            'form.product_english_title' => 'nullable|string|max:255',  // 英文标题，可为空，最大长度 255
            //
            // 关联外键验证
            'form.copyright_uuids' => 'required|array',  // 所属版权，必须存在于 copyrights 表中
            'form.copyright_uuids.*' => 'required|uuid|exists:copyright_center.copyrights,uuid',  // 所属版权，必须存在于 copyrights 表中
            'form.fabric_ids' => 'required|array|exists:produce_brace.produce_fabrics,id',  // 面料类型，必须存在于 fabrics 表中
            'form.design_ids' => 'required|array|exists:produce_brace.produce_designs,id',  // 设计类型，必须存在于 designs 表中
            'form.trend_ids' => 'required|array|exists:produce_brace.produce_trends,id',  // 趋势类型，必须存在于 trends 表中
            'form.craft_ids' => 'required|array|exists:produce_brace.produce_crafts,id',  // 工艺类型，必须存在于 crafts 表中
            'form.shade_ids' => 'required|array|exists:produce_brace.produce_shades,id',  // 图案类型，必须存在于 shades 表中
            'form.purpose_ids' => 'required|array|exists:produce_brace.produce_purposes,id',  // 目的类型，必须存在于 purposes 表中
            'form.accessory_ids' => 'required|array|exists:produce_brace.produce_accessories,id',  // 配件类型，必须存在于 accessories 表中
            'form.suit_ids' => 'required|array|exists:produce_brace.produce_suits,id',  // 适用型号类型，必须存在于 suits 表中
            'form.spec_id' => 'required|integer|exists:produce_brace.produce_specs,id',  // 规格 ID，必须是有效的 ID

            // SKU 验证
            'form.arr_skus' => ['required', 'array', function ($attribute, $value, $fail) {
                // 自定义验证逻辑，确保 SKU 数据与规格、适用、自定义数据一致
                $this->validateSkusConsistency($fail);
            }], // SKU 必须是数组
            'form.arr_skus.*.suit_id' => 'required|integer|exists:produce_brace.produce_suits,id',  // 每个 SKU 必须有一个有效的 suit_id
            'form.arr_skus.*.spec_id' => 'required|integer|exists:produce_brace.produce_specs,id',  // 每个 SKU 必须有一个有效的 spec_id
            'form.arr_skus.*.custom_id' => 'required|integer|exists:produce_brace.produce_customs,id',  // 每个 SKU 必须有一个有效的 custom_id
            'form.arr_skus.*.sku_sn' => 'required|string|max:255',  // SKU 编号，最大长度 255
            'form.arr_skus.*.sku_image' => 'required|string|max:255',  // SKU 图片 URL，最大长度 255
            'form.arr_skus.*.attributes' => 'required|array',  // 每个 SKU 必须有一个属性数组
            'form.arr_skus.*.attributes.*' => 'required|string|max:255',  // 每个属性必须是字符串，最大长度 255
            'form.arr_skus.*.formats' => 'required|array',  // SKU 格式，必须是数组
            'form.arr_skus.*.formats.*' => 'required|string|max:255',  // 每个格式必须是字符串，最大长度 255
            'form.arr_skus.*.theme_names' => 'required|array',  // 可选的主题名称数组
            'form.arr_skus.*.theme_names.*' => 'required|string|max:255',  // 每个主题名称必须是字符串，最大长度 255
            'form.arr_skus.*.is_offline' => 'required|boolean',  // 是否下线，必须是布尔值
            'form.arr_skus.*.weight' => 'required|numeric|min:0',  // SKU 重量，最小值 0
            'form.arr_skus.*.stock' => 'required|integer|min:0',  // SKU 库存，最小值 0
            'form.arr_skus.*.market_price' => 'required|numeric|min:0',  // 市场价格，最小值 0
            'form.arr_skus.*.price' => 'required|numeric|min:0',  // 售价，最小值 0

            // 关键字
            'form.keywords' => 'required|array',  // 关键字必须是数组
            'form.keywords.*' => 'required|string|max:255',  // 每个关键字必须是字符串，最大长度 255
            //
            'form.arr_suits' => 'required|array',
            'form.arr_suits.*.id' => 'required|integer|exists:produce_brace.produce_suits,id',
            'form.arr_suits.*.value' => 'required|string|max:255',
            //
            'form.arr_specs' => 'required|array',
            'form.arr_specs.*.id' => 'required|integer|exists:produce_brace.produce_specs,id',
            'form.arr_specs.*.value' => 'required|string|max:255',
            'form.arr_specs.*.theme_names' => 'required|array',
            'form.arr_specs.*.theme_names.*' => 'required|string|max:255',
            //
            'form.arr_customs' => 'required|array',
            'form.arr_customs.*.id' => 'required|integer|exists:produce_brace.produce_customs,id',
            'form.arr_customs.*.value' => 'required|string|max:255',
            // 属性验证
            'attributes' => 'required|array',  // 属性必须是数组
            //attributes.suits
            'attributes.suits' => 'required|array',  // 适用型号必须是数组
            'attributes.suits.*.id' => 'required|integer|exists:produce_brace.produce_suits,id',  // 每个型号必须有一个有效的 suit_id
            'attributes.suits.*.uuid' => 'required|uuid|exists:produce_brace.produce_suits,uuid',  // 每个型号必须有一个有效的 suit_uuid
            'attributes.suits.*.name' => 'required|string|max:255',  // 每个型号的名称必须是字符串，最大长度 255
            'attributes.suits.*.value' => 'required|string|max:255',  // 每个型号的值必须是字符串，最大长度 255
            // attributes.specs
            'attributes.specs' => 'required|array',  // 规格必须是数组
            'attributes.specs.*.id' => 'required|integer|exists:produce_brace.produce_specs,id',  // 每个规格必须有一个有效的 spec_id
            'attributes.specs.*.uuid' => 'required|uuid|exists:produce_brace.produce_specs,uuid',  // 每个规格必须有一个有效的 spec_uuid
            'attributes.specs.*.name' => 'required|string|max:255',  // 每个规格的名称必须是字符串，最大长度 255
            'attributes.specs.*.value' => 'max:255',  // 每个规格的值必须是字符串，最大长度 255
            // attributes.customs
            'attributes.customs' => 'required|array',  // 定制选项必须是数组
            'attributes.customs.*.id' => 'required|integer|exists:produce_brace.produce_customs,id',  // 每个定制选项必须有一个有效的 custom_id
            'attributes.customs.*.uuid' => 'required|uuid|exists:produce_brace.produce_customs,uuid',  // 每个定制选项必须有一个有效的 custom_uuid
            'attributes.customs.*.name' => 'required|string|max:255',  // 每个定制选项的名称必须是字符串，最大长度 255
            'attributes.customs.*.value' => 'required|string|max:255',  // 每个定制选项的值必须是字符串，最大长度 255
            // 其他通用规则
        ];
        // 获取请求 IP 地址
        $ip = $this->ip();
        // 如果是 127.0.0.1 请求，则不验证媒体 UUID 相关字段
        if ($ip === '127.0.0.1') {
            //
            $rules = array_merge($rules, [
                // media_picture 验证：必须存在，数组类型，且长度为1
                'form.media_picture' => ['required', 'array', 'size:1'],
                'form.media_picture.*.url' => ['required', 'url'],

                // media_images 验证：必须存在，数组类型，长度介于3到30之间
                'form.media_images' => ['required', 'array', 'min:3', 'max:30'],
                'form.media_images.*.url' => ['required', 'url'],

                // media_descries 验证：必须存在，数组类型，长度介于3到30之间
                'form.media_descries' => ['required', 'array', 'min:3', 'max:30'],
                'form.media_descries.*.url' => ['required', 'url'],

                // media_white 验证：可选，数组类型，长度最多为1
                'form.media_white' => ['sometimes', 'array', 'max:1'],
                'form.media_white.*.url' => ['required_with:form.media_white', 'url'],

                // media_video 验证：可选，数组类型，长度最多为1
                'form.media_video' => ['sometimes', 'array', 'max:1'],
                'form.media_video.*.url' => ['required_with:form.media_video', 'url'],

                // spec_image 验证：可选，数组类型，长度最多为1
                'form.arr_specs.*.spec_image' => ['sometimes', 'array', 'max:1'],
                'form.arr_specs.*.spec_image.*.url' => ['required_with:form.arr_specs.*.spec_image', 'url'],

                // sku_image 验证：可选，数组类型，长度最多为1
                'form.arr_skus.*.sku_image' => ['sometimes', 'array', 'max:1'],
                'form.arr_skus.*.sku_image.*.url' => ['required_with:form.arr_skus.*.sku_image', 'url'],
            ]);
        } else {
            //
            $rules = array_merge($rules, [

                // media_picture 验证：必须存在，数组类型，且长度为1
                'form.media_picture' => ['required', 'array', 'size:1'],
                'form.media_picture.*.uuid' => ['required', 'uuid', 'exists:media_center.media,uuid'],
                'form.media_picture.*.url' => ['required', 'url'],

                // media_images 验证：必须存在，数组类型，长度介于3到40之间
                'form.media_images' => ['required', 'array', 'min:3', 'max:40'],
                'form.media_images.*.uuid' => ['required', 'uuid', 'exists:media_center.media,uuid'],
                'form.media_images.*.url' => ['required', 'url'],

                // media_descries 验证：必须存在，数组类型，长度介于3到40之间
                'form.media_descries' => ['required', 'array', 'min:3', 'max:40'],
                'form.media_descries.*.uuid' => ['required', 'uuid', 'exists:media_center.media,uuid'],
                'form.media_descries.*.url' => ['required', 'url'],

                // media_white 验证：可选，数组类型，长度最多为1
                'form.media_white' => ['sometimes', 'array', 'max:1'],
                'form.media_white.*.uuid' => ['required_with:form.media_white', 'uuid', 'exists:media_center.media,uuid'],
                'form.media_white.*.url' => ['required_with:form.media_white', 'url'],

                // media_video 验证：可选，数组类型，长度最多为1
                'form.media_video' => ['sometimes', 'array', 'max:1'],
                'form.media_video.*.uuid' => ['required_with:form.media_video', 'uuid', 'exists:media_center.media,uuid'],
                'form.media_video.*.url' => ['required_with:form.media_video', 'url'],

                // spec_image 验证：可选，数组类型，长度最多为1
                'form.arr_specs.*.spec_image' => ['sometimes', 'array', 'max:1'],
                'form.arr_specs.*.spec_image.*.uuid' => ['required_with:form.arr_specs.*.spec_image', 'uuid', 'exists:media_center.media,uuid'],
                'form.arr_specs.*.spec_image.*.url' => ['required_with:form.arr_specs.*.spec_image', 'url'],

                // sku_image 验证：可选，数组类型，长度最多为1
                'form.arr_skus.*.sku_image' => ['sometimes', 'array', 'max:1'],
                'form.arr_skus.*.sku_image.*.uuid' => ['required_with:form.arr_skus.*.sku_image', 'uuid', 'exists:media_center.media,uuid'],
                'form.arr_skus.*.sku_image.*.url' => ['required_with:form.arr_skus.*.sku_image', 'url'],
            ]);
        }
        // 如果是更新操作，可以添加或修改规则
        if ($isUpdate) {
            //
            // 获取当前路由中传递的 UUID 参数
            $uuid = $this->route('product_draft');
            // 将 UUID 验证规则动态添加到规则中
            if ($uuid) {
                // 将 UUID 添加到请求数据中，方便在验证规则中使用
                $this->merge(['uuid' => $uuid]);
                //
                $rules = array_merge($rules, [
                    'uuid' => 'required|uuid|exists:product_draft.product_drafts,uuid', // 验证是否存在于 `product_draft.product_drafts` 表中
                ]);
            }
            //
            $check = $this->input('form.check'); // 获取 `check` 字段的值
            $edit_type_key = $this->input('form.edit_type_key'); // 获取 `check` 字段的值
            //
            $updateRules = [
                'form.edit_type_key' => ['required', new EnumRule(ProductEditTypeEnum::class)], // 枚举类型验证
                'form.check' => 'required|string', // 审核状态必须有值
            ];
            //
            // 根据 `check` 的值动态设置验证规则
            if ($check === 'reject') {
                //
                $updateRules = array_merge($updateRules, [
                    'form.audit_content' => 'required|string|max:255',  // 审核内容在拒绝时为必填
                    'form.audit_remark' => 'required|string|max:255',  // 审核备注在拒绝时为必填
                    'form.reason_ids' => 'required|array',           // 拒绝时原因必填
                    'form.reason_ids.*' => 'required|integer|exists:reason_brace.reasons,id',
                ]);
            } elseif ($check === 'approve') {
                //
                if ($edit_type_key === ProductEditTypeEnum::new_product_release()->key()) {
                    //
                    $updateRules = array_merge($updateRules, [
                        'form.describe_level' => 'required|integer|min:10|max:100', // 通过审核时这些字段必填
                        'form.quality_level' => 'required|integer|min:10|max:100',
                        'form.property_id' => 'required|integer|exists:produce_brace.produce_properties,id',
                    ]);
                }
            }
            //
            $rules = array_merge($rules, $updateRules);
            // 例如，某些字段在更新时可能不是必须的，可以在这里调整
            // $rules['form.some_field'] = ['sometimes', 'rule'];
        }
        //
        return $rules;
    }

    /**
     * 自定义方法，用于验证 SKU 数据与规格、适用、自定义数据的一致性
     * @param callable $fail 验证失败时的回调函数
     */
    protected function validateSkusConsistency(callable $fail)
    {
        // 将输入数据转换为集合，方便后续的查找操作
        $arr_skus = collect($this->input('arr_skus'));
        $arr_specs = collect($this->input('arr_specs'));
        $arr_suits = collect($this->input('arr_suits'));
        $arr_customs = collect($this->input('arr_customs', [])); // 自定义属性可以为空

        // 遍历所有 SKU 进行一致性检查
        $arr_skus->each(function ($sku, $index) use ($arr_specs, $arr_suits, $arr_customs, $fail) {
            // 验证 spec_id 是否有效
            $specAttr = $arr_specs->firstWhere('id', $sku['spec_id']);
            if (!$specAttr) {
                return $fail(trans('validation.invalid_spec_id', ['index' => $index, 'spec_id' => $sku['spec_id']]));
            }

            // 验证 suit_id 是否有效
            $suitAttr = $arr_suits->firstWhere('id', $sku['suit_id']);
            if (!$suitAttr) {
                return $fail(trans('validation.invalid_suit_id', ['index' => $index, 'suit_id' => $sku['suit_id']]));
            }

            // 验证 custom_id 是否有效（如果存在）
            if (isset($sku['custom_id'])) {
                //
                $customAttr = $arr_customs->firstWhere('id', $sku['custom_id']);
                //
                if (!$customAttr) {
                    //
                    return $fail(trans('validation.invalid_custom_id', ['index' => $index, 'custom_id' => $sku['custom_id']]));
                }
            }
            // 一致性检查通过，其他逻辑可以在此添加...
        });
    }

    /**
     * 获取已定义验证规则的错误消息。
     * @return array
     */
    public function messages()
    {
        // 自定义验证失败时的错误消息
        return [
            // 基本产品信息
            'form.store_uuid.required' => '店铺不能为空。',
            'form.store_uuid.uuid' => '店铺格式不正确。',
            'form.store_uuid.exists' => '所选店铺不存在。',
            'form.schema_uuid.required' => '生产模式不能为空。',
            'form.schema_uuid.uuid' => '生产模式格式不正确。',
            'form.schema_uuid.exists' => '所选生产模式不存在。',
            'form.product_name.required' => '产品名称不能为空。',
            'form.product_name.string' => '产品名称必须是字符串。',
            'form.product_name.max' => '产品名称不能超过 255 个字符。',
            'form.product_title.required' => '产品标题不能为空。',
            'form.product_title.string' => '产品标题必须是字符串。',
            'form.product_title.max' => '产品标题不能超过 1000 个字符。',
            'form.product_sn.required' => '产品编号不能为空。',
            'form.product_sn.string' => '产品编号必须是字符串。',
            'form.product_sn.max' => '产品编号不能超过 255 个字符。',
            'form.product_english_title.string' => '产品英文标题必须是字符串。',
            'form.product_english_title.max' => '产品英文标题不能超过 255 个字符。',

            // 媒体相关
            'form.media_picture_url.required' => '主图不能为空。',
            'form.media_picture_url.url' => '主图格式不正确。',
            'form.media_images.required' => '媒体图片数组不能为空。',
            'form.media_images.array' => '媒体图片必须是数组。',
            'form.media_images.*.required' => '每个媒体图片不能为空。',
            'form.media_images.*.url' => '媒体图片格式不正确。',
            'form.media_descries.required' => '媒体描述数组不能为空。',
            'form.media_descries.array' => '媒体描述必须是数组。',
            'form.media_descries.*.required' => '每个媒体描述不能为空。',
            'form.media_descries.*.url' => '媒体描述格式不正确。',
            'form.media_white.url' => '白色背景图片格式不正确。',
            'form.media_video.url' => '视频格式不正确。',

            // 媒体 UUIDs 验证
            'form.media_image_uuid.required' => '主图不能为空。',
            'form.media_image_uuid.uuid' => '主图格式不正确。',
            'form.media_image_uuid.exists' => '主图不存在。',
            'form.media_image_uuids.required' => '媒体图片数组不能为空。',
            'form.media_image_uuids.array' => '媒体图片必须是数组。',
            'form.media_image_uuids.*.required' => '每个媒体图片不能为空。',
            'form.media_image_uuids.*.uuid' => '媒体图片格式不正确。',
            'form.media_image_uuids.*.exists' => '媒体图片不存在。',
            'form.media_desc_uuids.required' => '媒体描述数组不能为空。',
            'form.media_desc_uuids.array' => '媒体描述必须是数组。',
            'form.media_desc_uuids.*.required' => '每个媒体描述不能为空。',
            'form.media_desc_uuids.*.uuid' => '媒体描述格式不正确。',
            'form.media_desc_uuids.*.exists' => '媒体描述不存在。',
            'form.media_white_uuid.uuid' => '白色背景图片格式不正确。',
            'form.media_white_uuid.exists' => '白色背景图片不存在。',
            'form.media_video_uuid.uuid' => '视频格式不正确。',
            'form.media_video_uuid.exists' => '视频不存在。',

            // 关联外键验证
            'form.fabric_ids.required' => '面料类型不能为空。',
            'form.fabric_ids.array' => '面料必须是数组。',
            'form.fabric_ids.exists' => '所选面料不存在。',
            'form.design_ids.required' => '设计类型不能为空。',
            'form.design_ids.array' => '设计必须是数组。',
            'form.design_ids.exists' => '所选设计不存在。',
            'form.trend_ids.required' => '趋势类型不能为空。',
            'form.trend_ids.array' => '趋势必须是数组。',
            'form.trend_ids.exists' => '所选趋势不存在。',
            'form.craft_ids.required' => '工艺类型不能为空。',
            'form.craft_ids.array' => '工艺必须是数组。',
            'form.craft_ids.exists' => '所选工艺不存在。',
            'form.shade_ids.required' => '图案类型不能为空。',
            'form.shade_ids.array' => '图案必须是数组。',
            'form.shade_ids.exists' => '所选图案不存在。',
            'form.purpose_ids.required' => '目的类型不能为空。',
            'form.purpose_ids.array' => '目的必须是数组。',
            'form.purpose_ids.exists' => '所选目的不存在。',
            'form.accessory_ids.required' => '配件类型不能为空。',
            'form.accessory_ids.array' => '配件必须是数组。',
            'form.accessory_ids.exists' => '所选配件不存在。',
            'form.suit_ids.required' => '适用型号类型不能为空。',
            'form.suit_ids.array' => '适用型号必须是数组。',
            'form.suit_ids.exists' => '所选适用型号不存在。',

            'form.spec_id.required' => '规格不能为空。',
            'form.spec_id.integer' => '规格必须是整数。',
            'form.spec_id.exists' => '所选规格不存在。',

            // SKU 验证
            'form.arr_skus.required' => 'SKU 数组不能为空。',
            'form.arr_skus.array' => 'SKU 必须是数组。',
            'form.arr_skus.*.suit_id.required' => 'SKU 的适用型号不能为空。',
            'form.arr_skus.*.suit_id.integer' => 'SKU 的适用型号必须是整数。',
            'form.arr_skus.*.suit_id.exists' => 'SKU 的适用型号不存在。',
            'form.arr_skus.*.spec_id.required' => 'SKU 的规格不能为空。',
            'form.arr_skus.*.spec_id.integer' => 'SKU 的规格必须是整数。',
            'form.arr_skus.*.spec_id.exists' => 'SKU 的规格不存在。',
            'form.arr_skus.*.custom_id.required' => 'SKU 的自定义选项不能为空。',
            'form.arr_skus.*.custom_id.integer' => 'SKU 的自定义选项必须是整数。',
            'form.arr_skus.*.custom_id.exists' => 'SKU 的自定义选项不存在。',
            'form.arr_skus.*.sku_sn.required' => 'SKU 编号不能为空。',
            'form.arr_skus.*.sku_sn.string' => 'SKU 编号必须是字符串。',
            'form.arr_skus.*.sku_sn.max' => 'SKU 编号不能超过 255 个字符。',
            'form.arr_skus.*.sku_image.required' => 'SKU 图片不能为空。',
            'form.arr_skus.*.sku_image.string' => 'SKU 图片必须是字符串。',
            'form.arr_skus.*.sku_image.max' => 'SKU 图片不能超过 255 个字符。',
            'form.arr_skus.*.attributes.required' => 'SKU 属性数组不能为空。',
            'form.arr_skus.*.attributes.array' => 'SKU 属性必须是数组。',
            'form.arr_skus.*.attributes.*.required' => 'SKU 属性值不能为空。',
            'form.arr_skus.*.attributes.*.string' => 'SKU 属性值必须是字符串。',
            'form.arr_skus.*.attributes.*.max' => 'SKU 属性值不能超过 255 个字符。',
            'form.arr_skus.*.formats.required' => 'SKU 格式数组不能为空。',
            'form.arr_skus.*.formats.array' => 'SKU 格式必须是数组。',
            'form.arr_skus.*.formats.*.required' => 'SKU 格式值不能为空。',
            'form.arr_skus.*.formats.*.string' => 'SKU 格式值必须是字符串。',
            'form.arr_skus.*.formats.*.max' => 'SKU 格式值不能超过 255 个字符。',
            'form.arr_skus.*.theme_names.required' => 'SKU 主题名称数组不能为空。',
            'form.arr_skus.*.theme_names.array' => 'SKU 主题名称必须是数组。',
            'form.arr_skus.*.theme_names.*.required' => 'SKU 主题名称值不能为空。',
            'form.arr_skus.*.theme_names.*.string' => 'SKU 主题名称值必须是字符串。',
            'form.arr_skus.*.theme_names.*.max' => 'SKU 主题名称值不能超过 255 个字符。',
            'form.arr_skus.*.sku_image_uuid.required' => 'SKU 图片不能为空。',
            'form.arr_skus.*.sku_image_uuid.uuid' => 'SKU 图片格式不正确。',
            'form.arr_skus.*.sku_image_uuid.exists' => 'SKU 图片不存在。',
            'form.arr_skus.*.is_offline.required' => 'SKU 的上下线状态不能为空。',
            'form.arr_skus.*.is_offline.boolean' => 'SKU 的上下线状态必须是布尔值。',
            'form.arr_skus.*.weight.required' => 'SKU 重量不能为空。',
            'form.arr_skus.*.weight.numeric' => 'SKU 重量必须是数字。',
            'form.arr_skus.*.weight.min' => 'SKU 重量不能小于 0。',
            'form.arr_skus.*.stock.required' => 'SKU 库存不能为空。',
            'form.arr_skus.*.stock.integer' => 'SKU 库存必须是整数。',
            'form.arr_skus.*.stock.min' => 'SKU 库存不能小于 0。',
            'form.arr_skus.*.market_price.required' => 'SKU 市场价格不能为空。',
            'form.arr_skus.*.market_price.numeric' => 'SKU 市场价格必须是数字。',
            'form.arr_skus.*.market_price.min' => 'SKU 市场价格不能小于 0。',
            'form.arr_skus.*.price.required' => 'SKU 价格不能为空。',
            'form.arr_skus.*.price.numeric' => 'SKU 价格必须是数字。',
            'form.arr_skus.*.price.min' => 'SKU 价格不能小于 0。',

            // 关键字
            'form.keywords.required' => '关键字数组不能为空。',
            'form.keywords.array' => '关键字必须是数组。',
            'form.keywords.*.required' => '关键字不能为空。',
            'form.keywords.*.string' => '关键字必须是字符串。',
            'form.keywords.*.max' => '关键字不能超过 255 个字符。',

            // 套装
            'form.arr_suits.required' => '套装数组不能为空。',
            'form.arr_suits.array' => '套装必须是数组。',
            'form.arr_suits.*.id.required' => '套装不能为空。',
            'form.arr_suits.*.id.integer' => '套装必须是整数。',
            'form.arr_suits.*.id.exists' => '套装不存在。',
            'form.arr_suits.*.value.required' => '套装值不能为空。',
            'form.arr_suits.*.value.string' => '套装值必须是字符串。',
            'form.arr_suits.*.value.max' => '套装值不能超过 255 个字符。',

            // 规格
            'form.arr_specs.required' => '规格数组不能为空。',
            'form.arr_specs.array' => '规格必须是数组。',
            'form.arr_specs.*.id.required' => '规格不能为空。',
            'form.arr_specs.*.id.integer' => '规格必须是整数。',
            'form.arr_specs.*.id.exists' => '规格不存在。',
            'form.arr_specs.*.value.required' => '规格值不能为空。',
            'form.arr_specs.*.value.string' => '规格值必须是字符串。',
            'form.arr_specs.*.value.max' => '规格值不能超过 255 个字符。',
            'form.arr_specs.*.theme_names.required' => '规格主题名称数组不能为空。',
            'form.arr_specs.*.theme_names.array' => '规格主题名称必须是数组。',
            'form.arr_specs.*.theme_names.*.required' => '规格主题名称值不能为空。',
            'form.arr_specs.*.theme_names.*.string' => '规格主题名称值必须是字符串。',
            'form.arr_specs.*.theme_names.*.max' => '规格主题名称值不能超过 255 个字符。',
            'form.arr_specs.*.spec_image_uuid.required' => '规格图片不能为空。',
            'form.arr_specs.*.spec_image_uuid.uuid' => '规格图片格式不正确。',
            'form.arr_specs.*.spec_image_uuid.exists' => '规格图片不存在。',

            // 自定义选项
            'form.arr_customs.required' => '自定义选项数组不能为空。',
            'form.arr_customs.array' => '自定义选项必须是数组。',
            'form.arr_customs.*.id.required' => '自定义选项不能为空。',
            'form.arr_customs.*.id.integer' => '自定义选项必须是整数。',
            'form.arr_customs.*.id.exists' => '自定义选项不存在。',
            'form.arr_customs.*.value.required' => '自定义选项值不能为空。',
            'form.arr_customs.*.value.string' => '自定义选项值必须是字符串。',
            'form.arr_customs.*.value.max' => '自定义选项值不能超过 255 个字符。',

            // 属性验证
            'attributes.required' => '属性数组不能为空。',
            'attributes.array' => '属性必须是数组。',

            // 属性 - 适用型号
            'attributes.suits.required' => '适用型号属性数组不能为空。',
            'attributes.suits.array' => '适用型号属性必须是数组。',
            'attributes.suits.*.id.required' => '适用型号属性不能为空。',
            'attributes.suits.*.id.integer' => '适用型号属性必须是整数。',
            'attributes.suits.*.id.exists' => '适用型号属性不存在。',
            'attributes.suits.*.uuid.required' => '适用型号属性不能为空。',
            'attributes.suits.*.uuid.uuid' => '适用型号属性格式不正确。',
            'attributes.suits.*.uuid.exists' => '适用型号属性不存在。',
            'attributes.suits.*.name.required' => '适用型号属性名称不能为空。',
            'attributes.suits.*.name.string' => '适用型号属性名称必须是字符串。',
            'attributes.suits.*.name.max' => '适用型号属性名称不能超过 255 个字符。',
            'attributes.suits.*.value.required' => '适用型号属性值不能为空。',
            'attributes.suits.*.value.string' => '适用型号属性值必须是字符串。',
            'attributes.suits.*.value.max' => '适用型号属性值不能超过 255 个字符。',

            // 属性 - 规格
            'attributes.specs.required' => '规格属性数组不能为空。',
            'attributes.specs.array' => '规格属性必须是数组。',
            'attributes.specs.*.id.required' => '规格属性不能为空。',
            'attributes.specs.*.id.integer' => '规格属性必须是整数。',
            'attributes.specs.*.id.exists' => '规格属性不存在。',
            'attributes.specs.*.uuid.required' => '规格属性不能为空。',
            'attributes.specs.*.uuid.uuid' => '规格属性格式不正确。',
            'attributes.specs.*.uuid.exists' => '规格属性不存在。',
            'attributes.specs.*.name.required' => '规格属性名称不能为空。',
            'attributes.specs.*.name.string' => '规格属性名称必须是字符串。',
            'attributes.specs.*.name.max' => '规格属性名称不能超过 255 个字符。',
            'attributes.specs.*.value.max' => '规格属性值不能超过 255 个字符。',

            // 属性 - 自定义选项
            'attributes.customs.required' => '自定义属性数组不能为空。',
            'attributes.customs.array' => '自定义属性必须是数组。',
            'attributes.customs.*.id.required' => '自定义属性不能为空。',
            'attributes.customs.*.id.integer' => '自定义属性必须是整数。',
            'attributes.customs.*.id.exists' => '自定义属性不存在。',
            'attributes.customs.*.uuid.required' => '自定义属性不能为空。',
            'attributes.customs.*.uuid.uuid' => '自定义属性格式不正确。',
            'attributes.customs.*.uuid.exists' => '自定义属性不存在。',
            'attributes.customs.*.name.required' => '自定义属性名称不能为空。',
            'attributes.customs.*.name.string' => '自定义属性名称必须是字符串。',
            'attributes.customs.*.name.max' => '自定义属性名称不能超过 255 个字符。',
            'attributes.customs.*.value.required' => '自定义属性值不能为空。',
            'attributes.customs.*.value.string' => '自定义属性值必须是字符串。',
            'attributes.customs.*.value.max' => '自定义属性值不能超过 255 个字符。',
            //
            // 基本产品信息
            'form.describe_level.required' => '审核通过时，描述评分为必填项。',
            'form.quality_level.required' => '审核通过时，质量评分为必填项。',
            'form.property_level.required' => '审核通过时，属性评分为必填项。',
            //
            'form.edit_type_key.required' => '编辑类型不能为空。',
            'form.edit_type_key.enum' => '编辑类型值不正确。',
            'form.check.required' => '审核状态不能为空。',
            //
            'form.describe_level.integer' => '描述评分必须是整数。',
            'form.describe_level.min' => '描述评分不能低于 10。',
            'form.describe_level.max' => '描述评分不能高于 100。',
            'form.quality_level.integer' => '质量评分必须是整数。',
            'form.quality_level.min' => '质量评分不能低于 10。',
            'form.quality_level.max' => '质量评分不能高于 100。',
            'form.property_level.integer' => '属性评分必须是整数。',
            'form.property_level.min' => '属性评分不能低于 10。',
            'form.property_level.max' => '属性评分不能高于 100。',
            //
            'form.reason_ids.required' => '审核拒绝时，原因列表为必填项。',
            'form.reason_ids.array' => '原因必须是数组。',
            'form.reason_ids.*.id.exists' => '所选原因不存在。',
            'form.reason_ids.*.id.integer' => '原因必须是整数。',
            //
            'form.audit_content.required' => '审核内容为必填项。',
            'form.audit_content.string' => '审核内容必须是字符串。',
            'form.audit_content.max' => '审核内容不能超过 255 个字符。',
            'form.audit_remark.required' => '审核备注为必填项。',
            'form.audit_remark.string' => '审核备注必须是字符串。',
            'form.audit_remark.max' => '审核备注不能超过 255 个字符。',
        ];
    }

    /**
     * 获取验证数据的自定义属性名称。
     * @return array
     */
    public function attributes()
    {
        // 可以在这里定义属性的中文名称，用于错误消息中
        return [
            // 基本产品信息
            'form.store_uuid' => '店铺 UUID',
            'form.schema_uuid' => '生产模式 UUID',
            'form.product_name' => '产品名称',
            'form.product_title' => '产品标题',
            'form.product_sn' => '产品编号',
            'form.product_english_title' => '产品英文标题',

            // media_picture 错误消息
            'form.media_picture.required' => '请上传主图。',
            'form.media_picture.array' => '主图必须是一个数组。',
            'form.media_picture.size' => '主图必须包含1张图片。',
            'form.media_picture.*.uuid.required' => '主图的UUID为必填项。',
            'form.media_picture.*.uuid.uuid' => '主图的UUID无效。',
            'form.media_picture.*.uuid.exists' => '主图的UUID不存在。',
            'form.media_picture.*.url.required' => '主图的URL为必填项。',
            'form.media_picture.*.url.url' => '主图的URL无效。',

            // media_images 错误消息
            'form.media_images.required' => '请上传产品图片。',
            'form.media_images.array' => '产品图片必须是一个数组。',
            'form.media_images.min' => '至少需要上传3张产品图片。',
            'form.media_images.max' => '产品图片最多上传30张。',
            'form.media_images.*.uuid.required' => '产品图片的UUID为必填项。',
            'form.media_images.*.uuid.uuid' => '产品图片的UUID无效。',
            'form.media_images.*.uuid.exists' => '产品图片的UUID不存在。',
            'form.media_images.*.url.required' => '产品图片的URL为必填项。',
            'form.media_images.*.url.url' => '产品图片的URL无效。',

            // media_descries 错误消息
            'form.media_descries.required' => '请上传产品详情图。',
            'form.media_descries.array' => '产品详情图必须是一个数组。',
            'form.media_descries.min' => '至少需要上传3张产品详情图。',
            'form.media_descries.max' => '产品详情图最多上传30张。',
            'form.media_descries.*.uuid.required' => '产品详情图的UUID为必填项。',
            'form.media_descries.*.uuid.uuid' => '产品详情图的UUID无效。',
            'form.media_descries.*.uuid.exists' => '产品详情图的UUID不存在。',
            'form.media_descries.*.url.required' => '产品详情图的URL为必填项。',
            'form.media_descries.*.url.url' => '产品详情图的URL无效。',

            // media_white 错误消息
            'form.media_white.array' => '白底图必须是一个数组。',
            'form.media_white.max' => '白底图最多上传1张。',
            'form.media_white.*.uuid.required_with' => '白底图的UUID为必填项。',
            'form.media_white.*.uuid.uuid' => '白底图的UUID无效。',
            'form.media_white.*.uuid.exists' => '白底图的UUID不存在。',
            'form.media_white.*.url.required_with' => '白底图的URL为必填项。',
            'form.media_white.*.url.url' => '白底图的URL无效。',

            // media_video 错误消息
            'form.media_video.array' => '视频必须是一个数组。',
            'form.media_video.max' => '视频最多上传1个。',
            'form.media_video.*.uuid.required_with' => '视频的UUID为必填项。',
            'form.media_video.*.uuid.uuid' => '视频的UUID无效。',
            'form.media_video.*.uuid.exists' => '视频的UUID不存在。',
            'form.media_video.*.url.required_with' => '视频的URL为必填项。',
            'form.media_video.*.url.url' => '视频的URL无效。',

            // 关联外键验证
            'form.fabric_ids' => '面料类型',
            'form.design_ids' => '设计类型',
            'form.trend_ids' => '趋势类型',
            'form.craft_ids' => '工艺类型',
            'form.shade_ids' => '图案类型',
            'form.purpose_ids' => '目的类型',
            'form.accessory_ids' => '配件类型',
            'form.suit_ids' => '适用型号类型',
            'form.spec_id' => '规格 ID',
            // SKU 验证
            'form.arr_skus' => 'SKU 数组',
            'form.arr_skus.*.suit_id' => 'SKU 适用型号 ID',
            'form.arr_skus.*.spec_id' => 'SKU 规格 ID',
            'form.arr_skus.*.custom_id' => 'SKU 自定义选项 ID',
            'form.arr_skus.*.sku_sn' => 'SKU 编号',
            'form.arr_skus.*.sku_image' => 'SKU 图片 URL',
            'form.arr_skus.*.attributes' => 'SKU 属性数组',
            'form.arr_skus.*.attributes.*' => 'SKU 属性值',
            'form.arr_skus.*.formats' => 'SKU 格式数组',
            'form.arr_skus.*.formats.*' => 'SKU 格式值',
            'form.arr_skus.*.theme_names' => 'SKU 主题名称数组',
            'form.arr_skus.*.theme_names.*' => 'SKU 主题名称值',
            'form.arr_skus.*.sku_image_uuid' => 'SKU 图片 UUID',
            'form.arr_skus.*.is_offline' => 'SKU 上下线状态',
            'form.arr_skus.*.weight' => 'SKU 重量',
            'form.arr_skus.*.stock' => 'SKU 库存',
            'form.arr_skus.*.market_price' => 'SKU 市场价格',
            'form.arr_skus.*.price' => 'SKU 价格',
            // 关键字
            'form.keywords' => '关键字数组',
            'form.keywords.*' => '关键字',
            // 套装
            'form.arr_suits' => '套装数组',
            'form.arr_suits.*.id' => '套装 ID',
            'form.arr_suits.*.value' => '套装值',
            // 规格
            'form.arr_specs' => '规格数组',
            'form.arr_specs.*.id' => '规格 ID',
            'form.arr_specs.*.value' => '规格值',
            'form.arr_specs.*.theme_names' => '规格主题名称数组',
            'form.arr_specs.*.theme_names.*' => '规格主题名称值',
            'form.arr_specs.*.spec_image_uuid' => '规格图片 UUID',
            // 自定义选项
            'form.arr_customs' => '自定义选项数组',
            'form.arr_customs.*.id' => '自定义选项 ID',
            'form.arr_customs.*.value' => '自定义选项值',
            // 属性验证
            'attributes' => '属性数组',
            // 属性 - 适用型号
            'attributes.suits' => '适用型号属性数组',
            'attributes.suits.*.id' => '适用型号属性 ID',
            'attributes.suits.*.uuid' => '适用型号属性 UUID',
            'attributes.suits.*.name' => '适用型号属性名称',
            'attributes.suits.*.value' => '适用型号属性值',
            // 属性 - 规格
            'attributes.specs' => '规格属性数组',
            'attributes.specs.*.id' => '规格属性 ID',
            'attributes.specs.*.uuid' => '规格属性 UUID',
            'attributes.specs.*.name' => '规格属性名称',
            'attributes.specs.*.value' => '规格属性值',
            // 属性 - 自定义选项
            'attributes.customs' => '自定义属性数组',
            'attributes.customs.*.id' => '自定义属性 ID',
            'attributes.customs.*.uuid' => '自定义属性 UUID',
            'attributes.customs.*.name' => '自定义属性名称',
            'attributes.customs.*.value' => '自定义属性值',
            //
            'form.edit_type_key' => '编辑类型',
            'form.check' => '审核状态',
            'form.describe_level' => '描述评分',
            'form.quality_level' => '质量评分',
            'form.property_level' => '属性评分',
            'form.audit_content' => '审核内容',
            'form.audit_remark' => '审核备注',
            'form.reason_ids' => '原因类型',
            'form.reason_ids.*.id' => '原因 ID',
            //
        ];
    }
}
