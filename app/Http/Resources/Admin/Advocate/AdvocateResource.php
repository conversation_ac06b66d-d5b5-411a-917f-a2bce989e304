<?php

namespace App\Http\Resources\Admin\Advocate;

use App\Http\Resources\BaseResource;
use App\Services\BaseService;
use Illuminate\Http\Request;

class AdvocateResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'mobile' => $this->mobile ?? null,
            'email' => $this->email ?? null,
            'roles_count' => $this->roles_count ?? null,
            'permissions_count' => $this->permissions_count ?? null,
            'name' => $this->name ?? null,
            'nickname' => $this->nickname ?? null,
            'advocate_name' => $this->advocate_name ?? null,
            //
            'created_time' => isset($this->created_at) ? BaseService::serializeDate($this->created_at) : null,
            'updated_time' => isset($this->updated_at) ? BaseService::serializeDate($this->updated_at) : null,
        ]);
    }
}
