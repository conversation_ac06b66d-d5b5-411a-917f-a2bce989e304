<?php

namespace App\Http\Resources\Admin\Advocate;

use App\Http\Resources\BaseResource;
use App\Services\BaseService;
use Illuminate\Http\Request;

class AdvocateRoleResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'display_name' => $this->display_name ?? null,
            'description' => $this->description ?? null,
            //
            'created_time' => isset($this->created_at) ? BaseService::serializeDate($this->created_at) : null,
            'updated_time' => isset($this->updated_at) ? BaseService::serializeDate($this->updated_at) : null,
        ]);
    }
}
