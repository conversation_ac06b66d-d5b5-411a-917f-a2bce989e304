<?php

namespace App\Http\Resources\Admin\Copyright;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CopyrightAgentResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'agent_name' => $this->agent_name ?? null,
            'agent_english_name' => $this->agent_english_name ?? null,
            'agent_description' => $this->agent_description ?? null,
            //
            'sort' => $this->sort ?? null,
        ]);
    }
}
