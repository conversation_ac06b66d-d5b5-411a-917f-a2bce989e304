<?php

namespace App\Http\Resources\Admin\Copyright;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CopyrightHolderResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'holder_name' => $this->holder_name ?? null,
            'holder_english_name' => $this->holder_english_name ?? null,
            'holder_english_value' => $this->holder_english_value ?? null,
            'holder_description' => $this->holder_description ?? null,
            //
            'sort' => $this->sort ?? null,
        ]);
    }
}
