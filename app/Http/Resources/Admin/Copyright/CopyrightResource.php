<?php

namespace App\Http\Resources\Admin\Copyright;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CopyrightResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            'property_uuid' => $this->property->uuid ?? null,
            'agent_uuid' => $this->agent->uuid ?? null,
            'holder_uuid' => $this->holder->uuid ?? null,
            //
            'property_name' => $this->property->property_name ?? null,
            'agent_name' => $this->agent->agent_name ?? null,
            'holder_name' => $this->holder->holder_name ?? null,
            //
            'copyright_name' => $this->copyright_name ?? null,
            'copyright_english_name' => $this->copyright_english_name ?? null,
            'copyright_description' => $this->copyright_description ?? null,
            //
            'registration_number' => $this->registration_number ?? null,
            'registration_detail' => $this->registration_detail ?? null,
            //
            'copyright_agent' => $this->copyright_agent ?? null,
            'copyright_notices' => $this->copyright_notices ?? null,
            //
            'country_origin' => (string)$this->country_origin ?? null,
            'dispute_type' => (string)$this->dispute_type ?? null,
            'dispute_label' => $this->dispute_type->label ?? null,
            'dispute_color' => $this->dispute_type->color() ?? null,
            //
            'related_awards' => $this->related_awards ?? null,
            //
            'sort' => $this->sort ?? null,
        ];
    }
}
