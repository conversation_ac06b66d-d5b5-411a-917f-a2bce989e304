<?php

namespace App\Http\Resources\Admin\Media;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class MediaResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'url' => $this->original_url ?? null,
            'original_url' => $this->original_url ?? null,
        ]);
    }
}
