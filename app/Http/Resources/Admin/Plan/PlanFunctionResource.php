<?php

namespace App\Http\Resources\Admin\Plan;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class PlanFunctionResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            'uuid' => $this->uuid ?? null,
            'function_name' => $this->function_name ?? null,
            'function_description' => $this->function_description ?? null,
            'function_price' => $this->function_price ?? null,
        ]);
    }
}
