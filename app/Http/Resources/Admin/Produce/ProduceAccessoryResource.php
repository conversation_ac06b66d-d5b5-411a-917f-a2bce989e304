<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceAccessoryResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'accessory_no' => $this->accessory_no,
            'accessory_name' => $this->accessory_name,
            'accessory_english_name' => $this->accessory_english_name,
            'alias_names' => $this->alias_names,
            'accessory_value' => $this->accessory_value,
            'accessory_english_value' => $this->accessory_english_value,
            'accessory_thumb' => $this->accessory_thumb,
            'accessory_description' => $this->accessory_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
