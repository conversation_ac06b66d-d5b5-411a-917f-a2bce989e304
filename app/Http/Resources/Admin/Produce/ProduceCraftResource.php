<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceCraftResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'craft_no' => $this->craft_no,
            'craft_name' => $this->craft_name,
            'craft_english_name' => $this->craft_english_name,
            'alias_names' => $this->alias_names,
            'craft_value' => $this->craft_value,
            'craft_english_value' => $this->craft_english_value,
            'craft_thumb' => $this->craft_thumb,
            'craft_description' => $this->craft_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
