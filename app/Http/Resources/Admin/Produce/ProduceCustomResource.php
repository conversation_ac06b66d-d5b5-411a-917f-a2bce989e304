<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use App\Models\ProduceBrace\ProduceCustom;
use Illuminate\Http\Request;

class ProduceCustomResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'sort' => $this->sort ?? null,
            'custom_name' => $this->custom_name ?? null,
            'custom_english_name' => $this->custom_english_name ?? null,
            'custom_description' => $this->custom_description ?? null,
            'custom_type' => $this->custom_type ?? null,
            'value' => $this->custom_name ?? null,
            '_lft' => $this->_lft ?? null,
            '_rgt' => $this->_rgt ?? null,
            'children' => ProduceCustom::select(['id', 'custom_name', 'parent_id', '_lft', '_rgt'])->descendantsOf($this->id)->toTree() ?? null,
        ]);
    }
}
