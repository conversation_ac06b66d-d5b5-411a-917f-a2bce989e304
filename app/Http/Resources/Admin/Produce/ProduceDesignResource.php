<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceDesignResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid,
            'platform_id' => $this->platform_id,
            'site_id' => $this->site_id,
            'category_id' => $this->category_id,
            'group_id' => $this->group_id,
            'design_no' => $this->design_no,
            'design_name' => $this->design_name,
            'design_english_name' => $this->design_english_name,
            'alias_names' => $this->alias_names,
            'design_value' => $this->design_value,
            'design_english_value' => $this->design_english_value,
            'design_thumb' => $this->design_thumb,
            'design_description' => $this->design_description,
            'deep' => $this->deep,
            'sort' => $this->sort,
            'is_thumb' => $this->is_thumb,
        ]);
    }
}
