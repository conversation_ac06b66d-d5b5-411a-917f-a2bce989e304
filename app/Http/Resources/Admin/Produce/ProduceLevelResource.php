<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceLevelResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            'uuid' => $this->uuid ?? null,
            'sort' => $this->sort ?? null,
            'level_name' => $this->level_name ?? null,
            'level_english_name' => $this->level_english_name ?? null,
            'level' => $this->level ?? null,
            'level_description' => $this->level_description ?? null,
            'deep' => $this->deep ?? null,
        ]);
    }
}
