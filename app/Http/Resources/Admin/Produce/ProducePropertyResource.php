<?php

namespace App\Http\Resources\Admin\Produce;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProducePropertyResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'property_name' => $this->property_name ?? null,
            'property_english_name' => $this->property_english_name ?? null,
            'property_description' => $this->property_description ?? null,
            'sort' => $this->sort ?? null,
        ]);
    }
}
