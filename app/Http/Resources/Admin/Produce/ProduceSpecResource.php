<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use App\Models\ProduceBrace\ProduceSpec;
use Illuminate\Http\Request;

class ProduceSpecResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'sort' => $this->sort ?? null,
            'spec_name' => $this->spec_name ?? null,
            'spec_english_name' => $this->spec_english_name ?? null,
            'spec_description' => $this->spec_description ?? null,
            'value' => $this->spec_name ?? null,
            '_lft' => $this->_lft ?? null,
            '_rgt' => $this->_rgt ?? null,
            'children' => ProduceSpec::select(['id', 'uuid', 'spec_name', 'spec_name as value', 'spec_name', 'parent_id', '_lft', '_rgt'])->descendantsOf($this->id)->toTree() ?? null,
        ]);
    }
}
