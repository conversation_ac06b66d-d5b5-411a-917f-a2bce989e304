<?php

namespace App\Http\Resources\Admin\Produce;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProduceTagResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'sort' => $this->sort ?? null,
            'tag_name' => $this->tag_name ?? null,
            'tag_english_name' => $this->tag_english_name ?? null,
            'tag_description' => $this->tag_description ?? null,
        ]);
    }
}
