<?php

namespace App\Http\Resources\Admin\Seller;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class SellerGroupResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'group_name' => $this->group_name ?? null,
            'group_english_name' => $this->group_english_name ?? null,
            'group_description' => $this->group_description ?? null,
            'sort' => $this->sort ?? null,
        ]);
    }
}
