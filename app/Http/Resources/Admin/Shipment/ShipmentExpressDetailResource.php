<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\Admin\Sheet\SheetResource;
use App\Http\Resources\Admin\Waybill\WaybillResource;
use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ShipmentExpressDetailResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            'platform_id' => $this->platform_id ?? null,
            'site_id' => $this->site_id ?? null,
            //
            'waybill_type' => $this->waybill_type ?? null,
            'waybill_account' => $this->waybill_account ?? null,
            //
            'cp_code' => $this->cp_code ?? null,
            'cp_name' => $this->cp_name ?? null,
            //
            'transport_id' => $this->waybill->transport_id ?? null,
            'waybill_name' => $this->waybill->waybill_name ?? null,
            'waybill_nickname' => $this->waybill->waybill_nickname ?? null,
            //
            'shipping_type' => $this->shipping_type ?? null,
            //
            'province_name' => $this->province_name ?? null,
            //
            'freight_time' => $this->freight_time ?? null,
            'is_default' => $this->is_default ?? null,
            //
            'shipment_freights' => isset($this->shipment_freights) ? ShipmentFreightResource::collection($this->shipment_freights) : null,
            'sheets' => isset($this->sheet) ? new SheetResource($this->sheet) : null,
            'waybill' => isset($this->waybill) ? new WaybillResource($this->waybill) : null,
            //
            'sheet_uuid' => $this->sheet->uuid ?? null,
            'waybill_uuid' => $this->waybill->uuid ?? null,
            'waybill_station_uuid' => $this->waybill_station->uuid ?? null,
            'branch_code' => $this->waybill->branch_code ?? null,
            'express_corp_uuid' => $this->corp->uuid ?? null,
            'shipment_address_uuid' => $this->shipment_address->uuid ?? null,
            'freight_address_uuid' => $this->freight_address->uuid ?? null,
        ];
    }
}
