<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ShipmentFreightResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            'platform_id' => $this->platform_id ?? null,
            'site_id' => $this->site_id ?? null,
            //
            'shipment_express_id' => $this->shipment_express_id ?? null,
            'express_corp_id' => $this->express_corp_id ?? null,
            //
            'cp_code' => $this->cp_code ?? null,
            'cp_name' => $this->cp_name ?? null,
            //
            'country_code' => $this->country_code ?? null,
            'country_name' => $this->country_name ?? null,
            //
            'province_code' => $this->province_code ?? null,
            'province_name' => $this->province_name ?? null,
            //
            'freight_time' => $this->freight_time ?? null,
            //
            'package_price' => $this->package_price ?? null,
            'service_price' => $this->service_price ?? null,
            'freight_price' => $this->freight_price ?? null,
            //
            'first_val_meter' => $this->first_val_meter ?? null,
            'first_val_price' => $this->first_val_price ?? null,
            //
            'second_val_meter' => $this->second_val_meter ?? null,
            'second_val_price' => $this->second_val_price ?? null,
            //
            'is_limited' => $this->is_limited ?? null,
            //
            'send_id' => $this->send_id ?? null,
            'send_code' => $this->send_code ?? null,
            'send_name' => $this->send_name ?? null,
            //
            'receive_id' => $this->receive_id ?? null,
            'receive_name' => $this->receive_name ?? null,
            'receive_level' => $this->receive_level ?? null,
            //
            'region_send_id' => $this->region_send_id ?? null,
            'region_receive_id' => $this->region_receive_id ?? null,
            //
            'three_g_price' => $this->three_g_price ?? null,
            'five_g_price' => $this->five_g_price ?? null,
            //
            'one_kg_price' => $this->one_kg_price ?? null,
            'two_kg_price' => $this->two_kg_price ?? null,
            'three_kg_price' => $this->three_kg_price ?? null,
            'four_kg_price' => $this->four_kg_price ?? null,
            'five_kg_price' => $this->five_kg_price ?? null,
            //
            'ten_kg_price' => $this->ten_kg_price ?? null,
            'twenty_kg_price' => $this->twenty_kg_price ?? null,
            'thirty_kg_price' => $this->thirty_kg_price ?? null,
            'fifty_kg_price' => $this->fifty_kg_price ?? null,
            'sixty_kg_price' => $this->sixty_kg_price ?? null,
        ];
    }
}
