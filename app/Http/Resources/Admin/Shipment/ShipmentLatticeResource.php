<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ShipmentLatticeResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'lattice_name' => $this->lattice_name ?? null,
            'lattice_bulletin' => $this->lattice_bulletin ?? null,
            'lattice_images' => $this->lattice_images ?? null,
            'shipment_address_uuid' => $this->shipment_address->uuid ?? null,
            'lattice_address' => $this->lattice_address ?? null,
            'lattice_address_detail' => $this->lattice_address_detail ?? null,
            'mobile' => $this->mobile ?? null,
            'mobile_backup' => $this->mobile_backup ?? null,
            'week' => $this->week ?? null,
            'start_time' => $this->start_time ?? null,
            'end_time' => $this->end_time ?? null,
            'wechat' => $this->wechat ?? null,
            'qq' => $this->qq ?? null,
            'qq_qun' => $this->qq_qun ?? null,
            'email' => $this->email ?? null,
        ]);
    }
}
