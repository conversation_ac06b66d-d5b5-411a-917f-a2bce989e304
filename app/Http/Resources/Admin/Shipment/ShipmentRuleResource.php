<?php

namespace App\Http\Resources\Admin\Shipment;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ShipmentRuleResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'box_price' => $this->box_price,
            'carry_price' => $this->carry_price,
            'pack_price' => $this->pack_price,
            'shipment_vest' => $this->shipment_vest,
            'conduct_vest' => $this->conduct_vest,
            'interact_type' => $this->interact_type,
            'is_default' => $this->is_default,
        ]);
    }
}
