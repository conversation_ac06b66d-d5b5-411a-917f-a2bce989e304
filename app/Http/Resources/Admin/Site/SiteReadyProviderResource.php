<?php

namespace App\Http\Resources\Admin\Site;

use App\Http\Resources\Admin\Store\StoreResource;
use App\Http\Resources\Admin\User\UserResource;
use App\Http\Resources\BaseResource;
use App\Models\IndustryBrace\Category;
use App\Models\SiteBrace\SiteMarket;
use App\Models\StoreCenter\Store;
use App\Models\UserCenter\User;
use Illuminate\Http\Request;

class SiteReadyProviderResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $Store = Store::where('platform_id', '=', config('app.platform_id'))->orderBy('id', 'desc')->get();
        //
        return array_filter([
            'site_uuid' => $this->uuid ?? null,
            'site_name' => $this->site_name ?? null,
            'short_name' => $this->short_name ?? null,
            'country_name' => $this->country_name ?? null,
            'province_name' => $this->province_name ?? null,
            'city_name' => $this->city_name ?? null,
            'area_name' => $this->area_name ?? null,
            'street_name' => $this->street_name ?? null,
            'markets' => SiteMarket::where('platform_id', '=', config('app.platform_id'))->where('site_id', '=', $this->id)->orderBy('sort')->get()->toTree(),
            'cats' => Category::where('platform_id', '=', config('app.platform_id'))->where('is_state', '=', 1)->orderBy('sort')->get()->toTree(),
            'users' => UserResource::collection(User::where('platform_id', '=', config('app.platform_id'))->where('is_state', '=', 1)->get()),
            'stores' => StoreResource::collection($Store)->resource,
        ]);
    }
}
