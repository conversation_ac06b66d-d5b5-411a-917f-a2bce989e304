<?php

namespace App\Http\Resources\Admin\Site;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class SiteResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'site_name' => $this->site_name ?? null,
            'short_name' => $this->short_name ?? null,
            'country_name' => $this->country_name ?? null,
            'province_name' => $this->province_name ?? null,
            'city_name' => $this->city_name ?? null,
            'area_name' => $this->area_name ?? null,
            'street_name' => $this->street_name ?? null,
            'regions' => isset($this->regions) ? $this->regions()->select('id')->pluck('id')->toArray() : null,
        ]);
    }
}
