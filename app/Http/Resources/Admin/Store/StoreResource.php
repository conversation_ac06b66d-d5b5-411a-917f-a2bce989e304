<?php

namespace App\Http\Resources\Admin\Store;

use App\Http\Resources\Admin\Seller\SellerInfoResource;
use App\Http\Resources\Admin\Ship\ShipExpressResource;
use App\Http\Resources\Admin\Ship\ShipPickupResource;
use App\Http\Resources\BaseResource;
use App\Models\CommonBrace\Week;
use Illuminate\Http\Request;

class StoreResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'lattice_uuid' => $this->lattice->uuid ?? null,
            'store_name' => $this->store_name ?? null,
            'store_logo' => $this->store_logo ?? null,
            'store_photos' => $this->store_photos ?? null,
            //
            'stall_number' => $this->stall_number ?? null,
            //
            'english_name' => $this->english_name ?? null,
            'short_name' => $this->short_name ?? null,
            'introduction' => $this->introduction ?? null,
            //
            'info' => isset($this->seller_info) ? new SellerInfoResource($this->seller_info) : null,
            //
            'license_merchant_name' => isset($this->seller_info) ? $this->seller_info->license_merchant_name : null,
            'license_legal_representative' => isset($this->seller_info) ? $this->seller_info->license_legal_representative : null,
            //
            'wechat_nickname' => isset($this->user) ? $this->user->nickname : null,
            'wechat_img_url' => isset($this->user) ? $this->user->head_img_url : null,
            //
            'wechat_merchant_name' => isset($this->seller_info) ? $this->seller_info->license_merchant_name : null,
            'wechat_name' => isset($this->seller_info) ? $this->seller_info->license_legal_representative : null,
            //
            'markets' => $this->markets()->select('market_name')->pluck('market_name')->toArray() ?? null,
            'categories' => $this->categories()->select('category_name')->pluck('category_name')->toArray() ?? null,
            'week' => $this->week ?? null,
            'weeks' => Week::select('week_id', 'week_name')->get() ?? null,
            //
            'payee' => $this->wallet_payee ?? null,
            //
            'mobile' => $this->mobile ?? null,
            'mobile_backup' => $this->mobile_backup ?? null,
            'wechat' => $this->wechat ?? null,
            'email' => $this->email ?? null,
            'qq' => $this->qq ?? null,
            //
            'week_names' => $this->week()->select('week_name')->pluck('week_name')->toArray() ?? null,
            'express_uuid' => $this->shipment_expresses()->where('is_default', '=', 1)->first()->uuid ?? null,
            //
            'market_ids' => $this->markets()->select('id')->pluck('id')->toArray(),
            'category_ids' => $this->categories()->select('id')->pluck('id')->toArray(),
            'camp_ids' => $this->camps()->select('id')->pluck('id')->toArray(),
            //
            'cats' => $this->store_cats ? StoreCatResource::collection($this->store_cats) : null,
            'pickups' => $this->ship_pickups ? ShipPickupResource::collection($this->ship_pickups) : null,
            'expresses' => $this->shipment_expresses ? ShipExpressResource::collection($this->shipment_expresses) : null,
            //
            'is_launch' => $this->is_launch->key() ?? null,
            'launch_label' => $this->is_launch->label ?? null,
            'is_edit' => $this->is_edit->key() ?? null,
        ]);
    }
}
