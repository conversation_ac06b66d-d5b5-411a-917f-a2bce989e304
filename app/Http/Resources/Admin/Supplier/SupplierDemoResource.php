<?php

namespace App\Http\Resources\Admin\Supplier;

use App\Http\Resources\Admin\Seller\SellerInfoResource;
use App\Http\Resources\BaseResource;
use App\Models\CommonBrace\Week;
use App\Models\IndustryBrace\Category;
use App\Models\SiteBrace\SiteMarket;
use App\Services\BaseService;
use Illuminate\Http\Request;

class SupplierDemoResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'english_name' => $this->english_name ?? null,
            'nickname' => $this->nickname ?? null,
            'short_name' => $this->short_name ?? null,
            'introduction' => $this->introduction ?? null,
            'content' => $this->content ?? null,
            'promotion_info' => $this->promotion_info ?? null,
            'logo_url' => $this->logo_url ?? null,
            'photo_url' => $this->photo_url ?? null,
            'phone' => $this->phone ?? null,
            'brand_name' => $this->brand_name ?? null,
            //
            'cat' => $this->cat ?? null,
            'copy_cat' => $this->copy_cat ?? null,
            'market' => $this->market ?? null,
            //
            'stall' => $this->stall ?? null,
            'week_ids' => $this->weeks ?? null,
            'start_time' => $this->start_time ?? null,
            'end_time' => $this->end_time ?? null,
            'company' => $this->company ?? null,
            'contact_name' => $this->contact_name ?? null,
            //
            'info' => isset($this->info) ? new SellerInfoResource($this->info) : null,
            //
            'license_merchant_name' => isset($this->info) ? $this->info->license_merchant_name : null,
            'license_legal_representative' => isset($this->info) ? $this->info->license_legal_representative : null,
            //
            'wechat_nickname' => isset($this->user) ? $this->user->nickname : null,
            'wechat_img_url' => isset($this->user) ? $this->user->head_img_url : null,
            //
            'wechat_merchant_name' => isset($this->info) ? $this->info->license_merchant_name : null,
            'wechat_name' => isset($this->info) ? $this->info->license_legal_representative : null,
            //
            'week' => $this->week ?? null,
            'weeks' => Week::select('week_id', 'week_name')->get() ?? null,
            //
            'payee' => $this->payee ?? null,
            //
            'mobile' => $this->mobile ?? null,
            'mobile_backup' => $this->mobile_backup ?? null,
            'wechat' => $this->wechat ?? null,
            'email' => $this->email ?? null,
            'qq' => $this->qq ?? null,
            //
            'audit_edit' => $this->audit_edit ?? null,
            'edit_type' => $this->edit_type ?? null,
            'reject_reason' => $this->reject_reason ?? null,
            //
            'is_edit' => $this->is_edit ?? null,
            'main_state' => $this->main_state ?? null,
            //
            'created_time' => BaseService::serializeDate($this->created_at) ?? null,
            'updated_time' => BaseService::serializeDate($this->updated_at) ?? null,
            //
            'supplier_name' => $this->supplier->name ?? null,
            'supplier_english_name' => $this->supplier->english_name ?? null,
            'supplier_nickname' => $this->supplier->nickname ?? null,
            'supplier_shortname' => $this->supplier->short_name ?? null,
            'supplier_introduction' => $this->supplier->introduction ?? null,
            'supplier_content' => $this->supplier->content ?? null,
            'supplier_promotion_info' => $this->supplier->promotion_info ?? null,
            'supplier_logo_url' => $this->supplier->logo_url ?? null,
            'supplier_photo_url' => $this->supplier->photo_url ?? null,
            'supplier_phone' => $this->supplier->phone ?? null,
            'supplier_brand_name' => $this->supplier->brand_name ?? null,
            //
            'supplier_cat' => $this->supplier->cat ?? null,
            'supplier_copy_cat' => $this->supplier->copy_cat ?? null,
            'supplier_market' => $this->supplier->market ?? null,
            //
            'supplier_stall' => $this->supplier->stall ?? null,
            'supplier_week_ids' => $this->supplier->weeks ?? null,
            'supplier_start_time' => $this->supplier->start_time ?? null,
            'supplier_end_time' => $this->supplier->end_time ?? null,
            'supplier_company' => $this->supplier->company ?? null,
            'supplier_contact_name' => $this->supplier->contact_name ?? null,
            //
            'supplier_mobile' => $this->supplier->mobile ?? null,
            'supplier_mobile_backup' => $this->supplier->mobile_backup ?? null,
            'supplier_wechat' => $this->supplier->wechat ?? null,
            'supplier_email' => $this->supplier->email ?? null,
            'supplier_qq' => $this->supplier->qq ?? null,
            //
            'supplier_markets' => $this->supplier->markets()->select('market_name')->pluck('market_name')->toArray() ?? null,
            'supplier_categories' => $this->supplier->categories()->select('category_name')->pluck('category_name')->toArray() ?? null,
            //
            'markets' => $this->market ? SiteMarket::where('platform_id', '=', config('app.platform_id'))->whereIn('id', $this->market)->select('market_name')->pluck('market_name')->toArray() : null,
            'categories' => $this->cat ? Category::where('platform_id', '=', config('app.platform_id'))->whereIn('id', $this->cat)->select('category_name')->pluck('category_name')->toArray() : null,
        ]);
    }
}
