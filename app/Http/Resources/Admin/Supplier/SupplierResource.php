<?php

namespace App\Http\Resources\Admin\Supplier;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class SupplierResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'nickname' => $this->nickname ?? null,
            'introduction' => $this->introduction ?? null,
            'sort' => $this->sort ?? null,
            'user_uuid' => $this->user->uuid ?? null,
            //
            'license_registration_num' => $this->info->license_registration_num ?? null,
            'license_merchant_name' => $this->info->license_merchant_name ?? null,
            'license_legal_representative' => $this->info->license_legal_representative ?? null,
            //
            'market_ids' => $this->markets()->select('id')->pluck('id')->toArray() ?? [],
            'category_ids' => $this->categories()->select('id')->pluck('id')->toArray() ?? [],
            'store_uuids' => $this->stores()->select('uuid')->pluck('uuid')->toArray() ?? [],
        ]);
    }
}
