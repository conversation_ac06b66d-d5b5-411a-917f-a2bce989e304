<?php

namespace App\Http\Resources\Admin\User;

use App\Http\Resources\BaseResource;
use App\Services\BaseService;
use Illuminate\Http\Request;

class UserPermissionResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'name' => $this->name ?? null,
            'display_name' => $this->display_name ?? null,
            'description' => $this->description ?? null,
            //
            'created_time' => isset($this->created_at) ? BaseService::serializeDate($this->created_at) : null,
            'updated_time' => isset($this->updated_at) ? BaseService::serializeDate($this->updated_at) : null,
        ]);
    }
}
