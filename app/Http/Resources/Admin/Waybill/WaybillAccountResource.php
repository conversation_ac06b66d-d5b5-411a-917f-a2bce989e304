<?php

namespace App\Http\Resources\Admin\Waybill;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class WaybillAccountResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'source_id' => $this->source_id ?? null,
            'source_name' => $this->source_name ?? null,
            'waybill_id' => $this->waybill_id ?? null,
            //
            'sheet_type' => $this->sheet_type ?? null,
            'waybill_type' => $this->waybill_type ?? null,
            //
            'waybill_name' => $this->waybill_name ?? null,
            'waybill_nickname' => $this->waybill_nickname ?? null,
            'cp_name' => $this->cp_name ?? null,
            'waybills' => isset($this->waybills) ? $this->waybills : null,
        ]);
    }
}
