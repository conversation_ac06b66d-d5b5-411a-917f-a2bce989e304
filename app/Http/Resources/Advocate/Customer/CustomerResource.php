<?php

namespace App\Http\Resources\Advocate\Customer;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class CustomerResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $store = $this->store;
        //
        return array_filter([
            'uuid' => $this->uuid,
            //
            'customer_source' => $this->customer_source,
            'priority_type' => $this->priority_type,
            'lifecycle_stage' => $this->lifecycle_stage,
            //
            'stall_number' => $this->stall_number,
            'stall_nickname' => $this->stall_nickname,
            //
            'contact_name' => $this->contact_name,
            'contact_phone' => $this->contact_phone,
            'contact_mobile' => $this->contact_mobile,
            'contact_qq' => $this->contact_qq,
            'contact_qun' => $this->contact_qun,
            //
            'company_name' => $this->company_name,
            'contact_address' => $this->contact_address,
            //
            'main_cat' => $this->main_cat,
            'notes' => $this->notes,
            //
            'source_state' => $this->customer_source->label,
            'priority_state' => $this->priority_type->label,
            'qq_state' => $this->is_qq->label,
            'qun_state' => $this->is_qun->label,
            'launch_state' => $this->lifecycle_stage->value == 3 ? 'Open' : 'Closed',
            //
            'is_launch' => $this->is_launch ?? null,
            //
            'sort' => $this->sort ?? null,
            //
            'store_name' => $store->store_name ?? null,
            'store_logo' => $store->store_logo ?? null,
            'store_photos' => $store->store_photos ?? null,
            //
            'english_name' => $store->english_name ?? null,
            'short_name' => $store->short_name ?? null,
            'introduction' => $store->introduction ?? null,
            //
            'store_url' => isset($store->uuid) ? config('app.main_url') . '/store/' . $store->uuid : null,
            //
            'store_launch_label' => isset($store) ? $store->is_launch->label : null,
            'store_launch_state' => isset($store) ? $store->is_launch->key() : null,
            //
            'store_edit_label' => isset($store) ? $store->is_edit->label : null,
            'store_edit_state' => isset($store) ? $store->is_edit->key() : null,
            //
            'store_label' => isset($store) ? $store->store_state->label : null,
            'store_state' => isset($store) ? $store->store_state->key() : null,
            //
            'market_names' => isset($store) ? $store->markets()->select('market_name')->orderBy('id', 'desc')->limit(1)->pluck('market_name')->toArray() : null,
            'categories_names' => isset($store) ? $store->categories()->select('category_name')->pluck('category_name')->toArray() : null,
            'camp_names' => isset($store) ? $store->camps()->select('category_name')->pluck('category_name')->toArray() : null,
            'week_names' => isset($store) ? $store->week()->select('week_name')->pluck('week_name')->toArray() : null,
        ]);
    }
}
