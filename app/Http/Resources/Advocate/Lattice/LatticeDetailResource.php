<?php

namespace App\Http\Resources\Advocate\Lattice;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class LatticeDetailResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'platform_id' => $this->platform_id ?? null,
            'uuid' => $this->uuid ?? null,
            'lattice_name' => $this->lattice_name ?? null,
            'sort' => $this->sort ?? null,
            'market_ids' => $this->markets()->select('id')->pluck('id')->toArray(),
            'category_ids' => $this->categories()->select('id')->pluck('id')->toArray(),
        ]);
    }
}
