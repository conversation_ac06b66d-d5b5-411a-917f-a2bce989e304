<?php

namespace App\Http\Resources\Advocate\Media;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

/**
 * 媒体资源类
 * 该类负责将媒体资源转换为数组形式
 */
class MediaResource extends BaseResource
{
    /**
     * 将资源转换为数组
     * @param Request $request 请求对象
     * @return array<string, mixed> 返回媒体资源的数组表示
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null, // 媒体资源的唯一标识符
            'url' => $this->url ?? null,  // 媒体资源的链接
        ]);
    }
}
