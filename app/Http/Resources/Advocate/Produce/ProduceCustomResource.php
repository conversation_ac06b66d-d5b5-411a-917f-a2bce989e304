<?php

namespace App\Http\Resources\Advocate\Produce;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class ProduceCustomResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'name' => $this->custom_name ?? null,
            'custom_name' => $this->custom_name ?? null,
            'custom_english_name' => $this->custom_english_name ?? null,
            'value' => $this->custom_name ?? null,
            'is_disabled' => DB::raw('"true" as disabled') ?? null,
        ]);
    }
}
