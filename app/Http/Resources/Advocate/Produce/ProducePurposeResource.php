<?php

namespace App\Http\Resources\Advocate\Produce;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProducePurposeResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'purpose_name' => $this->purpose_name ?? null,
            'purpose_english_name' => $this->purpose_english_name ?? null,
            'value' => $this->purpose_name ?? null,
        ]);
    }
}
