<?php

namespace App\Http\Resources\Advocate\Produce;

use App\Http\Resources\Advocate\Brand\BrandResource;
use App\Models\ProduceBrace\ProduceSuit;
use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class ProduceSchemaDetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'site_id' => $this->site_id ?? null,
            'schema_uuid' => $this->uuid ?? null,
            'category_id' => $this->category_id ?? null,
            'schema_name' => $this->schema_name ?? null,
            'schema_english_name' => $this->schema_english_name ?? null,
            'schema_keyword' => $this->schema_keyword ?? null,
            'category_name' => $this->category_name ?? null,
            //
            'stock' => $this->stock ?? null,
            'weight' => $this->weight ?? null,
            //
            'suit_number' => $this->suit_number ?? null,
            'spec_number' => $this->spec_number ?? null,
            'custom_number' => $this->custom_number ?? null,
            'fabric_number' => $this->fabric_number ?? null,
            'design_number' => $this->design_number ?? null,
            'trend_number' => $this->trend_number ?? null,
            'craft_number' => $this->craft_number ?? null,
            'shade_number' => $this->shade_number ?? null,
            'purpose_number' => $this->purpose_number ?? null,
            'accessory_number' => $this->accessory_number ?? null,
            //
            'suits' => ProduceSuit::select(['id', 'uuid', 'suit_name as name', 'suit_name as value', 'suit_name', 'parent_id', '_lft', '_rgt', DB::raw('"true" as disabled')])->descendantsOf($this->suit_id)->toTree() ?? null,
            'specs' => $this->specs ? ProduceSpecResource::collection($this->specs) : null,
            'customs' => $this->custom->children ? ProduceCustomResource::collection($this->custom->children) : null,
            //
            'keywords' => $this->keywords()->select('word')->pluck('word')->toArray() ?? null,
            //
            'fabrics' => $this->fabrics ? ProduceFabricResource::collection($this->fabrics->first()->children()->get()) : null,
            'designs' => $this->designs ? ProduceDesignResource::collection($this->designs->first()->children()->get()) : null,
            'trends' => $this->trends ? ProduceTrendResource::collection($this->trends->first()->children()->get()) : null,
            'crafts' => $this->crafts ? ProduceCraftResource::collection($this->crafts->first()->children()->get()) : null,
            'shades' => $this->shades ? ProduceShadeResource::collection($this->shades->first()->children()->get()) : null,
            'purposes' => $this->purposes ? ProducePurposeResource::collection($this->purposes->first()->children()->get()) : null,
            'accessories' => $this->accessories ? ProduceAccessoryResource::collection($this->accessories->first()->children()->get()) : null,
            //
            'brands' => $this->brands ? BrandResource::collection($this->brands) : null,
            //
            'is_state' => $this->is_state ?? null,
        ]);
    }
}
