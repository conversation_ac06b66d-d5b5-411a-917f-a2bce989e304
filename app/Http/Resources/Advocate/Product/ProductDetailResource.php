<?php

namespace App\Http\Resources\Advocate\Product;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductDetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            //
            'store_uuid' => $this->store->uuid ?? null,
            'schema_uuid' => $this->schema->uuid ?? null,
            //
            'store_name' => $this->store->store_name ?? null,
            'stall_number' => $this->store->stall_number ?? null,
            //
            'category_name' => $this->category->category_name ?? null,
            'schema_name' => $this->schema->schema_name ?? null,
            'spec_name' => $this->spec->spec_name ?? null,
            //
            'product_no' => $this->product_no ?? null,
            'product_name' => $this->product_name ?? null,
            'product_title' => $this->product_title ?? null,
            'product_english_title' => $this->product_english_title ?? null,
            'product_sn' => $this->product_sn ?? null,
            'product_barcode' => $this->product_barcode ?? null,
            //
            'price' => ($this->min_price === $this->max_price) ? "￥{$this->min_price}" : "￥{$this->min_price}~￥{$this->max_price}",
            //
            'product_image_uuid' => $this->product_image_uuid ?? null,
            'product_image_uuids' => $this->product_image_uuids ?? null,
            'product_desc_uuids' => $this->product_desc_uuids ?? null,
            'product_white_uuid' => $this->product_white_uuid ?? null,
            'product_video_uuid' => $this->product_video_uuid ?? null,
            //
            'product_picture_url' => $this->product_picture_url ?? null,
            'product_images' => $this->product_images ?? null,
            'product_desc' => $this->product_desc ?? null,
            'product_white' => $this->product_white ?? null,
            'product_video' => $this->product_video ?? null,
            'keywords' => $this->keywords ?? null,
            //
            'fabric_ids' => $this->fabric_ids ?? null,
            'design_ids' => $this->design_ids ?? null,
            'trend_ids' => $this->trend_ids ?? null,
            'craft_ids' => $this->craft_ids ?? null,
            'shade_ids' => $this->shade_ids ?? null,
            'purpose_ids' => $this->purpose_ids ?? null,
            'accessory_ids' => $this->accessory_ids ?? null,
            'suit_ids' => $this->suit_ids ?? null,
            'spec_id' => $this->spec_id ?? null,
            'attributes' => $this->attributes ?? null,
            'suits' => $this->suits ?? null,
            'specs' => $this->specs ?? null,
            'customs' => $this->customs ?? null,
            'skus' => $this->skus ?? null,
            //
            //
            'suit_number' => count($this->arr_suits) ?? null,
            'sku_number' => count($this->arr_skus) ?? null,
            //
            'store_state_label' => $this->store->store_state->label ?? null,
            //
            'edit_type_key' => $this->edit_type->key() ?? null,
            'edit_type_label' => $this->edit_type->label ?? null,
            //
            'is_offline_label' => $this->is_offline->label ?? null,
            'is_closure_label' => $this->is_closure->label ?? null,
            //
            'product_state_label' => $this->product_state->label ?? null,
            'draft_status_label' => $this->draft_status->label ?? null,
        ]);
    }
}
