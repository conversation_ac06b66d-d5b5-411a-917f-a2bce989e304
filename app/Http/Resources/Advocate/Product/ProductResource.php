<?php

namespace App\Http\Resources\Advocate\Product;

use App\Enums\Is\IsEditEnum;
use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'product_uuid' => $this->uuid ?? null,
            //
            'store_uuid' => $this->store->uuid ?? null,
            'schema_uuid' => $this->schema->uuid ?? null,
            //
            'store_name' => $this->store->store_name ?? null,
            'stall_number' => $this->store->stall_number ?? null,
            //
            'category_name' => $this->category->category_name ?? null,
            'schema_name' => $this->schema->schema_name ?? null,
            'spec_name' => $this->spec->spec_name ?? null,
            //
            'product_no' => $this->product_no ?? null,
            'product_name' => $this->product_name ?? null,
            'product_title' => $this->product_title ?? null,
            'product_english_title' => $this->product_english_title ?? null,
            'product_sn' => $this->product_sn ?? null,
            'product_barcode' => $this->product_barcode ?? null,
            //
            'price' => ($this->min_price === $this->max_price) ? "￥{$this->min_price}" : "￥{$this->min_price}~￥{$this->max_price}",
            //
            'product_image_uuid' => $this->product_image_uuid ?? null,
            'product_image_uuids' => $this->product_image_uuids ?? null,
            'product_desc_uuids' => $this->product_desc_uuids ?? null,
            'product_white_uuid' => $this->product_white_uuid ?? null,
            'product_video_uuid' => $this->product_video_uuid ?? null,
            //
            'product_picture_url' => $this->product_picture_url ?? null,
            'product_images' => $this->product_images ?? null,
            'product_desc' => $this->product_desc ?? null,
            'product_white' => $this->product_white ?? null,
            'product_video' => $this->product_video ?? null,
            'keywords' => $this->keywords()->select('word')->pluck('word')->toArray() ?? null ?? null,
            //
            'fabric_ids' => $this->fabric_ids ?? null,
            'design_ids' => $this->design_ids ?? null,
            'trend_ids' => $this->trend_ids ?? null,
            'craft_ids' => $this->craft_ids ?? null,
            'shade_ids' => $this->shade_ids ?? null,
            'purpose_ids' => $this->purpose_ids ?? null,
            'accessory_ids' => $this->accessory_ids ?? null,
            'suit_ids' => $this->suit_ids ?? null,
            'spec_id' => $this->spec_id ?? null,
            //
            'suits' => $this->suits ?? null,
            'specs' => $this->specs ?? null,
            'customs' => $this->customs ?? null,
            //
            'skus' => isset($this->skus) ? ProductSkuResource::collection($this->skus) : null,
            //
            'product_url' => config('app.main_url') . '/product/' . $this->uuid ?? null,
            // 添加 is_edit 字段
            'is_edit' => $this->is_edit !== IsEditEnum::editable(),
            //
            'store_state_label' => $this->store->store_state->label ?? null,
            'store_state_color' => $this->store->store_state->color() ?? null,
            //
            'is_offline_label' => $this->is_offline->label ?? null,
            'is_offline_color' => $this->is_offline->color() ?? null,
            'is_closure_label' => $this->is_closure->label ?? null,
            'is_closure_color' => $this->is_closure->color() ?? null,
            'is_edit_label' => $this->is_edit->label ?? null,
            'is_edit_color' => $this->is_edit->color() ?? null,
            //
            'product_state_label' => $this->product_state->label ?? null,
            'product_state_color' => $this->product_state->color() ?? null,
        ]);
    }
}
