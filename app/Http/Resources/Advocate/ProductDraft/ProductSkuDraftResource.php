<?php

namespace App\Http\Resources\Advocate\ProductDraft;

use App\Http\Resources\Advocate\Product\ProductSkuResource;
use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSkuDraftResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'sku_id' => $this->sku_id ?? null,
            'sku_sn' => $this->sku_sn ?? null,
            'stock' => $this->stock ?? null,
            'weight' => $this->weight ?? null,
            'market_price' => $this->market_price ?? null,
            'price' => $this->price ?? null,
            'is_offline' => $this->is_offline ?? null,
            'is_closure' => $this->is_closure ?? null,
            'is_change' => $this->is_change ?? null,
            'change_sku_sn' => $this->change_sku_sn ?? null,
            'change_stock' => $this->change_stock ?? null,
            'change_weight' => $this->change_weight ?? null,
            'change_market_price' => $this->change_market_price ?? null,
            'change_price' => $this->change_price ?? null,
            'change_is_offline' => $this->change_is_offline ?? null,
            'change_is_closure' => $this->change_is_closure ?? null,
            'price_change' => $this->price_change ?? null,
            //
            'is_offline_label' => $this->is_offline->label ?? null,
            'is_offline_color' => $this->is_offline->color() ?? null,
            'is_closure_label' => $this->is_closure->label ?? null,
            'is_closure_color' => $this->is_closure->color() ?? null,
            //
            'sku' => isset($this->sku) ? new ProductSkuResource($this->sku) : null
        ]);
    }
}
