<?php

namespace App\Http\Resources\Advocate\Rice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * RiceCornDetailResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class RiceCornDetailResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $rice_corn_seeds = $this->rice_corn_seeds;
        //
        foreach ($rice_corn_seeds as $rice_corn_seed) {
            //
            if ($rice_corn_seed->seed_name === 'p-404' || $rice_corn_seed->seed_name === 'p-3216') {
                //
                $spec_label = $rice_corn_seed->seed_label;
                $spec_attr = $rice_corn_seed->seed_text;
                $spec_no = $rice_corn_seed->id;
                $seed_image_url = $rice_corn_seed->seed_image_url;
            }
            //
            if ($rice_corn_seed->seed_name === 'p-100018377' || $rice_corn_seed->seed_name === 'p-3151') {
                //
                $suit_label = $rice_corn_seed->seed_label;
                $suit_attr = $rice_corn_seed->seed_text;
                $suit_no = $rice_corn_seed->id;
            }
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'sku_uuid' => $this->uuid ?? null,
            'corn_uuid' => $this->uuid ?? null,
            //
            'spec_no' => $spec_no ?? null,
            'suit_no' => $suit_no ?? null,
            'suit_attr' => $suit_attr ?? null,
            'spec_attr' => $spec_attr ?? null,
            'spec_label' => $spec_label ?? null,
            'suit_label' => $suit_label ?? null,
            'corn_image_url' => $seed_image_url ?? null,
            'seed_image_url' => $seed_image_url ?? null,
            //
            'corn_no' => $this->corn_no ?? null,
            'corn_sn' => $this->corn_sn ?? rand(1000, 9999),
            'corn_stock' => $this->corn_stock > 0 ? $this->corn_stock : rand(500, 999),
            'corn_price' => isset($this->corn_price) ? (float)$this->corn_price : 0,
            'corn_status' => $this->corn_status ? 'online' : 'offline',
            //
            'corn_length' => $this->corn_length > 0 ? $this->corn_length : 12.0,
            'corn_width' => $this->corn_width > 0 ? $this->corn_width : 10.0,
            'corn_height' => $this->corn_height > 0 ? $this->corn_height : 3.5,
            'corn_weight' => $this->corn_weight > 0 ? $this->corn_weight : 40.0,
            'corn_volume' => $this->corn_volume > 0 ? $this->corn_volume : 420.0,
            //
            'corn_key' => $this->corn_key ?? null,
            'corn_values' => $this->corn_values ?? null,
            //
            'rice_seeds' => isset($this->rice_corn_seeds) ? RiceSeedResource::collection($this->rice_corn_seeds()->get()) : null,
        ]);
    }
}
