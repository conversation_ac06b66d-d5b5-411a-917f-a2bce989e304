<?php

namespace App\Http\Resources\Advocate\Rice;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RiceDetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        if ($this->source_id === 5) {
            //
            $pin_url = 'https://detail.1688.com/offer/' . $this->pin_no . '.html';
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'rice_uuid' => $this->uuid ?? null,
            //
            'group_id' => $this->group_id ?? null,
            //
            'rice_number' => $this->rice_number ?? '无货号',
            'category_name' => $this->category_name ?? '无分类名称',
            'category_no' => $this->category_no ?? '无分类ID',
            'group_name' => $this->group_name ?? '无分类组名称',
            'rice_scene' => $this->rice_scene ?? '无场景',
            'price_range' => $this->price_range ?? '无价格范围',
            'amount_sale' => $this->amount_sale ?? 0,
            'rice_video' => $this->rice_video ?? null,
            //
            'pin_url' => $pin_url ?? null,
            //
            //
            'rice_json' => $this->rice_json ?? null,
            'price_ranges' => $this->price_ranges ?? null,
            //
            'relation_json' => $this->relation_json ?? null,
            'product_json' => $this->product_json ?? null,
            //
            'from_data' => $this->from_data ?? null,
            'full_tips' => $this->full_tips ?? null,
            'record_no' => $this->record_no ?? null,
            'plan_no' => $this->plan_no ?? null,
            'publish_id' => $this->publish_id ?? null,
            'publish_time' => $this->publish_time ?? now(),
            'publish_tips' => $this->publish_tips ?? null,
            'relation_no' => $this->relation_no ?? null,
            'publish_state' => $this->publish_state ?? 1,
            'timing_time' => $this->timing_time ?? now(),
            //
            'rice_title' => $this->rice_title ?? null,
            'rice_image' => $this->rice_image ?? null,
            //
            'length' => $this->length ?? null,
            'width' => $this->width ?? null,
            'height' => $this->height ?? null,
            'weight' => $this->weight ?? null,
            'cbu_unit' => $this->cbu_unit ?? null,
            'total_sales' => $this->total_sales ?? null,
            //
            'min_price' => $this->min_price ?? null,
            'max_price' => $this->max_price ?? null,
            //
            'quotation_type' => $this->quotation_type ?? null,
            'begin_amount' => $this->begin_amount ?? null,
            'freight_no' => $this->freight_no ?? null,
            'freight_type' => $this->freight_type ?? null,
            'online_trade' => $this->online_trade ?? null,
            'light_custom' => $this->light_custom ?? null,
            'shelf_time' => $this->shelf_time ?? null,
            'cbu_send_address' => $this->cbu_send_address ?? null,
            'total_sales2' => $this->total_sales2 ?? null,
            'user_category' => $this->user_category ?? null,
            'detail_video' => $this->detail_video ?? null,
            'primary_video' => $this->primary_video ?? null,
            //
            'is_product' => $this->is_product ?? 0,
            'is_trade' => $this->is_trade ?? 0,
            'is_mix' => $this->is_mix ?? 0,
            'is_identity' => $this->is_identity ?? 0,
            'is_full' => $this->is_full ?? 1,
            //
            'rice_preview' => $this->rice_preview ?? null,
            'rice_images' => $this->rice_images ?? null,
            'rice_descries' => $this->rice_descries ?? null,
            //
            'rice_name' => $this->rice_name ?? null,
            'rice_image_url' => $this->rice_image_url ?? null,
            'rice_description' => $this->rice_description ?? null,
            //
            'keywords' => isset($this->keywords) ? $this->keywords()->get()->pluck('word') : null,
            //'keyword'          => isset($this->keywords) ? $this->keywords()->get()->pluck('word') : null,
            //
            //'rice_props'       => isset($this->rice_props) ? $this->rice_props()->get() : null,
            //'rice_seeds'       => isset($this->rice_seeds) ? RiceSeedResource::collection($this->rice_seeds()->get()) : null,
            'rice_corns' => isset($this->rice_corns) ? RiceCornDetailResource::collection($this->rice_corns()->orderBy('id', 'desc')->get()) : null,
        ]);
    }
}
