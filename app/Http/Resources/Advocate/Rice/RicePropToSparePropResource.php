<?php

namespace App\Http\Resources\Advocate\Rice;

use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RicePropToSparePropResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'category_no' => $this->category_no ?? null,
            'prop_no' => $this->prop_no ?? null,
            'prop_text' => $this->prop_text ?? null,
            'prop_value' => $this->prop_value ?? null,
        ]);
    }
}
