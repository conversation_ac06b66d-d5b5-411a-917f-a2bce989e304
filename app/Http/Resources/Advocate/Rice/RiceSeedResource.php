<?php

namespace App\Http\Resources\Advocate\Rice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * RiceSeedResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class RiceSeedResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        if ($this->seed_name === 'p-404' || $this->seed_name === 'p-3216') {
            //
            $seed_text = $this->seed_text;
            $spec_no = $this->id;
            $seed_image_url = $this->seed_image_url;
            $attr_type = 'spec';
            $seed_name = 'p-404';
        }
        //
        if ($this->seed_name === 'p-100018377' || $this->seed_name === 'p-3151') {
            //
            $seed_text = $this->seed_text;
            $suit_no = $this->id;
            $attr_type = 'suit';
            $seed_name = 'p-100018377';
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'seed_uuid' => $this->uuid ?? null,
            'category_no' => $this->category_no ?? null,
            'seed_no' => $suit_no ?? null,
            'spec_no' => $spec_no ?? null,
            //
            'attr_type' => $attr_type ?? null,
            //
            'seed_name' => $seed_name ?? null,
            'seed_value' => $this->seed_value ?? null,
            'seed_text' => $seed_text ?? null,
            'seed_custom' => $this->seed_custom ?? null,
            'seed_image_url' => $this->seed_image_url ?? null,
            'spec_image_url' => $seed_image_url ?? null,
            //
            //'rice_corns'     => isset($this->rice_corn_seeds) ? RiceCornDetailResource::collection($this->rice_corn_seeds()->orderBy('id', 'desc')->get()) : null,
        ]);
    }

}
