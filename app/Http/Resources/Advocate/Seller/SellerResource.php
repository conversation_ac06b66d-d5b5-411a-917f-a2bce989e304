<?php

namespace App\Http\Resources\Advocate\Seller;

use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class SellerResource extends BaseResource
{
    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
        ]);
    }
}
