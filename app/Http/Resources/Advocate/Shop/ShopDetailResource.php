<?php

namespace App\Http\Resources\Advocate\Shop;

use App\Http\Resources\Advocate\Rice\RiceResource;
use App\Traits\System\Enum\HasEnumTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ShopDetailResource extends JsonResource
{
    use HasEnumTrait;

    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return [
            'uuid' => $this->uuid ?? null,
            'company_name' => $this->company_name ?? null,
            'company_province' => $this->company_province ?? null,
            'company_city' => $this->company_city ?? null,
            'company_address' => $this->company_address ?? null,
            'company_similar' => $this->company_similar ?? null,
            'shop_no' => $this->shop_no ?? null,
            'shop_code' => $this->shop_code ?? null,
            'shop_real' => $this->shop_real ?? null,
            'shop_name' => $this->shop_name ?? null,
            'shop_num' => $this->shop_num ?? null,
            'shop_icon' => $this->shop_icon ?? null,
            'shop_domain' => $this->shop_domain ?? null,
            'latitude' => $this->latitude ?? null,
            'longitude' => $this->longitude ?? null,
            'member_level' => $this->member_level ?? null,
            'member_tags' => $this->member_tags ?? null,
            'member_booked_count' => $this->member_booked_count ?? null,
            'tags' => $this->tags ?? null,
            'parents' => $this->parents ?? null,
            'company_offers' => $this->company_offers ?? null,
            'business_mode' => $this->business_mode ?? null,
            'annual_revenue' => $this->annual_revenue ?? null,
            'buyer_will_cnt' => $this->buyer_will_cnt ?? null,
            'technology_type' => $this->technology_type ?? null,
            'composite_score' => $this->composite_score ?? null,
            'algo_type' => $this->algo_type ?? null,
            'proofing' => $this->proofing ?? null,
            'employees_count' => $this->employees_count ?? null,
            'compliance_rate' => $this->compliance_rate ?? null,
            'repeat_rate' => $this->repeat_rate ?? null,
            'sale_quantity_3m' => $this->sale_quantity_3m ?? null,
            'sale_quantity' => $this->sale_quantity ?? null,
            'serve_score' => $this->serve_score ?? null,
            'quantity_begin' => $this->quantity_begin ?? null,
            'quantity_month' => $this->quantity_month ?? null,
            'quantity_3m' => $this->quantity_3m ?? null,
            'pay_ord_amt_3m' => $this->pay_ord_amt_3m ?? null,
            'pay_mord_cnt_3m' => $this->pay_mord_cnt_3m ?? null,
            'jgdz_pay_amt' => $this->jgdz_pay_amt ?? null,
            'fuzzy_pay_amt_3m' => $this->fuzzy_pay_amt_3m ?? null,
            'tp_member' => $this->tp_member ?? null,
            'tp_num' => $this->tp_num ?? null,
            'tp_year' => $this->tp_year ?? null,
            //
            'shop_count' => $this->shop_count ?? null,
            'category_count' => $this->category_count ?? null,
            'camp_count' => $this->camp_count ?? null,
            'trim_count' => $this->trim_count ?? null,
            //
            'category_name' => $this->category->category_name ?? null,
            'camp_name' => $this->camp->category_name ?? null,
            // 分页，每页返回10条记录->select('pin_no', 'pin_subject', 'rice_title', 'rice_image', 'min_price', 'amount_sale', 'rice_state')
            'shop_rices' => $this->rices()->exists() ? RiceResource::collection($this->rices()->orderBy('id', 'desc')->paginate(25))->resource : [],
        ];
    }
}
