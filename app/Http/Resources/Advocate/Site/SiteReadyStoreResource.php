<?php

namespace App\Http\Resources\Advocate\Site;

use App\Http\Resources\BaseResource;
use App\Models\IndustryBrace\Category;
use App\Models\SiteBrace\SiteMarket;
use Illuminate\Http\Request;

class SiteReadyStoreResource extends BaseResource
{


    /**
     * Transform the resource into an array
     * 将资源转换为数组
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'site_uuid' => $this->uuid ?? null,
            'site_name' => $this->site_name ?? null,
            'short_name' => $this->short_name ?? null,
            'country_name' => $this->country_name ?? null,
            'province_name' => $this->province_name ?? null,
            'city_name' => $this->city_name ?? null,
            'area_name' => $this->area_name ?? null,
            'street_name' => $this->street_name ?? null,
            'markets' => SiteMarket::where('platform_id', '=', config('app.platform_id'))->where('site_id', '=', $this->id)->orderBy('id')->get()->toTree(),
            'cats' => Category::where('platform_id', '=', config('app.platform_id'))->where('is_state', '=', 1)->orderBy('id')->get()->toTree(),
        ]);
    }
}
