<?php

namespace App\Http\Resources\Advocate\View;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * ViewItemResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class ViewItemResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //自定义转换示例：
        return [
            'uuid' => $this->uuid,
            //
            'category_name' => $this->category_name,
            //
            'agent_count' => $this->agent_count,
            'evaluate_count' => $this->evaluate_count,
            //
            'delivery_cycle' => $this->delivery_cycle,
            'publish_time' => $this->publish_time,
            //
            'item_price' => $this->item_price,
            'agent_price' => $this->agent_price,
            //
            'sale_quantity' => $this->sale_quantity,
            //
            'offer_url' => $this->offer_url,
            'pic_url' => $this->pic_url,
            //
            'item_no' => $this->item_no,
            'item_title' => $this->item_title,
            'item_label' => $this->item_label,
            'item_unit' => $this->item_unit,
            //
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
        // 使用默认转换（返回模型的所有可见属性）
        // return parent::toArray($request);
    }

    /**
     * 获取应该随资源一起返回的其他数据
     * @param Request $request
     * @return array<string, mixed>
     */
    // public function with(Request $request): array
    // {
    //     return [
    //         'meta' => [
    //             'version' => '1.0.0',
    //             'api_status' => 'stable',
    //         ],
    //     ];
    // }

    /**
     * 自定义资源的包装键
     * @return string|null
     */
    // public static function wrap(): ?string
    // {
    //     return 'user';  // 将默认的 'data' 改为 'user'
    //     // return null;  // 不使用包装键
    // }

    /**
     * 确定资源是否应该被缓存
     * @param Request $request
     * @return bool
     */
    // public function shouldCache(Request $request): bool
    // {
    //     return true;
    // }
}
