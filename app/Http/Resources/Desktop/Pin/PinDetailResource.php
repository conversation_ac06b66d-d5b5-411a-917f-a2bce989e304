<?php

namespace App\Http\Resources\Desktop\Pin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * PinDetailResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class PinDetailResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $shop = $this->shop;
        //
        if ($this->source_id === 5) {
            //
            $pin_url = 'https://detail.1688.com/offer/' . $this->pin_no . '.html';
        }
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'pin_uuid' => $this->uuid ?? null,
            'pin_no' => $this->pin_no ?? null,
            'pin_link' => $this->pin_link ?? null,
            'pin_pic' => $this->pin_pic ?? null,
            'pin_subject' => $this->pin_subject ?? null,
            'pin_title' => $this->pin_title ?? null,
            'pin_label' => $this->pin_label ?? null,
            'pin_unit' => $this->pin_unit ?? null,
            'pin_price' => $this->pin_price ?? null,
            'agent_price' => $this->agent_price ?? null,
            //
            'category_name' => $this->category_name ?? null,
            //
            'pin_url' => $pin_url ?? null,
            //
            'pin_category_no' => $this->pin_category_no ?? null,
            'pin_category_name' => $this->pin_category_name ?? null,
            //
            'booked_count' => $this->booked_count ?? null,
            'member_booked_count' => $this->member_booked_count ?? null,
            'primary_rank_score' => $this->primary_rank_score ?? null,
            'quantity_sum_month' => $this->quantity_sum_month ?? null,

            'delivery_cycle' => $this->delivery_cycle ?? null,
            //
            'thirty_count' => $this->thirty_count ?? null,
            'sale_quantity' => $this->sale_quantity ?? null,
            'agent_count' => $this->agent_count ?? null,
            'evaluate_count' => $this->evaluate_count ?? null,
            //
            'is_offer' => $this->is_offer ?? null,
            //
            'publish_time' => $this->publish_time ?? null,
            //
            'shop_uuid' => $shop->uuid ?? null,
            'shop_no' => $shop->shop_no ?? null,
            'shop_name' => $shop->shop_name ?? null,
            'shop_state' => $shop->shop_state ?? null,
            'company_name' => $shop->company_name ?? null,
            'company_province' => $shop->company_province ?? null,
            'company_city' => $shop->company_city ?? null,
            'company_address' => $shop->company_address ?? null,
            'fuzzy_pay_amt_3m' => $shop->fuzzy_pay_amt_3m ?? null,
            //
            'batch_push_button' => true,
            //
            //'shop_rices'        => isset($this->shop_rices) ? RiceSimpleResource::collection($this->shop_rices()->orderBy('id', 'desc')->limit(4)->get()) : null,
        ]);
    }
}
