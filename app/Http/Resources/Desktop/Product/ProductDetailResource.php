<?php

namespace App\Http\Resources\Desktop\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * ProductDetailResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class ProductDetailResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        $suits = $this->suits()->get();
        //
        return array_filter([
            'uuid' => $this->uuid,
            'product_uuid' => $this->uuid,
            'store_uuid' => $this->store->uuid,
            'store_name' => $this->store->store_name,
            'store_logo' => $this->store->store_logo,
            //
            'product_no' => $this->product_no,
            'product_title' => $this->product_title,
            'product_name' => $this->product_name,
            //
            'max_price' => $this->max_price,
            'min_price' => $this->min_price,
            //
            'schema_name' => $this->schema_name,
            'suit_name' => $this->suit_name,
            'theme_name' => $this->theme_name,
            'brand_name' => $this->brand_name,
            //
            'product_suits' => $suits ? ProductSuitResource::collection($suits) : null,
            'product_attrs' => isset($this->product_attrs) ? ProductAttrResource::collection($this->product_attrs->where('attr_type', 'spec')) : null,
            // 图片和视频资源的处理，确保只返回url的数组
            'product_picture_url' => $this->product_picture_url ?? $this->product_picture_url,
            'product_picture' => isset($this->product_picture) ? new ProductImageResource($this->product_picture) : null,
            'product_images' => isset($this->product_images) ? ProductImageResource::collection($this->product_images) : null,
            'product_descries' => isset($this->product_descries) ? ProductImageResource::collection($this->product_descries) : null,
            'white_image' => isset($this->product_white) ? new ProductImageResource($this->product_white) : null,
            'product_video' => isset($this->product_video) ? new ProductImageResource($this->product_video) : null,
        ]);
    }
}
