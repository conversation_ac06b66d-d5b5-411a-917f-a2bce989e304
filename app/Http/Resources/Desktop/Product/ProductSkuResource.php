<?php

namespace App\Http\Resources\Desktop\Product;


use App\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class ProductSkuResource extends BaseResource
{


    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $this->increaseStock(rand(1, 10));
        $this->decreaseStock(rand(1, 2));
        //
        return [
            'uuid' => $this->uuid ?? null,
            'sku_uuid' => $this->uuid ?? null,
            //
            'store_uuid' => $this->store->uuid ?? null,
            'store_name' => $this->store->store_name ?? null,
            'store_logo' => $this->store->store_logo ?? null,
            'product_no' => $this->product_no ?? null,
            'sku_no' => $this->sku_no ?? null,
            //
            'suit_attr' => $this->suit_attr ?? null,
            'spec_attr' => $this->spec_attr ?? null,
            'custom_attr' => $this->custom_attr ?? null,
            //
            'sku_name' => $this->sku_name ?? null,
            'sku_image_url' => $this->sku_image_url ?? null,
            //
            'price' => floatval($this->price ?? 0),
            'market_price' => floatval($this->market_price ?? 0),
            //
            'quantity' => floatval($this->quantity ?? 0),
            'stock' => floatval($this->stock),
            //
            'is_offline' => $this->is_offline ?? null,
            'is_closure' => $this->is_closure ?? null,
        ];
    }
}
