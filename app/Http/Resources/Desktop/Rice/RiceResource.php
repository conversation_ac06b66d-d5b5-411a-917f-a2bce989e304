<?php

namespace App\Http\Resources\Desktop\Rice;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * RiceResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class RiceResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        if ($this->source_id === 5) {
            //
            $pin_url = 'https://detail.1688.com/offer/' . $this->pin_no . '.html';
        }
        //
        $shop = $this->shop;
        $store = $this->store;
        $pin = $this->pin;
        // 使用默认转换（返回模型的所有可见属性）
        return array_filter([
            'rice_id' => $this->id ?? null,
            'uuid' => $this->uuid ?? null,
            'rice_uuid' => $this->uuid ?? null,
            //
            'group_id' => $this->group_id ?? null,
            'group_name' => $this->group_name ?? null,
            //
            'store_name' => $store->store_name ?? null,
            'stall_number' => $store->stall_number ?? null,
            //
            'category_name' => $this->category_name ?? null,
            'category_no' => $this->category_no ?? null,
            //
            'rice_title' => $this->rice_title ?? null,
            'rice_number' => $this->rice_number ?? null,
            'rice_scene' => $this->rice_scene ?? null,
            //
            'rice_category_no' => $this->rice_category_no ?? '无分类ID',
            'rice_category_name' => $this->rice_category_name ?? '无分类名称',
            //
            'rice_image' => $this->rice_image ?? null,
            //
            'min_price' => $this->min_price ?? 0,
            'max_price' => $this->max_price ?? 0,
            'cbu_unit' => $this->cbu_unit ?? 0,
            //
            'shop_uuid' => $shop->uuid ?? null,
            'shop_no' => $this->shop_no ?? null,
            'shop_name' => $this->shop_name ?? null,
            'shop_url' => $this->shop_url ?? $shop->shop_url ?? null,
            //
            'pin_no' => $this->pin_no ?? null,
            'pin_subject' => $this->pin_subject ?? null,
            'pin_url' => $pin_url ?? null,
            //
            'pin_category_name' => $pin->category_name ?? null,
            //
            'sale_quantity' => $pin->sale_quantity ?? null,
            'agent_count' => $pin->agent_count ?? null,
            'thirty_count' => $pin->thirty_count ?? null,
            'evaluate_count' => $pin->evaluate_count ?? null,
            //
            'keywords' => isset($this->keywords) ? $this->keywords()->select('uuid', 'word_no', 'word')->get() : null,
            //
            'score_time' => $this->score_time ?? null,
            'confirm_time' => $this->confirm_time ?? null,
            'publish_time' => $this->publish_time ?? null,
            //
            'amount_sale' => $this->amount_sale ?? 0,
        ]);
    }
}
