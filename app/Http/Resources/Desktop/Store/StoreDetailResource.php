<?php

namespace App\Http\Resources\Desktop\Store;

use App\Http\Resources\Admin\Seller\SellerInfoResource;
use App\Http\Resources\Admin\Ship\ShipExpressResource;
use App\Http\Resources\Admin\Ship\ShipPickupResource;
use App\Http\Resources\Admin\Store\StoreCatResource;
use App\Http\Resources\Advocate\Customer\CustomerResource;
use App\Http\Resources\Advocate\Rice\RiceResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * StoreDetailResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class StoreDetailResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'uuid' => $this->uuid ?? null,
            'lattice_uuid' => $this->lattice->uuid ?? null,
            'store_name' => $this->store_name ?? null,
            'store_logo' => $this->store_logo ?? null,
            'store_photos' => $this->store_photos ?? null,
            //
            'stall_number' => $this->stall_number ?? null,
            //
            'english_name' => $this->english_name ?? null,
            'short_name' => $this->short_name ?? null,
            'introduction' => $this->introduction ?? null,
            //
            'customer' => isset($this->customer) ? new CustomerResource($this->customer) : null, //  CustomerResource 实例
            'info' => isset($this->seller_info) ? new SellerInfoResource($this->seller_info) : null,
            //
            'license_merchant_name' => isset($this->seller_info) ? $this->seller_info->license_merchant_name : null,
            'license_legal_representative' => isset($this->seller_info) ? $this->seller_info->license_legal_representative : null,
            //
            'wechat_nickname' => isset($this->user) ? $this->user->nickname : null,
            'wechat_img_url' => isset($this->user) ? $this->user->head_img_url : null,
            //
            'wechat_merchant_name' => isset($this->seller_info) ? $this->seller_info->license_merchant_name : null,
            'wechat_name' => isset($this->seller_info) ? $this->seller_info->license_legal_representative : null,
            //
            'market_names' => $this->markets()->select('market_name')->pluck('market_name')->toArray() ?? null,
            //
            'market_ids' => $this->markets()->select('id')->pluck('id')->toArray(),
            'category_ids' => $this->categories()->select('id')->pluck('id')->toArray(),
            'camp_ids' => $this->camps()->select('id')->pluck('id')->toArray(),
            //
            'mobile' => $this->mobile ?? null,
            'mobile_backup' => $this->mobile_backup ?? null,
            'wechat' => $this->wechat ?? null,
            'email' => $this->email ?? null,
            'qq' => $this->qq ?? null,
            //
            'cats' => $this->store_cats ? StoreCatResource::collection($this->store_cats) : null,
            'pickups' => $this->ship_pickups ? ShipPickupResource::collection($this->ship_pickups) : null,
            'expresses' => $this->shipment_expresses ? ShipExpressResource::collection($this->shipment_expresses) : null,
            //
            'launch_label' => $this->is_launch->label ?? null,
            'is_launch' => $this->is_launch->key() ?? null,
            //
            'edit_label' => $this->is_edit->label ?? null,
            'is_edit' => $this->is_edit->key() ?? null,
            //
            'store_rices' => $this->rices()->exists() ? RiceResource::collection($this->rices()->orderBy('id', 'desc')->paginate(25))->resource : [],
        ]);
    }

}
