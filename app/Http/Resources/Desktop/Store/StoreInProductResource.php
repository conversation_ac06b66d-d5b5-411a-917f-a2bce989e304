<?php

namespace App\Http\Resources\Desktop\Store;

use App\Http\Resources\Desktop\Product\ProductResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * StoreInProductResource 资源类
 * 此类用于将单个模型实例转换为 JSON 响应。
 * 资源类可以对模型数据进行转换和格式化，使其更适合 API 响应。
 */
class StoreInProductResource extends JsonResource
{
    /**
     * 将资源转换为数组
     * 定义如何将模型实例转换为数组形式。
     * 可以选择性地包含或排除模型的属性，并添加额外的计算属性。
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        //
        return array_filter([
            'store_uuid' => $this->uuid ?? null,
            'site_id' => $this->site_id ?? null,
            'store_code' => $this->store_code ?? null,
            'store_name' => $this->store_name ?? null,
            'store_logo' => $this->store_logo ?? null,
            'stall_number' => $this->stall_number ?? null,
            'logo_url' => $this->logo_url ?? null,
            //
            'start_time' => $this->start_time ?? null,
            'end_time' => $this->end_time ?? null,
            //
            //'expresses'       => empty($this->shipment_expresses) ? null : ShipmentExpressResource::collection($this->shipment_expresses),
            'products' => isset($this->products) ? ProductResource::collection($this->products()->where('platform_id', '=', config('app.platform_id'))->inRandomOrder()->limit(3)->get())->resource : null,
            'express_uuid' => $express_default->uuid ?? null,
            'express_default' => $express_default->uuid ?? null,
            //
            'is_provider' => $this->is_provider,
            'is_supplier' => $this->is_supplier,
            //
            'is_delivery' => $this->is_delivery,
            'is_spread' => $this->is_spread,
        ]);
    }

}
