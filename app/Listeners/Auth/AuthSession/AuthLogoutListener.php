<?php

namespace App\Listeners\Auth\AuthSession;

use Illuminate\Auth\Events\Logout;
use Illuminate\Http\Request;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;
use Rappasoft\LaravelAuthenticationLog\Traits\AuthenticationLoggable;

class AuthLogoutListener
{
    // 定义一个Request属性，用于存储当前的HTTP请求实例
    public Request $request;

    // 构造函数，初始化Request属性
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    // 事件处理方法，接收一个事件实例
    public function handle($event): void
    {
        // 获取配置文件中的登出事件类，如果没有设置，则默认为Illuminate\Auth\Events\Logout
        $listener = config('authentication-log.events.logout', Logout::class);

        // 检查事件实例是否为登出事件的实例，如果不是则直接返回
        if (!$event instanceof $listener) {
            return;
        }

        // 如果事件中有用户对象
        if ($event->user) {
            // 检查用户类是否使用了AuthenticationLoggable trait，如果没有使用则直接返回
            if (!in_array(AuthenticationLoggable::class, class_uses_recursive(get_class($event->user)))) {
                return;
            }

            $user = $event->user; // 获取用户对象

            // 获取用户的IP地址，如果配置文件中设置了behind_cdn则从特定的HTTP头字段中获取IP地址
            if (config('authentication-log.behind_cdn')) {
                $ip = $this->request->server(config('authentication-log.behind_cdn.http_header_field'));
            } else {
                $ip = $this->request->ip();
            }

            $userAgent = $this->request->userAgent(); // 获取用户的浏览器信息
            // 查询最后一次成功登录的认证日志
            $log = $user->authentications()->whereIpAddress($ip)->whereUserAgent($userAgent)->orderByDesc('login_at')->first();

            // 如果没有找到对应的日志，则创建一个新的认证日志实例
            if (!$log) {
                //
                $log = new AuthenticationLog([
                    'ip_address' => $ip,
                    'user_agent' => $userAgent,
                ]);
            }
            // 设置登出时间
            $log->logout_at = now();
            // 保存认证日志
            $user->authentications()->save($log);
        }
    }
}
