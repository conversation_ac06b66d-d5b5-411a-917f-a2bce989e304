<?php

namespace App\Mail\Platform\User;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UserAuthNewDeviceMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     * 创建一个新的消息实例
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the message envelope.
     * 拿邮件信封
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'User Auth New Device Mail',
        );
    }

    /**
     * Get the message content definition.
     * 获取邮件内容定义
     */
    public function content(): Content
    {
        return new Content(
            view: 'view.name',
        );
    }

    /**
     * Get the attachments for the message.
     * 获取邮件的附件
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
