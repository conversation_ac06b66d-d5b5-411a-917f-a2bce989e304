<?php

namespace App\Models\Ali1688OrderCenter;

use App\Enums\Ali1688\Ali1688OrderStatusEnum;
use App\Enums\Id\SourceIdEnum;
use App\Enums\State\DeliveryStateEnum;
use App\Enums\State\OrderStateEnum;
use App\Models\RetailCenter\Retail;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ali1688Order extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'ali1688_order_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'guarantees_terms' => 'array',
        //        'native_logistics'      => 'array',
        //        'product_items'         => 'array',
        //        'trade_terms'           => 'array',
        //        'ext_attributes'        => 'array',
        //        'order_rate_info'       => 'array',
        'order_invoice_info' => 'array',
        'customs' => 'array',
        'overseas_extra_address' => 'array',
        //        'buyer_contact'         => 'array',
        'receiver_info' => 'array',
        'step_order_list' => 'array',
        //        'seller_contact'        => 'array',
        'new_step_order_list' => 'array',
        //        'order_biz_info'        => 'array',
        'quote_list' => 'array',
        'encrypt_out_order_info' => 'array',
        'pay_channel_list' => 'array',
        'pay_channel_code_list' => 'array',
        'print_data' => 'array',
        'source_id' => SourceIdEnum::class,
        'buyer_contact' => 'array',
        'seller_contact' => 'array',
        'native_logistics' => 'array',
        'product_items' => 'array',
        'trade_terms' => 'array',
        'ext_attributes' => 'array',
        'order_rate_info' => 'array',
        'order_biz_info' => 'array',
        'ali1688_item_status' => Ali1688OrderStatusEnum::class,
        'trade_state' => OrderStateEnum::class,
        'delivery_state' => DeliveryStateEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'lattices.lattice_name' => 10,
            'lattices.english_name' => 10,
            'lattices.stall_name' => 10,
        ]
    ];

    /**
     * 获取格子的统计数据
     * @return array
     */
    public static function getAli1688DeliveryCount(): array
    {
        // 定义状态值到返回键名的映射
        $Statuses = [
            'match_count' => DeliveryStateEnum::match()->value,
            'print_count' => DeliveryStateEnum::print()->value,
            'shipment_count' => DeliveryStateEnum::shipment()->value,
            'done_count' => DeliveryStateEnum::done()->value,
            'cancelled_count' => DeliveryStateEnum::cancelled()->value,
        ];
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('delivery_state', $Statuses);
    }

    /**
     * 获取格子的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $Statuses = [
            'pending_count' => OrderStateEnum::wait()->value,
            'confirm_count' => OrderStateEnum::confirm()->value,
            'work_count' => OrderStateEnum::work()->value,
            'picker_count' => OrderStateEnum::picker()->value,
            'take_count' => OrderStateEnum::take()->value,
            'courier_count' => OrderStateEnum::courier()->value,
            'shipment_count' => OrderStateEnum::shipment()->value,
            'transport_count' => OrderStateEnum::transport()->value,
            'distribution_count' => OrderStateEnum::distribution()->value,
            'halt_count' => OrderStateEnum::halt()->value,
            'done_count' => OrderStateEnum::done()->value,
            //
            'lock_count' => OrderStateEnum::lock()->value,
            'cancel_count' => OrderStateEnum::cancel()->value,
            'refund_count' => OrderStateEnum::refund()->value,
            'settlement_count' => OrderStateEnum::settlement()->value,
            'exception_count' => OrderStateEnum::exception()->value,
        ];
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('order_state', $Statuses);
    }

    protected static function values(): array
    {
        return [
            'default' => 0, // 默认状态
            'confirm_count' => 1, // 用户下单
            'work_count' => 2, // 商家处理
            'picker_count' => 3, // 商家拣货
            'take_count' => 4, // 商家出货
            'courier_count' => 5, // 商家发货
            'shipment_count' => 6, // 物流运输
            'done_count' => 7, // 物流配送
            'lock_count' => 8, // 账单结算
            'cancel_count' => 9, // 订单异常
            //
            'wait_count' => 9, // 订单异常
            'refund_count' => 9, // 订单异常
        ];
    }

    /**
     * @return BelongsTo
     */
    public function retail()
    {
        //
        return $this->belongsTo(Retail::class, 'retail_id', 'id');
    }

    public function ali1688OrderInfo()
    {
        //
        return $this->hasOne(Ali1688OrderInfo::class, 'ali1688_order_id');
    }


    public function subs()
    {
        //
        return $this->hasMany(Ali1688OrderSub::class, 'ali1688_order_id');
    }

    public function items()
    {
        //
        return $this->hasMany(Ali1688OrderItem::class, 'ali1688_order_id');
    }

    public function ali1688OrderTerms()
    {
        //
        return $this->hasMany(Ali1688OrderTerm::class, 'ali1688_order_id');
    }

    public function ali1688NativeLogistic()
    {
        //
        return $this->hasOne(Ali1688OrderNativeLogistic::class, 'ali1688_order_id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
