<?php

namespace App\Models\CopyrightCenter;

use App\Enums\Type\DisputeTypeEnum;
use App\Models\ProduceBrace\ProduceProperty;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasSearchableTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Copyright extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasSearchableTrait;
    use GeneratesUuid;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 自动翻译
     */
    public $translatable = ['alias_names'];
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'copyright_center';
    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'dispute_type' => DisputeTypeEnum::class,
    ];

    /**
     * Searchable rules.
     * @var array
     */
    protected $searchable = [
        /**
         * Columns and their priority in search results.
         * Columns with higher values are more important.
         * Columns with equal values have equal importance.
         * @var array
         */
        'columns' => [
            'copyrights.copyright_name' => 30,
            'copyrights.copyright_english_name' => 20,
            'copyrights.copyright_description' => 10,
        ],
    ];

    /**
     * @return BelongsTo
     */
    public function property()
    {
        //
        return $this->belongsTo(ProduceProperty::class, 'property_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function agent()
    {
        //
        return $this->belongsTo(CopyrightAgent::class, 'agent_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function holder()
    {
        //
        return $this->belongsTo(CopyrightHolder::class, 'holder_id', 'id');
    }
}
