<?php

namespace App\Models\IndustryBrace;

use App\Models\Ali1688Center\Ali1688Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\ProduceBrace\ProduceGroup;
use App\Models\ProduceBrace\ProduceSchema;
use App\Models\SiteBrace\Site;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kalnoy\Nestedset\NodeTrait;

class Category extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use NodeTrait;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'industry_brace';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @var array
     */
    public static function getCateGoryTree()
    {
        //
        $CateGoryTree = static::where('platform_id', '=', config('app.platform_id'))->where('is_state', '=', 1)->orderBy('sort')->get()->toTree();
        //
        return $CateGoryTree;
    }

    /**
     * @return HasOne
     */
    public function ali1688_category()
    {
        //
        return $this->hasOne(Ali1688Category::class, 'category_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function parent()
    {
        //
        return $this->belongsTo(Category::class);
    }

    /**
     * @return HasMany
     */
    public function groups()
    {
        //
        return $this->hasMany(ProduceGroup::class, 'category_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function schemas()
    {
        //
        return $this->hasMany(ProduceSchema::class, 'category_id', 'id');
    }

    /**
     * @return BelongsToMany
     */
    public function keywords()
    {
        //
        return $this->belongsToMany(Keyword::class, 'industry_brace.category_by_keyword')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function industries()
    {
        //
        return $this->belongsToMany(Industry::class, 'industry_brace.category_by_industry')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function cat_stores()
    {
        //
        return $this->belongsToMany(Store::class, 'store_center.store_by_category')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function camp_stores()
    {
        //
        return $this->belongsToMany(Store::class, 'store_center.store_by_camp')->withTimestamps();
    }

    /**
     * 定义与 Site 的一对多关系
     * * @return BelongsTo
     **/
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

