<?php

namespace App\Models\KeywordCenter;

use App\Enums\State\WordStateEnum;
use App\Models\PinCenter\Pin;
use App\Models\ProductCenter\Product;
use App\Models\RiceCenter\Rice;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Keyword extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'keyword_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'keywords.id' => 10,
            'keywords.word' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getKeywordCount(): array
    {
        // 定义状态值到返回键名的映射
        $wordStatus = [
            'default_count' => WordStateEnum::default()->value,
            'priority_count' => WordStateEnum::priority()->value,
            'recommended_count' => WordStateEnum::recommended()->value,
            'incorrect_count' => WordStateEnum::incorrect()->value,
        ];
        //
        //dd($wordStatus);
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('word_state', $wordStatus);
    }

    /**
     * @return BelongsToMany
     */
    public function rices()
    {
        //
        return $this->belongsToMany(Rice::class, 'rice_center.rice_by_keyword'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function pins()
    {
        //
        return $this->belongsToMany(Pin::class, 'pin_center.pin_by_keyword'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function products()
    {
        //
        return $this->belongsToMany(Product::class, 'product_center.product_by_keyword'); // 定义多对多关系
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

