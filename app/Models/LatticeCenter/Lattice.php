<?php

namespace App\Models\LatticeCenter;

use App\Enums\State\LatticeStateEnum;
use App\Models\IndustryBrace\Category;
use App\Models\SiteBrace\Site;
use App\Models\SiteBrace\SiteMarket;
use App\Models\StoreCenter\Store;
use App\Models\UserCenter\User;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lattice extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 定义哪些字段是可排序的。
     * @var array
     */
    public $sortable = ['created_at', 'updated_at'];
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'lattice_center';
    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';
    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];
    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'lattices.lattice_name' => 10,
            'lattices.english_name' => 10,
            'lattices.stall_name' => 10,
        ]
    ];

    /**
     * 获取格子的统计数据
     * @return array
     */
    public static function getLatticeCount(): array
    {
        // 定义状态值到返回键名的映射
        $latticeStatuses = [
            'open_count' => LatticeStateEnum::open()->value,
            'leased_count' => LatticeStateEnum::leased()->value,
            'online_count' => LatticeStateEnum::online()->value,
            'closed_count' => LatticeStateEnum::closed()->value,
        ];

        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('lattice_state', $latticeStatuses);
    }

    /**
     * @return BelongsToMany
     */
    public function markets()
    {
        //
        return $this->belongsToMany(SiteMarket::class, 'lattice_center.lattice_by_site_market')->withTimestamps();
    }

    /**
     * 定义与 Category 的多对多关系
     * @return BelongsToMany
     */
    public function categories()
    {
        //
        return $this->belongsToMany(Category::class, 'lattice_center.lattice_by_category')->withTimestamps();
    }

    /**
     * 定义与 Camp 的多对多关系
     * @return BelongsToMany
     */
    public function camps()
    {
        //
        return $this->belongsToMany(Category::class, 'lattice_center.lattice_by_camp')->withTimestamps();
    }

    /**
     * 定义与 Site 的一对多关系
     * @return BelongsTo
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * 定义与 User 的一对多关系
     * @return BelongsTo
     */
    public function user()
    {
        //
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 定义与 Store 的一对一关系
     * @return HasOne
     */
    public function store()
    {
        //
        return $this->hasOne(Store::class, 'lattice_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

