<?php

namespace App\Models\MoldCenter;

use App\Enums\Is\IsDefaultEnum;
use App\Enums\Type\AutomationTypeEnum;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Mold extends Model
{
    use GeneratesUuid, HasFactory, SoftDeletes, DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'mold_center';

    /**
     * 使用默认 UUID 生成器
     * @var string
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'automation_type' => AutomationTypeEnum::class,
        'is_default' => IsDefaultEnum::class,
    ];

    /**
     * 获取标签名称的访问器
     * 假设你有一个标签类型映射
     * @return string
     */
    public function getLabelNameAttribute(): string
    {
        $labels = [
            0 => '默认',
            1 => '促销',
            2 => '新品',
            // 添加更多标签类型
        ];

        return $labels[$this->label_type] ?? '未知';
    }

    /**
     * 获取标签颜色的访问器
     * 假设你有一个标签颜色映射
     * @return string
     */
    public function getLabelColorAttribute(): string
    {
        $colors = [
            0 => 'blue',
            1 => 'red',
            2 => 'green',
            // 添加更多标签类型对应的颜色
        ];

        return $colors[$this->label_type] ?? 'gray';
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @return $this
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
