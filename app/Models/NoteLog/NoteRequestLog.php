<?php

namespace App\Models\NoteLog;

use App\Models\Team\Team;
use App\Traits\System\Date\DateSerializationTrait;
use Bilfeldt\RequestLogger\Contracts\RequestLoggerInterface;
use Bilfeldt\RequestLogger\Database\Factories\RequestLogFactory;
use Bilfeldt\RequestLogger\RequestLoggerFacade;
use Carbon\Carbon;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Illuminate\View\View;

class NoteRequestLog extends Model implements RequestLoggerInterface
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;
    use MassPrunable;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'note_system';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'ip',
        'session',
        'middleware',
        'status',
        'method',
        'route',
        'path',
        'headers',
        'payload',
        'response_headers',
        'response_body',
        'duration',
        'memory',
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'middleware' => 'json',
        'headers' => 'json',
        'payload' => 'json',
        'response_headers' => 'json',
        'response_body' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    //======================================================================
    // ACCESSORS
    //======================================================================

    //======================================================================
    // MUTATORS
    //======================================================================

    //======================================================================
    // SCOPES
    //======================================================================

    //======================================================================
    // RELATIONS
    //======================================================================

    protected static function newFactory()
    {
        //
        return RequestLogFactory::new();
    }

    /**
     * Get the prunable model query.
     * @return Builder
     */
    public function prunable()
    {
        //
        return static::where('created_at', '<=', Date::now()->subDays(config('request-logger.drivers.model.prune')));
    }

    //======================================================================
    // METHODS
    //======================================================================

    /** @inerhitDoc */
    public function log(Request $request, $response, ?int $duration = null, ?int $memory = null): void
    {
        //
        $model = new static();
        $model->uuid = $request->getUniqueId();
        $model->ip = request()->ip;
        $model->session = $request->hasSession() ? $request->session()->getId() : null;
        $model->middleware = array_values(optional($request->route())->gatherMiddleware() ?? []);
        $model->method = $request->getMethod();
        $model->route = optional($request->route())->getName() ?? optional($request->route())->uri(); // Note that $request->route()->uri() does not replace the placeholders while $request->getRequestUri() replaces the placeholders
        $model->path = $request->path();
        $model->status = $response->getStatusCode();
        $model->headers = $this->getFiltered($request->headers->all()) ?: null;
        $model->payload = $this->getFiltered($request->input()) ?: null;
        $model->response_headers = $this->getFiltered($response->headers->all()) ?: null;
        $model->response_body = $this->getLoggableResponseContent($response);
        $model->duration = $duration;
        $model->memory = round($memory / 1024 / 1024, 2); // [MB]

        //
        if ($user = $request->user()) {
            //
            $model->user()->associate($user);
            $model->user_id = $user->id;
        }
        //
        if ($team = $this->getRequestTeam($request)) {
            //
            $model->team()->associate($team);
        }
        //
        $model->save();
    }

    protected function getFiltered(array $data)
    {
        //
        return $this->replaceParameters($data, RequestLoggerFacade::getFilters());
    }

    protected function replaceParameters(array $array, array $hidden, string $value = '********'): array
    {
        //
        foreach ($hidden as $parameter) {
            if (Arr::get($array, $parameter)) {
                Arr::set($array, $parameter, '********');
            }
        }
        //
        return $array;
    }

    protected function getLoggableResponseContent(\Symfony\Component\HttpFoundation\Response $response): array
    {
        $content = $response->getContent();

        if (is_string($content)) {
            //
            if (is_array(json_decode($content, true)) && json_last_error() === JSON_ERROR_NONE) {
                //
                return $this->contentWithinLimits($content) ? $this->getFiltered(json_decode($content, true)) : ['Purged By bilfeldt/laravel-request-logger'];
            }

            if (Str::startsWith(strtolower($response->headers->get('Content-Type')), 'text/plain')) {
                //
                return $this->contentWithinLimits($content) ? [$content] : ['purge' => 'bilfeldt/laravel-request-logger'];
            }
        }

        if ($response instanceof RedirectResponse) {
            //
            return ['redirect' => $response->getTargetUrl()];
        }

        if ($response instanceof Response && $response->getOriginalContent() instanceof View) {
            return [
                'view' => $response->getOriginalContent()->getPath(),
                //'data' => $this->extractDataFromView($response->getOriginalContent()),
            ];
        }

        return ['html' => 'non-json'];
    }

    protected function contentWithinLimits(string $content): bool
    {
        //
        return intdiv(mb_strlen($content), 1000) <= 64;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(config('auth.providers.users.model'));
    }

    protected function getRequestTeam(Request $request): ?Model
    {
        //
        if ($request->route('team') instanceof Model) {
            //
            return $request->route('team');
        }

        if ($user = $request->user()) {
            //
            return method_exists($user, 'currentTeam') ? $user->currentTeam : null;
        }

        return null;
    }

    public function team(): BelongsTo
    {
        //
        return $this->belongsTo(Team::class);
    }

    public function aggregate(Request $request, $response, Carbon $date): void
    {
        // There should be a compound index with all these fields for performance
        // This is required to prevent updating detailed records
        //(string) $date,
        static::firstOrNew(['uuid' => null, 'date' => null, 'user_id' => optional($request->user())->getKey(), 'team_id' => $this->getRequestTeam($request), 'ip' => request()->ip, 'method' => $request->getMethod(), 'route' => $request->route()->getName(), 'status' => $response->getStatusCode(),], ['counter' => 0,])->increment('counter')->save();
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->client_ip = request()->ip();
        return $this;
    }
}

