<?php

namespace App\Models\PinCenter;

use App\Enums\State\PinStateEnum;
use App\Models\PegCenter\Peg;
use App\Models\ProductCenter\Product;
use App\Models\RiceCenter\Rice;
use App\Models\ShopCenter\Shop;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use DateTime;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Pin extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * Specify the amount of time to cache queries.
     * Do not specify or set it to null to disable caching.
     * @var int|DateTime
     */
    protected $cacheCooldownSeconds = 3600; // 5 minutes

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'pin_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'pin_words' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'pins.pin_no' => 10,
            'pins.pin_name' => 10,
            'pins.pin_subject' => 10,
            'pins.pin_title' => 10,
            'pins.category_name' => 10,
            'pins.brief' => 10,
            'shop_center.shops.shop_no' => 10,
            'shop_center.shops.company_name' => 10,
            'shop_center.shops.company_address' => 10,
            'shop_center.shops.shop_code' => 10,
            'shop_center.shops.shop_enterprise' => 10,
            'shop_center.shops.shop_name' => 10,
            'shop_center.shops.shop_domain' => 10,
        ],
        'joins' => [
            'shop_center.shops' => ['pins.id', 'shops.shop_id'], // 使用别名
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        //
        // 动态构建 ['new_count' => 0, 'active_count' => 1, ...]
        $pinStatus = [
            'new_count' => PinStateEnum::new()->value,
            'machine_count' => PinStateEnum::machine()->value,
            'active_count' => PinStateEnum::active()->value,
            'filter_count' => PinStateEnum::filter()->value,
            'wait_count' => PinStateEnum::wait()->value,
            'archived_count' => PinStateEnum::archived()->value,
            'collect_count' => PinStateEnum::collect()->value,
            'dispense_count' => PinStateEnum::dispense()->value,
            'optimize_count' => PinStateEnum::optimize()->value,
            'review_count' => PinStateEnum::review()->value,
            'craw_count' => PinStateEnum::craw()->value,
            'mate_count' => PinStateEnum::mate()->value,
            'range_count' => PinStateEnum::range()->value,
            'ready_count' => PinStateEnum::ready()->value,
            'place_count' => PinStateEnum::place()->value,
            'shelf_count' => PinStateEnum::shelf()->value,
            'expire_count' => PinStateEnum::expire()->value,
            'remove_count' => PinStateEnum::remove()->value,
        ];
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('pin_state', $pinStatus);
    }

    /**
     * @return BelongsTo
     */
    public function shop()
    {
        //
        return $this->belongsTo(Shop::class, 'shop_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function store()
    {
        //
        return $this->belongsTo(Store::class, 'store_id');
    }

    /**
     * @return BelongsTo
     */
    public function product()
    {
        //
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * @return HasMany
     */
    public function rice()
    {
        //
        return $this->hasMany(Rice::class, 'pin_id'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function rices()
    {
        //
        return $this->belongsToMany(Rice::class, 'pin_center.pin_by_rice')->withTimestamps(); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function pins()
    {
        //
        return $this->belongsToMany(Pin::class, 'pin_center.pin_by_pin', 'related_id', 'pin_id')->withTimestamps(); // 定义多对多关系
    }


    /**
     * @return BelongsToMany
     */
    public function shops()
    {
        //
        return $this->belongsToMany(Shop::class, 'pin_center.pin_by_shop')->withTimestamps(); // 定义多对多关系
    }


    /**
     * @return BelongsToMany
     */
    public function stores()
    {
        //
        return $this->belongsToMany(Store::class, 'pin_center.pin_by_store')->withTimestamps(); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function products()
    {
        //
        return $this->belongsToMany(Product::class, 'pin_center.pin_by_product')->withTimestamps(); // 定义多对多关系
    }


    /**
     * @return BelongsToMany
     */
    public function pegs()
    {
        //
        return $this->belongsToMany(Peg::class, 'peg_center.peg_by_pin')->withTimestamps(); // 定义多对多关系
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
