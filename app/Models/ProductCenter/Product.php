<?php

namespace App\Models\ProductCenter;

use App\Casts\Amount\AmountToHundred;
use App\Enums\Is\IsCheckEnum;
use App\Enums\Is\IsClosureEnum;
use App\Enums\Is\IsEditEnum;
use App\Enums\Is\IsOfflineEnum;
use App\Enums\Method\DeliveryMethodEnum;
use App\Enums\Method\GETMethodEnum;
use App\Enums\State\ProductStateEnum;
use App\Enums\Type\ActivityTypeEnum;
use App\Enums\Type\BusinessTypeEnum;
use App\Enums\Type\DelayTypeEnum;
use App\Enums\Type\DiyTypeEnum;
use App\Enums\Type\HandTypeEnum;
use App\Enums\Type\KindTypeEnum;
use App\Enums\Type\LimitTypeEnum;
use App\Enums\Type\PolicyTypeEnum;
use App\Enums\Type\PreTypeEnum;
use App\Enums\Type\PurchaseTypeEnum;
use App\Enums\Type\RangeTypeEnum;
use App\Enums\Type\RebateTypeEnum;
use App\Enums\Type\RetailTypeEnum;
use App\Enums\Type\SaleTypeEnum;
use App\Enums\Type\SceneTypeEnum;
use App\Enums\Type\SkuTypeEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Enums\Vest\SourceVestEnum;
use App\Models\CopyrightCenter\Copyright;
use App\Models\IndustryBrace\Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\ProduceBrace\ProduceAccessory;
use App\Models\ProduceBrace\ProduceCraft;
use App\Models\ProduceBrace\ProduceDesign;
use App\Models\ProduceBrace\ProduceFabric;
use App\Models\ProduceBrace\ProduceLevel;
use App\Models\ProduceBrace\ProducePurpose;
use App\Models\ProduceBrace\ProduceSchema;
use App\Models\ProduceBrace\ProduceShade;
use App\Models\ProduceBrace\ProduceSpec;
use App\Models\ProduceBrace\ProduceSuit;
use App\Models\ProduceBrace\ProduceTrend;
use App\Models\ProductDraft\ProductDraft;
use App\Models\SiteBrace\Site;
use App\Models\SiteBrace\SiteMarket;
use App\Models\StoreCenter\Store;
use App\Models\StoreCenter\StoreCat;
use App\Models\StoreCenter\StoreClass;
use App\Models\ThemeCenter\Theme;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

// 添加别名

/**
 * 产品模型类
 * 该类表示产品的模型，包含产品的属性、关系和方法。
 */
class Product extends Model
{
    use GeneratesUuid;
    use HasTranslations;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 可翻译的属性
     * @var array
     */
    public $translatable = ['alias_names'];
    /**
     * 定义哪些字段是可排序的。
     * @var array
     */
    public $sortable = ['created_at', 'updated_at'];
    /**
     * 设置当前模型使用的数据库连接名
     * @var string
     */
    protected $connection = 'product_center';
    /**
     * 使用有序的 UUID 版本
     * @var string
     */
    protected $uuidVersion = 'ordered';
    /**
     * 不可批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];
    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];
    /**
     * 属性类型转换
     * @var array<string, string>
     */
    protected $casts = [
        'min_price' => AmountToHundred::class,
        'max_price' => AmountToHundred::class,
        'media_images' => 'array',
        'media_descries' => 'array',
        'media_images_ids' => 'array',
        'media_desc_ids' => 'array',
        'params' => 'array',
        'draft_data' => 'array',
        'attributes' => 'array',
        'theme_attributes' => 'array',
        'draft_skus' => 'array',
        'keywords' => 'array',
        'themes' => 'array',
        'suits' => 'array',
        'cats' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'pre_type' => PreTypeEnum::class,
        'purchase_type' => PurchaseTypeEnum::class,
        'diy_type' => DiyTypeEnum::class,
        'activity_type' => ActivityTypeEnum::class,
        'scene_type' => SceneTypeEnum::class,
        'kind_type' => KindTypeEnum::class,
        'business_type' => BusinessTypeEnum::class,
        'rebate_type' => RebateTypeEnum::class,
        'sku_type' => SkuTypeEnum::class,
        'range_type' => RangeTypeEnum::class,
        'delay_type' => DelayTypeEnum::class,
        'hand_type' => HandTypeEnum::class,
        'policy_type' => PolicyTypeEnum::class,
        'limit_type' => LimitTypeEnum::class,
        'sale_type' => SaleTypeEnum::class,
        'retail_type' => RetailTypeEnum::class,
        'delivery_method' => DeliveryMethodEnum::class,
        'get_method' => GETMethodEnum::class,
        //
        'source_vest' => SourceVestEnum::class,
        'seller_vest' => OwnerVestEnum::class,
        'product_vest' => OwnerVestEnum::class,
        'provision_vest' => OwnerVestEnum::class,
        'delivery_vest' => OwnerVestEnum::class,
        //
        'is_edit' => IsEditEnum::class,
        'is_check' => IsCheckEnum::class,
        //
        'is_offline' => IsOfflineEnum::class,
        'is_closure' => IsClosureEnum::class,
        //
        'product_state' => ProductStateEnum::class,
    ];

    /**
     * 可搜索的字段和权重
     * @var array
     */
    protected $searchable = [
        'columns' => [
            // 产品表字段
            'products.product_title' => 10,
            'products.product_no' => 10,
            'products.product_english_title' => 10,
            'products.product_name' => 10,
            // SKU 表字段
            'product_skus.sku_sn' => 10,
            'product_skus.sku_name' => 10,
            'product_skus.suit_attr' => 10,
            'product_skus.spec_attr' => 10,
            'product_skus.custom_attr' => 10,
            // 关键词表字段
            'keywords.word' => 10,
            // 主题表字段
            'themes.theme_name' => 10,
            // 新增的关联表字段
            'categories.category_name' => 10,
            'produce_suits.suit_name' => 10,
            'produce_specs.spec_name' => 10,
            'produce_fabrics.fabric_name' => 10,
            'produce_designs.design_name' => 10,
            'produce_trends.trend_name' => 10,
            'produce_crafts.craft_name' => 10,
            'produce_shades.shade_name' => 10,
            'produce_purposes.purpose_name' => 10,
            'produce_accessories.accessory_name' => 10,
        ],
        'joins' => [
            // 已有的联接
            'product_skus' => ['products.id', 'product_skus.product_id'],
            'product_by_keyword' => ['products.id', 'product_by_keyword.product_id'],
            'keyword_center.keywords' => ['product_by_keyword.keyword_id', 'keyword_center.keywords.id'],
            'product_by_theme' => ['products.id', 'product_by_theme.product_id'],
            'theme_center.themes' => ['product_by_theme.theme_id', 'theme_center.themes.id'],
            // 新增的联接
            'product_by_category' => ['products.id', 'product_by_category.product_id'],
            'industry_brace.categories' => ['product_by_category.category_id', 'categories.id'],

            'product_by_produce_suit' => ['products.id', 'product_by_produce_suit.product_id'],
            'produce_brace.produce_suits' => ['product_by_produce_suit.produce_suit_id', 'produce_suits.id'],

            'product_by_produce_spec' => ['products.id', 'product_by_produce_spec.product_id'],
            'produce_brace.produce_specs' => ['product_by_produce_spec.produce_spec_id', 'produce_specs.id'],

            'product_by_produce_fabric' => ['products.id', 'product_by_produce_fabric.product_id'],
            'produce_brace.produce_fabrics' => ['product_by_produce_fabric.produce_fabric_id', 'produce_fabrics.id'],

            'product_by_produce_design' => ['products.id', 'product_by_produce_design.product_id'],
            'produce_brace.produce_designs' => ['product_by_produce_design.produce_design_id', 'produce_designs.id'],

            'product_by_produce_trend' => ['products.id', 'product_by_produce_trend.product_id'],
            'produce_brace.produce_trends' => ['product_by_produce_trend.produce_trend_id', 'produce_trends.id'],

            'product_by_produce_craft' => ['products.id', 'product_by_produce_craft.product_id'],
            'produce_brace.produce_crafts' => ['product_by_produce_craft.produce_craft_id', 'produce_crafts.id'],

            'product_by_produce_shade' => ['products.id', 'product_by_produce_shade.product_id'],
            'produce_brace.produce_shades' => ['product_by_produce_shade.produce_shade_id', 'produce_shades.id'],

            'product_by_produce_purpose' => ['products.id', 'product_by_produce_purpose.product_id'],
            'produce_brace.produce_purposes' => ['product_by_produce_purpose.produce_purpose_id', 'produce_purposes.id'],

            'product_by_produce_accessory' => ['products.id', 'product_by_produce_accessory.product_id'],
            'produce_brace.produce_accessories' => ['product_by_produce_accessory.produce_accessory_id', 'produce_accessories.id'],
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $productStatuses = [
            'online_count' => ProductStateEnum::online()->value,
            'release_count' => ProductStateEnum::release()->value,
            'unavailable_count' => ProductStateEnum::unavailable()->value,
            'discontinued_count' => ProductStateEnum::discontinued()->value,
            'lock_count' => ProductStateEnum::frozen()->value,
        ];
        //
        //dd($productStatuses);
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('product_state', $productStatuses);
    }

    /**
     * 模型的引导方法
     * 注册模型观察者等操作
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        // 为 Product 模型注册观察者
        //self::observe(ProductObserver::class);
    }

    /**
     * 获取产品所属的站点
     * @return BelongsTo 返回站点关联
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * 获取产品所属的店铺
     * @return BelongsTo 返回店铺关联
     */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    /**
     * 获取产品所属的店铺分类
     * @return BelongsToMany 返回店铺分类关联
     */
    public function cats(): BelongsToMany
    {
        return $this->belongsToMany(StoreCat::class, 'product_center.product_by_store_cat')->withTimestamps();
    }

    /**
     * 获取产品所属的演示数据
     * @return BelongsTo 返回演示关联
     */
    public function draft(): BelongsTo
    {
        return $this->belongsTo(ProductDraft::class, 'draft_id', 'id');
    }

    /**
     * 获取产品的信用信息
     * @return HasOne 返回信用关联
     */
    public function credit(): HasOne
    {
        return $this->hasOne(ProductCredit::class, 'product_id', 'id');
    }

    /**
     * 获取产品所属的生产模式
     * @return BelongsTo 返回生产模式关联
     */
    public function schema(): BelongsTo
    {
        return $this->belongsTo(ProduceSchema::class, 'schema_id', 'id');
    }

    /**
     * 获取产品所属的类别
     * @return BelongsTo 返回类别关联
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 获取产品所属的市场
     * @return BelongsToMany 返回市场关联
     */
    public function markets(): BelongsToMany
    {
        //
        return $this->belongsToMany(SiteMarket::class, 'product_center.product_by_site_market')->withTimestamps();
    }

    /**
     * 获取产品所属的行业分类
     * @return BelongsToMany 返回行业分类关联
     */
    public function categories(): BelongsToMany
    {
        //
        return $this->belongsToMany(Category::class, 'product_center.product_by_category')->withTimestamps();
    }

    /**
     * 获取产品所属的店铺分类
     * @return BelongsTo 返回店铺分类关联
     */
    public function storeClass(): BelongsTo
    {
        //
        return $this->belongsTo(StoreClass::class, 'store_class_id', 'id');
    }

    /**
     * 获取产品的选项
     * @return HasMany 返回选项关联
     */
    public function options(): HasMany
    {
        //
        return $this->hasMany(ProductOption::class, 'product_id');
    }

    /**
     * 获取产品的属性
     * @return HasMany 返回属性关联
     */
    public function product_attrs(): HasMany
    {
        //
        return $this->hasMany(ProductAttr::class, 'product_id');
    }

    /**
     * 获取产品的 SKU 列表
     * @return HasMany 返回 SKU 关联
     */
    public function product_skus(): HasMany
    {
        //
        return $this->hasMany(ProductSku::class, 'product_id');
    }

    /**
     * 获取产品的主图
     * @return hasOne 返回主图关联
     */
    public function product_picture(): hasOne
    {
        //
        return $this->hasOne(ProductImage::class, 'product_id')->where('type', 'picture');
    }

    /**
     * 获取产品的所有图片
     * @return hasMany 返回所有图片关联
     */
    public function product_images(): hasMany
    {
        //
        return $this->hasMany(ProductImage::class, 'product_id')->where('type', 'images');
    }

    /**
     * 获取产品的描述图片
     * @return hasMany 返回描述图片关联
     */
    public function product_descries(): hasMany
    {
        //
        return $this->hasMany(ProductImage::class, 'product_id')->where('type', 'descries');
    }

    /**
     * 获取产品的白底图
     * @return HasOne 返回白底图关联
     */
    public function product_white(): hasOne
    {
        //
        return $this->hasOne(ProductImage::class, 'product_id')->where('type', 'white');
    }

    /**
     * 获取产品的视频
     * @return HasOne 返回视频关联
     */
    public function product_video(): hasOne
    {
        //
        return $this->hasOne(ProductImage::class, 'product_id')->where('type', 'video');
    }

    /**
     * 获取产品的关键词
     * @return BelongsToMany 返回关键词关联
     */
    public function keywords(): BelongsToMany
    {
        //
        return $this->belongsToMany(Keyword::class, 'product_center.product_by_keyword')->withTimestamps();
    }

    /**
     * 获取产品所属的套装
     * @return BelongsToMany 返回套装关联
     */
    public function suits(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceSuit::class, 'product_center.product_by_produce_suit')->withTimestamps();
    }

    /**
     * 获取产品所属的规格
     * @return BelongsToMany 返回规格关联
     */
    public function specs(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceSpec::class, 'product_center.product_by_produce_spec')->withTimestamps();
    }

    /**
     * 获取产品使用的面料
     * @return BelongsToMany 返回面料关联
     */
    public function fabrics(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceFabric::class, 'product_center.product_by_produce_fabric')->withTimestamps();
    }

    /**
     * 获取产品的设计
     * @return BelongsToMany 返回设计关联
     */
    public function designs(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceDesign::class, 'product_center.product_by_produce_design')->withTimestamps();
    }

    /**
     * 获取产品的流行趋势
     * @return BelongsToMany 返回流行趋势关联
     */
    public function trends(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceTrend::class, 'product_center.product_by_produce_trend')->withTimestamps();
    }

    /**
     * 获取产品的工艺
     * @return BelongsToMany 返回工艺关联
     */
    public function crafts(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceCraft::class, 'product_center.product_by_produce_craft')->withTimestamps();
    }

    /**
     * 获取产品的色系
     * @return BelongsToMany 返回色系关联
     */
    public function shades(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceShade::class, 'product_center.product_by_produce_shade')->withTimestamps();
    }

    /**
     * 获取产品的用途
     * @return BelongsToMany 返回用途关联
     */
    public function purposes(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProducePurpose::class, 'product_center.product_by_produce_purpose')->withTimestamps();
    }

    /**
     * 获取产品的配件
     * @return BelongsToMany 返回配件关联
     */
    public function accessories(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceAccessory::class, 'product_center.product_by_produce_accessory')->withTimestamps();
    }

    /**
     * 获取产品的主题
     * @return BelongsToMany 返回主题关联
     */
    public function themes(): BelongsToMany
    {
        //
        return $this->belongsToMany(Theme::class, 'product_center.product_by_theme')->withTimestamps();
    }

    /**
     * 获取产品的等级
     * @return BelongsToMany 返回等级关联
     */
    public function levels(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProduceLevel::class, 'product_center.product_by_produce_level')->withTimestamps();
    }

    /**
     * 获取产品的版权
     * @return BelongsToMany 返回版权关联
     */
    public function copyrights(): BelongsToMany
    {
        //
        return $this->belongsToMany(Copyright::class, 'product_center.product_by_copyright')->withTimestamps();
    }

    /**
     * 获取产品的完整标题
     * @return string 返回产品完整标题
     */
    public function getFullTitle(): string
    {
        //
        return "{$this->product_name} - {$this->product_title}";
    }

    /**
     * 设置产品的价格区间
     * @param float $minPrice 最低价格
     * @param float $maxPrice 最高价格
     * @return void
     */
    public function setPriceRange(float $minPrice, float $maxPrice): void
    {
        $this->min_price = $minPrice;
        $this->max_price = $maxPrice;
        $this->save();
    }

    /**
     * 获取产品的平均评分
     * @return float 返回平均评分
     */
    public function getAverageRating(): float
    {
        return $this->reviews()->avg('rating') ?? 0.0;
    }

    /**
     * 获取产品的评论
     * @return HasMany 返回评论关联
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'product_id');
    }

    /**
     * 添加产品评论
     * @param int $userId 用户ID
     * @param int $rating 评分
     * @param string $comment 评论内容
     * @return Model
     */
    public function addReview(int $userId, int $rating, string $comment)
    {
        return $this->reviews()->create([
            'user_id' => $userId,
            'rating' => $rating,
            'comment' => $comment,
        ]);
    }

    /**
     * 获取产品的 URL
     * @return string 返回产品的链接
     */
    public function getUrl(): string
    {
        return route('product.show', ['id' => $this->id]);
    }

    /**
     * 获取产品的完整信息
     * @return array 返回产品的所有属性
     */
    public function getFullInfo(): array
    {
        return $this->toArray();
    }

    /**
     * 初始化模型的默认属性
     * 在保存或更新模型之前，设置一些默认属性
     * @return $this
     */
    public function syncOriginal()
    {
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
