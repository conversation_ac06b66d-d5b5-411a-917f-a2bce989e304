<?php

namespace App\Models\ProductCenter;

use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 产品属性模型类
 * 该类表示产品的属性模型，包含属性的属性、关系和方法。
 */
class ProductAttr extends Model
{
    use GeneratesUuid;

    // 使用 UUID 生成器 Trait

    use HasFactory;

    // 使用工厂 Trait

    use SoftDeletes;

    // 使用软删除 Trait

    use DateSerializationTrait;

    // 使用日期序列化 Trait

    /**
     * 设置当前模型使用的数据库连接名
     * @var string
     */
    protected $connection = 'product_center';

    /**
     * 使用有序的 UUID 版本
     * @var string
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键字段
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性类型转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function spec_image()
    {
        //
        return $this->belongsTo(ProductImage::class, 'spec_image_id');
    }

    /**
     * 获取属性的图片 URL
     * @return string
     */
    public function getSpecImageUrlAttribute(): string
    {
        $imgDomain = config('app.img_domain');
        $imagePath = $this->attributes['spec_image_path'] ?? ''; // 确保字段存在
        return $imgDomain . '/' . ltrim($imagePath, '/');
    }

    /**
     * 初始化模型的默认属性
     * 在保存或更新模型之前，设置一些默认属性
     * @return $this
     */
    public function syncOriginal()
    {
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }

    /**
     * 获取属性的名称
     * @return string
     */
    public function getName(): string
    {
        return $this->attr_name ?? '';
    }

    /**
     * 设置属性的值
     * @param string $value 属性值
     * @return void
     */
    public function setValue(string $value): void
    {
        $this->attr_value = $value;
        $this->save();
    }

    /**
     * 获取属性的完整信息
     * @return array
     */
    public function getFullInfo(): array
    {
        return $this->toArray();
    }

    /**
     * 判断属性是否可编辑
     * @return bool
     */
    public function isEditable(): bool
    {
        return $this->is_edit == true; // 假设有 is_edit 字段
    }

    /**
     * 添加关联的 SKU
     * @param int $skuId SKU 的 ID
     * @return void
     */
    public function addSku(int $skuId): void
    {
        $this->product_skus()->attach($skuId);
    }

    /**
     * 获取与属性关联的 SKU
     * @return BelongsToMany
     */
    public function product_skus(): BelongsToMany
    {
        //
        return $this->belongsToMany(ProductSku::class, 'product_center.product_sku_attrs', 'product_attr_id', 'product_sku_id')->withTimestamps();
    }

    /**
     * 移除关联的 SKU
     * @param int $skuId SKU 的 ID
     * @return void
     */
    public function removeSku(int $skuId): void
    {
        $this->product_skus()->detach($skuId);
    }

    /**
     * 添加选项
     * @param array $optionData 选项数据
     * @return ProductOption
     */
    public function addOption(array $optionData): ProductOption
    {
        return $this->options()->create($optionData);
    }

    /**
     * 获取与属性关联的选项
     * @return HasMany
     */
    public function options(): HasMany
    {
        return $this->hasMany(ProductOption::class, 'product_attr_id');
    }

    /**
     * 获取属性的所有选项
     * @return Collection
     */
    public function getOptions()
    {
        return $this->options()->get();
    }
}
