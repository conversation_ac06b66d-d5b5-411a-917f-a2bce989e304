<?php

namespace App\Models\ProviderCenter;

use App\Models\CommonBrace\Week;
use App\Models\IndustryBrace\Category;
use App\Models\LatticeCenter\Lattice;
use App\Models\OrderCenter\Order;
use App\Models\PlanBrace\PlanFunction;
use App\Models\SellerCenter\SellerInfo;
use App\Models\ShipmentCenter\Shipment;
use App\Models\ShipmentCenter\ShipmentAddress;
use App\Models\ShipmentCenter\ShipmentExpress;
use App\Models\ShipmentCenter\ShipmentFreight;
use App\Models\ShipmentCenter\ShipmentLattice;
use App\Models\ShipmentCenter\ShipmentPickup;
use App\Models\SiteBrace\Site;
use App\Models\SiteBrace\SiteMarket;
use App\Models\StoreCenter\Store;
use App\Models\UserCenter\User;
use App\Models\WalletCenter\WalletPayee;
use App\Models\WarehouseCenter\WarehousePosition;
use App\Services\Plugin\Subscriptions\Traits\HasPlanSubscriptions;
use App\Traits\System\Date\DateSerializationTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Image\Exceptions\InvalidManipulation;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Provider extends Model implements HasMedia
{
    use InteractsWithMedia;
    use HasPlanSubscriptions;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'provider_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];
    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'cat' => 'array',
        'copy_cat' => 'array',
        'photo_url' => 'array',
        'week' => 'array',
        'market' => 'array',
        'deleted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * @param Media|null $media
     * @return void
     * @throws InvalidManipulation
     */
    public function registerMediaConversions(Media $media = null): void
    {
        //
        $this->addMediaConversion('thumb')->width(300)->height(300);
    }

    /*
     *
     */
    public function registerMediaCollections(): void
    {
        //
        $this->addMediaCollection('big-files')->useDisk('ftp');
    }

    /**
     * @return BelongsToMany
     */
    public function markets()
    {
        //
        return $this->belongsToMany(SiteMarket::class, 'provider_center.provider_by_site_market')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function categories()
    {
        //
        return $this->belongsToMany(Category::class, 'provider_center.provider_by_category')->withTimestamps();
    }

    /**
     * @return BelongsTo
     */
    public function site()
    {
        //
        return $this->belongsTo(Site::class, 'site_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function user()
    {
        //
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @return BelongsToMany
     */
    public function stores()
    {
        //
        return $this->belongsToMany(Store::class, 'provider_center.provider_by_store')->withTimestamps();
    }

    /**
     * @return HasMany
     */
    public function orders()
    {
        //
        return $this->hasMany(Order::class, 'provider_id', 'id');
    }


    /**
     * @return MorphMany
     */
    public function shipment_address()
    {
        //
        return $this->morphMany(ShipmentAddress::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipment_expresses()
    {
        //
        return $this->morphMany(ShipmentExpress::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipment_pickups()
    {
        //
        return $this->morphMany(ShipmentPickup::class, 'model');
    }

    /**
     * @return MorphOne
     */
    public function shipment_lattice()
    {
        //
        return $this->morphOne(ShipmentLattice::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function shipment_lattices()
    {
        //
        return $this->morphMany(ShipmentLattice::class, 'model');
    }

    /**
     * @return MorphOne
     */
    public function wallet_payee()
    {
        //
        return $this->morphOne(WalletPayee::class, 'model');
    }

    /**
     * @return MorphMany
     */
    public function warehouse_position()
    {
        //
        return $this->morphMany(WarehousePosition::class, 'model');
    }

    /**
     * @return HasMany
     */
    public function shipments()
    {
        //
        return $this->hasMany(Shipment::class, 'provider_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function lattice()
    {
        //
        return $this->belongsTo(Lattice::class, 'lattice_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function expresses()
    {
        //
        return $this->hasMany(ShipmentExpress::class, 'provider_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function pickups()
    {
        //
        return $this->hasMany(ShipmentPickup::class, 'provider_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function freight()
    {
        //
        return $this->hasMany(ShipmentFreight::class, 'provider_id', 'id');
    }

    /**
     * @return BelongsToMany
     */
    public function week()
    {
        //
        return $this->belongsToMany(Week::class, 'provider_center.provider_by_week')->withTimestamps();
    }

    /**
     * @return BelongsToMany
     */
    public function functions()
    {
        //
        return $this->belongsToMany(PlanFunction::class, 'provider_center.provider_by_plan_function')->withTimestamps();
    }

    /**
     * @return MorphOne
     */
    public function seller_info()
    {
        //
        return $this->morphOne(SellerInfo::class, 'model');
    }

    /**
     * @return HasOne
     */
    public function info()
    {
        //
        return $this->hasOne(SellerInfo::class, 'provider_id', 'id');
    }

    /**
     * @return HasOne
     */
    public function payee()
    {
        //
        return $this->hasOne(WalletPayee::class, 'provider_id', 'id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
