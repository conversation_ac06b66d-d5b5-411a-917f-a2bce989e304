<?php

namespace App\Models\RetailCenter;

use App\Enums\Id\SourceIdEnum;
use App\Enums\Is\IsBindingEnum;
use App\Enums\State\RetailStateEnum;
use App\Models\Ali1688Center\Ali1688Basic;
use App\Models\Ali1688ItemCenter\Ali1688Item;
use App\Models\Ali1688TradeCenter\Ali1688Trade;
use App\Models\PlatformBrace\Source;
use App\Models\RetailBrace\RetailPermission;
use App\Models\RetailBrace\RetailRole;
use App\Models\ShopCenter\Shop;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasAuthenticationLogTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laratrust\Contracts\LaratrustUser;
use Laratrust\Traits\HasRolesAndPermissions;
use Laravel\Sanctum\HasApiTokens;

class Retail extends Authenticatable implements LaratrustUser
{
    use HasApiTokens, Notifiable, HasRolesAndPermissions, HasAuthenticationLogTrait;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'retail_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可以批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'source_id' => SourceIdEnum::class,
        'is_binding' => IsBindingEnum::class,
        'password' => 'hashed',
        'email_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'retails.retail_name' => 10,
            'retails.retail_subject' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $Status = [
            'default_count' => RetailStateEnum::default()->value,
            'subscribe_count' => RetailStateEnum::subscribe()->value,
            'expire_count' => RetailStateEnum::expire()->value,
            'completed_count' => RetailStateEnum::completed()->value,
        ];
        //
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('retail_state', $Status);
    }

    /**
     * 获取用户
     */
    public function shop()
    {
        //
        return $this->belongsTo(Shop::class, 'shop_id', 'id');
    }

    /**
     * 获取用户
     */
    public function ali1688_basic()
    {
        //
        return $this->hasOne(Ali1688Basic::class, 'retail_id', 'id');
    }

    /**
     * 获取用户
     */
    public function source()
    {
        //
        return $this->belongsTo(Source::class, 'source_id', 'id');
    }

    /**
     * 获取用户
     */
    public function ali1688Item()
    {
        //
        return $this->hasMany(Ali1688Item::class, 'retail_id', 'id');
    }

    /**
     * 获取用户
     */
    public function ali1688Trades()
    {
        //
        return $this->hasMany(Ali1688Trade::class, 'retail_id', 'id');
    }

    public function ali1688Basic()
    {
        //
        return $this->hasOne(Ali1688Basic::class, 'retail_id', 'id');
    }

    /**
     * 获取 Retail 的所有角色。
     */
    public function roles(): MorphToMany
    {
        //
        return $this->morphToMany(RetailRole::class, 'retail', 'retail_by_retail_role', 'retail_id', 'role_id');
    }


    /**
     * 获取 Retail 的所有权限。
     */
    public function permissions(): MorphToMany
    {
        //
        return $this->morphToMany(RetailPermission::class, 'retail', 'retail_by_retail_permission', 'retail_id', 'permission_id');
    }


    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}

