<?php

namespace App\Models\RetailCenter;

use App\Traits\System\Date\DateSerializationTrait;
use B<PERSON><PERSON>t\LaravelRouteStatistics\Database\Factories\RouteStatisticFactory;
use Bilfeldt\LaravelRouteStatistics\Jobs\CreateLog;
use B<PERSON><PERSON>t\RequestLogger\Contracts\RequestLoggerInterface;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Date;

class RetailRouteLog extends Model implements RequestLoggerInterface
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'retail_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];


    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    //======================================================================
    // ACCESSORS
    //======================================================================

    //======================================================================
    // MUTATORS
    //======================================================================

    //======================================================================
    // SCOPES
    //======================================================================

    protected static function newFactory()
    {
        //
        return RouteStatisticFactory::new();
    }

    public function scopeWhereApi(Builder $query): Builder
    {
        //
        return $query->where('route', 'LIKE', 'api.%');
    }

    //======================================================================
    // RELATIONS
    //======================================================================

    public function scopeWhereWeb(Builder $query): Builder
    {
        //
        return $query->where('route', 'NOT LIKE', 'api.%');
    }

    public function team(): BelongsTo
    {
        //
        return $this->belongsTo(Team::class);
    }

    //======================================================================
    // METHODS
    //======================================================================

    public function log(Request $request, $response, ?int $time = null, ?int $memory = null): void
    {
        //
        if ($route = $request->route()?->getName() ?? $request->route()?->uri()) {
            //
            $attributes = $this->getLogAttributes($request, $response, $time, $memory);
            //
            if (config('route-statistics.queued')) {
                //
                CreateLog::dispatch($attributes);
            } else {
                //
                static::firstOrCreate($attributes, ['counter' => 0])->increment('counter', 1);
            }
        }
    }

    public function getLogAttributes(Request $request, $response, ?int $time = null, ?int $memory = null): array
    {
        //
        return [
            'user_id' => $request->user()?->getKey(),
            'team_id' => $this->getRequestTeam($request)?->getKey(),
            'method' => $request->getMethod(),
            'route' => $request->route()?->getName() ?? $request->route()?->uri(),
            'status' => $response->getStatusCode(),
            'ip' => request()->ip,
            'date' => $this->getDate(),
        ];
    }

    public function user(): BelongsTo
    {
        //
        return $this->belongsTo(config('auth.providers.users.model'));
    }

    protected function getRequestTeam(Request $request): ?Model
    {
        //
        if ($request->route('team') instanceof Model) {
            //
            return $request->route('team');
        }

        if ($user = $request->user()) {
            //
            return method_exists($user, 'currentTeam') ? $user->currentTeam : null;
        }
        //
        return null;
    }

    protected function getDate()
    {
        //
        $date = Date::now();
        $aggregate = config('route-statistics.aggregate');
        //
        if ($aggregate && !in_array($aggregate, ['YEAR', 'MONTH', 'DAY', 'HOUR', 'MINUTE'])) {
            //
            throw new OutOfBoundsException('Invalid date aggregation');
        }

        if (in_array($aggregate, ['YEAR', 'MONTH', 'DAY', 'HOUR', 'MINUTE'])) {
            $date->setSecond(0);
        }

        if (in_array($aggregate, ['YEAR', 'MONTH', 'DAY', 'HOUR'])) {
            $date->setMinute(0);
        }

        if (in_array($aggregate, ['YEAR', 'MONTH', 'DAY'])) {
            $date->setHour(0);
        }

        if (in_array($aggregate, ['YEAR', 'MONTH'])) {
            $date->setDay(1);
        }

        if (in_array($aggregate, ['YEAR'])) {
            $date->setMonth(1);
        }
        //
        return $date;
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->client_ip = request()->ip();
        return $this;
    }
}
