<?php

namespace App\Models\RiceCenter;

use App\Enums\State\RiceStateEnum;
use App\Models\IndustryBrace\Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\PegCenter\Peg;
use App\Models\PinCenter\Pin;
use App\Models\ProduceBrace\ProduceProperty;
use App\Models\ProduceBrace\ProduceSuit;
use App\Models\ShopCenter\Shop;
use App\Models\StoreCenter\Store;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Database\Factories\RiceCenter\RiceFactory;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Rice 模型
 * @package App\Models\RiceCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|Rice query() 开始查询
 * @method static Builder|Rice where($column, $operator = null, $value = null) 条件查询
 */
class Rice extends Model
{
    /** @use HasFactory<RiceFactory> */
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'rice_center';
    protected $table = 'rices';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        //
    ];

    /**
     * 隐藏的属性（不会在数组/JSON中显示）
     * @var array<int, string>
     */
    protected $hidden = [
        //
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 应该被转换成原生类型的属性
     * @var array<string, string>
     */
    protected $casts = [
        //
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        //
        'rice_descries' => 'array',
        'rice_json' => 'array',
        'price_ranges' => 'array',
        'rice_images' => 'array',
        'relation_json' => 'array',
        'product_json' => 'array',
        'rice_state' => RiceStateEnum::class,
    ];


    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'rices.shop_no' => 10,
            'rices.shop_name' => 10,
            //
            'rices.pin_no' => 10,
            'rices.pin_subject' => 10,
            //
            'rices.rice_title' => 10,
            'rices.rice_category_no' => 10,
            'rices.rice_category_name' => 10,
            'rices.rice_number' => 10,
        ],
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $Statuses = [
            'new_count' => RiceStateEnum::new()->value,
            'expand_count' => RiceStateEnum::expand()->value,
            'collect_count' => RiceStateEnum::collect()->value,
            'optimize_count' => RiceStateEnum::optimize()->value,
            'review_count' => RiceStateEnum::review()->value,
            'dispense_count' => RiceStateEnum::dispense()->value,
            'mate_count' => RiceStateEnum::mate()->value,
            'shine_count' => RiceStateEnum::shine()->value,
            'sow_count' => RiceStateEnum::sow()->value,
            'cope_count' => RiceStateEnum::cope()->value,
            'meal_count' => RiceStateEnum::meal()->value,
            'cancel_count' => RiceStateEnum::cancel()->value,
            'discard_count' => RiceStateEnum::discard()->value,
        ];
        //
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('rice_state', $Statuses);
    }


    /**
     * @return HasMany
     */
    public function rice_seeds()
    {
        //
        return $this->hasMany(RiceSeed::class, 'rice_id'); // 定义多对多关系
    }

    /**
     * @return HasMany
     */
    public function rice_corns()
    {
        //
        return $this->hasMany(RiceCorn::class, 'rice_id'); // 定义多对多关系
    }

    /**
     * @return HasMany
     */
    public function rice_props()
    {
        //
        return $this->hasMany(RiceProp::class, 'rice_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function category()
    {
        //
        return $this->belongsTo(Category::class, 'category_id'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function keywords()
    {
        //
        return $this->belongsToMany(Keyword::class, 'rice_center.rice_by_keyword')->withTimestamps(); // 定义多对多关系
    }

    /**
     * @return HasOne
     */
    public function rice_products()
    {
        //
        return $this->hasOne(Store::class, 'rice_center.rice_by_product'); // 定义多对多关系
    }


    /**
     * @return HasOne
     */
    public function rice_property()
    {
        //
        return $this->hasOne(ProduceProperty::class, 'rice_center.rice_by_property'); // 定义多对多关系
    }


    /**
     * @return HasMany
     */
    public function rice_images()
    {
        //
        return $this->hasMany(RiceImage::class, 'rice_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function store()
    {
        //
        return $this->belongsTo(Store::class, 'store_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function shop()
    {
        //
        return $this->belongsTo(Shop::class, 'shop_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function pin()
    {
        //
        return $this->belongsTo(Pin::class, 'pin_id'); // 定义多对多关系
    }

    /**
     * @return BelongsTo
     */
    public function suit()
    {
        //
        return $this->belongsTo(ProduceSuit::class, 'suit_id'); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function shops()
    {
        //
        return $this->belongsToMany(Shop::class, 'rice_center.rice_by_shop')->withTimestamps(); // 定义多对多关系
    }

    /**
     * @return BelongsToMany
     */
    public function pegs()
    {
        //
        return $this->belongsToMany(Peg::class, 'peg_center.peg_by_rice')->withTimestamps(); // 定义多对多关系
    }

    /**
     * 获取价格字段，若最大价格与最小价格相同，返回最大价格，否则返回价格范围
     * @return string
     */
    public function getPriceAttribute()
    {
        //
        // If min_price is the same as max_price, return max_price, otherwise return the range.
        if ($this->min_price == $this->max_price) {
            //
            return $this->max_price;
        }
        //
        return $this->min_price . ' ~ ' . $this->max_price;
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
