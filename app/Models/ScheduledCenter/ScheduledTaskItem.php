<?php

namespace App\Models\ScheduledCenter;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Spatie\ScheduleMonitor\Support\Concerns\UsesScheduleMonitoringModels;

// Eloquent 查询构造器

// Eloquent 工厂方法

// Eloquent 批量修剪 Trait，用于清理旧数据

// Eloquent 模型基类

// 一对一/一对多反向关联

/**
 * ScheduledTaskItem 模型
 * @package App\Models\ScheduledCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|ScheduledTaskItem query() 开始查询
 * @method static Builder|ScheduledTaskItem where($column, $operator = null, $value = null) 条件查询
 */
class ScheduledTaskItem extends Model
{
    // 引入相关特性，使用到调度监控模型、模型工厂和批量修剪功能
    use UsesScheduleMonitoringModels;
    use HasFactory;
    use MassPrunable;

    // $guarded 数组为空，表示允许批量赋值所有属性
    public const TYPE_STARTING = 'starting';

    // 指定可以进行批量赋值的属性，设置为空表示所有字段都可批量赋值
    public const TYPE_FINISHED = 'finished';

    // 定义常量，表示日志条目的类型
    public const TYPE_FAILED = 'failed'; // 任务开始
    public const TYPE_SKIPPED = 'skipped'; // 任务完成
    public $guarded = [];   // 任务失败
    /**
     * 指定需要进行类型转换的字段
     * @var array
     */
    public $casts = [
        'meta' => 'array', // meta 字段以数组存储，方便记录额外的元数据
    ];  // 任务跳过
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'schedule_center';

    /**
     * 定义与监控任务之间的反向一对多关联关系
     * 一个日志条目必定从属于某个监控任务
     * @return BelongsTo
     */
    public function monitoredScheduledTask(): BelongsTo
    {
        // 通过 'monitored_scheduled_task_id' 进行关联
        return $this->belongsTo($this->getMonitoredScheduleTaskModel(), 'monitored_scheduled_task_id');
    }

    /**
     * 更新日志条目的 meta 元数据
     * 例如可存储任务运行时间、退出码、失败原因等
     * @param array $values 需要存储或更新的元数据
     * @return $this 返回当前日志模型实例，便于链式调用
     */
    public function updateMeta(array $values): self
    {
        $this->update(['meta' => $values]);

        return $this;
    }

    /**
     * 定义需要被修剪（删除）的数据范围，用于自动清理过旧的日志记录
     * 这里根据配置文件中的天数阈值来筛选需要删除的日志条目
     * @return Builder 返回 Eloquent 查询构造器对象
     */
    public function prunable(): Builder
    {
        // 从配置文件读取需要保留的天数
        $days = config('schedule-monitor.delete_log_items_older_than_days');

        // 返回一个查询，用于删除在指定天数之前创建的日志记录
        return static::where('created_at', '<=', now()->subDays($days));
    }
}
