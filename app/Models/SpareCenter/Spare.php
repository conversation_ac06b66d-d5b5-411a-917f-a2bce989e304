<?php

namespace App\Models\SpareCenter;

use App\Enums\State\SpareStateEnum;
use App\Enums\Type\AutomationTypeEnum;
use App\Enums\Vest\SpareVestEnum;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Spare extends Model
{
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    // 使用日期序列化 Trait
    use DateSerializationTrait;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'spare_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        //
        'spare_images' => 'array',
        'spare_descries' => 'array',
        'spare_pictures' => 'array',
        'spare_skus' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        //
        'automation_type' => AutomationTypeEnum::class,
        //
        'spare_vest' => SpareVestEnum::class,
        'spare_state' => SpareStateEnum::class,
    ];

    /**
     *可搜索规则。
     *持有可搜索字段列表的属性。。。
     * @var array
     */
    protected $searchable = [
        /**
         *列及其在搜索结果中的优先级。
         *值越高的列越重要。
         *具有相同值的列具有相同的重要性。
         * @var array
         */
        'columns' => [
            //
            'spares.lattice_name' => 10,
            'spares.english_name' => 10,
            'spares.stall_name' => 10,
        ]
    ];

    /**
     * 获取格子的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $PushStatuses = [
            'selected_count' => SpareStateEnum::selected()->value,
            'draft_count' => SpareStateEnum::draft()->value,
            'exhibit_count' => SpareStateEnum::exhibit()->value,
            'reserved_count' => SpareStateEnum::reserved()->value,
            'retry_count' => SpareStateEnum::retry()->value,
            'archived_count' => SpareStateEnum::archived()->value,
            'cancel_count' => SpareStateEnum::cancel()->value,
            'expired_count' => SpareStateEnum::expired()->value,
        ];
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('spare_state', $PushStatuses);
    }

    /**
     * @return HasMany
     */
    public function spare_sales()
    {
        //
        return $this->hasMany(SpareSale::class, 'spare_id');
    }


    /**
     * @return HasMany
     */
    public function spare_lines()
    {
        //
        return $this->hasMany(SpareLine::class, 'spare_id');
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }
}
