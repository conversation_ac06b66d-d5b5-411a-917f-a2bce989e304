<?php

namespace App\Models\SpareCenter;

use App\Traits\System\Date\DateSerializationTrait;
use Illuminate\Database\Eloquent\Model;

class SpareWord extends Model
{
    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 不使用自增主键
     * @var bool
     */
    public $incrementing = false;
    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'spare_center';
    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';
    /**
     * 设置自定义的主键
     * @var string
     */
    protected $primaryKey = 'source_id';
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'platform_id',
        'source_id',
        'user_id',
        'retail_id',
        'spare_id',
        'keyword_id',
        'word',
    ];

    /**
     * 序列化时隐藏的属性
     * @var array
     */
    protected $hidden = [
        'client_ip',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 属性强制转换
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        return $this;
    }
}
