<?php

namespace App\Models\ViewCenter;

use App\Enums\Type\ViewTypeEnum;
use App\Traits\System\Date\DateSerializationTrait;
use App\Traits\System\Models\HasFilterableTrait;
use App\Traits\System\Models\HasSearchableTrait;
use App\Traits\System\Models\HasSortableTrait;
use App\Traits\System\Models\HasStatisticsTrait;
use Database\Factories\ViewCenter\ViewItemFactory;
use Dyrynda\Database\Support\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * ViewItem 模型
 * @package App\Models\ViewCenter
 * @property int $uuid       主键ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @method static Builder|ViewItem query() 开始查询
 * @method static Builder|ViewItem where($column, $operator = null, $value = null) 条件查询
 */
class ViewItem extends Model
{
    /** @use HasFactory<ViewItemFactory> */
    use HasFactory;
    use GeneratesUuid;
    use SoftDeletes;
    use HasSearchableTrait;
    use HasSortableTrait;
    use HasFilterableTrait;
    use HasStatisticsTrait;

    // 使用日期序列化 Trait
    use DateSerializationTrait;

    /**
     * 设置当前模型使用的数据库连接名。
     * @var string
     */
    protected $connection = 'view_center';

    /**
     * 使用默认 UUID
     * @var string[]
     */
    protected $uuidVersion = 'ordered';

    /**
     * 不可以批量赋值的属性
     * @var array
     */
    protected $guarded = ['id', 'uuid'];

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array<int, string>
     */
    protected $fillable = [
        //
    ];

    /**
     * 隐藏的属性（不会在数组/JSON中显示）
     * @var array<int, string>
     */
    protected $hidden = [
        //
    ];

    /**
     * 应该被转换成原生类型的属性
     * @var array<string, string>
     */
    protected $casts = [
        // 'options' => 'array',
        // 'is_active' => 'boolean',
        // 'price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getCountsByStatus(): array
    {
        // 定义状态值到返回键名的映射
        $wordStatus = [
            'default_count' => ViewTypeEnum::default()->value,
            'priority_count' => ViewTypeEnum::default()->value,
            'recommended_count' => ViewTypeEnum::default()->value,
            'incorrect_count' => ViewTypeEnum::default()->value,
        ];
        //
        //dd($wordStatus);
        // 使用 HasStatisticsTrait 的方法进行分组统计并映射键名
        return self::getMappedCounts('word_state', $wordStatus);
    }

    /**
     * 同步原始属性。
     * 模型的属性默认值，如果没有传入属性数组，外部需要保证自己有进行过数据初始化
     * @var array
     */
    public function syncOriginal()
    {
        // 同步客户端 IP 到模型属性
        $this->platform_id = config('app.platform_id');
        $this->client_ip = request()->ip();
        return $this;
    }

}
