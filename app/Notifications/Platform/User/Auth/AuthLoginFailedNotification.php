<?php

namespace App\Notifications\Platform\User\Auth;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;

class AuthLoginFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $office_template;
    protected $authenticationLog;

    /**
     * 创建一个新的通知实例。
     * @param AuthenticationLog $authenticationLog
     */
    public function __construct(AuthenticationLog $authenticationLog)
    {
        $this->office_template = 'VaxFf_m3iuz12epDuGoiV3GA2d7N95V9sNuZK109LnU';
        $this->authenticationLog = $authenticationLog;
    }

    /**
     * 获取通知的传递渠道。
     * @param object $notifiable
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $via = ['database'];

        if ($notifiable->is_email) {
            $via[] = 'mail'; // 邮件渠道
        }

        if ($notifiable->is_office) {
            $via[] = 'office'; // 办公室通知渠道
        }

        if ($notifiable->is_mobile) {
            $via[] = 'mobile'; // 移动端通知渠道
        }

        if ($notifiable->is_telegram) {
            $via[] = 'telegram'; // Telegram 通知渠道
        }

        return array_filter($via); // 过滤掉空值
    }

    /**
     * 获取通知的邮件表示形式。
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject(__('您的账户有一次登录失败'))
            ->line(__('我们检测到您的账户有一次登录失败。以下是详细信息：'))
            ->line(__('登录时间: :time', ['time' => $this->authenticationLog->login_at]))
            ->line(__('IP 地址: :ip', ['ip' => $this->authenticationLog->ip_address]))
            ->line(__('浏览器: :browser', ['browser' => $this->authenticationLog->user_agent]))
            ->line(__('位置: :location', ['location' => $this->authenticationLog->location ?? '未知']))
            ->line(__('如果这不是您本人操作，请立即采取措施保障您的账户安全。'))
            ->action(__('立即查看'), url('/settings/security'))
            ->line(__('感谢您使用 :app 。', ['app' => config('app.name')]));
    }

    /**
     * 获取 WeWork Webhook 渠道的通知内容。
     * @param mixed $notifiable
     * @return array<string, mixed>
     */
    public function toWeWorkWebhook($notifiable): array
    {
        return [
            'type' => 'text',
            'message' => [
                'content' => '您的账户有一次登录失败。',
                'mentioned_list' => ['@all'], // 提及的用户
                'mentioned_mobile_list' => ['***********'], // 提及的用户手机号
            ],
        ];
    }

    /**
     * 获取通知的数组表示形式。
     * @param object $notifiable
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $notifiable->id ?? null,
            'account' => $notifiable,
            'time' => $this->authenticationLog->login_at,
            'ipAddress' => $this->authenticationLog->ip_address,
            'browser' => $this->authenticationLog->user_agent,
            'location' => $this->authenticationLog->location ?? '未知',
        ];
    }
}
