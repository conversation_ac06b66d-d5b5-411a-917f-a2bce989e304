<?php

namespace App\Notifications\Platform\User\Auth;

use App\Notify\Admin\AdminWechatToXiZhiChannelNotify;
use App\Notify\Admin\AdminWeWorkToWebhookChannelNotify;
use App\Notify\Seller\SellerWeWorkToOrderAgentChannelNotify;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Auth\User;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;

class AuthWelcomeUserNotification extends Notification implements ShouldQueue
{
    /** @var User 用户实例 */
    protected $user;

    /** @var string 显示欢迎表单的URL */
    protected $showWelcomeFormUrl;

    /** @var Carbon 有效期 */
    protected $validUntil;

    /** @var string 办公室通知模板 */
    protected $office_template;

    /** @var AuthenticationLog 认证日志实例 */
    protected $authenticationLog;

    /**
     * 创建一个新的通知实例。
     * @param AuthenticationLog $authenticationLog
     */
    public function __construct(AuthenticationLog $authenticationLog)
    {
        $this->validUntil = now()->addDay(); // 有效期设为当前时间加一天
        $this->showWelcomeFormUrl = config('app.url'); // 设置显示欢迎表单的URL
        $this->office_template = 'VaxFf_m3iuz12epDuGoiV3GA2d7N95V9sNuZK109LnU'; // 设置办公室通知模板
        $this->authenticationLog = $authenticationLog;
    }

    /**
     * Determine which connections should be used for each notification channel.
     * @return array<string, string>
     */
    public function viaConnections(): array
    {
        return [
            'mail' => 'database',
            'database' => 'sync',
        ];
    }

    /**
     * 获取通知的传递渠道。
     * @param mixed $notifiable
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        //
        $via = ['database']; // 默认使用数据库通知
        //
        if ($notifiable->is_email) {
            //
            $via[] = 'mail'; // 邮件渠道
        }
        //
        if ($notifiable->is_office) {
            //
            $via[] = 'office'; // 办公室通知渠道
        }
        //
        if ($notifiable->is_mobile) {
            $via[] = 'mobile'; // 移动端通知渠道
        }
        //
        if ($notifiable->is_telegram) {
            $via[] = 'telegram'; // Telegram 通知渠道
        }
        //
        $via[] = AdminWeWorkToWebhookChannelNotify::class;
        $via[] = AdminWechatToXiZhiChannelNotify::class;
        $via[] = SellerWeWorkToOrderAgentChannelNotify::class;
        //
        return array_filter($via); // 过滤掉空值
    }

    /**
     * 获取通知的邮件表示形式。
     * @param mixed $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        //
        return (new MailMessage())
            ->subject(Lang::get('欢迎加入 :app', ['app' => config('app.name')])) // 邮件主题为 "欢迎加入"
            ->line(Lang::get('我们很高兴地欢迎您加入 :app！', ['app' => config('app.name')])) // 欢迎用户
            ->line(Lang::get('您的账户已成功创建。请点击下面的按钮完成设置：'))
            ->action(Lang::get('开始设置'), $this->showWelcomeFormUrl)
            ->line(Lang::get('感谢您选择我们！'));
    }

    /**
     * 获取通知的数组表示形式。
     * @param mixed $notifiable
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        //
        return [
            'user_id' => $notifiable->id ?? null,
            'welcome_url' => $this->showWelcomeFormUrl,
            'valid_until' => $this->validUntil->toDateTimeString(),
        ];
    }

    /**
     * 获取 XiZhi 渠道的通知表示形式。
     * @param mixed $notifiable
     * @return array<string, mixed>
     */
    public function toXiZhi($notifiable): array
    {
        return [
            'type' => 'single',
            'message' => [
                'title' => '欢迎加入',
                'content' => sprintf(
                    "欢迎您加入 :app！您的账户已成功创建。\n\n登录时间: %s\nIP 地址: %s\n浏览器: %s\n位置: %s",
                    $this->authenticationLog->login_at,
                    $this->convertToString($this->authenticationLog->ip_address),
                    $this->convertToString($this->authenticationLog->user_agent),
                    $this->convertToString($this->authenticationLog->location ?? '未知')
                ),
            ],
        ];
    }

    /**
     * 将值转换为字符串。
     * @param mixed $value
     * @return string
     */
    protected function convertToString($value): string
    {
        if (is_array($value)) {
            return implode(', ', $value); // 如果是数组，转换为逗号分隔的字符串
        } elseif (is_object($value)) {
            return json_encode($value); // 如果是对象，转换为 JSON 字符串
        }
        return (string)$value; // 其他情况转换为字符串
    }

    /**
     * 获取 WeWork Webhook 渠道的通知表示形式。
     * @param mixed $notifiable
     * @return array<string, mixed>
     */
    public function toWeWorkWebhook($notifiable): array
    {
        return [
            'type' => 'template_card',
            'message' => [
                'card_type' => 'text_notice', // 卡片类型
                'source' => [
                    'icon_url' => 'https://example.com/icon.png',
                    'desc' => '小新Easywork工作中心',
                    'desc_color' => 0,
                ],
                'main_title' => [
                    'title' => '欢迎加入多销路',
                    'desc' => '您的账户已成功创建，欢迎您加入！',
                ],
                'emphasis_content' => [
                    'title' => '100',
                    'desc' => '初始积分',
                ],
                'sub_title_text' => '请点击下面的按钮进行账户设置',
                'horizontal_content_list' => [
                    [
                        'keyname' => '账户创建时间',
                        'value' => $this->authenticationLog->login_at,
                    ],
                ],
                'card_action' => [
                    'type' => 1,
                    'url' => $this->showWelcomeFormUrl,
                ],
            ],
        ];
    }

    /**
     * 获取 WeWork Order Agent 渠道的通知表示形式。
     * @param mixed $notifiable
     * @return array<string, mixed>
     */
    public function toWeWorkOrderAgent($notifiable): array
    {
        //
        return [
            'msg_type' => 'text_card',
            'title' => '欢迎使用我们的平台',
            'description' => sprintf(
                "您的账户已成功创建，欢迎加入多销路\n\n登录时间: %s\nIP 地址: %s\n浏览器: %s\n位置: %s",
                $this->authenticationLog->login_at,
                $this->convertToString($this->authenticationLog->ip_address),
                $this->convertToString($this->authenticationLog->user_agent),
                $this->convertToString($this->authenticationLog->location ?? '未知')
            ),
            'url' => $this->showWelcomeFormUrl,
            'touser' => ['SuHui', 'XuanFengLiuXing'], // 用户列表
            'toparty' => ['SuHui', 'XuanFengLiuXing'], // 用户列表
            'totag' => ['SuHui', 'XuanFengLiuXing'], // 用户列表
        ];
    }
}
