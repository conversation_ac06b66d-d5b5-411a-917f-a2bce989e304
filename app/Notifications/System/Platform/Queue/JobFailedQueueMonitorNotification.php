<?php

namespace App\Notifications\System\Platform\Queue;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification as IlluminateNotification;
use Illuminate\Queue\Events\JobFailed;

class JobFailedQueueMonitorNotification extends IlluminateNotification
{
    use Queueable;

    /**
     * @var string 通知模板ID
     */
    public $office_template;

    /**
     * @var JobFailed Laravel队列失败事件实例
     */
    protected JobFailed $event;

    /**
     * 创建一个新的通知实例
     */
    public function __construct()
    {
        // 设置Office通知的模板ID
        $this->office_template = 'VaxFf_m3iuz12epDuGoiV3GA2d7N95V9sNuZK109LnU';
    }

    /**
     * 获取通知的传递渠道
     * @param object $notifiable 通知实体对象
     * @return array<string> 返回通知渠道数组
     */
    public function via(object $notifiable): array
    {
        $channels = config('failed-job-monitor.channels') ?? [];

        // 加入默认数据库通道
        //        $channels[] = 'database';
        //        //
        //        dd($notifiable);
        //        // 如果设置为接收邮件通知，则加入邮件通道
        //        if ($notifiable->is_email == 1) {
        //            $channels[] = 'mail';
        //        }
        //
        //        // 如果设置为接收Office通知，则加入Office通道
        //        if ($notifiable->is_office == 1) {
        //            $channels[] = 'office'; // 假设office是一个自定义的通知渠道
        //        }

        return $channels;
    }

    /**
     * 获取失败事件
     * @return JobFailed 返回队列失败事件
     */
    public function getEvent(): JobFailed
    {
        return $this->event;
    }

    /**
     * 设置失败事件
     * @param JobFailed $event 队列失败事件
     * @return self 返回当前实例，支持链式操作
     */
    public function setEvent(JobFailed $event): self
    {
        $this->event = $event;
        return $this;
    }

    /**
     * 获取通知的邮件表示形式
     * @param $notifiable
     * @return MailMessage 返回邮件消息对象
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage())
            ->error()
            ->subject('队列在 ' . config('app.url') . ' 上失败')
            ->line("异常消息: {$this->event->exception->getMessage()}")
            ->line("队列类名: {$this->event->job->resolveName()}")
            ->line("队列内容: {$this->event->job->getRawBody()}")
            ->line("异常堆栈: {$this->event->exception->getTraceAsString()}")
            ->when(config('horizon') !== null, fn(MailMessage $mailMessage) => $mailMessage->action('查看错误详情', url(config('horizon.path') . '/failed/' . $this->event->job->getJobId())));
    }

    /**
     * 获取通知的数组表示形式
     * @param object $notifiable 通知实体对象
     * @return array<string, mixed> 返回通知信息数组
     */
    public function toArray(object $notifiable): array
    {
        //
        return [
            'user_id' => $notifiable->id ?? null,
        ];
    }
}
