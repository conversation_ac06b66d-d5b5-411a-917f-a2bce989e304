<?php

namespace App\Notify\Seller;

use Illuminate\Notifications\Notification;
use NewWeChat\NewWeChat;
use NewWeChat\Work\Message\NotificationService;

/**
 * Class SellerWeWorkToOrderAgentChannelNotify.
 * 处理订单代理渠道的 WeChat Work 通知。
 */
class SellerWeWorkToOrderAgentChannelNotify
{
    protected NotificationService $service; // WeChat Work 通知服务实例

    /**
     * 构造函数，初始化 WeChat Work Notification Service
     */
    public function __construct()
    {
        // 使用环境变量配置 WeChat Work 客户端
        $client = NewWeChat::work([
            'corp_id' => env('WECHAT_WORK_CORP_ID', 'ww63326e0137b13d31'), // 企业ID
            'agent_id' => env('WECHAT_WORK_AGENT_ID', 1000028), // 应用ID
            'secret' => env('WECHAT_WORK_AGENT_CONTACTS_SECRET', 'Cbdka_ppYFGA1HH9b3y5ujPKz-zMyrQ4F23AGbONSOQ'), // 应用Secret
            'token' => env('WECHAT_WORK_TOKEN', '26krZKpeMb'),
            'aes_key' => env('WECHAT_WORK_AES_KEY', 'edGMhSoxrjTzHAiBgVDm3HeXmUqBG4P5KvW4gAFnhNs'),
        ]);
        // 初始化通知服务实例，并传递 WeChat Work 客户端
        $this->service = new NotificationService($client);
    }

    /**
     * 发送给定的通知
     * @param mixed $notifiable 通知接收者
     * @param Notification $notification 通知实例
     */
    public function send($notifiable, Notification $notification): void
    {
        // 从通知实例中获取 WeChat Work 格式的数据
        $data = $notification->toWeWorkOrderAgent($notifiable);
        // 调用通知服务类发送消息
        $this->service->sendNotification($data);
    }
}
