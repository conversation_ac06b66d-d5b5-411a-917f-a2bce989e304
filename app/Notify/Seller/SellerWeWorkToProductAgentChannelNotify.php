<?php

namespace App\Notify\Seller;

use Exception;
use Illuminate\Notifications\Notification;
use NewWeChat\Kernel\Messages\Article;
use NewWeChat\Kernel\Messages\Card;
use NewWeChat\Kernel\Messages\File;
use NewWeChat\Kernel\Messages\Image;
use NewWeChat\Kernel\Messages\MiniprogramNotice;
use NewWeChat\Kernel\Messages\News;
use NewWeChat\Kernel\Messages\TaskCard;
use NewWeChat\Kernel\Messages\Text;
use NewWeChat\Kernel\Messages\TextCard;
use NewWeChat\Kernel\Messages\Video;
use NewWeChat\Kernel\Messages\Voice;
use NewWeChat\NewWeChat;
use NewWeChat\Work\GroupRobot\Messages\Markdown;

class SellerWeWorkToProductAgentChannelNotify
{
    protected $client;

    /**
     * 构造函数，初始化 WeChat Work Agent 客户端
     */
    public function __construct()
    {
        // 从配置文件中获取企业微信的配置
        $config = [
            'corp_id' => env('WECHAT_WORK_CORP_ID', 'ww63326e0137b13d31'),
            'agent_id' => env('WECHAT_WORK_AGENT_ID', 1000026),
            'secret' => env('WECHAT_WORK_AGENT_CONTACTS_SECRET', 'Sut2wpyM0XrZfB_jmWcsGnbeMP-YyzDPTD2f_HiKJ0A'),
        ];
        // 使用配置创建 WeChat Work Agent 客户端实例
        $this->client = NewWeChat::work($config);
    }

    /**
     * 发送给定的通知
     */
    public function send($notifiable, Notification $notification): void
    {
        // 获取 WeChat Work 格式的通知数据
        $data = $notification->toWeWorkOrderAgent($notifiable);
        // 获取 Messenger 实例
        $messenger = $this->client->messenger;
        // 根据消息类型创建不同的消息对象
        switch ($data['type']) {
            case 'text':
                $message = new Text($data['content']);
                break;
            case 'image':
                $message = new Image($data['media_id']);
                break;
            case 'voice':
                $message = new Voice($data['media_id'], $data['recognition']);
                break;
            case 'video':
                $message = new Video($data['media_id'], $data['title'], $data['description']);
                break;
            case 'file':
                $message = new File($data['media_id']);
                break;
            case 'mpnews':
                $message = new Article($data['media_id']);
                break;
            case 'news':
                $message = new News($data['articles']);
                break;
            case 'text_card':
                $message = new TextCard([
                    'title' => $data['title'],
                    'description' => $data['description'],
                    'url' => $data['url'],
                ]);
                break;
            case 'task_card':
                $message = new TaskCard($data['task_data']);
                break;
            case 'markdown':
                $message = new Markdown($data['content']);
                break;
            case 'miniprogram_notice':
                $message = new MiniprogramNotice($data['content']);
                break;
            case 'card':
                $message = new Card($data['card']);
                break;
            default:
                throw new Exception("Unsupported message type: {$data['type']}");
        }
        // 发送消息给指定的用户
        $messenger->message($message)->toUser($data['users'])->send();
    }
}
