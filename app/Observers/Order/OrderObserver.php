<?php

namespace App\Observers\Order;

use App\Enums\State\Order\OrderMainStateEnum;
use App\Models\OrderCenter\Order;
use App\Services\Seller\OrderNoteService;
use App\Services\Web\CartService;

class OrderObserver
{
    /**
     * 在提交所有事务后处理事件。
     * @var bool
     */
    public $afterCommit = true;

    /**
     * Handle the Order "created" event.
     */
    public function created(Order $Order): void
    {
        //
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $Order): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($Order->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($Order->wasChanged('main_state')) {
            // to do something
            if ($Order->main_state == OrderMainStateEnum::OrderCreated()) {
                // to do something
                if ($Order->type == 'shipper') {
                    //记录订单记录
                    CartService::noteUserCreateCartShipperToOrderByOrderNote($Order->user, $Order, '用户下单');
                    //
                    CartService::noteUserCreateCartShipperToOrderServeByOrderNote($Order->user, $Order, $Order->shipper->ship_express, '选择承运快递');
                } elseif ($Order->type == 'spread') {
                    //记录订单记录
                    CartService::noteUserCreateCartSpreadToOrderByOrderNote($Order->user, $Order, '用户下单');
                }
            } elseif ($Order->main_state == OrderMainStateEnum::OrderProcessing()) {
                //
                if ($Order->is_out_stock == 1) {
                    //记录订单记录
                    OrderNoteService::noteStoreConfirmOrderUpOrderByOrderNote($Order->store, $Order, '商家处理');
                } elseif ($Order->is_out_stock == 2) {
                    //记录订单记录
                    OrderNoteService::noteStoreConfirmOrderUpOrderPartByOrderNote($Order->store, $Order, '商家处理');
                }
            } elseif ($Order->main_state == OrderMainStateEnum::OrderPicking()) {
                //
                OrderNoteService::noteStorePickDoneUpOrderByOrderNote($Order->store, $Order, '商家拣货');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderShipping()) {
                //
                OrderNoteService::noteStorePickDoneUpOrderByOrderNote($Order->store, $Order, '商家出货');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderShipment()) {
                //
                OrderNoteService::noteStorePickDoneUpOrderByOrderNote($Order->store, $Order, '商家发货');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderTransport()) {
                //
                OrderNoteService::noteStorePickDoneUpOrderByOrderNote($Order->store, $Order, '物流运输');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderDistribution()) {
                //打印电子面单
                OrderNoteService::noteStoreDeliveryPrintWaybillByOrderNote($Order->store, $Order, '物流配送');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderSettlement()) {
                //回传发货单号
                OrderNoteService::noteStoreDeliveryShipmentWaybillByOrderNote($Order->store, $Order, '账单结算');
            } elseif ($Order->main_state == OrderMainStateEnum::OrderException()) {
                //
                dd(123);
            } else {
                //
            }
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($Order->wasChanged('is_waybill')) {
            // to do something
            if ($Order->main_state == OrderMainStateEnum::default() || $Order->main_state == OrderMainStateEnum::default()) {
                //
                if ($Order->is_waybill == OrderMainStateEnum::default()) {
                    //
                    OrderNoteService::noteStoreDeliveryGetWaybillByOrderNote($Order->store, $Order, '获取电子面单单号');
                }
            }
        }
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $Order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $Order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $Order): void
    {
        //
    }
}
