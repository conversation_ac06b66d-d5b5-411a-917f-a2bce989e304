<?php

namespace App\Observers\Plan;

use App\Models\Plan\PlanReg;

class PlanRegObserver
{
    /**
     * Handle the PlanReg "created" event.
     */
    public function created(PlanReg $PlanReg): void
    {
        //
    }

    /**
     * Handle the PlanReg "updated" event.
     */
    public function updated(PlanReg $PlanReg): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($PlanReg->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($PlanReg->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the PlanReg "deleted" event.
     */
    public function deleted(PlanReg $PlanReg): void
    {
        //
    }

    /**
     * Handle the PlanReg "restored" event.
     */
    public function restored(PlanReg $PlanReg): void
    {
        //
    }

    /**
     * Handle the PlanReg "force deleted" event.
     */
    public function forceDeleted(PlanReg $PlanReg): void
    {
        //
    }
}
