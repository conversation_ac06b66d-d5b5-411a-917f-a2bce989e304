<?php

namespace App\Observers\Product;

use App\Models\ProductCenter\Product;

class ProductObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Product $Product): void
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Product $Product): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($Product->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($Product->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $Product): void
    {
        //
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Product $Product): void
    {
        //
    }

    /**
     * Handle the Product "force deleted" event.
     */
    public function forceDeleted(Product $Product): void
    {
        //
    }
}
