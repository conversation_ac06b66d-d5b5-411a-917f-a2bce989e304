<?php

namespace App\Observers\Provider;

use App\Models\Provider\ProviderDemo;

class ProviderDemoObserver
{
    /**
     * 处理 ProviderDemo "created" 事件.
     */
    public function created(ProviderDemo $providerDemo): void
    {
        //
    }

    /**
     * 处理 ProviderDemo "updated" 事件.
     */
    public function updated(ProviderDemo $providerDemo): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($providerDemo->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($providerDemo->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * 处理 ProviderDemo "deleted" 事件.
     */
    public function deleted(ProviderDemo $providerDemo): void
    {
        //
    }

    /**
     * 处理 ProviderDemo "restored" 事件.
     */
    public function restored(ProviderDemo $providerDemo): void
    {
        //
    }

    /**
     * 处理 ProviderDemo "force deleted" 事件.
     */
    public function forceDeleted(ProviderDemo $providerDemo): void
    {
        //
    }
}
