<?php

namespace App\Observers\Store;

use App\Models\StoreCenter\Store;

class StoreObserver
{
    /**
     * Handle the Store "created" event.
     */
    public function created(Store $Store): void
    {
        //
    }

    /**
     * Handle the Store "updated" event.
     */
    public function updated(Store $Store): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($Store->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($Store->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the Store "deleted" event.
     */
    public function deleted(Store $Store): void
    {
        //
    }

    /**
     * Handle the Store "restored" event.
     */
    public function restored(Store $Store): void
    {
        //
    }

    /**
     * Handle the Store "force deleted" event.
     */
    public function forceDeleted(Store $Store): void
    {
        //
    }
}
