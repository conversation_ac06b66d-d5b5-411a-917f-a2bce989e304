<?php

namespace App\Observers\Supplier;

use App\Models\Supplier\Supplier;

class SupplierObserver
{
    /**
     * 处理 Supplier "created" 事件.
     */
    public function created(Supplier $supplier): void
    {
        //
    }

    /**
     * 处理 Supplier "updated" 事件.
     */
    public function updated(Supplier $supplier): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($supplier->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($supplier->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * 处理 Supplier "deleted" 事件.
     */
    public function deleted(Supplier $supplier): void
    {
        //
    }

    /**
     * 处理 Supplier "restored" 事件.
     */
    public function restored(Supplier $supplier): void
    {
        //
    }

    /**
     * 处理 Supplier "force deleted" 事件.
     */
    public function forceDeleted(Supplier $supplier): void
    {
        //
    }
}
