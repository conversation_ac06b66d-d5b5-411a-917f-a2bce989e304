<?php

namespace App\Observers\Wallet;

use App\Models\Wallet\WalletPayee;

class WalletPayeeObserver
{
    /**
     * Handle the WalletPayee "created" event.
     */
    public function created(WalletPayee $WalletPayee): void
    {
        //
    }

    /**
     * Handle the WalletPayee "updated" event.
     */
    public function updated(WalletPayee $WalletPayee): void
    {
        //isDirty (and getDirty) 用在保存前置执行，查看哪些属性在从数据库检索到调用之间被修改过 监听某些字段的更新
        if ($WalletPayee->isDirty('main_state')) {
            // to do something
        }

        //wasChanged (and getChanges) 是保存后置执行，查看属性是否在上次保存中（从代码到数据库）被修改或者更新. 监听某些字段的更新
        if ($WalletPayee->wasChanged('main_state')) {
            // to do something
        }
    }

    /**
     * Handle the WalletPayee "deleted" event.
     */
    public function deleted(WalletPayee $WalletPayee): void
    {
        //
    }

    /**
     * Handle the WalletPayee "restored" event.
     */
    public function restored(WalletPayee $WalletPayee): void
    {
        //
    }

    /**
     * Handle the WalletPayee "force deleted" event.
     */
    public function forceDeleted(WalletPayee $WalletPayee): void
    {
        //
    }
}
