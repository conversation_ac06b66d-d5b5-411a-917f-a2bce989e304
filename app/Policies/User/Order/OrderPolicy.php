<?php

namespace App\Policies\User\Order;

use App\Models\Order\Order;
use App\Models\User\User;

class OrderPolicy
{
    /**
     * Determine whether the user can view any models.
     * 确定同事是否可以查看任何模型。
     */
    public function viewAny(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     * 确定同事是否可以查看模型
     */
    public function view(User $user, Order $order): bool
    {
        //
    }

    /**
     * Determine whether the user can create models.
     * 确定同事是否可以创建模型
     */
    public function create(User $user): bool
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     * 确定同事是否可以更新模型
     */
    public function update(User $user, Order $order): bool
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     * 确定同事是否可以删除模型
     */
    public function delete(User $user, Order $order): bool
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     * 确定同事是否可以恢复模型
     */
    public function restore(User $user, Order $order): bool
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     * 确定同事是否可以永久删除模型
     */
    public function forceDelete(User $user, Order $order): bool
    {
        //
    }
}
