<?php

namespace App\Providers;

use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 将当前菜单的活动状态变量 'menuActive' 共享给所有视图
        View::share('menuActive', $this->getMenuActive());
    }

    /**
     * 获取当前菜单的活动状态
     * @return string 当前路径，以根路径 '/' 为基准
     */
    private function getMenuActive()
    {
        // 获取当前请求的路径
        $currentPath = request()->path();

        // 如果路径是根路径，则返回 '/'
        // 否则返回带有 '/' 前缀的路径
        return $currentPath === '/' ? '/' : '/' . $currentPath;
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
