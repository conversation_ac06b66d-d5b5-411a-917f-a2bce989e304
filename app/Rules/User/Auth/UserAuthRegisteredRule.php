<?php

namespace App\Rules\User\Auth;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Translation\PotentiallyTranslatedString;

class UserAuthRegisteredRule implements ValidationRule
{
    /**
     * Indicates whether the rule should be implicit.
     * @var bool
     */
    public $implicit = true;

    /**
     * Run the validation rule.
     * @param Closure(string): PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        //
    }
}
