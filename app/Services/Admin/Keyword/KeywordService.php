<?php


namespace App\Services\Admin\Keyword;

use App\Models\KeywordCenter\Keyword;
use App\Services\BaseService;

class KeywordService extends BaseService
{
    /**
     * @param $keywords
     * @return array
     */
    public static function SyncKeywordDataToIds($keywords)
    {
        //
        $keyword_ids = [];
        foreach ($keywords as $keyword) {
            //查询$Keyword的Model
            $Keyword = Keyword::where('platform_id', '=', config('app.platform_id'))->where('word', '=', $keyword)->first();

            //
            if (isset($Keyword)) {
                //
                $keyword_ids = array_merge($keyword_ids, [$Keyword->id]);
            } else {
                //
                $Keyword = new Keyword();
                $Keyword->platform_id = config('app.platform_id');
                $Keyword->keyword = $keyword;
                $Keyword->keyword_count = 1;
                $Keyword->save();
                //
                $keyword_ids = array_merge($keyword_ids, [$Keyword->id]);
            }
        }
        //
        return $keyword_ids;
    }

    /**
     * @param $keywords
     * @return array
     */
    public static function SyncKeywordDataToKeywords($keywords)
    {
        //
        $keyword_lists = [];
        foreach ($keywords as $keyword) {
            //查询$Keyword的Model
            $Keyword = Keyword::where('platform_id', '=', config('app.platform_id'))->where('word', '=', $keyword)->first();
            //
            if (isset($Keyword)) {
                //
                $keyword_lists = array_merge($keyword_lists, [$Keyword]);
            } else {
                //
                $Keyword = new Keyword();
                $Keyword->platform_id = config('app.platform_id');
                $Keyword->word = $keyword;
                $Keyword->word_count = 1;
                $Keyword->save();
                //
                $keyword_lists = array_merge($keyword_lists, [$Keyword]);
            }
        }
        //
        return $keyword_lists;
    }
}
