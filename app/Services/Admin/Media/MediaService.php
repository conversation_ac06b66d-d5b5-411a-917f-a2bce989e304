<?php

namespace App\Services\Admin\Media;

use App\Http\Resources\Admin\Media\MediaResource;
use App\Models\MediaCenter\Media;
use App\Services\BaseService;

class MediaService extends BaseService
{
    // 获取首页数据
    public static function getMediaUuidToMediaId($uuid)
    {
        //
        $Media = Media::where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        $media = new MediaResource($Media);
        //
        return $media ?? null;
    }

    // 获取首页数据
    public static function getMediaIdToMediaUuid($id)
    {
        //
        $Media = Media::where('platform_id', '=', config('app.platform_id'))->find($id);
        $media = new MediaResource($Media);
        //
        return $media ?? null;
    }
}
