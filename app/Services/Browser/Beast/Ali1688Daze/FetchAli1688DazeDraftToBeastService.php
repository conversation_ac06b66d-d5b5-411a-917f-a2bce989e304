<?php

namespace App\Services\Browser\Beast\Ali1688Daze;

use App\Models\KeywordCenter\Keyword;
use App\Models\PlatformBrace\Source;
use App\Services\BaseService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Throwable;

class FetchAli1688DazeDraftToBeastService extends BaseService
{
    /**
     * 发起分页数据的获取请求，并执行相应处理
     * * @param int $pageIndex
     * * @param int $pageSize
     * @param int $pageIndex
     * @param int $pageSize
     * @return array|object
     * @throws ConnectionException
     * @throws Throwable
     */
    public static function fetchPaginatedData(int $pageIndex, int $pageSize)
    {
        // 对应您的 cURL 请求中所有 Header 参数
        $response = Http::withHeaders([
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'zh-CN,zh;q=0.9',
            'accesstoken' => 'alibabaweb123bbe60ab30d49b33d88d',
            'content-type' => 'application/json',
            'origin' => 'https://sjzl.fjdaze.com',
            'priority' => 'u=1, i',
            'referer' => 'https://sjzl.fjdaze.com/',
            's-d' => 'fdec974900224fe912a841763fcfef14',
            's-h' => '1742580601629',
            's-j' => 'ebce5570cf9a3a471db823458203ba5c',
            's-m' => '1d2140781520d14b3d183b61fea7dd98dcfa',
            's-r' => '16eb5d271f9331418c1bfca1628e485df36b',
            's-s' => '71d60e8cb8112cd8d60ced71a783da76',
            's-t' => '1742580611628',
            'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"Windows"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-site',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ])
            ->timeout(1000)
            ->post(
                'https://api.fjdaze.com/draftBoxManagerHost/api/DraftBox/Search',
                [
                    'state' => [200, 404],
                    'isNeedCategoryGroupName' => true,
                    'pageIndex' => $pageIndex,
                    'pageSize' => $pageSize,
                ]
            );
        //
        // 将返回结果转为对象
        $data = json_decode($response->body(), true);
        //
        // 依据业务逻辑进行判断与处理
        if (isset($data['code']) && $data['code'] == 200) {
            // 将数据批量处理后返回
            $Source = Source::find(5);
            // 查询状态为 0 且 word 为指定值的关键词
            $keywords = Keyword::where('word_state', 0)->get();
            //
            foreach ($data['result']['list'] as $riceData) {
                //
                SaveAli1688DazeDraftToBeastService::batchFetchDraftBeast($riceData, $Source, $keywords);
            }
            //
            echo $pageIndex;
            //
            return [
                'pageSize' => $pageSize,
                'pageIndex' => $pageIndex,
                'items' => $data['result']['list'] ?? [],
                'totalCount' => $data['result']['totalCount'] ?? 0,
                'totalPages' => ceil(($data['result']['totalCount'] ?? 0) / $pageSize),
            ];
        }
        //
        echo 'error';
        return ['error' => '请求失败，无法获取数据。'];
    }
}
