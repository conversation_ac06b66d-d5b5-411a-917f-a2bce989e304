<?php

namespace App\Services\Browser\Pin\Ali1688Cps;

use App\Api\Ali1688Cps\Ali1688CpsOfferApi;
use App\Models\IndustryBrace\Category;
use App\Models\PinCenter\Pin;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Exception;

class Ali1688CpsOfferService extends BaseService
{
    /**
     * 分页抓取并保存全部数据
     * @param array $Ali1688AccessToken
     * @param array $params
     * @param int $pageSize
     * @return void
     * @throws Exception
     */
    public static function fetchAndStore($Ali1688AccessToken, array $params, $pageSize = 20)
    {
        $pageNo = 1;
        $firstPageData = self::requestData($Ali1688AccessToken, $params, $pageNo, $pageSize);

        // 保存第一页
        self::saveData($firstPageData['list']);

        // 总页数
        $totalRow = $firstPageData['totalRow'];
        $totalPages = ($totalRow > 0) ? ceil($totalRow / $pageSize) : 1;

        // 后续页
        for ($p = 2; $p <= $totalPages; $p++) {
            //
            $pageData = self::requestData($Ali1688AccessToken, $params, $p, $pageSize);
            self::saveData($pageData['list']);
        }
    }

    /**
     * 单次请求数据 (对象风格)
     * @param array $Ali1688AccessToken ['client_id','client_secret','access_token']
     * @param array $params 必填至少 feedInfo 或 categoryId
     * @param int $pageNo 页码
     * @param int $pageSize 每页条数
     * @return array  [ 'list' => (array of stdClass), 'totalRow' => int ]
     * @throws Exception
     */
    public static function requestData($Ali1688AccessToken, array $params, int $pageNo = 1, int $pageSize = 20)
    {
        // 注入分页
        $params['pageNo'] = $pageNo;
        $params['pageSize'] = $pageSize;

        // 调用封装的 API 方法，返回 stdClass
        $rawResponse = Ali1688CpsOfferApi::getAli1688CpsOfferList($Ali1688AccessToken, $params);

        // 判断 result 是否为空
        if (empty($rawResponse->result) || !count($rawResponse->result)) {
            throw new Exception("接口 result 内容为空或缺失");
        }

        // 获取 result (数组 of stdClass)
        $resultList = $rawResponse->result;
        if (is_object($resultList)) {
            // 若只有单个对象，转换为数组
            $resultList = [$resultList];
        }

        // 获取 totalRow
        $totalRow = isset($rawResponse->totalRow) ? (int)$rawResponse->totalRow : 0;

        return [
            'list' => $resultList,
            'totalRow' => $totalRow,
        ];
    }

    /**
     * 保存数据到 Pin 表（存在则更新，空则新建）
     * @param array $resultList 数组 of stdClass (每个元素为一条商品记录)
     * @return void
     */
    public static function saveData(array $resultList)
    {
        foreach ($resultList as $item) {
            // 取出已存在记录或创建新对象
            $Pin = Pin::where('pin_no', $item->offerId)->first() ?? new Pin();

            // 找出分类(第三级分类) - 仅示例
            $categoryNo = static::splitCategoryId($item->categoryId, 'third') ?? $Pin->pin_category_no;
            $Category = Category::where('ali1688_cat_no', $categoryNo)->first();

            // 获取或创建店铺
            $shopNo = $item->sellerId ?? $Pin->shop_no ?? 0;
            $Shop = Shop::where('shop_no', $shopNo)->first() ?? new Shop();

            // 更新或设置店铺信息
            $Shop->source_id = 5;
            $Shop->shop_no = $shopNo;
            $Shop->is_shili = $item->slsjFlag ?? $Shop->is_shili ?? 0;
            $Shop->is_depth = $item->sdrzFlag ?? $Shop->is_depth ?? 0;
            $Shop->is_import = $item->jkhyFlag ?? $Shop->is_import ?? 0;
            $Shop->is_drop = $item->yjdf_flag ?? $Shop->is_drop ?? 0;
            $Shop->is_hyper = $item->hyperFlag ?? $Shop->is_hyper ?? 0;
            $Shop->save();

            // 关联 Pin -> Shop
            $Pin->shop_id = $Shop->id;
            $Pin->shop_no = $Shop->shop_no;
            $Pin->shop_name = $Shop->shop_name ?? $Pin->shop_name; // 若Shop有名称可赋值

            // 关联分类
            $Pin->category_id = $Category->id ?? null;
            $Pin->source_id = 5;

            // 保存商品信息
            $Pin->pin_no = $item->offerId ?? $Pin->pin_no;
            $Pin->pin_subject = $item->title ?? $Pin->pin_subject;
            $Pin->pin_pic = $item->imgUrl ?? $Pin->pin_pic;
            $Pin->pin_price = $item->price ?? $Pin->pin_price;
            $Pin->sale_quantity = $item->saleQuantity ?? $Pin->sale_quantity;
            $Pin->pin_url = $item->url ?? $Pin->pin_url;
            $Pin->pin_ratio = $item->ratio ?? $Pin->pin_ratio;
            $Pin->pin_commission = $item->tkCommission ?? $Pin->pin_commission;
            $Pin->pin_promotion = $item->tkCnt ?? $Pin->pin_promotion;
            $Pin->pin_begin = $item->quantityBegin ?? $Pin->pin_begin;
            $Pin->pin_unit = $item->unit ?? $Pin->pin_unit;

            $Pin->pin_industry_no = static::splitCategoryId($item->categoryId, 'first') ?? $Pin->pin_industry_no;
            $Pin->pin_category_no = $categoryNo;
            $Pin->pin_buyer = $item->oldBuyerRatio ?? $Pin->pin_buyer;
            $Pin->is_cps = 1;

            $Pin->save();
        }
    }

    /**
     * 根据分类字符串和等级返回对应分类 ID
     * 例："0\t1042954\t201556615\t127406011"
     * 若首位是 "0" 则删除；后面的依次为第一、第二、第三分类
     * @param string $categoryId
     * @param string $level 'first', 'second', 'third'
     * @return string|null
     */
    public static function splitCategoryId(string $categoryId, string $level): ?string
    {
        $parts = explode("\t", $categoryId);

        if (isset($parts[0]) && $parts[0] === '0') {
            array_shift($parts);
        }

        $mapping = [
            'first' => 0,
            'second' => 1,
            'third' => 2,
        ];

        if (!isset($mapping[$level])) {
            return null;
        }

        return $parts[$mapping[$level]] ?? null;
    }
}
