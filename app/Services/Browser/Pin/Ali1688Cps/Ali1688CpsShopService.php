<?php

namespace App\Services\Browser\Pin\Ali1688Cps;

use App\Api\Ali1688Cps\Ali1688CpsShopApi;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Exception;

class Ali1688CpsShopService extends BaseService
{
    /**
     * @param array $Ali1688AccessToken
     * @param array $params
     * @param int $pageSize
     * @return void
     * @throws Exception
     */
    public static function fetchAndStore(array $Ali1688AccessToken, array $params, int $pageSize = 20): void
    {
        // 设置初始页参数
        $params['pageNo'] = $params['pageNo'] ?? 1;
        $params['pageSize'] = $params['pageSize'] ?? $pageSize;

        // 第一次请求
        $firstPage = self::requestData($Ali1688AccessToken, $params);
        self::saveData($firstPage['list']);

        $totalRow = $firstPage['totalRow'];
        $totalPages = ($totalRow > 0) ? ceil($totalRow / $params['pageSize']) : 1;

        // 循环翻页
        for ($p = 2; $p <= $totalPages; $p++) {
            $params['pageNo'] = $p;
            $pageData = self::requestData($Ali1688AccessToken, $params);
            self::saveData($pageData['list']);
        }
    }

    /**
     * 单页请求数据 (返回对象形式)
     * @param array $Ali1688AccessToken ['client_id','client_secret','access_token']
     * @param array $params 至少包含 categoryId, pageNo, pageSize 等
     * @return array  [ 'list' => (array of stdClass), 'totalRow' => int ]
     * @throws Exception
     */
    public static function requestData(array $Ali1688AccessToken, array $params): array
    {
        //
        //dd($params);
        // 调用 API
        $rawResponse = Ali1688CpsShopApi::getAli1688CpsShopList($Ali1688AccessToken, $params);
        // 期望返回类似：
        //  {
        //    "result": [
        //      {
        //        "sellerId": 123,
        //        "loginId": "...",
        //        "companyName": "店铺名称",
        //        "tradeGrade": 4.5,
        //        "ratio": 0.05,
        //        "productCnt": 100,
        //        "tkCnt": 50,
        //        "linkUrl": "https://xxx",
        //      }
        //    ],
        //    "totalRow": 999
        //  }
        // 确认 $rawResponse 是 stdClass，并且含有 ->result / ->totalRow
        if (!is_object($rawResponse)) {
            //
            throw new Exception("API 返回格式非对象，请检查接口调用");
        }
        //
        //dd($rawResponse);
        // 判断 result 是否存在、并且是数组
        if (isset($rawResponse->result) || is_array($rawResponse->result)) {
            // 获取 totalRow
            $totalRow = isset($rawResponse->totalRow) ? (int)$rawResponse->totalRow : 0;
            //
            return [
                'list' => $rawResponse->result, // 数组 of stdClass
                'totalRow' => $totalRow,
            ];
        }
    }

    /**
     * 保存店铺数据到本地 `Shop` 表
     * @param array $shopList 数组 of stdClass (每个元素是一家店铺)
     * @return void
     */
    public static function saveData(array $shopList): void
    {
        //
        //dd($shopList);
        //
        foreach ($shopList as $item) {
            //
            // $item 是 stdClass，比如 $item->sellerId, $item->companyName
            if (empty($item->sellerId)) {
                //
                continue;
            }
            // 以 sellerId 作为唯一主键判断
            $shop = Shop::where('shop_no', $item->sellerId)->first() ?? new Shop();
            // 更新或写入字段 (若只想插入不更新，需额外逻辑判断)
            $shop->shop_no = $item->sellerId;
            $shop->shop_name = $item->companyName ?? $shop->shop_name;
            $shop->shop_url = $item->linkUrl ?? $shop->shop_url;
            // 自定义字段
            $shop->shop_grade = $item->tradeGrade ?? $shop->shop_grade;
            $shop->shop_ratio = $item->ratio ?? $shop->shop_ratio;   // 平均佣金比例
            $shop->shop_num = $item->productCnt ?? $shop->quantity_count;  // 商品数量
            $shop->promotion_count = $item->tkCnt ?? $shop->promotion_count;       // 30天推广量
            // 保存
            $shop->save();
            //
            //dd($shop);
            //array:1 [▼ // app\Services\Browser\Pin\Ali1688Cps\Ali1688CpsShopService.php:66
            //  0 => {#2428 ▼$shop->shop_login_id = $item->loginId ?? $shop->shop_login_id;
            //    +"sellerId": 4070461154
            //    +"companyName": "义乌市恰美工艺品有限公司"
            //    +"linkUrl": "https://shop5m11514820041.1688.com"
            //
            //            //    +"tkCnt": 2406
            //            //    +"productCnt": 77
            //    +"tradeGrade": 4
            //    +"ratio": 0.01
            //  }
            //]
        }
    }
}
