<?php

namespace App\Services\Browser\Plant\Ali1688Cps;

use App\Api\Ali1688Cps\Ali1688CpsOfferApi;
use App\Jobs\Browser\Plant\Ali1688Cps\FetchAli1688CpsOfferToPlantQueue;
use App\Models\IndustryBrace\Category;
use App\Models\PlantCenter\Plant;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\DB;

class FetchAli1688CpsOfferToPlantService extends BaseService
{
    /**
     * 分页抓取并保存全部数据 (结合队列处理后续页)
     * @param array $Ali1688AccessToken
     * @param array $params
     * @param int $pageSize
     * @return void
     * @throws Exception
     */
    public static function fetchAndStore($Ali1688AccessToken, array $params, $pageSize = 20)
    {
        // 第 1 页先同步抓取，确保能拿到 totalRow
        $pageNo = 1;
        $firstPageData = FetchAli1688CpsOfferToPlantService::requestData($Ali1688AccessToken, ['categoryId' => 7], $pageNo, $pageSize);
        //
        if ($firstPageData['list']) {
            // 同步保存第一页数据
            FetchAli1688CpsOfferToPlantService::saveData($firstPageData['list']);
            // 计算总页数
            $totalRow = $firstPageData['totalRow'];
            $totalPages = ($totalRow > 0) ? ceil($totalRow / $pageSize) : 1;
            // 从第 2 页起，改用队列异步处理
            for ($p = 2; $p <= $totalPages; $p++) {
                // 派发队列任务
                FetchAli1688CpsOfferToPlantQueue::dispatch(
                    $Ali1688AccessToken,
                    $params,
                    $p,
                    $pageSize
                )->onConnection('crawler_redis');
            }
        }
    }

    /**
     * 单次请求数据 (对象风格)
     * @param array $Ali1688AccessToken ['client_id','client_secret','access_token']
     * @param array $params 必填至少 feedInfo 或 categoryId
     * @param int $pageNo 页码
     * @param int $pageSize 每页条数
     * @return array  [ 'list' => (array of stdClass), 'totalRow' => int ]
     * @throws Exception
     */
    public static function requestData($Ali1688AccessToken, array $params, int $pageNo = 1, int $pageSize = 20)
    {
        // 注入分页
        $params['pageNo'] = $pageNo;
        $params['pageSize'] = $pageSize;

        // 调用封装的 API 方法，返回 stdClass
        $rawResponse = Ali1688CpsOfferApi::getAli1688CpsOfferList($Ali1688AccessToken, $params);
        //
        //dd($rawResponse);
        // null ⇒ ISPInvokeError ⇒ 跳过
        if ($rawResponse === null) {
            //
            return ['list' => [], 'totalRow' => 0];
        }
        //
        if (isset($rawResponse)) {
            // 判断 result 是否为空
            if (empty($rawResponse->result) || !count($rawResponse->result)) {
                //
                throw new Exception("接口 result 内容为空或缺失");
            }
            // 获取 result (数组 of stdClass)
            $resultList = $rawResponse->result;
            //
            if (is_object($resultList)) {
                // 若只有单个对象，转换为数组
                $resultList = [$resultList];
            }
            // 获取 totalRow
            $totalRow = isset($rawResponse->totalRow) ? (int)$rawResponse->totalRow : 0;
        }
        //
        return [
            'list' => $resultList ?? null,
            'totalRow' => $totalRow ?? null,
        ];
    }

    /**
     * 保存数据到 Plant 表（存在则更新，空则新建）
     * @param array $resultList 数组 of stdClass (每个元素为一条商品记录)
     * @return void
     */
    public static function saveData(array $resultList)
    {
        // 使用事务处理，保证数据一致性
        DB::beginTransaction();
        //
        try {
            //
            foreach ($resultList as $item) {
                // 取出已存在记录或创建新对象
                $Plant = Plant::where('plant_no', $item->offerId)->first();
                //
                if (!isset($Plant)) {
                    //
                    $Plant = new Plant();
                    $Plant->plant_state = 1;
                }
                // 找出分类(第三级分类) - 仅示例
                $categoryNo = FetchAli1688CpsOfferToPlantService::splitCategoryId($item->categoryId, 'third') ?? $Plant->plant_category_no;
                $Category = Category::where('ali1688_cat_no', $categoryNo)->first();
                // 获取或创建店铺
                $shopNo = $item->sellerId ?? $Plant->shop_no ?? 0;
                $Shop = Shop::where('shop_no', $shopNo)->first() ?? new Shop();
                // 更新或设置店铺信息
                $Shop->source_id = 5;
                $Shop->shop_no = $shopNo;
                $Shop->is_shili = $item->slsjFlag ?? $Shop->is_shili ?? 0;
                $Shop->is_depth = $item->sdrzFlag ?? $Shop->is_depth ?? 0;
                $Shop->is_import = $item->jkhyFlag ?? $Shop->is_import ?? 0;
                $Shop->is_drop = $item->yjdf_flag ?? $Shop->is_drop ?? 0;
                $Shop->is_hyper = $item->hyperFlag ?? $Shop->is_hyper ?? 0;
                $Shop->save();
                // 关联 Plant -> Shop
                $Plant->shop_id = $Shop->id;
                $Plant->shop_no = $Shop->shop_no;
                $Plant->shop_name = $Shop->shop_name ?? $Plant->shop_name; // 若Shop有名称可赋值
                // 关联分类
                $Plant->category_id = $Category->id ?? null;
                $Plant->source_id = 5;
                // 保存商品信息
                $Plant->plant_no = $item->offerId ?? $Plant->plant_no;
                $Plant->plant_subject = $item->title ?? $Plant->plant_subject;
                $Plant->plant_pic = $item->imgUrl ?? $Plant->plant_pic;
                $Plant->plant_price = $item->price ?? $Plant->plant_price;
                $Plant->sale_quantity = $item->saleQuantity ?? $Plant->sale_quantity;
                $Plant->plant_url = $item->url ?? $Plant->plant_url;
                $Plant->plant_ratio = $item->ratio ?? $Plant->plant_ratio;
                $Plant->plant_commission = $item->tkCommission ?? $Plant->plant_commission;
                $Plant->plant_promotion = $item->tkCnt ?? $Plant->plant_promotion;
                $Plant->plant_begin = $item->quantityBegin ?? $Plant->plant_begin;
                $Plant->plant_unit = $item->unit ?? $Plant->plant_unit;
                //
                $Plant->plant_industry_no = FetchAli1688CpsOfferToPlantService::splitCategoryId($item->categoryId, 'first') ?? $Plant->plant_industry_no;
                $Plant->plant_category_no = $categoryNo;
                $Plant->plant_buyer = $item->oldBuyerRatio ?? $Plant->plant_buyer;
                $Plant->is_cps = 1;
                $Plant->plant_state = 3;
                //
                $Plant->update_time = now();
                $Plant->save();
            }
            // 提交事务
            DB::commit();
        } catch (Exception $e) {
            // 发生异常时回滚事务
            DB::rollBack();
        }
    }

    /**
     * 根据分类字符串和等级返回对应分类 ID
     * 例："0\t1042954\t201556615\t127406011"
     * 若首位是 "0" 则删除；后面的依次为第一、第二、第三分类
     * @param string $categoryId
     * @param string $level 'first', 'second', 'third'
     * @return string|null
     */
    public static function splitCategoryId(string $categoryId, string $level): ?string
    {
        //
        $parts = explode("\t", $categoryId);
        // 若第一个元素是 "0"，则去除它
        if (isset($parts[0]) && $parts[0] === '0') {
            array_shift($parts);
        }

        $mapping = [
            'first' => 0,
            'second' => 1,
            'third' => 2,
        ];

        if (!isset($mapping[$level])) {
            return null;
        }
        //
        return $parts[$mapping[$level]] ?? null;
    }
}
