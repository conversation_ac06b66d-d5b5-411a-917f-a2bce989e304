<?php

namespace App\Services\Browser\Plant\Ali1688Cps;

use App\Models\PinCenter\Pin;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\DB;

class SaveAli1688CpsPlantToPinService extends BaseService
{
    /**
     * 保存Ali1688 Cps商品信息到Pin表
     * 该方法通过事务处理，确保数据一致性
     * @param $PlantList
     * @return void
     * @throws Exception
     */
    public static function SaveAli1688CpsPlantToPinData($Plant)
    {
        // 使用事务处理，保证数据一致性
        DB::beginTransaction();
        //
        try {
            // 取出已存在记录或创建新对象
            $Pin = Pin::where('pin_no', $Plant->plant_no)->first() ?? new Pin();
            // 关联 Plant -> Shop
            $Pin->shop_id = $Plant->shop_id ?? $Pin->shop_id;
            $Pin->shop_no = $Plant->shop_no ?? $Pin->shop_no;
            $Pin->shop_name = $Plant->shop_name ?? $Pin->shop_name; // 若Shop有名称可赋值
            // 关联分类
            $Pin->category_id = $Plant->category_id ?? $Pin->category_id;
            $Pin->source_id = $Plant->source_id ?? $Pin->source_id;
            // 保存商品信息
            $Pin->pin_no = $Plant->plant_no ?? $Pin->pin_no;
            $Pin->pin_subject = $Plant->plant_subject ?? $Pin->pin_subject;
            $Pin->pin_pic = $Plant->plant_pic ?? $Pin->pin_pic;
            $Pin->pin_price = $Plant->plant_price ?? $Pin->pin_price;
            $Pin->sale_quantity = $Plant->sale_quantity ?? $Pin->sale_quantity;
            $Pin->pin_url = $Plant->plant_url ?? $Pin->pin_url;
            $Pin->pin_ratio = $Plant->plant_ratio ?? $Pin->pin_ratio;
            $Pin->pin_commission = $Plant->plant_commission ?? $Pin->pin_commission;
            $Pin->pin_promotion = $Plant->plant_promotion ?? $Pin->pin_promotion;
            $Pin->pin_begin = $Plant->plant_begin ?? $Pin->pin_begin;
            $Pin->pin_unit = $Plant->plant_unit ?? $Pin->pin_unit;
            // 额外字段
            $Pin->pin_industry_no = $Plant->plant_industry_no ?? $Pin->plant_industry_no;
            $Pin->pin_category_no = $Plant->pin_category_no ?? $Pin->pin_category_no;
            $Pin->pin_buyer = $Plant->pin_buyer ?? $Pin->pin_buyer;
            $Pin->is_cps = $Plant->is_cps ?? $Pin->is_cps;
            // 保存 Pin 信息
            $Pin->save();
            // 更新 Plant 状态
            $Plant->plant_state = 2;
            $Plant->plant_state = now();
            $Plant->save();
            //提交事务
            DB::commit();
        } catch (Exception $e) {
            //
            // 发生异常时回滚事务
            DB::rollBack();
        }
    }
}
