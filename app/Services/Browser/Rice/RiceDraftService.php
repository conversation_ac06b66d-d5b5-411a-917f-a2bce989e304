<?php

namespace App\Services\Browser\Rice;

use App\Models\IndustryBrace\Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\PlatformBrace\Source;
use App\Models\RiceCenter\Rice;
use App\Services\BaseService;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Throwable;

class RiceDraftService extends BaseService
{
    /**
     * 发起分页数据的获取请求，并执行相应处理
     * * @param int $pageIndex
     * * @param int $pageSize
     * @param int $pageIndex
     * @param int $pageSize
     * @return array|object
     * @throws ConnectionException
     * @throws Throwable
     */
    public static function fetchPaginatedData(int $pageIndex, int $pageSize)
    {
        // 对应您的 cURL 请求中所有 Header 参数
        $response = Http::withHeaders([
            'accept' => 'application/json, text/plain, */*',
            'accept-language' => 'zh-CN,zh;q=0.9',
            'accesstoken' => 'alibabaweb123bbe60ab30d49b33d88d',
            'content-type' => 'application/json',
            'origin' => 'https://sjzl.fjdaze.com',
            'priority' => 'u=1, i',
            'referer' => 'https://sjzl.fjdaze.com/',
            's-d' => 'fdec974900224fe912a841763fcfef14',
            's-h' => '1742580601629',
            's-j' => 'ebce5570cf9a3a471db823458203ba5c',
            's-m' => '1d2140781520d14b3d183b61fea7dd98dcfa',
            's-r' => '16eb5d271f9331418c1bfca1628e485df36b',
            's-s' => '71d60e8cb8112cd8d60ced71a783da76',
            's-t' => '1742580611628',
            'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"Windows"',
            'sec-fetch-dest' => 'empty',
            'sec-fetch-mode' => 'cors',
            'sec-fetch-site' => 'same-site',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ])
            ->timeout(1000)
            ->post(
                'https://api.fjdaze.com/draftBoxManagerHost/api/DraftBox/Search',
                [
                    'state' => [200, 404],
                    'isNeedCategoryGroupName' => true,
                    'pageIndex' => $pageIndex,
                    'pageSize' => $pageSize,
                ]
            );
        //
        // 将返回结果转为对象
        $data = json_decode($response->body(), true);
        //
        // 依据业务逻辑进行判断与处理
        if (isset($data['code']) && $data['code'] == 200) {
            // 将数据批量处理后返回
            $Source = Source::find(5);
            // 查询状态为 0 且 word 为指定值的关键词
            $keywords = Keyword::where('word_state', 0)->get();
            //
            foreach ($data['result']['list'] as $riceData) {
                //
                RiceDraftService::batchFetchDraftRice($riceData, $Source, $keywords);
            }
            //
            echo $pageIndex;
            //
            return [
                'pageSize' => $pageSize,
                'pageIndex' => $pageIndex,
                'items' => $data['result']['list'] ?? [],
                'totalCount' => $data['result']['totalCount'] ?? 0,
                'totalPages' => ceil(($data['result']['totalCount'] ?? 0) / $pageSize),
            ];
        }
        //
        echo 'error';
        return ['error' => '请求失败，无法获取数据。'];
    }

    /**
     * 批量将指定的 Rice 数据发布（转换并入库）到 Spare
     * @param $riceData
     * @param $Source
     * @param $keywords
     * @return void
     * @throws Throwable
     */
    public static function batchFetchDraftRice($riceData, $Source = [], $keywords = []): void
    {
        //
        // 如果你的前端是直接传数组而不是 JSON 字符串，则不需要 json_decode，直接用 $request->input('form.rice_json') 即可。
        // 但这里假设它是字符串，需要 decode。且默认返回的是 stdClass 对象数组。
        DB::beginTransaction();
        //
        try {
            //
            $matchedKeywordIds = [];
            // $riceData 就是一个 stdClass 对象，属性通过 -> 访问
            $rice = Rice::where('draft_no', $riceData['draftBoxId'])->first();
            $Category = Category::where('ali1688_cat_no', $riceData['categoryId'])->first();
            //
            if (!isset($rice)) {
                // 没有则新建
                $rice = new Rice();
            }
            // 以下为一对一赋值（使用对象属性而非数组下标）
            $rice->category_id = $rice->category_id ?? $Category->id;
            $rice->source_id = $rice->source_id ?? $Source->id;
            $rice->rice_category_no = $riceData['categoryId'] ?? $rice->rice_category_no;
            $rice->rice_category_name = $riceData['categoryName'] ?? $rice->rice_category_name;
            $rice->group_id = $riceData['groupId'] ?? $rice->group_id;
            $rice->group_name = $riceData['categoryGroupName'] ?? $rice->group_name;
            //
            $rice->rice_title = $rice->rice_title ?? $riceData['subject'];
            $rice->rice_number = $rice->rice_number ?? $riceData['cargoNumber'];
            $rice->rice_images = json_decode($riceData['images']) ?? $rice->rice_images;
            $rice->rice_preview = $rice->rice_preview ?? (isset($riceData['images']) ? json_decode($riceData['images'])[0] : $rice->rice_preview);
            $rice->rice_image = $rice->rice_image ?? (isset($riceData['images']) ? json_decode($riceData['images'])[0] : $rice->rice_image);
            //
            $rice->from_data = $riceData['dataFrom'] ?? $rice->from_data;
            $rice->rice_json = json_decode($riceData['productInfoJson']) ?? $rice->rice_json;
            $rice->product_json = json_decode($riceData['showProductInfoJson']) ?? $rice->product_json;
            $rice->relation_json = json_decode($riceData['relationDataJson']) ?? $rice->relation_json;
            //
            $rice->amount_sale = $riceData['amountOnSale'] ?? $rice->amount_sale;
            $rice->price_ranges = json_decode($riceData['priceRanges']) ?? $rice->price_ranges;
            $rice->full_tips = $riceData['dataFullTips'] ?? $rice->full_tips;
            $rice->rice_scene = $riceData['productScene'] ?? $rice->rice_scene;
            //
            $rice->publish_id = $riceData['publishProductId'] ?? $rice->publish_id;
            $rice->draft_no = $riceData['draftBoxId'] ?? $rice->draft_no;
            $rice->relation_no = $riceData['relationDataId'] ?? $rice->relation_no;
            $rice->plan_no = $riceData['planNo'] ?? $rice->plan_no;
            $rice->record_no = $riceData['detailMakingRecordId'] ?? $rice->record_no;
            //
            $rice->create_time = $riceData['createTime'] ?? $rice->create_time;
            $rice->modify_time = $riceData['modifyTime'] ?? $rice->modify_time;
            $rice->timing_time = $riceData['timingPublishTime'] ?? $rice->timing_time;
            //
            $rice->is_product = $riceData['isIdentityProduct'] ?? 0;
            $rice->is_identity = $riceData['isIdentity'] ?? 0;
            $rice->is_trade = $riceData['isSupportOnlineTrade'] ?? 0;
            $rice->is_mix = $riceData['isSupportMix'] ?? 0;
            $rice->is_full = $riceData['isDataFull'] ?? 1;
            //
            $rice->publish_tips = $riceData['publishTips'] ?? $rice->publish_tips;
            $rice->publish_state = $riceData['timingPublishState'] ?? $rice->publish_state;
            //
            if (count($rice->rice_json) > 0) {
                $rice->is_valid = 1;
            } else {
                $rice->is_valid = 0;
            }
            // 保存
            $rice->save();
            //
            if (isset($keywords)) {
                // 遍历查询到的关键词，判断 $rice->rice_title 中是否存在该关键词
                foreach ($keywords as $keyword) {
                    if (strpos($rice->rice_title, $keyword->word) !== false) {
                        $matchedKeywordIds[] = $keyword->id;
                    }
                }
                // 如果匹配到关键词，则建立多对多关联，避免重复添加
                if (!empty($matchedKeywordIds)) {
                    //
                    $rice->keywords()->syncWithoutDetaching($matchedKeywordIds);
                }
            }
            // 提交事务
            DB::commit();
        } catch (Exception $e) {
            // 回滚事务
            DB::rollBack();
        }
    }
}
