<?php

namespace App\Services\Browser\Shop\Ali1688Cps;

use App\Api\Ali1688Cps\Ali1688CpsShopApi;
use App\Jobs\Browser\Shop\Ali1688Cps\FetchAli1688CpsShopToShopQueue;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Exception;

class FetchAli1688CpsShopToShopService extends BaseService
{
    /**
     * 拉取并存储所有分页数据（调用队列处理后续页）
     * @param array $Ali1688AccessToken 令牌信息
     * @param array $params 请求的其他参数（如 categoryId, pageNo 等）
     * @param int $pageSize 默认每页 20 条
     * @return void
     * @throws Exception
     */
    public static function fetchAndStore(array $Ali1688AccessToken, array $params, int $pageSize = 20): void
    {
        // 1. 设置页码/页大小参数
        $params['pageNo'] = $params['pageNo'] ?? 1;
        $params['pageSize'] = $params['pageSize'] ?? $pageSize;

        // 2. 先抓取第一页数据（同步）
        $firstPage = self::requestData($Ali1688AccessToken, $params);
        //   保存第一页数据
        self::saveData($firstPage['list']);

        // 3. 计算总页数
        $totalRow = $firstPage['totalRow'];
        $totalPages = $totalRow > 0 ? (int)ceil($totalRow / $params['pageSize']) : 1;

        // 4. 从第二页开始，其余都丢到队列中处理
        for ($p = 2; $p <= $totalPages; $p++) {
            //
            $newParams = $params;
            $newParams['pageNo'] = $p;
            // 将该页的任务加入队列异步执行
            FetchAli1688CpsShopToShopQueue::dispatch($Ali1688AccessToken, $newParams);
        }
    }

    /**
     * 单页请求数据 (返回数组格式)
     * @param array $Ali1688AccessToken 形如 ['client_id','client_secret','access_token']
     * @param array $params 至少包含 categoryId, pageNo, pageSize
     * @return array  [ 'list' => (array of stdClass), 'totalRow' => int ]
     * @throws Exception
     */
    public static function requestData(array $Ali1688AccessToken, array $params): array
    {
        // 1. 调用远程 API
        $rawResponse = Ali1688CpsShopApi::getAli1688CpsShopList($Ali1688AccessToken, $params);

        // 2. 简单校验响应格式是否为 object 且包含所需字段
        if (!is_object($rawResponse)) {
            throw new Exception("API 返回格式非对象，请检查接口调用或返回内容");
        }
        if (!isset($rawResponse->result) || !is_array($rawResponse->result)) {
            throw new Exception("API 返回结果中缺少 [result] 字段或类型错误");
        }

        // 3. 提取数据
        $totalRow = isset($rawResponse->totalRow) ? (int)$rawResponse->totalRow : 0;

        return [
            'list' => $rawResponse->result,
            'totalRow' => $totalRow,
        ];
    }

    /**
     * 保存店铺数据到本地 Shop 表
     * @param array $shopList 数组 of stdClass (每个元素表示一家店铺)
     * @return void
     */
    public static function saveData(array $shopList): void
    {
        //
        foreach ($shopList as $item) {
            // 如果 sellerId 为空，跳过
            if (empty($item->sellerId)) {
                continue;
            }
            // 根据 sellerId 查询/创建对应 Shop
            $shop = Shop::where('shop_no', $item->sellerId)->first() ?? new Shop();
            // 更新或写入字段
            // （若只想插入不更新，可根据需要做额外判断）
            $shop->shop_no = $item->sellerId;
            $shop->shop_name = $item->companyName ?? $shop->shop_name;
            $shop->shop_url = $item->linkUrl ?? $shop->shop_url;
            $shop->shop_grade = $item->tradeGrade ?? $shop->shop_grade;
            $shop->shop_ratio = $item->ratio ?? $shop->shop_ratio;       // 平均佣金比例
            $shop->shop_num = $item->productCnt ?? $shop->shop_num;         // 商品数量
            $shop->promotion_count = $item->tkCnt ?? $shop->promotion_count;  // 30天推广量
            //
            $shop->save();
        }
    }
}
