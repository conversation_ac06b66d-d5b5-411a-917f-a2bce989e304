<?php

namespace App\Services\Browser\Shop\Ali1688MiaoShou;

use App\Jobs\Browser\Pin\Ali1688MiaoShou\SaveAli1688MiaoShouItemToPinQueue;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 1688喵手店铺商品同步到Pin中心服务
 * 实现要点：
 * - 分页批量拉取1688店铺所有商品数据
 * - 通过队列异步写入Pin表，极大提升大店铺数据同步性能
 * - 自动去重，健壮容错，日志详尽
 */
class FetchAli1688MiaoShouShopToPinService extends BaseService
{
    /**
     * 主入口：全量同步指定1688店铺下的商品数据
     * 流程概述：
     * 1. 请求第一页商品数据，并获得商品总数
     * 2. 推送第一页商品到队列
     * 3. 更新Shop统计
     * 4. 若需分页，则循环抓取后续页并推送
     * @param object $Shop 店铺模型对象，需含shop_url、shop_real等字段
     * @return void
     */
    public static function FetchAli1688MiaoShouShopToPinData($Shop): void
    {
        //
        set_time_limit(3600000000); // 避免大店铺同步超时
        //
        // 构造必要的cookie（如需长期有效应支持配置）
        $cookies = [
            'Hm_lvt_819279152059dbb9aa3ba9b1e2269b4e' => '1744827886,1744859012',
            'acn_s' => 't4lqjp82lfduja1fah183endr4',
            'acn' => 't4lqjp82lfduja1fah183endr4',
            'acw_tc' => '0b62601e17488500508026586ee00301fbb0ed8785f71d4ba78146f671cdc7',
        ];
        //
        $pageNo = 1;      // 起始页
        $pageSize = 20;   // 每页条数（1688接口约定）
        $pageSpan = 100;  // 推荐固定100
        //
        try {
            // 1. 拉取第一页，拿到商品总数
            $data = static::fetchAli1688Page($Shop, $pageNo, $pageSize, $pageSpan, $cookies);

            if (!$data || !isset($data['skuList'])) {
                //
                Log::error("店铺商品数据抓取失败：未获取到有效数据", [
                    'shop_id' => $Shop->id,
                    'shop_name' => $Shop->shop_name ?? '未知店铺'
                ]);
                //
                return;
            }

            //
            $total = intval($data['total'] ?? 0);
            $skuList = $data['skuList'] ?? [];
            $shopArray = $Shop->toArray();

            // 2. 统一推送队列（自动去重，便于后续维护）
            static::saveAli1688PinList($skuList, $shopArray);

            // 3. Shop表统计信息只需更新一次
            $Shop->shop_rate = $skuList[0]['sourceNameFW'] ?? $Shop->shop_rate ?? null;
            $Shop->shop_num = $total;
            $Shop->update_time = now();
            $Shop->save();
            //
            Log::error("1688 API需要抓取数量", [
                'shop_id' => $Shop->id,
                'page_no' => $pageNo,
                'work_count' => $pageSize * $pageSpan * $pageNo,
                'total_count' => $total
            ]);
            //
            // 4. 若商品总数大于拉取数，则继续分页抓取
            if ($total > ($pageSize * $pageSpan * $pageNo)) {
                //
                static::fetchAli1688Paged($Shop, $pageNo + 1, $pageSize, $pageSpan, $total, $cookies);
            }
        } catch (Exception $e) {
            //
            dd($e->getMessage());
            Log::error("店铺商品同步过程发生异常", [
                'shop_id' => $Shop->id ?? null,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }

    /**
     * 统一API请求方法：抓取某一页商品数据
     * @param object $Shop 店铺对象
     * @param int $pageNo 当前页码
     * @param int $pageSize 每页条数
     * @param int $pageSpan 分页跨度
     * @param array $cookies 请求cookie
     * @return array|null      API成功时返回数据数组，否则返回null
     */
    public static function fetchAli1688Page($Shop, $pageNo, $pageSize, $pageSpan, $cookies = [])
    {
        //
        $headers = [
            'Accept' => 'application/json, text/plain, */*',
            'Accept-Language' => 'zh-CN,zh;q=0.9',
            'Connection' => 'keep-alive',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Origin' => 'https://v2.acn.chengji-inc.com',
            'Referer' => 'https://v2.acn.chengji-inc.com/move/target?copyType=shop_address',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-origin',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"Windows"',
        ];
        //
        try {
            //
            $response = Http::timeout(6000000000)
                ->withOptions(['verify' => false])
                ->withHeaders($headers)
                ->withCookies($cookies, 'v2.acn.chengji-inc.com')
                ->asForm()
                ->post('https://v2.acn.chengji-inc.com/api/api_v2/move/batch/searchProducts', [
                    'pageNo' => $pageNo,
                    'pageSize' => $pageSize,
                    'source' => '1688',
                    'isFastMode' => 1,
                    'domain' => $Shop->shop_url . '?' . $Shop->shop_real,
                    'seller' => $Shop->shop_name ?? $Shop->company_name,
                    'pageSpan' => $pageSpan,
                    'keyword' => '',
                    'sortValue' => 'timedown',
                    'categoryId' => '',
                    'onlineTimeLimit' => '',
                ]);
            //
            if ($response->successful()) {
                //
                //dd($response->json());
                //
                Log::error("1688 API正在请求页面", [
                    'shop_id' => $Shop->id,
                    'page_no' => $pageNo,
                    'work_count' => $pageSize * $pageSpan * $pageNo,
                ]);
                //
                return $response->json();
            } else {
                //
                dd($response);
                //
                Log::error("1688 API请求失败", [
                    'shop_id' => $Shop->id,
                    'page_no' => $pageNo,
                    'status_code' => $response->status(),
                    'response_body' => $response->body()
                ]);
                return null;
            }
        } catch (Exception $e) {
            //
            dd($e->getMessage());
            Log::error("API请求过程发生异常", [
                'shop_id' => $Shop->id ?? null,
                'page_no' => $pageNo,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * 批量保存商品数据并推送到队列（自动去重）
     * @param array $skuList 商品列表（每条为一件商品的原始数组）
     * @param array $shopArray 店铺信息数组
     * @return void
     */
    public static function saveAli1688PinList(array $skuList, array $shopArray)
    {
        //
        foreach ($skuList as $pinData) {
            // itemId或productId作唯一标识
            $pinNo = $pinData['itemId'] ?? $pinData['productId'] ?? null;
            //
            if (!$pinNo) {
                // 缺唯一标识跳过
                Log::warning('商品数据缺少唯一标识，已跳过', ['data' => $pinData]);
                continue;
            }
            // 将任务推送到指定队列 'work_remote'
            // 你需要使用 dispatch() 方法并通过 ->onQueue() 指定队列名称
            dispatch(new SaveAli1688MiaoShouItemToPinQueue($pinData, $shopArray))->onQueue('work')->onConnection('work_remote');  // 指定队列使用的连接
        }
    }
    //dispatch(new SaveAli1688MiaoShouItemToPinQueue($pinData, $shopArray));

    /**
     * 循环分页抓取剩余所有商品数据，并批量推送队列
     * @param object $Shop 店铺对象
     * @param int $startPage 起始页码（通常为2）
     * @param int $pageSize 每页商品数
     * @param int $pageSpan 分页跨度
     * @param int $total 商品总数
     * @param array $cookies 请求cookie
     * @return void
     */
    public static function fetchAli1688Paged($Shop, $startPage, $pageSize, $pageSpan, $total, $cookies)
    {
        //
        $shopArray = $Shop->toArray();
        $maxPages = 1000;  // 防死循环
        $pageNo = $startPage;
        $fetched = ($startPage - 1) * $pageSize;
        //
        try {
            //
            while ($fetched < $total && $pageNo <= $maxPages) {
                //
                $data = static::fetchAli1688Page($Shop, $pageNo, $pageSize, $pageSpan, $cookies);

                if ($data && isset($data['skuList']) && count($data['skuList'])) {
                    //
                    static::saveAli1688PinList($data['skuList'], $shopArray);
                    $fetched += count($data['skuList']);
                } else {
                    //
                    Log::warning("分页抓取中断：当前页无有效数据", [
                        'shop_id' => $Shop->id,
                        'page_no' => $pageNo,
                        'fetched_count' => $fetched,
                        'total_count' => $total
                    ]);
                    break;
                }
                //
                $pageNo++;
            }
            //
            Log::info("店铺商品分页抓取完成", [
                'shop_id' => $Shop->id,
                'total_pages' => $pageNo - $startPage,
                'fetched_count' => $fetched,
                'total_count' => $total
            ]);
        } catch (Exception $e) {
            //
            dd($e->getMessage());
            Log::error("分页抓取过程发生异常", [
                'shop_id' => $Shop->id ?? null,
                'page_no' => $pageNo,
                'fetched_count' => $fetched,
                'total_count' => $total,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
        }
    }
}
