<?php

namespace App\Services\Browser\Shop\Ali1688MiaoShou;

use App\Models\ShopCenter\ShopCat;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchAli1688MiaoShouShopToShopCatService extends BaseService
{
    /**
     * 保存店铺数据到本地 Shop 表
     * @param array $shopList 数组 of stdClass (每个元素表示一家店铺)
     * @return void
     */
    public static function FetchAli1688MiaoShouShopToShopCatData($Shop): void
    {
        //
        set_time_limit(36000); // Set the maximum execution time to 300 seconds (5 minutes)->whereNotNull('shop_domain')
        //
        try {
            //
            // 发送 API 请求，获取店铺分类数据
            $response = Http::withHeaders([
                'Accept' => 'application/json, text/plain, */*',
                'Accept-Language' => 'zh-CN,zh;q=0.9',
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Sec-CH-UA' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'Sec-CH-UA-Mobile' => '?0',
                'Sec-CH-UA-Platform' => '"Windows"',
                'Sec-Fetch-Dest' => 'empty',
                'Sec-Fetch-Mode' => 'cors',
                'Sec-Fetch-Site' => 'same-origin',
                'Cookie' => 'Hm_lvt_819279152059dbb9aa3ba9b1e2269b4e=1744827886,1744859012;acw_tc=0b62602617453862955056911ed8d0ec11b18df4fb3f3e8a5f58d33d73a502;',
                'Referer' => 'https://v2.acn.chengji-inc.com/move/target?copyType=shop_address',
                'Referrer-Policy' => 'strict-origin-when-cross-origin',
            ])
                ->asForm()
                ->post('https://v2.acn.chengji-inc.com/api/api_v2/move/batch/searchShopCatTree', [
                    'shopUrl' => $Shop->shop_url,
                    'source' => '1688',
                    'isFastMode' => '1',
                ]);

            // 获取 API 响应数据
            $data = $response->json();

            // 如果请求成功，处理分类数据
            if ($data['result'] === 'success') {
                // 判断是否存在分类数据
                if (isset($data['shopCateArray']) && !empty($data['shopCateArray'])) {
                    // 遍历每个分类
                    foreach ($data['shopCateArray'] as $category) {
                        // 如果分类信息不完整，则跳过
                        if (empty($category['cid']) || empty($category['name'])) {
                            continue;
                        }

                        // 创建父分类节点
                        $parentCategory = ShopCat::create([
                            'shop_id' => $Shop->id,
                            'cat_no' => $category['cid'],
                            'cat_name' => $category['name'],
                            'parent_id' => null, // 根节点没有父节点
                        ]);

                        // 遍历子分类并创建节点
                        foreach ($category['subShopCates'] ?? [] as $subCategory) {
                            //
                            if (empty($subCategory['cid']) || empty($subCategory['name'])) {
                                //
                                continue;
                            }
                            // 创建子分类节点
                            $subCat = new ShopCat([
                                'shop_id' => $Shop->id,
                                'cat_no' => $subCategory['cid'],
                                'cat_name' => $subCategory['name'],
                            ]);
                            // 将子分类附加到父分类节点
                            $parentCategory->appendNode($subCat);
                        }
                    }
                }

                // 更新店铺信息
                $Shop->shop_num = $data['total'] ?? $Shop->shop_num;
                $Shop->update_time = now();
                $Shop->save();
            } else {
                // 如果请求失败，记录错误日志
                Log::error("Shop ID: {$Shop->id} - 请求失败，返回结果: {$data['result']}");
            }
        } catch (Exception $e) {
            //
            Log::error("Error for Shop ID: {$Shop->id} - " . $e->getMessage());
        }
    }
}
