<?php

namespace App\Services\Chain\Pin;

use App\Models\PinCenter\Pin;
use App\Models\RiceCenter\Rice;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * PinToRiceService 服务类
 * 功能说明：
 * 该服务负责同步Pin和Rice之间的数据，确保两个实体之间的信息一致性。
 * 当Pin完成分类后，需要将相关信息同步到对应的Rice记录中，同时也要将Rice的信息反向同步到Pin。
 * 同步内容包括：
 * - 店铺信息（shop_id, shop_no, shop_name, shop_url）
 * - 商品信息（pin_subject, rice_number）
 * - 状态标记（is_rice, is_pin, rice_state）
 * - 时间戳（rice_time）
 */
class PinToRiceService
{
    /**
     * 更新Pin与Rice的关联状态
     * 处理流程：
     * 1. 根据pin_no查找对应的Rice记录
     * 2. 如果找到Rice，双向同步数据
     * 3. 标记同步状态，记录同步时间
     * @param Pin $pin 需要处理的Pin对象
     * @return bool 返回是否成功更新
     */
    public static function updateUpdateRiceState(Pin $pin): bool
    {
        try {
            // 开启数据库事务，确保数据一致性
            return DB::transaction(function () use ($pin) {
                /*------------------------------------------------------------------
                | 1. 查找对应的Rice记录
                | 条件：pin_no相同且rice_state大于0（有效状态）
                ------------------------------------------------------------------*/
                $rice = Rice::where('pin_no', $pin->pin_no)
                    ->where('rice_state', '>', 0)
                    ->first();

                // 如果没有找到对应的Rice记录，记录日志并返回false
                if (!$rice) {
                    Log::info('未找到对应的Rice记录', [
                        'pin_id' => $pin->id,
                        'pin_no' => $pin->pin_no
                    ]);
                    return false;
                }

                /*------------------------------------------------------------------
                | 2. 准备同步数据
                | 使用 ?? 操作符优先使用已有数据，避免覆盖
                ------------------------------------------------------------------*/
                $now = Carbon::now();

                /*------------------------------------------------------------------
                | 3. 更新Pin记录
                | 从Rice同步数据到Pin，只更新Pin中缺失的字段
                ------------------------------------------------------------------*/
                $pinUpdated = false;

                // 同步商品编号
                if (empty($pin->pin_number) && !empty($rice->rice_number)) {
                    $pin->pin_number = $rice->rice_number;
                    $pinUpdated = true;
                }

                // 同步店铺信息
                if (empty($pin->shop_id) && !empty($rice->shop_id)) {
                    $pin->shop_id = $rice->shop_id;
                    $pinUpdated = true;
                }

                if (empty($pin->shop_no) && !empty($rice->shop_no)) {
                    $pin->shop_no = $rice->shop_no;
                    $pinUpdated = true;
                }

                if (empty($pin->shop_name) && !empty($rice->shop_name)) {
                    $pin->shop_name = $rice->shop_name;
                    $pinUpdated = true;
                }

                if (empty($pin->shop_url) && !empty($rice->shop_url)) {
                    $pin->shop_url = $rice->shop_url;
                    $pinUpdated = true;
                }

                // 同步商品标题
                if (empty($pin->pin_subject) && !empty($rice->pin_subject)) {
                    $pin->pin_subject = $rice->pin_subject;
                    $pinUpdated = true;
                }

                // 标记Pin已关联Rice
                $pin->rice_state = 1;  // 已关联状态
                $pin->is_rice = 1;     // 标记已有对应Rice
                $pin->rice_time = $now; // 记录关联时间

                // 保存Pin的更改
                $pin->save();

                /*------------------------------------------------------------------
                | 4. 更新Rice记录
                | 从Pin同步数据到Rice，只更新Rice中缺失的字段
                ------------------------------------------------------------------*/
                $riceUpdated = false;

                // 同步店铺信息到Rice
                if (empty($rice->shop_id) && !empty($pin->shop_id)) {
                    $rice->shop_id = $pin->shop_id;
                    $riceUpdated = true;
                }

                if (empty($rice->shop_no) && !empty($pin->shop_no)) {
                    $rice->shop_no = $pin->shop_no;
                    $riceUpdated = true;
                }

                if (empty($rice->shop_name) && !empty($pin->shop_name)) {
                    $rice->shop_name = $pin->shop_name;
                    $riceUpdated = true;
                }

                if (empty($rice->shop_url) && !empty($pin->shop_url)) {
                    $rice->shop_url = $pin->shop_url;
                    $riceUpdated = true;
                }

                // 同步商品标题到Rice
                if (empty($rice->pin_subject) && !empty($pin->pin_subject)) {
                    $rice->pin_subject = $pin->pin_subject;
                    $riceUpdated = true;
                }

                // 标记Rice已关联Pin
                $rice->is_pin = 1; // 标记已有对应Pin

                // 保存Rice的更改
                $rice->save();

                // 记录成功日志
                Log::info('Pin与Rice同步成功', [
                    'pin_id' => $pin->id,
                    'pin_no' => $pin->pin_no,
                    'rice_id' => $rice->id,
                    'pin_updated' => $pinUpdated,
                    'rice_updated' => $riceUpdated
                ]);

                return true;
            });

        } catch (Throwable $e) {
            // 捕获并记录异常
            Log::error('Pin与Rice同步失败', [
                'pin_id' => $pin->id,
                'pin_no' => $pin->pin_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
}
