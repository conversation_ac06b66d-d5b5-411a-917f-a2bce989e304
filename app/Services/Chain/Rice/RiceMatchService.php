<?php

namespace App\Services\Chain\Rice;

use App\Enums\State\RiceStateEnum;
use App\Enums\State\StoreStateEnum;
use App\Models\IndustryBrace\Category;
use App\Models\StoreCenter\Store;
use App\Services\BaseService;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class RiceMatchService extends BaseService
{
    /**
     * 匹配并更新稻米类别
     * @param string $categoryUuid 类别的UUID
     * @param Collection $Rices 稻米对象集合
     * @return array 返回更新后的稻米记录
     * @throws Exception 如果未找到类别记录，抛出异常
     */
    public static function batchMatchCategory(string $categoryUuid, $Rices): array
    {
        //
        $Category = Category::where('platform_id', config('app.platform_id'))
            ->whereUuid($categoryUuid)
            ->where('level', 2)
            ->where('is_state', 1)
            ->first();

        if (!$Category) {
            //
            throw new Exception("未找到指定的类别记录，UUID: {$categoryUuid}");
        }
        //
        foreach ($Rices as $Rice) {
            //
            $Rice->category_id = $Category->id;
            $Rice->retail_category_no = $Category->ali1688_cat_no;
            $Rice->rice_state = RiceStateEnum::category();
            $Rice->is_category = 1;
            $Rice->save();
        }
        //
        return $Rices->toArray();
    }

    /**
     * 匹配并更新稻米商店
     * @param string $storeUuid 商店的UUID
     * @param Collection $Rices 稻米对象集合
     * @return array 返回更新后的稻米记录
     * @throws Exception 如果未找到商店记录，抛出异常
     */
    public static function batchMatchStore(string $storeUuid, $Rices): array
    {
        $Store = Store::where('platform_id', config('app.platform_id'))
            ->whereUuid($storeUuid)
            ->where('store_state', StoreStateEnum::soft())
            ->first();

        if (!$Store) {
            throw new Exception("未找到指定的商店记录，UUID: {$storeUuid}");
        }

        foreach ($Rices as $Rice) {
            $Rice->store_id = $Store->id;
            //            $Rice->rice_state = RiceStateEnum::store();
            $Rice->is_store = 1;
            $Rice->save();
        }
        //
        return $Rices->toArray();
    }

    /**
     * 匹配并更新稻米的 schema
     * @param Collection $Rices 稻米对象集合
     * @return array 返回更新后的稻米记录
     */
    public static function batchMatchSchema($Rices): array
    {
        foreach ($Rices as $Rice) {
            $Rice->schema_state = 'updated';
            $Rice->save();
        }

        return $Rices->toArray();
    }

    /**
     * 匹配并更新关键词
     * @param Collection $Rices 稻米对象集合
     * @return array 返回更新后的稻米记录
     */
    public static function batchMatchKeyword($Rices): array
    {
        foreach ($Rices as $Rice) {
            $Rice->syncKeywords();  // 示例同步关键词方法
            $Rice->save();
        }

        return $Rices->toArray();
    }

    /**
     * 匹配并更新稻米
     * @param Collection $Rices 稻米对象集合
     * @return array 返回更新后的稻米记录
     */
    public static function batchMatchRice($Rices): array
    {
        //
        foreach ($Rices as $Rice) {
            $Rice->rice_state = 'updated';
            $Rice->save();
        }

        return $Rices->toArray();
    }
}
