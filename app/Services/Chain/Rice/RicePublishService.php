<?php

namespace App\Services\Chain\Rice;

use App\Enums\Is\IsProcessedEnum;
use App\Enums\State\AlbumStateEnum;
use App\Enums\State\SpareStateEnum;
use App\Enums\Vest\SpareVestEnum;
use App\Models\SpareCenter\Spare;
use App\Models\SpareCenter\SpareLine;
use App\Models\SpareCenter\SpareSale;
use App\Models\SpareCenter\SpareWord;
use App\Services\Admin\Keyword\KeywordService;
use App\Services\BaseService;
use App\Services\Chain\Log;
use Exception;
use Illuminate\Support\Facades\DB;

class RicePublishService extends BaseService
{
    /**
     * 批量将指定的 Rice 数据发布（转换并入库）到 Spare
     * @param string $moldUuid 指定的 mold 唯一标识
     * @param array $retailUuids 零售商（Retail）的 uuid 列表
     * @param array $riceUuids Rice 的 uuid 列表
     * @throws Exception
     */
    public static function batchPublishRice($retails, $rices, $mold): void
    {
        //
        try {
            // 开启事务，确保数据一致性
            DB::beginTransaction();
            /**
             * 4. 逐个零售商 + Rice 循环，写入相应的 Spare / SpareSale / SpareLine / SpareWord
             */
            foreach ($retails as $retail) {
                //
                foreach ($rices as $riceModel) {
                    //
                    RicePublishService::PublishRice($riceModel, $retail, $mold);
                }
            }
            // 提交事务
            DB::commit();
        } catch (Exception $e) {
            // 回滚事务，记录异常日志并抛出
            DB::rollBack();
            Log::error('batchPublish Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * 批量将指定的 Rice 数据发布（转换并入库）到 Spare
     * @param string $moldUuid 指定的 mold 唯一标识
     * @param array $retailUuids 零售商（Retail）的 uuid 列表
     * @param array $riceUuids Rice 的 uuid 列表
     * @throws Exception
     */
    public static function PublishRice($riceModel, $retail, $mold)
    {
        //
        try {
            // 开启事务，确保数据一致性
            DB::beginTransaction();
            /**
             * == (A) 直接组装 Spare 需要的部分字段，代替原先的 RiceToSpare ==
             */
            $addPrice = $mold->markup_add_price ?? 0;
            // 后面写入 SpareWord 用到的关键词（数组）
            $keywords = $riceModel->keywords()->pluck('word')->toArray();
            // 将组装好的字段写入 Spare，如果存在就更新，否则创建
            // 尝试根据 user_id 查找 Spare 记录
            $spare = Spare::where('user_id', $retail->user_id)->where('rice_id', $riceModel->id)->first();
            // 如果没有找到对应的 Spare，再尝试根据 retail_id 查找
            if (!$spare) {
                //
                $spare = Spare::where('retail_id', $retail->id)->where('rice_id', $riceModel->id)->first();
            }
            // 如果依然没有找到记录，创建一个新的 Spare 实例
            if (!$spare) {
                //
                $spare = new Spare();
            }
            // 逐一赋值
            $spare->platform_id = $retail->platform_id;
            $spare->site_id = $retail->site_id;
            $spare->source_id = $retail->source_id;
            $spare->user_id = $retail->user_id;
            $spare->retail_id = $retail->id;
            $spare->mold_id = $mold->id ?? null;
            //
            $spare->automation_type = $mold->automation_type ?? null;
            $spare->rice_id = $riceModel->id;
            $spare->industry_id = $riceModel->industry_id;
            $spare->category_id = $riceModel->category_id;
            $spare->product_id = $riceModel->product_id;
            $spare->product_no = $riceModel->product_no;
            $spare->store_id = $riceModel->store_id;
            //
            $spare->schema_id = $riceModel->schema_id;
            $spare->suit_id = $riceModel->suit_id;
            $spare->spec_id = $riceModel->spec_id;
            $spare->custom_id = $riceModel->custom_id;
            $spare->brand_id = $riceModel->brand_id;
            $spare->retail_category_no = $riceModel->retail_category_no;
            $spare->spare_vest = SpareVestEnum::rice();
            $spare->spare_name = $riceModel->rice_name ?? '无标题';
            $spare->spare_title = $riceModel->rice_title;
            $spare->spare_image_url = $riceModel->rice_preview;
            $spare->spare_images = $riceModel->rice_images;
            $spare->spare_description = $riceModel->rice_description;
            $spare->spare_descries = count($riceModel->rice_descries) ? $riceModel->rice_descries : $riceModel->rice_images;
            //
            $spare->is_processed = IsProcessedEnum::unprocessed();
            $spare->album_state = AlbumStateEnum::pending();
            //$spare->rice_state   = RiceStateEnum::range();
            $spare->spare_state = SpareStateEnum::draft();
            // 保存记录
            $spare->save();

            /**
             * == (B) 遍历 RiceCorn，创建 / 更新 SpareSale ==
             */
            $corns = $riceModel->rice_corns()->orderBy('id', 'desc')->get();
            //
            foreach ($corns as $corn) {
                // 解析 spec / suit (示例逻辑)
                $specAttr = null;
                $specNo = null;
                $suitAttr = null;
                $suitNo = null;
                $seedImageUrl = null;
                // 遍历该 corn 下所有 seed，判断 seed_name 来决定属性
                foreach ($corn->rice_corn_seeds as $seed) {
                    if ($seed->seed_name === 'p-404' || $seed->seed_name === 'p-3216') {
                        //
                        $specAttr = '【' . $seed->seed_text . '】';
                        $specNo = $seed->id;
                        $seedImageUrl = $seed->seed_image_url;
                    }
                    if ($seed->seed_name === 'p-100018377' || $seed->seed_name === 'p-3151') {
                        //
                        $suitAttr = '【' . $seed->seed_text . '】';
                        $suitNo = $seed->id;
                    }
                }
                // 创建 / 更新 SpareSale
                $spareSale = SpareSale::updateOrCreate(
                    [
                        'retail_id' => $retail->id,
                        'spare_id' => $spare->id,
                        'sale_no' => $corn->id,
                    ],
                    [
                        'suit_no' => $suitNo ?? null,
                        'spec_no' => $specNo ?? null,
                        'suit_attr' => $suitAttr ?? null,
                        'spec_attr' => $specAttr ?? null,
                        'sale_stock' => $corn->corn_stock > 0 ? $corn->corn_stock : rand(300, 999),
                        'sale_key' => $corn->corn_key ?? null,
                        'sale_values' => $corn->corn_values ?? null,
                        'sale_price' => isset($corn->corn_price) ? (ceil($corn->corn_price) - 1 + $addPrice) : 0,
                        'sale_sn' => (string)rand(1000, 9999),
                        'sale_status' => $corn->corn_status ? 'online' : 'offline',
                        'sale_image_url' => $seedImageUrl ?? null,
                        //
                        'sale_length' => $corn->corn_length > 0 ? $corn->corn_length : 12.0,
                        'sale_width' => $corn->corn_width > 0 ? $corn->corn_width : 10.0,
                        'sale_height' => $corn->corn_height > 0 ? $corn->corn_height : 3.5,
                        'sale_weight' => $corn->corn_weight > 0 ? $corn->corn_weight : 40.0,
                        'sale_volume' => $corn->corn_volume > 0 ? $corn->corn_volume : 420.0,
                        'sale_multi' => 0 ?? null,
                    ]
                );

                /**
                 * == (C) 遍历该 SpareSale 的所有 Seeds，创建 / 更新 SpareLine，并建立多对多关联 ==
                 */
                $cornSeeds = $corn->rice_corn_seeds()->orderBy('id', 'desc')->get();
                //
                $spareLineIds = [];
                foreach ($cornSeeds as $seed) {
                    // 简单逻辑：根据 seed_name 判断它是 spec 还是 suit
                    $attrType = null;
                    $seedText = null;
                    //
                    if ($seed->seed_name === 'p-404' || $seed->seed_name === 'p-3216') {
                        $attrType = 'spec';
                        $seed_name = 'p-404';
                        $seedText = '【' . $seed->seed_text . '】';
                    }
                    if ($seed->seed_name === 'p-100018377' || $seed->seed_name === 'p-3151') {
                        $attrType = 'suit';
                        $seed_name = 'p-100018377';
                        $seedText = '【' . $seed->seed_text . '】';
                    }
                    //
                    // 示例：根据 attrType 决定显示名称
                    $lineLabel = null;
                    if ($attrType === 'suit') {
                        $lineLabel = '颜色';
                    } elseif ($attrType === 'spec') {
                        $lineLabel = '适用型号';
                    }
                    // 创建 / 更新 SpareLine
                    $spareLine = SpareLine::updateOrCreate(
                        [
                            'retail_id' => $retail->id ?? null,
                            'spare_id' => $spare->id ?? null,
                            'line_name' => $seed_name ?? null,
                            'attr_type' => $attrType ?? null,
                            'line_label' => $lineLabel ?? null,
                            'line_value' => $seed->seed_value ?? null,
                            'line_text' => $seedText ?? null,
                        ],
                        [
                            'line_custom' => $seed->seed_custom ?? null,
                            'line_image_url' => $seed->seed_image_url ?? null,
                        ]
                    );
                    // 收集该 SpareLine 的 ID，用于关联到 spare_sale_lines
                    $spareLineIds[] = $spareLine->id;
                }
                // 建立多对多关联关系
                $spareSale->spare_sale_lines()->sync($spareLineIds);
            }

            /**
             * == (D) 关键词写入 SpareWord ==
             * 先通过 KeywordService::SyncKeywordDataToKeywords 拿到或创建所有关键字记录
             */
            $keywordModels = KeywordService::SyncKeywordDataToKeywords($keywords);
            //
            foreach ($keywordModels as $keywordModel) {
                //
                $SpareWord = SpareWord::where('retail_id', $retail->id)->where('spare_id', $spare->id)->where('keyword_id', $keywordModel->id)->where('word', $keywordModel->word)->first();
                //
                if (is_null($SpareWord)) {
                    // 建立或更新关联
                    SpareWord::updateOrCreate(
                        [
                            'retail_id' => $retail->id ?? null,
                            'spare_id' => $spare->id ?? null,
                            'keyword_id' => $keywordModel->id ?? null,
                            'source_id' => $spare->source_id ?? null,
                            'user_id' => $spare->user_id ?? null,
                            'word' => $keywordModel->word ?? null,
                        ],
                        []
                    );
                }
            }
            /**
             * == (E) 最后更新 Rice 的状态 ==
             */
            $riceModel->spare_time = now();
            //$riceModel->rice_state   = RiceStateEnum::ready();
            $riceModel->save();
            // 提交事务
            DB::commit();
            //
            return $riceModel;
        } catch (Exception $e) {
            // 回滚事务，记录异常日志并抛出
            DB::rollBack();
            Log::error('batchPublish Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
}
