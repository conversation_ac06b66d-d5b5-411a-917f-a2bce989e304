<?php

namespace App\Services\Chain\Rice;

use App\Services\BaseService;

class RiceSuitService extends BaseService
{

    public static function washText($seed_text)
    {
        // 去除重复的词汇
        $removeWords = [
            '耳机', '有线版', '无线版', '无孔', '耳机套', '保护套', '新', '基础款', '硅胶', '丝印', '无扣', '荧光', 'tpu',
            '套', '2024', '2021', '送挂钩', '老款', '白底', '白边', '苹果', '机', '分体', '盒装', '尼龙', '贴皮',
            '商务', '皮', '款', '连体带扣', '挂坠', '四角裤', '登山扣', '无开孔', '底部无开孔', '厚度18mm', '简易', '带垫', '金属贴',
            '挂件', '带扣眼', '不带扣眼', '三角裤', '收音', '标准版底部不带扬声孔', '降噪版底部带扬声孔', '包装盒', '适用', '降噪版',
            '普通版', '底部', '带扬声孔', '大理石', '贴纸', 'pc', '透明', '无扬声孔', '防尘贴', '安卓', '挂扣', '没背带',
            '电镀带扣', '配手腕绳', '电镀', '水贴', '防水', '普通', '5代', '挂绳孔版', '带包装', '五件', '薄13mm', '同',
            '配挂钩', '标准版', '不', '厚度', '18mm', 'py', '图案', '耳帽', '于', '型号', '全系列', '系列', '(2021款)',
            '（包装盒）', '(2022款)', '（没背带）', '（配手腕绳）', '薄13mm', '25mm', '加厚', '带扣13mm', '耳塞', '耳塞帽一对装',
            '防丢绳', '磁吸', 'Pc', 'Py', 'py', '第', '厚22mm', '2022', ' 带扣13mm', '带扣2mm', '厚22mm',
            ' 带扣', '13mm', '2mm', ' 挂绳', '挂绳', '孔版', '薄', '配', '官方', '配官方', '版', '厚22mm', '盒子', '带挂钩', '帽1对装', '1对装', '帽', 'Or', ' Or '
        ];

        // 移除重复的词汇，确保词汇唯一
        $removeWords = array_unique($removeWords);

        // 用于中文数字转化的映射
        $chineseToNum = [
            "一" => "1", "二" => "2", "三" => "3", "四" => "4", "五" => "5", "六" => "6", "七" => "7", "八" => "8", "九" => "9", "十" => "10"
        ];


        // 准备一个数组，用来存储处理后的词汇
        $words = [];
        foreach ($seed_text as $item) {
            // 去除非字母、数字和空格的字符（标点符号等）
            $input = preg_replace('/[^\w\d\s\/]/u', '', $item);

            // 去除不需要的词汇（通过正则匹配）
            $input = preg_replace('/(' . implode('|', $removeWords) . ')/u', '', $input);

            // 替换多余的空格，保证只有一个空格
            $input = preg_replace('/\s+/', ' ', $input);  // 将多个空格替换为单个空格
            $input = trim($input); // 移除前后空格

            // 将中文数字转换为阿拉伯数字
            $input = strtr($input, $chineseToNum);

            // 将字符串转为小写，再将每个单词首字母大写
            $input = ucwords(strtolower($input));

            // 将处理过的词汇添加到数组中
            $words[] = $input;
        }
        //
        return array_unique($words);
    }


    public static function matchProducts(string $text): array
    {
        //
        $normalized = mb_strtolower(preg_replace('/\s+/', '', $text));
        $matches = [];
        //
        // 注意顺序：Pro系列必须在前
        $productPatterns = [
            // 第一优先级：Pro系列
            'AirPods Pro3' => ['pro3', 'pro[\W_]*3代?', 'pro[\W_]*3'],
            'AirPods Pro2' => [
                'pro[\W_]*2代',
                'pro[\W_]*2(?!代)',
                // 移除会导致冲突的 Pro1/2 模式
            ],
            'AirPods Pro' => ['pro(?![\d])', 'pro一代'],

            // 第二优先级：基础型号（需排除Pro系列）
            'AirPods4' => ['(?<!pro)4代', '(?<!pro)4(?!代)', '2024款'],
            'AirPods3' => ['(?<!pro)3代', '(?<!pro)3(?!代)', '2021款'],
            'AirPods2' => ['(?<!pro)2代', '(?<!pro)2(?!代)'],
            'AirPods1' => ['(?<!pro)1代', '(?<!pro)1(?!代)', '^airpods$'],
            'AirPods max' => ['max', 'max1', 'max2'],
        ];

        foreach ($productPatterns as $product => $patterns) {
            //
            foreach ($patterns as $pattern) {
                // 使用正则表达式进行匹配，确保正确处理Unicode和大小写
                if (preg_match("#{$pattern}#iu", $normalized)) {
                    $matches[] = $product;
                    break;
                }
            }
        }
        //
        if ($normalized == 'airpods' || $normalized == 'Airpods') {
            //
            $matches = array_merge($matches, ['AirPods1']);
        }
        //
        if ($normalized == 'Airpodspro3' || $normalized == 'airpodspro3' || $normalized == 'airPodspro3') {
            //
            $matches = ['AirPods Pro3'];
        }
        // 处理包含3/4与Pro组合的特殊逻辑
        if (preg_match('#(3|4)[^\w]*pro2?#iu', $normalized)) {
            //
            $matches = array_merge($matches, ['AirPods3', 'AirPods4', 'AirPods pro', 'AirPods pro2']);
        }

        // 使用 array_unique 移除重复项
        $uniqueMatches = array_unique($matches);

        // 检查数组中是否仅有 AirPods Pro2 或 AirPods2
        if (count($uniqueMatches) == 2 && (in_array("AirPods Pro2", $uniqueMatches) && in_array("AirPods2", $uniqueMatches))) {
            //
            $matches = ["AirPods Pro2"];
        }
        // 去重并返回
        return array_values(array_unique($matches));
    }
}
