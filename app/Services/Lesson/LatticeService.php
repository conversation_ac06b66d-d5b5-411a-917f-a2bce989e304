<?php

namespace App\Services\Lesson;

use App\Enums\State\LatticeStateEnum;
use App\Http\Resources\Advocate\Lattice\LatticeResource;
use App\Models\LatticeCenter\Lattice;
use App\Services\BaseService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class LatticeService extends BaseService
{
    /**
     * 获取产品的统计数据
     * @return array
     */
    public static function getLatticeCount()
    {
        // 1. 计算产品的统计数据 (CountData)
        $lattice_counts = [
            'open_count' => Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::open())->count(),
            'leased_count' => Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::leased())->count(),
            'online_count' => Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::online())->count(),
            'closed_count' => Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::closed())->count(),
        ];
        //
        return $lattice_counts;
    }

    /**
     * 获取筛选后的产品列表
     * @param array $filters
     * @param string $lattice_state
     * @param string|null $sortBy
     * @param string|null $order
     * @return LengthAwarePaginator
     */
    public static function getFilteredLatticeList(array $filters, string $lattice_state, string $sortBy = null, string $order = null)
    {
        //
        $Lattice = Lattice::where('lattice_state', $lattice_state);
        // 添加筛选条件
        if (isset($filters['keyword'])) {
            //
            $Lattice->search($filters['keyword']);
        }
        //
        if (isset($filters['date_from'])) {
            $Lattice->whereDate('created_at', '>=', $filters['date_from']);
        }
        //
        if (isset($filters['date_to'])) {
            $Lattice->whereDate('created_at', '<=', $filters['date_to']);
        }
        // 添加排序
        if ($sortBy && in_array($sortBy, ['lattice_name', 'created_at', 'updated_at'])) {
            //
            $order = $order === 'desc' ? 'desc' : 'asc';
            $Lattice->orderBy($sortBy, $order);
        } else {
            $Lattice->orderBy('created_at', 'desc');
        }
        // Paginate the result
        $Lattice = $Lattice->paginate(15);
        // 使用 StoreResource 进行资源封装和返回
        return LatticeResource::collection($Lattice)->resource;
    }

    /**
     * 获取待审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getLatticeOnlineList()
    {
        // 2. 获取待审核产品列表，并进行分页
        $Lattice = Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::open())->paginate(15);
        // 使用 LatticeResource 进行资源封装和返回
        return LatticeResource::collection($Lattice)->resource;
    }

    /**
     * 获取已审核产品的列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getLatticeOfflineList()
    {
        // 3. 获取已审核产品列表，并进行分页
        $Lattice = Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::online())->paginate(15);
        // 使用 LatticeResource 进行资源封装和返回
        return LatticeResource::collection($Lattice)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getLatticeClosureList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Lattice = Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::closed())->paginate(15);
        // 使用 LatticeResource 进行资源封装和返回
        return LatticeResource::collection($Lattice)->resource;
    }

    /**
     * 获取拒绝的产品列表，带分页
     * @return AnonymousResourceCollection
     */
    public static function getLatticeLockList()
    {
        // 4. 获取拒绝的产品列表，并进行分页
        $Lattice = Lattice::where('platform_id', '=', config('app.platform_id'))->where('lattice_state', LatticeStateEnum::temporarily())->paginate(15);
        // 使用 LatticeResource 进行资源封装和返回
        return LatticeResource::collection($Lattice)->resource;
    }

    /**
     * 获取单个产品的详细信息
     * @param string $uuid
     * @return LatticeResource
     */
    public static function getLatticeByUuid(string $uuid)
    {
        //
        $Lattice = Lattice::where('uuid', $uuid)->first();
        $Lattice = new LatticeResource($Lattice);
        //
        return $Lattice;
    }
}
