<?php

namespace App\Services\Plugin\Mobile;

use App\Models\Common\ComSms;
use App\Services\BaseService;
use Illuminate\Support\Facades\Auth;
use Overtrue\EasySms\EasySms;

class MobileVerifyCodeService extends BaseService
{
    //
    public static function getCode($mobile, $type)
    {
        $time = time();
        $code = rand(100000, 999999);
        //        $type   = request('mobile_type', '');
        //        $mobile = request('mobile', '');
        //
        if (empty($type) || empty($mobile)) {
            //
            return '参数不正确！';
        }
        //        try {
        //
        $result = MobileVerifyCodeService::send($mobile, $code);
        //
        if (!empty($result['code'])) {
            //
            return $result['message'];
        }
        $easySms = new ComSms();
        $easySms->platform_id = config('app.platform_id');
        $easySms->user_id = Auth::id() ?? null;
        $easySms->store_id = Auth::user()->store_id ?? null;
        $easySms->type = $type;
        $easySms->mobile = $mobile;
        $easySms->code = $code;
        $easySms->activity_time = $time;
        $easySms->exceed_time = $time + 300;
        $easySms->is_comefrom = 0;
        $easySms->save();
        //        }
        //        catch (\Exception $e) {
        //            boom($e->getMessage());
        //        }
    }

    //检查code

    public static function send($phone, $code)
    {
        //        try {
        $config = config('easysms');
        $easySms = new EasySms($config);
        $result = $easySms->send($phone, [
            'template' => 1343700,
            'data' => [$code],
        ]);
        //        }
        //        catch (\Exception $e) {
        //            $result = $e->getResults();
        //        }
        //
        if (!empty($result['qcloud']['status']) && ($result['qcloud']['status'] == "success")) {
            $code = 0;
            $message = 'success';
        } else {
            $rr = (array)$result['qcloud']['exception'];
            $message = !empty($rr['raw']['Response']['SendStatusSet'][0]['Code']) ? $rr['raw']['Response']['SendStatusSet'][0]['Code'] : 'fail';
        }

        return ['code' => $code, 'message' => $message];
    }

    public static function checkCode($mobile, $code)
    {
        if (empty($mobile) || empty($code)) {
            return false;
        }
        //
        $time = time();
        $easySms = ComSms::where(['user_id' => Auth::id(), 'mobile' => $mobile, 'code' => $code, 'is_comefrom' => 0, 'type' => 3])->where('exceed_time', '>', $time)->orderBy('id', 'desc')->first();
        if (!empty($easySms)) {
            $easySms->is_comefrom = 1;
            $easySms->update();
            return true;
        }
        return false;
    }
}
