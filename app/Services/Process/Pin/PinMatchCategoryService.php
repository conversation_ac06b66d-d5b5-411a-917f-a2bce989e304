<?php


namespace App\Services\Process\Pin;

use App\Models\PinCenter\Pin;
use App\Services\BaseService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Throwable;

/**
 * PinMatchCategoryService 服务类
 * 功能说明：
 * 该服务负责Pin与商品类目（Category）的匹配。
 * 通过分析Pin的标题、描述等信息，智能匹配对应的商品类目。
 * 核心功能：
 * 1. 类目规则管理：维护类目匹配规则
 * 2. 智能匹配：基于关键词和规则匹配类目
 * 3. 批量处理：支持批量Pin的类目分配
 * 4. 缓存优化：使用缓存提高匹配效率
 * 应用场景：
 * - 新Pin导入时的自动分类
 * - 定期类目维护和优化
 * - 商品类目迁移和调整
 */
class PinMatchCategoryService extends BaseService
{

    /**
     * 获取类目匹配规则
     * 规则结构：
     * [
     *     'category_id' => [
     *         'keywords' => ['关键词1', '关键词2'],
     *         'exclude_keywords' => ['排除词1', '排除词2'],
     *         'patterns' => ['/正则1/i', '/正则2/i'],
     *         'priority' => 100
     *     ]
     * ]
     * @param bool $useCache 是否使用缓存
     * @return array 类目匹配规则
     */
    public static function getCategoryMatchRules(bool $useCache = true): array
    {
        /*------------------------------------------------------------------
		 | 1. 关键词规则（三组 AND）
		 |    $rules[id] = [Brand[], Product[], Shell[]]
		 ------------------------------------------------------------------*/
        $rules = [
            // === iPhone 手机壳 ===
            7 => [
                /* Brand */
                ['苹果', 'iphone', 'iphone16', 'iphone15', 'iphone14', 'iphone13', 'iphone12', 'iphone11'],
                /* Product */
                ['手机', '折叠屏', 'iphone'],
                /* Shell —— 删除裸词『挂件』『链条』，用正则兜底 */
                ['手机壳', '手机套', '手机保护壳', '手机保护套',
                    '防摔手机壳', '磁吸手机壳', '指环手机壳',
                    'tpu', 'imd', '彩绘', '闪闪', '斑马纹', '创意',
                    // ↓ 仅当同一标题里出现 “iphone + (挂件|链条) + (壳|套|保护)” 才命中
                    '/iphone.*?(?:挂件|链条).*?(?:壳|套|保护)/ui',
                    // 兜底：手机…壳/套/保护
                    '/(?:手机).*?(?:壳|套|保护)/ui',
                ],
            ],

            // === 安卓手机壳（含折叠屏） ===
            8 => [
                // Brand —— 无改动
                ['三星', 'samsung', 'galaxy', 'zflip', 'z flip', 'zflip3', 'zflip4', 'zflip5', 'zflip6',
                    's24', 's24plus', 's24+', 's24ultra', 's25', 's25plus', 's25+', 's25ultra',
                    '华为', 'huawei', '荣耀', 'honor', '小米', 'xiaomi', 'redmi',
                    'oppo', 'vivo', 'realme', 'note20', 'reno', 'maimang'],
                // Product —— 可加 “翻盖” 但其实不影响
                ['手机', '折叠屏', '翻盖'],
                // Shell —— ★★★ 这里是关键补丁 ★★★
                ['手机壳', '手机套', '折叠屏手机壳', '折叠屏手机套',
                    '防摔手机壳', '磁吸手机壳', '指环手机壳', '支架手机壳',
                    '手机皮套', '翻盖手机皮套', '钱包手机皮套', '钱夹手机皮套',
                    '插卡手机皮套', '侧扣手机皮套', '油蜡皮手机皮套', '二合一手机壳',
                    // 正则兜底：手机 … 壳 ∣ 套 ∣ 皮套 ∣ 保护壳 ∣ 保护套
                    '/手机.*(?:壳|套|皮套|保护壳|保护套)/ui',
                ],
            ],

            // === AirPods 耳机壳 ===
            11 => [
                /* Brand */
                ['airpods', 'airpods2', 'airpods3', 'airpods4', 'airpodspro', '苹果耳机'],
                /* Product */
                ['耳机', 'buds', 'airpods'],
                /* Shell —— 同理删除裸词，正则收紧 */
                ['耳机壳', '耳机套', '耳机保护壳', '耳机保护套',
                    '防摔耳机壳', '液态硅胶耳机壳',
                    'tpu', 'imd', '彩绘', '斑马纹', '创意',
                    '/airpods.*?(?:挂件|链条).*?(?:壳|套|保护)/ui',
                    '/耳机.*?(?:壳|套|保护)/ui',
                ],
            ],

            // === 安卓系耳机壳 ===
            12 => [
                ['小米', 'xiaomi', '红米', 'redmi', 'airdots',
                    '华为', 'huawei', '荣耀', 'honor', 'freebuds', 'flypods',
                    '三星', 'samsung', 'galaxybuds', 'buds', 'budsplus', 'buds+',
                    'buds2', 'budsfe', 'budspro', 'budslive'],
                ['耳机', 'buds'],
                ['耳机壳', '耳机套', '耳机保护壳', '耳机保护套',
                    '液态硅胶耳机壳', '防摔耳机壳',
                    '/耳机.*(?:壳|套)/ui'],
            ],

            // === Apple Watch 表带 ===
            14 => [
                ['苹果', 'apple', 'iwatch', 'applewatch'],
                ['表', 'watch'],
                ['表带', '磁吸表带', '运动表带', '金属表带', '硅胶表带'],
            ],

            // === Apple Watch 表壳 ===
            15 => [
                ['苹果', 'apple', 'iwatch', 'applewatch'],
                ['表', 'watch'],
                ['表壳', '保护壳', '保护套',
                    '/表.*壳/ui'],
            ],
        ];
        //
        return $rules;
    }

    /**
     * 根据 Pin 商品信息和分类规则进行自动分类，自动写入分类状态。
     * @param Pin $pin 需要分类的 Pin 实体
     * @param array|null $rules 分类规则（格式: id => [brands[], products[], shells[]]）
     * @return int|null  匹配到的 category_id，未命中时返回 null
     */
    public static function matchPinCategory(Pin $pin, ?array $rules = null): ?int
    {
        // 获取当前时间
        $now = Carbon::now();
        //
        try {
            // 预处理，统一转小写，防止大小写影响匹配
            $subject = mb_strtolower($pin->pin_subject ?? '');
            //
            // 判断是否为耳机类商品，避免误判为手机壳
            $isEarphone = Str::contains($subject, '耳机');
            //
            $matchedId = null;
            //
            // 校验分类规则参数合法性
            if (empty($rules) || !is_array($rules)) {
                //
                throw new InvalidArgumentException('分类规则无效');
            }
            //
            // 分类规则遍历
            foreach ($rules as $id => [$brands, $products, $shells]) {

                // 特殊规则：耳机商品不走 7/8 号分类
                if ($isEarphone && in_array($id, [7, 8], true)) {
                    continue;
                }

                // 品牌、产品两组必须全部匹配
                if (!Str::contains($subject, $brands) || !Str::contains($subject, $products)) {
                    continue;
                }

                // shell 关键词支持普通词和正则，任意命中即通过
                $shellOk = false;
                foreach ($shells as $kw) {
                    //
                    if ($kw[0] === '/' && @preg_match($kw, $subject)) { // 正则
                        //
                        $shellOk = true;
                        break;
                    }
                    //
                    if (Str::contains($subject, $kw)) { // 普通关键词
                        //
                        $shellOk = true;
                        break;
                    }
                }
                //
                if (!$shellOk) {
                    continue;
                }
                //
                // 三组均命中，记录分类 id
                $matchedId = $id;
                break;
            }

            // --------------------------------------------------------------
            // 分类结果处理
            // --------------------------------------------------------------
            if ($matchedId !== null) {
                // 规则命中，写入分类
                if ($pin->category_id !== $matchedId) {
                    //
                    $pin->category_id = $matchedId;
                }
                //
                $pin->pin_state = 1; // 已分类
                $pin->is_category = 1;
            } else {
                // 未命中分类，进入特殊判定逻辑
                $pin->pin_state = 5; // 标记为“未识别”
                $pin->is_category = 0;
                //
                $shop = $pin->shop; // 关联店铺
                //
                // 判断商品是否为耳机或手机相关
                if (static::subjectHasHeadsetOrPhone($pin->pin_subject ?? $pin->pin_title)) {
                    //
                    // 店铺处于“正常”或“新入驻”状态
                    if (isset($shop) && in_array($shop->shop_state, [1, 2], true)) {
                        //
                        // 部分特定类目需人工处理
                        $manualCategoryNos = [
                            "201891801", "1044131", "1042207", "1046694", "201345601",
                            "124810006", "124592001", "124270010", "121504002", "1046692"
                        ];
                        //
                        if (isset($pin->pin_category_no) && in_array($pin->pin_category_no, $manualCategoryNos, true)) {
                            //
                            $pin->pin_state = 4; // 标记为“人工介入”
                            $pin->is_category = 0;
                        } else {
                            //
                            $pin->pin_state = 3; // 标记为“机器审核”
                            $pin->is_category = 0;
                        }
                    } else {
                        // 店铺异常或其它状态，直接过滤
                        $pin->pin_state = 2; // 标记为“过滤状态”
                        $pin->is_category = 0;
                    }
                }
            }

            // 最后写入确认时间，保存
            $pin->confirm_time = $now;
            $pin->save();

            // 返回分类ID或null
            return $matchedId;

        } catch (Throwable $e) {
            // 异常捕获，记录日志，便于追踪错误
            Log::error('Pin 分类匹配异常', [
                'pin_id' => $pin->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            //
            return null;
        }
    }


    /**
     * 判断 $pin_subject 是否包含“耳机”或“手机”
     * 兼容：
     *   1. 全角/半角空格
     *   2. 中文括号 vs 英文括号
     *   3. 破折号、斜杠等轻微噪声
     *   4. 大小写（主要针对英文单词）
     */
    public static function subjectHasHeadsetOrPhone($pin_subject): bool
    {
        //
        // ② 关键字数组同样做一次 strtolower()，保持一致
        $keywords = ['耳机', '手机', '套', '壳', '保护', '表带'];
        //
        // ③ 再用 Str::contains 判断
        return Str::contains($pin_subject, $keywords);
    }
}
