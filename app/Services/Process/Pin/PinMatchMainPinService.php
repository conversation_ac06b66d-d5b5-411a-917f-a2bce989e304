<?php

namespace App\Services\Process\Pin;

use App\Models\PegCenter\Peg;
use App\Models\PinCenter\Pin;
use App\Models\ShopCenter\Shop;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * PinMatchMainPinService 服务类
 * 功能说明：
 * 该服务负责处理Pin与主Pin的匹配关系。
 * 主要用于识别和关联同一商品的不同Pin记录，建立主从关系。
 * 应用场景：
 * - 商品去重：识别同一商品的多个Pin记录
 * - 主从关系建立：标记主Pin和副本Pin
 * - 数据同步：在主从Pin之间同步关键信息
 * 注意：这个类的方法名与实际功能不太匹配，但为了保持兼容性暂时保留
 */
class PinMatchMainPinService extends BaseService
{
    /**
     * 处理Pin与Peg的匹配关系
     * 功能说明：
     * 根据Rice的标题和Pin的标题，判断是否包含Peg的名称。
     * 如果匹配成功，则建立Rice与Peg的关联关系。
     * 处理逻辑：
     * 1. 遍历所有的Peg记录
     * 2. 检查Pin标题或Pin标题是否包含Peg名称
     * 3. 如果匹配，建立多对多关联
     * @param Collection|Peg $pegs Peg集合或单个Peg对象
     * @param Pin $pin 需要处理的Pin对象
     * @return void
     */
    public static function processPinMatchMainPin(Pin $pin): void
    {
        //
        try {
            //
            // 1. 获取对应的Pin记录
            if (!$pin) {
                //
                Log::warning('未找到对应的Pin记录', [
                    'pin_id' => $pin->id,
                    'pin_no' => $pin->pin_no
                ]);
            }
            //
            // 2. 处理图片URL，提取店铺编号
            $cleanUrl = self::removeImageSize($pin->rice_image);
            $imageShopNo = self::extractShopNoFromImage($cleanUrl);
            $pinShopNo = $pin->shop_no ?: ($pin->shop->shop_no ?? null);
            //
            // 3. 根据店铺编号匹配情况进行处理,图片店铺编号与数据库不一致，需要重新关联
            if (isset($imageShopNo) && $imageShopNo !== $pinShopNo) {
                //
                // 1. 查找店铺
                $shop = Shop::query()->where('platform_id', config('app.platform_id', 1))->where('shop_no', $imageShopNo)->first();
                //
                if (!$shop) {
                    //
                    Log::warning('根据图片shop_no未找到店铺', [
                        'shop_no' => $imageShopNo,
                        'pin_id' => $pin->id,
                    ]);
                    //
                    return;
                }
                // 店铺编号一致，直接标记为主记录
                // 标记Pin为主记录
                $pin->is_copy = 2;
                $pin->is_main = 0;
                $pin->save();
                //
                $pin->shops()->syncWithoutDetaching($shop);
            } elseif (isset($imageShopNo) && $imageShopNo === $pinShopNo) {
                // 店铺编号一致，直接标记为主记录
                // 标记Pin为主记录
                $pin->is_copy = 1;
                $pin->is_main = 0;
                $pin->save();
            }
            //
        } catch (Exception $e) {
            // 捕获异常，记录错误日志
            Log::error('PinMatchMainPinService 处理失败', [
                'pin_id' => $pin->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            // 重新抛出异常，让调用方决定如何处理
            throw $e;
        }
    }

    /**
     * 去除图片URL中的尺寸参数
     * @param string $url 原始URL
     * @return string 清理后的URL
     */
    private static function removeImageSize(string $url): string
    {
        // 移除 .数字x数字 格式的尺寸参数
        return preg_replace('/\.\d+x\d+(?=\.)/', '', $url);
    }

    /**
     * 从图片URL中提取店铺编号
     * @param string $imageUrl 图片URL
     * @return string|null 店铺编号
     */
    private static function extractShopNoFromImage(string $imageUrl): ?string
    {
        // 正则匹配 !!数字- 格式
        if (preg_match('/!!(\d+)-/', $imageUrl, $matches)) {
            //
            return $matches[1];
        }
        //
        return null;
    }

    /**
     * 提取图片关键名称
     * @param string $imageUrl 图片URL
     * @return string|null 图片关键名
     */
    private static function extractImageKey(string $imageUrl): ?string
    {
        // 先清理URL
        $cleanUrl = self::removeImageSize($imageUrl);

        // 提取文件名部分（不含后缀和额外参数）
        if (preg_match('#/([^/]+?)(?:_[^/]*|\.jpg|\.png)#i', $cleanUrl, $matches)) {
            //
            return $matches[1];
        }
        //
        return null;
    }
}
