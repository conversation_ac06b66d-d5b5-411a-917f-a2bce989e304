<?php

namespace App\Services\Process\Pin;

use App\Models\PinCenter\Pin;
use App\Services\BaseService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * PinMatchPinService 服务类
 * 功能说明：
 * 该服务负责处理Pin与Pin的匹配关系。
 * 主要用于在Pin和Pin之间建立关联，同步数据，确保数据一致性。
 * 核心功能：
 * 1. Pin-Pin匹配：通过pin_no、标题等信息匹配Pin和Pin
 * 2. 数据同步：在Pin和Pin之间同步店铺、商品等信息
 * 3. 状态管理：更新Pin和Pin的关联状态
 * 4. 批量处理：支持批量Pin-Pin匹配
 * 应用场景：
 * - 新Pin导入后自动关联Pin
 * - 数据修复：修正Pin-Pin关联错误
 * - 定期数据同步和维护
 */
class PinMatchPegService extends BaseService
{
    /**
     * 匹配Pin和Pin
     * 匹配策略：
     * 1. 优先通过pin_no精确匹配
     * 2. 其次通过标题相似度匹配
     * 3. 最后通过图片URL匹配
     * @param Pin $pin 需要匹配的Pin
     * @return Pin|null 匹配到的Pin，没有匹配返回null
     */
    public static function processPinMatchPeg(Pin $pin, $pegs): void
    {
        //
        try {
            //
            if ($pegs && count($pegs) > 0) {
                //
                // 2️⃣ 遍历每个Peg，查找是否需要关联
                foreach ($pegs as $peg) {
                    // 判断rice_title或pin_subject是否包含peg_name
                    if ((isset($pin->rice_title) && Str::contains($pin->rice_title, $peg->peg_name)) || (isset($pin->pin_subject) && Str::contains($pin->pin_subject, $peg->peg_name))) {
                        // 3️⃣ 同步Rice与Peg的关系
                        $pin->pegs()->syncWithoutDetaching($peg->id);
                    }
                }
            }
            //
        } catch (Exception $e) {
            // 捕获异常，记录错误日志
            Log::error('PinMatchMainPinService 处理失败', [
                'pin_id' => $pin->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            // 重新抛出异常，让调用方决定如何处理
            throw $e;
        }
    }
}
