<?php

namespace App\Services\Process\RiceCorn;

use App\Models\ProduceBrace\ProduceSuit;
use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceSeed;
use App\Services\BaseService;
use App\Services\Chain\Rice\RiceSuitService;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RiceCornMatchProduceSuitService extends BaseService
{
    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function processRiceSeedMatchProduceAirPodsSuit(RiceSeed $riceSeed, Rice $rice): void
    {
        //
        // 只处理seed_name为p-404或p-3216的RiceSeed，其他类型直接跳过
        if (!in_array($riceSeed->seed_name, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            // 可选：这里可以记录日志，或直接return
            return;
        }
        // 1. 更新RiceSeed，将其属性类型标记为'suit'，且标记为已校验
        $riceSeed->update([
            'attr_type' => 'suit',
            'is_check' => 1,
        ]);
        //
        // 使用闭包形式的 DB::transaction，可自动处理 commit / rollback
        DB::beginTransaction();
        //
        try {
            //
            // 4.1 调用 RiceSuitService::washText 对 seed_text 进行分词和清洗
            //     传入的是一个数组，所以需要用 [$riceSeed->seed_text]
            $keywords = RiceSuitService::washText([$riceSeed->seed_text]);
            //
            // 4.2 遍历所有关键词，对每个关键词调用 matchProducts 匹配产品名
            //     匹配结果可能是字符串或数组，全部合并去重后存入 $matchedProductNames
            $matchedProductNames = [];
            foreach ($keywords as $keyword) {
                //
                $productNames = RiceSuitService::matchProducts($keyword);
                // 如果匹配结果是数组，则合并到主数组
                if (is_array($productNames)) {
                    //
                    $matchedProductNames = array_merge($matchedProductNames, $productNames);
                } // 如果是字符串（单一产品名），也加入数组
                elseif ($productNames) {
                    //
                    $matchedProductNames[] = $productNames;
                }
            }
            //
            // 去重，防止重复产品名
            $matchedProductNames = array_unique($matchedProductNames);
            //
            // 4.3 根据所有匹配到的产品名，在 ProduceSuit 表中查找对应的产品
            $produceSuits = [];
            if (!empty($matchedProductNames)) {
                //
                $produceSuits = ProduceSuit::whereIn('suit_name', $matchedProductNames)->get();
            }
            //
            // 4.4 判断是否查到有效的产品
            if (!empty($produceSuits) && $produceSuits->isNotEmpty()) {
                // 只关联第一个找到的产品（实际业务可按需扩展）
                $firstSuit = $produceSuits->first();
                // 更新 RiceSeed 关联的 suit_id、seed_label 以及其他状态字段
                $riceSeed->update([
                    'suit_id' => $firstSuit->id ?? null,
                    'seed_label' => $firstSuit->suit_name ?? null,
                    'attr_type' => 'suit',
                    'is_check' => 1,
                ]);
                // 所有更新操作无异常则提交事务
                DB::commit();
            }
            //
            //
            // 5.2 通过rice_id查找关联的Rice主表
            if ($rice) {
                // 5.3 判断该Rice下所有RiceSeed是否都已校验（is_check都为1）
                //查找该Rice下is_check不为1的数量，如果为0说明全部已通过
                $allChecked = RiceSeed::where('rice_id', $rice->id)->where('is_check', '!=', 1)->count() === 0;
                //
                // 5.4 如果全部通过，则更新Rice表的is_check为1
                if ($allChecked) {
                    //
                    $rice->update(['is_check' => 1]);
                }
            }
            //
            // 5.6 只要本条数据所有步骤无异常，则提交事务
            DB::commit();
        } catch (Exception $e) {
            // 5.7 任意异常立即回滚本条事务，并计入失败
            DB::rollBack();
            //
            Log::error("RiceSeed规格处理失败，ID: {$riceSeed->id}，原因: {$e->getMessage()}");
        }
    }

    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function processRiceSeedMatchProduceIPhoneSuit(RiceSeed $riceSeed, Rice $rice): void
    {
        //
        //
        // 只处理seed_name为p-404或p-3216的RiceSeed，其他类型直接跳过
        if (!in_array($riceSeed->seed_name, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            // 可选：这里可以记录日志，或直接return
            return;
        }
        // 1. 更新RiceSeed，将其属性类型标记为'suit'，且标记为已校验
        $riceSeed->update([
            'attr_type' => 'suit',
            'is_check' => 1,
        ]);
        //
        // 使用闭包形式的 DB::transaction，可自动处理 commit / rollback
        DB::beginTransaction();
        //
        try {
            //
            // 5.6 只要本条数据所有步骤无异常，则提交事务
            DB::commit();
        } catch (Exception $e) {
            // 5.7 任意异常立即回滚本条事务，并计入失败
            DB::rollBack();
            //
            Log::error("RiceSeed规格处理失败，ID: {$riceSeed->id}，原因: {$e->getMessage()}");
        }
    }

    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function processRiceSeedMatchProduceOtherSuit(RiceSeed $riceSeed, Rice $rice): void
    {
        //
        //
        // 只处理seed_name为p-404或p-3216的RiceSeed，其他类型直接跳过
        if (!in_array($riceSeed->seed_name, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            // 可选：这里可以记录日志，或直接return
            return;
        }
        // 1. 更新RiceSeed，将其属性类型标记为'suit'，且标记为已校验
        $riceSeed->update([
            'attr_type' => 'suit',
            'is_check' => 1,
        ]);
        //
        // 使用闭包形式的 DB::transaction，可自动处理 commit / rollback
        DB::beginTransaction();
        //
        try {
            //
            // 5.6 只要本条数据所有步骤无异常，则提交事务
            DB::commit();
        } catch (Exception $e) {
            // 5.7 任意异常立即回滚本条事务，并计入失败
            DB::rollBack();
            //
            Log::error("RiceSeed规格处理失败，ID: {$riceSeed->id}，原因: {$e->getMessage()}");
        }
    }
}
