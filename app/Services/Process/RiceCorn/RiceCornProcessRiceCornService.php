<?php

namespace App\Services\Process\RiceCorn;

use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceCorn;
use App\Services\BaseService;

class RiceCornProcessRiceCornService extends BaseService
{
    /**
     * 处理单条 Rice 记录
     * @param Rice $rice
     */
    public static function RiceCornMatchProduceService(RiceCorn $riceCorn, Rice $rice): void
    {
        //
        // 只处理seed_name为p-404或p-3216的RiceCorn，其他类型直接跳过
        if (in_array($riceCorn->seed_name, ['p-404', 'p-3216'])) {
            //
        } elseif (in_array($riceCorn->seed_name, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            //
        } else {
            //
            return;
        }
    }
}
