<?php

namespace App\Services\Process\RiceSeed;

use App\Models\RiceCenter\Rice;
use App\Models\RiceCenter\RiceSeed;
use App\Services\BaseService;

class RiceSeedProcessRiceSeedService extends BaseService
{
    /**
     * 处理单条 RiceSeed 记录，自动分发至不同业务逻辑
     * @param RiceSeed $riceSeed
     * @param Rice $rice
     */
    public static function RiceSeedMatchProduceService(RiceSeed $riceSeed, Rice $rice): void
    {
        // 1. 规格类商品，直接走Spec逻辑
        if (in_array($riceSeed->seed_name, ['p-404', 'p-3216'])) {
            //
            RiceSeedMatchProduceSpecService::processRiceSeedMatchProduceSpec($riceSeed, $rice);
        } // 2. 套装类商品，判断是否airpods或iphone相关
        elseif (in_array($riceSeed->seed_name, ['p-100018377', 'p-3151', 'p-1234', 'p-446'])) {
            // 优先判定AirPods
            if ($rice->rice_category_no == '201891801' || (stripos($rice->pin_subject, 'airpods') !== false) || (stripos($rice->rice_title, 'airpods') !== false)) {
                //
                RiceSeedMatchProduceSuitService::processRiceSeedMatchProduceAirPodsSuit($riceSeed, $rice);
            } // 如果不是AirPods但符合iPhone条件，则执行iPhone Suit
            elseif ($rice->rice_category_no == '1046694' || (stripos($rice->pin_subject, 'iphone') !== false) || (stripos($rice->rice_title, 'iphone') !== false)) {
                //
                RiceSeedMatchProduceSuitService::processRiceSeedMatchProduceIPhoneSuit($riceSeed, $rice);
            } else {
                //
                RiceSeedMatchProduceSuitService::processRiceSeedMatchProduceOtherSuit($riceSeed, $rice);
            }
        }
        //
        // 3. 如本条riceSeed有参与任一业务处理，再进行关键词处理
        RiceSeedMatchRiceKeywordService::processRiceSeedMatchKeyword($riceSeed, $rice);
    }
}
