<?php

namespace App\Services\Seller;

use App\Enums\State\OrderMainStateEnum;
use App\Enums\State\OrderStateEnum;
use App\Enums\Type\OrderTypeEnum;
use App\Http\Resources\Seller\Order\OrderResource;
use App\Services\BaseService;

class DeliveryService extends BaseService
{
    /**
     * @param $User
     * @return mixed
     */
    public static function getStoreByDeliveryCountData($Store)
    {
        //
        $delivery_counts = [
            //
            'delivery_match_count' => $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
                    ->where('main_state', OrderMainStateEnum::OrderShipment())
                    ->where('is_waybill', 0)
                    ->where('is_lock', 0)
                    ->count() ?? 0,
            //
            'delivery_print_count' => $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
                    ->where('main_state', OrderMainStateEnum::OrderShipment())
                    ->where('main_state', OrderStateEnum::OrderShipmentGetExpressNoSucceed())
                    ->where('is_waybill', 1)
                    ->where('is_lock', 0)
                    ->count() ?? 0,
            //
            'delivery_shipment_count' => $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
                    ->where('main_state', OrderMainStateEnum::OrderShipment())
                    ->where('main_state', OrderStateEnum::OrderShipmentLabelPrinting())
                    ->where('is_waybill', 1)
                    ->where('is_lock', 0)
                    ->count() ?? 0,
            //
            'delivery_done_count' => $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
                    ->where('is_shipment', 1)
                    ->where('is_waybill', 1)
                    ->count() ?? 0,
        ];
        //
        return $delivery_counts;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByDeliveryMatchList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('main_state', OrderMainStateEnum::OrderShipment())
            ->where('is_waybill', 0)
            ->where('is_lock', 0)
            ->orderBy('create_time', 'desc')->paginate(15);
        $delivery_matchs = OrderResource::collection($Order)->resource;
        //
        return $delivery_matchs;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByDeliveryPrintList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('main_state', OrderMainStateEnum::OrderShipment())
            ->where('main_state', OrderStateEnum::OrderShipmentGetExpressNoSucceed())
            ->where('is_waybill', 1)
            ->where('is_lock', 0)
            ->orderBy('create_time', 'desc')->paginate(30);
        $delivery_prints = OrderResource::collection($Order)->resource;
        //
        return $delivery_prints;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByDeliveryShipmentList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('main_state', OrderMainStateEnum::OrderShipment())
            ->where('main_state', OrderStateEnum::OrderShipmentLabelPrinting())
            ->where('is_waybill', 1)
            ->where('is_lock', 0)
            ->orderBy('create_time', 'desc')->paginate(30);
        $delivery_shipments = OrderResource::collection($Order)->resource;
        //
        return $delivery_shipments;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByDeliveryDoneList($Store)
    {
        //
        $Order = $Store->orders()->where('platform_id', '=', config('app.platform_id'))->where('is_check', 1)->where('order_type', OrderTypeEnum::ShipperOrder())
            ->where('is_shipment', 1)
            ->where('is_waybill', 1)
            ->orderBy('create_time', 'desc')->paginate(30);
        $delivery_dones = OrderResource::collection($Order)->resource;
        //
        return $delivery_dones;
    }
}
