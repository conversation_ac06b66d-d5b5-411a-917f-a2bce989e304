<?php

namespace App\Services\Seller;

use App\Enums\State\StoreStateEnum;
use App\Models\ProviderCenter\Provider;
use App\Models\ProviderCenter\ProviderDemo;
use App\Models\StoreCenter\Store;
use App\Models\StoreCenter\StoreDemo;
use App\Services\Plugin\Sms\EasySmsService;
use Exception;
use Illuminate\Http\Request;

class InfoService
{
    /**
     * 创建 Provider 信息
     * @param Provider $provider
     * @param Request $request
     * @throws Exception
     */
    public static function storeProviderInfo(Provider $provider, Request $request)
    {
        // 验证请求数据
        $request->validate([
            'provider_logo' => 'required',
            'short_name' => 'required',
            'introduction' => 'required',
            'provider_photos' => 'required',
            'mobile' => 'required|min:11',
            'mobile_backup' => 'required|min:11',
            'email' => 'required|email',
            'qq' => 'required|min:3',
        ]);
        // 填充 Provider 信息
        $provider->fill($request->all());
        $provider->save();
    }

    /**
     * 更新 Provider 信息
     * @param Provider $provider
     * @param Request $request
     * @param string $type
     * @throws Exception
     */
    public static function updateProviderInfo(Provider $provider, Request $request, string $type)
    {
        // 根据类型更新 Provider 信息
        switch ($type) {
            case 'store_logo':
                $provider->store_logo = $request->input('store_logo', '');
                break;
            case 'store_photos':
                $provider->store_photos = $request->input('store_photos', '');
                break;
            case 'market':
                $provider->markets()->sync($request->input('market', []));
                $provider->stall = $request->input('stall', '');
                break;
            case 'cat':
                $provider->categories()->sync($request->input('cat', []));
                $provider->copy_cat = $request->input('copy_cat', '');
                break;
            case 'week':
                $provider->week()->sync($request->input('week', []));
                $provider->start_time = $request->input('start_time', null);
                $provider->end_time = $request->input('end_time', null);
                break;
            case 'mobile':
                if (!EasySmsService::checkCode($request->input('mobile'), $request->input('code'), $type)) {
                    //
                    throw new Exception('验证码不正确！');
                }
                //
                $provider->mobile = $request->input('mobile', '');
                break;
            case 'mobile_backup':
                //
                if (!EasySmsService::checkCode($request->input('mobile_backup'), $request->input('code'), $type)) {
                    //
                    throw new Exception('验证码不正确！');
                }
                $provider->mobile_backup = $request->input('mobile_backup', '');
                break;
            case 'email':
                $provider->email = $request->input('email', '');
                break;
            case 'wechat':
                $provider->wechat = $request->input('wechat', '');
                break;
            case 'qq':
                $provider->qq = $request->input('qq', '');
                break;
            default:
                throw new Exception('请求错误，请刷新页面！');
        }
        //
        $provider->save();
        //
        $ProviderDemo = new ProviderDemo();
        $ProviderDemo->platform_id = config('app.platform_id');
        $ProviderDemo->store_id = $provider->id;
        $ProviderDemo->provider_logo = request('provider_logo', '');
        $ProviderDemo->provider_photos = request('provider_photos', '');
        $ProviderDemo->nickname = request('nickname', '');
        $ProviderDemo->short_name = request('short_name', '');
        $ProviderDemo->introduction = request('introduction', '');
        $ProviderDemo->mobile = request('mobile', '');
        $ProviderDemo->mobile_backup = request('mobile_backup', '');
        $ProviderDemo->wechat = request('wechat', '');
        $ProviderDemo->email = request('email', '');
        $ProviderDemo->qq = request('qq', '');
        $ProviderDemo->qq_qun = request('qq_qun', '');
        $ProviderDemo->describe = '首次开店';
        //
        $ProviderDemo->audit_edit = 1;    //1
        $ProviderDemo->edit_type = 'all';// 1编辑内容 全部内容 all
        $ProviderDemo->main_state = 2;    //2-审核中；
        //
        $ProviderDemo->client_ip = request()->ip();
        $ProviderDemo->save();
    }

    /**
     * 创建 Store 信息
     * @param Store $Store
     * @param Request $request
     * @return void
     */
    public static function storeStoreInfo(Store $Store, Request $request)
    {
        // 验证请求数据
        $request->validate([
            'store_logo' => 'required',
            'short_name' => 'required',
            'introduction' => 'required',
            'store_photos' => 'required',
            'mobile' => 'required|min:11',
            'mobile_backup' => 'required|min:11',
            'email' => 'required|email',
            'qq' => 'required|min:3',
        ]);
        // 填充 Store 信息
        $Store->fill($request->all());
        $Store->save();
        //
        if ($Store->main_state == StoreStateEnum::default()) {
            //
            $Store->is_delivery = 1;//2-编辑审核中；
            $Store->is_edit = 1;//2-编辑审核中；
            $Store->main_state = StoreStateEnum::SoftOpening();//2-店铺状态审核中；
            $Store->client_ip = request()->ip();
            $Store->update();
            //
            $Store_info = $Store->seller_info;
            $Store_info->is_operate = 2;//2-店铺状态审核中；
            $Store_info->update();
        } else {
            //
            $Store->is_edit = 2;//2-驳回重审；
            $Store->client_ip = request()->ip();
            $Store->update();
            //
            $Store_info = $Store->seller_info;
            $Store_info->is_operate = 2;//2-店铺状态审核中；
            $Store_info->update();
        }
        //
        $StoreDemo = new StoreDemo();
        $StoreDemo->platform_id = config('app.platform_id');
        $StoreDemo->store_id = $Store->id;
        $StoreDemo->store_logo = request('store_logo', '');
        $StoreDemo->store_photos = request('store_photos', '');
        //        $StoreDemo->short_name      = request('short_name', '');
        $StoreDemo->short_name = request('short_name', '');
        $StoreDemo->introduction = request('introduction', '');
        $StoreDemo->mobile = request('mobile', '');
        $StoreDemo->mobile_backup = request('mobile_backup', '');
        $StoreDemo->wechat = request('wechat', '');
        $StoreDemo->email = request('email', '');
        $StoreDemo->qq = request('qq', '');
        $StoreDemo->qq_qun = request('qq_qun', '');
        $StoreDemo->describe = '首次开店';
        //
        $StoreDemo->audit_edit = 0;    //0首次开店
        $StoreDemo->edit_type = 'all';// 1编辑内容 全部内容 all
        $StoreDemo->main_state = StoreStateEnum::SoftOpening();    //2-审核中；
        //
        $StoreDemo->client_ip = request()->ip();
        $StoreDemo->save();
    }

    /**
     * 更新 Store 信息
     * @param Store $Store
     * @param Request $request
     * @param string $type
     * @throws Exception
     */
    public static function updateStoreInfo(Store $Store, Request $request, string $type)
    {
        // 根据类型更新 Store 信息
        switch ($type) {
            case 'store_logo':
                $Store->store_logo = $request->input('store_logo', '');
                break;
            case 'store_photos':
                $Store->store_photos = $request->input('store_photos', '');
                break;
            case 'market':
                $Store->markets()->sync($request->input('market', []));
                $Store->stall = $request->input('stall', '');
                break;
            case 'cat':
                $Store->categories()->sync($request->input('cat', []));
                $Store->copy_cat = $request->input('copy_cat', '');
                break;
            case 'week':
                $Store->week()->sync($request->input('week', []));
                $Store->start_time = $request->input('start_time', null);
                $Store->end_time = $request->input('end_time', null);
                break;
            case 'mobile':
                if (!EasySmsService::checkCode($request->input('mobile'), $request->input('code'), $type)) {
                    //
                    throw new Exception('验证码不正确！');
                }
                $Store->mobile = $request->input('mobile', '');
                break;
            case 'mobile_backup':
                if (!EasySmsService::checkCode($request->input('mobile_backup'), $request->input('code'), $type)) {
                    //
                    throw new Exception('验证码不正确！');
                }
                $Store->mobile_backup = $request->input('mobile_backup', '');
                break;
            case 'email':
                $Store->email = $request->input('email', '');
                break;
            case 'wechat':
                $Store->wechat = $request->input('wechat', '');
                break;
            case 'qq':
                $Store->qq = $request->input('qq', '');
                break;
            default:
                throw new Exception('请求错误，请刷新页面！');
        }
        //
        $Store->save();
        //
        $StoreDemo = new StoreDemo();
        $StoreDemo->platform_id = config('app.platform_id');
        $StoreDemo->store_id = $Store->id;
        $StoreDemo->store_logo = request('store_logo', '');
        $StoreDemo->store_photos = request('store_photos', '');
        $StoreDemo->nickname = request('nickname', '');
        $StoreDemo->short_name = request('short_name', '');
        $StoreDemo->introduction = request('introduction', '');
        $StoreDemo->mobile = request('mobile', '');
        $StoreDemo->mobile_backup = request('mobile_backup', '');
        $StoreDemo->wechat = request('wechat', '');
        $StoreDemo->email = request('email', '');
        $StoreDemo->qq = request('qq', '');
        $StoreDemo->qq_qun = request('qq_qun', '');
        $StoreDemo->describe = '首次开店';
        //
        $StoreDemo->audit_edit = 1;    //1
        $StoreDemo->edit_type = 'all';// 1编辑内容 全部内容 all
        $StoreDemo->main_state = StoreStateEnum::SoftOpening();    //2-审核中；
        //
        $StoreDemo->client_ip = request()->ip();
        $StoreDemo->save();
    }
}
