<?php

namespace App\Services\Seller;

use App\Models\MediaCenter\Media;
use App\Services\BaseService;

class MediaService extends BaseService
{
    // 获取首页数据
    public static function getMediaUuidToMediaId($uuid)
    {
        //
        $Media = Media::where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        //
        return $Media->id ?? null;
    }

    // 获取首页数据
    public static function getMediaUuidsToMediaIds($uuid)
    {
        //
        $Media_ids = Media::where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->select('id')->get()->pluck('id')->toArray();
        //
        return $Media_ids ?? null;
    }
}
