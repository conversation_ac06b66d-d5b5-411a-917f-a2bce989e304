<?php

namespace App\Services\Seller;

use App\Enums\Is\IsClosureEnum;
use App\Enums\Is\IsOfflineEnum;
use App\Enums\State\ProductStateEnum;
use App\Enums\Vest\OwnerVestEnum;
use App\Http\Resources\Seller\Product\ProductDemoResource;
use App\Http\Resources\Seller\Product\ProductResource;
use App\Models\IndustryBrace\Category;
use App\Models\KeywordCenter\Keyword;
use App\Models\ProduceBrace\ProduceParam;
use App\Models\ProductCenter\ProductParam;
use App\Services\BaseService;
use Illuminate\Support\Facades\Validator;

class ProductService extends BaseService
{
    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByProductCountData($Store)
    {
        //
        $product_counts = [
            //
            'product_online_count' => $Store->products()->where('is_offline', IsOfflineEnum::online())->where('is_closure', IsClosureEnum::sale())->where('product_vest', OwnerVestEnum::Store())->where('main_state', ProductStateEnum::Approved())->count() ?? 0,
            'product_offline_count' => $Store->products()->where('is_offline', IsOfflineEnum::offline())->where('is_closure', IsClosureEnum::sale())->where('product_vest', OwnerVestEnum::Store())->count() ?? 0,
            'product_closure_count' => $Store->products()->where('is_closure', IsClosureEnum::Discontinued())->where('product_vest', OwnerVestEnum::Store())->count() ?? 0,
            'product_demo_count' => $Store->product_demos()->where('product_vest', OwnerVestEnum::Store())->count() ?? 0,
        ];
        //
        return $product_counts;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByProductOnlineList($Store)
    {
        //
        $Product = $Store->products()->where('main_state', ProductStateEnum::Approved())->where('is_offline', IsOfflineEnum::online())->paginate(15);
        $products = ProductResource::collection($Product)->resource;
        //
        return $products;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByProductOfflineList($Store)
    {
        //
        $Product = $Store->products()->where('is_offline', IsOfflineEnum::offline())->where('product_vest', OwnerVestEnum::Store())->paginate(15);
        $products = ProductResource::collection($Product)->resource;
        //
        return $products;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByProductDemoList($Store)
    {
        //->where('product_vest', 1)
        $ProductDemo = $Store->product_demos()->where('product_vest', OwnerVestEnum::Store())->paginate(15);
        $product_demos = ProductDemoResource::collection($ProductDemo)->resource;
        //
        return $product_demos;
    }

    /**
     * @param $Store
     * @return mixed
     */
    public static function getStoreByProductClosureList($Store)
    {
        //
        $ProductClosure = $Store->products()->where('is_closure', IsClosureEnum::Discontinued())->where('product_vest', OwnerVestEnum::Store())->paginate(15);
        $product_closures = ProductResource::collection($ProductClosure)->resource;
        //
        return $product_closures;
    }

    //设置产品关键词
    public static function syncKeyWord($product, $keywords)
    {
        //
        $keyword_ids = [];
        //
        foreach ($keywords as $keyword) {
            //
            $Keyword = Keyword::where('platform_id', '=', config('app.platform_id'))->where('word', '=', $keyword)->first();
            //
            if (empty($Keyword)) {
                //
                $Keyword = new Keyword();
                $Keyword->platform_id = config('app.platform_id');
                $Keyword->keyword = $keyword;
                $Keyword->keyword_count = 1;
                $Keyword->save();
            }
            //
            array_push($keyword_ids, $Keyword->id);
        }
        //
        $product->keywords()->detach();
        $product->keywords()->attach($keyword_ids);
        //
        return $keyword_ids;
    }

    //设置产品分类
    public static function syncCategory($product, $categories)
    {
        //
        $Category_ids = Category::whereIn('id', $categories)->select('id')->pluck('id')->toArray();
        //
        $product->categories()->sync($Category_ids);
    }

    //设置产品属性
    public static function setParams($product, $params)
    {
        if (empty($params)) {
            //
            return false;
        }
        //
        $param_ids = [];
        //
        foreach ($params as $param) {
            //
            ProduceParam::where('id', $param['id'])->first();
            //
            $ProduceParamKey = ProduceParam::whereUuid($param['uuid'])->first();
            $ProduceParamValue = ProduceParam::whereUuid($param['value'])->first();
            //
            $ProductParam = ProductParam::firstWhere(['product_id' => $product->id, 'param_key_id' => $ProduceParamKey->id, 'param_value_id' => $ProduceParamValue->id]);
            //
            if (empty($ProductParam)) {
                //
                $ProductParam = new ProductParam();
                $ProductParam->platform_id = config('app.platform_id');
                $ProductParam->product_id = $product->id;
                $ProductParam->schema_id = $product->schema_id;
                //
                $ProductParam->param_key_id = $ProduceParamKey->id ?? null;
                $ProductParam->param_key_name = $ProduceParamKey->param_name ?? null;
                $ProductParam->param_key_english_name = $ProduceParamKey->param_english_name ?? null;
                $ProductParam->param_key_desc = $ProduceParamKey->param_description ?? null;
                $ProductParam->param_type = $ProduceParamKey->param_type ?? null;
                //
                $ProductParam->param_value_id = $ProduceParamValue->id ?? null;
                $ProductParam->param_value_name = $ProduceParamValue->param_name ?? null;
                $ProductParam->param_value_english_value = $ProduceParamValue->param_english_name ?? null;
                $ProductParam->param_value_desc = $ProduceParamValue->param_description ?? null;
                $ProductParam->save();
            }
            //
            array_push($param_ids, $ProductParam->id);
        }
        //
        ProductParam::where('product_id', $product->id)->whereNotIn('id', $param_ids)->delete();
    }

    /**
     * 设置产品标题
     * @param $subTitle
     * @param $groupName
     * @param $keywords
     * @return string
     */
    public static function getTitle($subTitle, $groupName, $keywords = [])
    {
        //
        $title = [];
        //
        if (!empty($subTitle)) {
            //
            array_push($title, $subTitle);
        }
        //
        if (!empty($groupName)) {
            //
            array_push($title, $groupName);
        }
        //
        if (!empty($keywords)) {
            //
            $keyword_str = implode(' ', $keywords);
            array_push($title, $keyword_str);
        }
        //
        shuffle($title);
        //
        $title_str = implode(' ', $title);
        //
        if (mb_strlen($title_str, 'utf-8') > 48) {
            //
            $title_str = mb_substr($title_str, 0, 48);
        }
        //
        return $title_str;
    }

    public static function validateUniqueLeafValues($jsonData)
    {
        //        dd($jsonData);
        //        $jsonData = json_decode($jsonData, true);

        $validator = Validator::make($jsonData, [
            '*' => [
                function ($attribute, $value, $fail) {
                    $leafValues = collect($value['leaf'])->pluck('value');

                    if ($leafValues->count() !== $leafValues->unique()->count()) {
                        $fail('The values under leaf must be unique.');
                    }
                },
            ],
        ]);

        return $validator;
    }
}
