<?php

namespace App\Services\Seller;

use App\Models\ProductCenter\ProductSkusDemo;
use App\Services\BaseService;

class ProductSkusDemoService extends BaseService
{
    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoToProductSkus($ProductSku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $ProductSku->amount;
        $ProductSkusDemo->price = $ProductSku->price;
        $ProductSkusDemo->reference_price = $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        return $ProductSkusDemo;
    }

    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoSnToProductSkusSn($ProductSku, $sku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSkusDemo->price = $sku['price'] ?? $ProductSku->price;
        $ProductSkusDemo->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $sku['is_offline'] ?? $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $sku['is_closure'] ?? $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        //
        $ProductSku->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSku->is_edit = 1;
        $ProductSku->main_state = 1;
        $ProductSku->client_ip = request()->ip();
        $ProductSku->save();
        //
        return $ProductSku;
    }

    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoWeightToProductSkusWeight($ProductSku, $sku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSkusDemo->price = $sku['price'] ?? $ProductSku->price;
        $ProductSkusDemo->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $sku['is_offline'] ?? $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $sku['is_closure'] ?? $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        //
        $ProductSku->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSku->is_edit = 1;
        $ProductSku->main_state = 1;
        $ProductSku->client_ip = request()->ip();
        $ProductSku->save();
        //
        return $ProductSku;
    }

    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoStockToProductSkusStock($ProductSku, $sku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSkusDemo->price = $sku['price'] ?? $ProductSku->price;
        $ProductSkusDemo->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $sku['is_offline'] ?? $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $sku['is_closure'] ?? $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        //
        $ProductSku->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSku->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSku->is_edit = 1;
        $ProductSku->main_state = 1;
        $ProductSku->client_ip = request()->ip();
        $ProductSku->save();
        //
        return $ProductSku;
    }

    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoMarketPriceToProductSkusMarketPrice($ProductSku, $sku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSkusDemo->price = $sku['price'] ?? $ProductSku->price;
        $ProductSkusDemo->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $sku['is_offline'] ?? $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $sku['is_closure'] ?? $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        //
        $ProductSku->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSku->is_edit = 1;
        $ProductSku->main_state = 1;
        $ProductSku->client_ip = request()->ip();
        $ProductSku->save();
        //
        return $ProductSku;
    }

    /**
     * @param $ProductSku
     * @param $sku
     * @return mixed
     */
    public static function upProductSkusDemoPriceToProductSkusPrice($ProductSku, $sku)
    {
        //保存ProductSkusDemo表数据
        $ProductSkusDemo = new ProductSkusDemo();
        //
        $ProductSkusDemo->platform_id = $ProductSku->platform_id;
        $ProductSkusDemo->region_id = $ProductSku->region_id;
        $ProductSkusDemo->site_id = $ProductSku->site_id;
        $ProductSkusDemo->site_market_id = $ProductSku->site_market_id;
        $ProductSkusDemo->category_id = $ProductSku->category_id;
        $ProductSkusDemo->corp_id = $ProductSku->corp_id;
        $ProductSkusDemo->seller_id = $ProductSku->seller_id;
        $ProductSkusDemo->store_id = $ProductSku->store_id;
        $ProductSkusDemo->provider_id = $ProductSku->provider_id;
        //
        $ProductSkusDemo->product_no = $ProductSku->product_no;
        $ProductSkusDemo->sku_no = $ProductSku->sku_no;
        //
        $ProductSkusDemo->product_type = 'App\Models\Product\Product';
        $ProductSkusDemo->product_id = $ProductSku->product_id;
        $ProductSkusDemo->sku_id = $ProductSku->id;
        //
        $ProductSkusDemo->shipment_pickup_id = $ProductSku->shipment_pickup_id;
        $ProductSkusDemo->shipment_address_id = $ProductSku->shipment_address_id;
        $ProductSkusDemo->shipment_express_id = $ProductSku->shipment_express_id;
        //
        $ProductSkusDemo->schema_id = $ProductSku->schema_id;
        $ProductSkusDemo->schema_name = $ProductSku->schema_name;
        //
        $ProductSkusDemo->theme_id = $ProductSku->theme_id;
        $ProductSkusDemo->theme_name = $ProductSku->theme_name;
        //
        $ProductSkusDemo->suit_attr = $ProductSku->suit_attr;
        $ProductSkusDemo->spec_attr = $ProductSku->spec_attr;
        $ProductSkusDemo->custom_attr = $ProductSku->custom_attr;
        //
        $ProductSkusDemo->suit_id = $ProductSku->suit_id;
        $ProductSkusDemo->suit_name = $ProductSku->suit_name;
        $ProductSkusDemo->spec_id = $ProductSku->spec_id;
        $ProductSkusDemo->spec_name = $ProductSku->spec_name;
        $ProductSkusDemo->brand_id = $ProductSku->brand_id;
        $ProductSkusDemo->media_image_id = $ProductSku->media_image_id;
        $ProductSkusDemo->wechat_img_url = $ProductSku->wechat_img_url;
        $ProductSkusDemo->sku_image = $ProductSku->sku_image;
        $ProductSkusDemo->sku_image_url = $ProductSku->sku_image_url;
        //
        $ProductSkusDemo->product_vest = $ProductSku->product_vest;
        $ProductSkusDemo->provision_vest = $ProductSku->provision_vest;
        $ProductSkusDemo->delivery_vest = $ProductSku->delivery_vest;
        //
        $ProductSkusDemo->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSkusDemo->price = $sku['price'] ?? $ProductSku->price;
        $ProductSkusDemo->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSkusDemo->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSkusDemo->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        $ProductSkusDemo->market_price = $sku['market_price'] ?? $ProductSku->market_price;
        //
        $ProductSkusDemo->sku_sn = $sku['sku_sn'] ?? $ProductSku->sku_sn;
        //
        $ProductSkusDemo->stock = $sku['stock'] ?? $ProductSku->stock;
        $ProductSkusDemo->jit_stock = $sku['stock'] ?? $ProductSku->jit_stock;
        //
        $ProductSkusDemo->weight = $sku['weight'] ?? $ProductSku->weight;
        //
        $ProductSkusDemo->is_offline = $sku['is_offline'] ?? $ProductSku->is_offline;
        $ProductSkusDemo->is_closure = $sku['is_closure'] ?? $ProductSku->is_closure;
        //
        $ProductSkusDemo->edit_type = 30;
        $ProductSkusDemo->is_edit = 2;
        $ProductSkusDemo->main_state = 2;
        //
        $ProductSkusDemo->client_ip = request()->ip();
        $ProductSkusDemo->save();
        //
        //
        $ProductSku->amount = $sku['price'] ?? $ProductSku->amount;
        $ProductSku->price = $sku['price'] ?? $ProductSku->price;
        $ProductSku->reference_price = $sku['price'] ?? $ProductSku->reference_price;
        $ProductSku->limited_price = $sku['price'] ?? $ProductSku->limited_price;
        $ProductSku->cost_price = $sku['price'] ?? $ProductSku->cost_price;
        //
        $ProductSku->is_edit = 1;
        $ProductSku->main_state = 1;
        $ProductSku->client_ip = request()->ip();
        $ProductSku->save();
        //
        return $ProductSku;
    }
}
