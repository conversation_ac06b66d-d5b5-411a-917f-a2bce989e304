<?php

namespace App\Services\Seller;

use App\Http\Resources\Seller\Shipment\ShipmentAddressResource;
use App\Http\Resources\Seller\Shipment\ShipmentLatticeResource;
use App\Models\CommonBrace\Week;
use App\Models\ShipmentCenter\ShipmentLattice;
use Exception;
use Illuminate\Http\Request;

class ShipmentLatticeService
{
    /**
     * 获取用户的所有发货格子信息
     * @param $store
     */
    public static function getAll($store)
    {
        // 查询用户所有发货格子信息
        $shipmentLattices = $store->shipment_lattice()->where('platform_id', '=', config('app.platform_id'))->get();
        // 使用资源类格式化发货格子信息
        $shipmentLatticesResource = ShipmentLatticeResource::collection($shipmentLattices)->resource;
        // 返回成功的响应结果
        return $shipmentLatticesResource;
    }

    /**
     * 返回创建格子时所需的表单数据
     * @param $store
     * @return array
     */
    public static function create($store)
    {
        // 获取发货格子信息资源
        $shipmentLattice = new ShipmentLatticeResource($store->shipment_lattice);
        // 获取发货地址信息资源
        $shipmentAddress = ShipmentAddressResource::collection(
            $store->shipment_address()->where('platform_id', '=', config('app.platform_id'))
                ->orderBy('is_default', 'desc')
                ->orderBy('id', 'desc')
                ->get()
        );
        // 获取星期信息
        $weeks = Week::select('week_id', 'week_name')->get() ?? null;
        // 返回表单数据
        return ['shipment_lattice' => $shipmentLattice, 'shipment_address' => $shipmentAddress, 'weeks' => $weeks];
    }

    /**
     * 创建新的发货格子信息
     * @param Request $request
     * @param         $store
     * @return void
     * @throws Exception
     */
    public static function store(Request $request, $store)
    {
        // 验证请求参数
        $request->validate([
            'lattice_name' => 'required',
            'lattice_bulletin' => 'required',
            'lattice_images' => 'required',
            'shipment_address_uuid' => 'required',
            'mobile' => 'required',
            'mobile_backup' => 'required',
            'week' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'wechat' => 'required',
        ]);
        // 检查发货格子数量是否超过限制
        $shipmentLattice = $store->shipment_lattice()->where('platform_id', '=', config('app.platform_id'))->first();
        //
        if (!isset($shipmentLattice)) {
            //
            $shipmentLattice = new ShipmentLattice();
        }
        // 获取发货地址实例
        $shipmentAddress = $store->shipment_address()->whereUuid($request->input('shipment_address_uuid'))->first();
        // 创建新的发货格子实例
        $shipmentLattice->platform_id = config('app.platform_id');
        $shipmentLattice->lattice_name = $request->input('lattice_name', '');
        $shipmentLattice->lattice_bulletin = $request->input('lattice_bulletin', '');
        $shipmentLattice->lattice_images = $request->input('lattice_images', '');
        $shipmentLattice->shipment_address_id = $shipmentAddress->id;
        $shipmentLattice->mobile = $request->input('mobile', '');
        $shipmentLattice->mobile_backup = $request->input('mobile_backup', '');
        $shipmentLattice->week = $request->input('week', '');
        $shipmentLattice->start_time = $request->input('start_time', '');
        $shipmentLattice->end_time = $request->input('end_time', null);
        $shipmentLattice->wechat = $request->input('wechat', 0);
        $shipmentLattice->client_ip = request()->ip;
        //
        $store->shipment_lattice()->save($shipmentLattice);
    }

    /**
     * 获取指定发货格子信息的详细信息用于编辑
     * @param        $store
     * @param string $uuid
     * @return array
     * @throws Exception
     */
    public static function edit($store, string $uuid)
    {
        // 获取指定UUID的发货格子实例
        $shipmentLattice = $store->shipment_lattice()->where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        if (empty($shipmentLattice)) {
            throw new Exception('参数不正确！');
        }

        // 获取发货格子信息资源
        $shipmentLatticeResource = new ShipmentLatticeResource($shipmentLattice);
        // 获取发货地址信息资源
        $shipmentAddress = ShipmentAddressResource::collection(
            $store->shipment_address()->where('platform_id', '=', config('app.platform_id'))
                ->orderBy('is_default', 'desc')
                ->orderBy('id', 'desc')
                ->get()
        );
        // 返回详细信息
        return ['shipment_lattice' => $shipmentLatticeResource, 'shipment_address' => $shipmentAddress];
    }

    /**
     * 更新指定的发货格子信息
     * @param Request $request
     * @param         $store
     * @param string $uuid
     * @return void
     * @throws Exception
     */
    public static function update(Request $request, $store, string $uuid)
    {
        // 验证请求参数
        $request->validate([
            'lattice_name' => 'required',
            'lattice_bulletin' => 'required',
            'lattice_images' => 'required',
            'shipment_address_uuid' => 'required',
            'mobile' => 'required',
            'mobile_backup' => 'required',
            'week' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'wechat' => 'required',
        ]);

        // 检查发货格子数量是否超过限制
        $count = $store->shipment_lattice()->where('platform_id', '=', config('app.platform_id'))->count();
        if ($count > 5) {
            throw new Exception('自提跑腿数量已满:)');
        }

        // 获取发货地址和发货格子实例
        $shipmentAddress = $store->shipment_address()->whereUuid($request->input('shipment_address_uuid'))->first();
        $shipmentLattice = $store->shipment_lattice()->whereUuid($uuid)->first();
        if (empty($shipmentLattice)) {
            throw new Exception('参数不正确！');
        }

        // 更新发货格子信息
        $shipmentLattice->lattice_name = $request->input('lattice_name', '');
        $shipmentLattice->lattice_bulletin = $request->input('lattice_bulletin', '');
        $shipmentLattice->lattice_images = $request->input('lattice_images', '');
        $shipmentLattice->shipment_address_id = $shipmentAddress->id;
        $shipmentLattice->mobile = $request->input('mobile', '');
        $shipmentLattice->mobile_backup = $request->input('mobile_backup', '');
        $shipmentLattice->week = $request->input('week', '');
        $shipmentLattice->start_time = $request->input('start_time', '');
        $shipmentLattice->end_time = $request->input('end_time', null);
        $shipmentLattice->wechat = $request->input('wechat', 0);
        $shipmentLattice->client_ip = request()->ip;
        $shipmentLattice->save();
    }

    /**
     * 删除指定的发货格子信息
     * @param        $store
     * @param string $uuid
     * @return void
     * @throws Exception
     */
    public static function destroy($store, string $uuid)
    {
        // 获取指定UUID的发货格子实例
        $shipmentLattice = $store->shipment_lattice()->where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        if (empty($shipmentLattice)) {
            throw new Exception('参数不正确！');
        }
        // 删除发货格子实例
        $shipmentLattice->delete();
    }
}
