<?php

namespace App\Services\Seller;

use App\Models\StoreCenter\StoreCat;
use App\Services\BaseService;

class StoreCatService extends BaseService
{
    // 获取首页数据
    public static function getStoreCatUuidToStoreCatId($uuid)
    {
        //
        $StoreCat = StoreCat::where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->first();
        //
        return $StoreCat->id ?? null;
    }

    // 获取首页数据
    public static function getStoreCatUuidsToStoreCatIds($uuid)
    {
        //
        $StoreCat_ids = StoreCat::where('platform_id', '=', config('app.platform_id'))->whereUuid($uuid)->select('id')->get()->pluck('id')->toArray();
        //
        return $StoreCat_ids ?? null;
    }
}
