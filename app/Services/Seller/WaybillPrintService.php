<?php

namespace App\Services\Seller;

use App\Enums\State\OrderStateEnum;
use App\Http\Resources\Seller\OrderWaybillPrint\OrderWaybillPrintResource;
use App\Models\OrderCenter\Order;
use App\Models\ShipmentCenter\ShipmentExpress;
use App\Models\WaybillCenter\WaybillDelivery;
use App\Services\BaseService;
use App\Services\Waybill\WaybillBirdService;

class WaybillPrintService extends BaseService
{
    /**
     * @param $order_uuid
     * @param $ship_express_uuid
     * @return mixed|null
     */
    public static function GetOrderProxyCaiNiaoCloudPrint($order_uuid, $ship_express_uuid)
    {
        //
        $Order = Order::where('platform_id', '=', config('app.platform_id'))->whereUuid($order_uuid)->first();
        $order = new OrderWaybillPrintResource($Order);
        $Store = $Order->store;
        //
        $ShipmentExpress = ShipmentExpress::where('platform_id', '=', config('app.platform_id'))->whereUuid($ship_express_uuid)->first();
        $Waybill = $ShipmentExpress->waybill;
        //
        if ($Waybill->is_regular == 1) {
            //
            $ShipmentAddress = $ShipmentExpress->shipment_address;
            $WaybillStation = $ShipmentExpress->waybill_station;
        } else {
            //
            $ShipmentAddress = $Order->shipper->shipment_address;
        }
        //
        $waybill_send_address = [
            'province' => $WaybillStation->province ?? $ShipmentAddress->province_name,
            'city' => $WaybillStation->city ?? $ShipmentAddress->city_name,
            'district' => $WaybillStation->area ?? $ShipmentAddress->area_name,
            'town' => $WaybillStation->county ?? $ShipmentAddress->town_name,
            'detail' => $WaybillStation->detail ?? $ShipmentAddress->detail_info,
            //
            'mobile' => $ShipmentAddress->mobile,
            'name' => $ShipmentAddress->contact_name,
            'phone' => $ShipmentAddress->contact_phone,
        ];
        //
        $print_orders_ens = json_encode($order);
        $print_orders_infos = json_decode($print_orders_ens, true);
        //
        $waybill_print_response = WaybillBirdService::GetWaybillBirdCloudPrintApply($Waybill->uuid, [$print_orders_infos], $waybill_send_address);
        //
        $waybill_response = $waybill_print_response['waybill_response'] ?? null;
        $Waybill = $waybill_print_response['waybill'] ?? null;
        //
        if (empty($waybill_response->code)) {
            //
            $print_responses = $waybill_response->modules->waybill_cloud_print_response;
            //
            foreach ($print_responses as $print_response) {
                //
                $WaybillDelivery = new WaybillDelivery();
                $WaybillDelivery->platform_id = config('app.platform_id');
                $WaybillDelivery->region_id = $Order->region_id ?? null;
                $WaybillDelivery->site_id = $Order->site_id ?? null;
                //
                $WaybillDelivery->corp_id = $Order->corp_id;
                $WaybillDelivery->user_id = $Order->user_id;
                //
                //                $WaybillDelivery->seller_id   = $Order->seller_id ?? null;
                //                $WaybillDelivery->store_id    = $Order->store_id ?? null;
                //                $WaybillDelivery->provider_id = $Order->provider_id ?? null;
                //
                $WaybillDelivery->order_id = $Order->id;
                $WaybillDelivery->waybill_id = $Waybill->id;
                $WaybillDelivery->waybill_account_id = $Waybill->waybill_account_id;
                $WaybillDelivery->sheet_id = $Waybill->sheet_id;
                //
                $WaybillDelivery->cp_code = $waybill_cloud_print_response->cp_code ?? null;
                $WaybillDelivery->real_cp_code = $waybill_cloud_print_response->real_cp_code ?? null;
                $WaybillDelivery->waybill_code = $waybill_cloud_print_response->waybill_code ?? null;
                $WaybillDelivery->print_data = json_decode($print_response->print_data, true) ?? null;
                $WaybillDelivery->main_state = 1;
                $WaybillDelivery->client_ip = request()->ip();
                $WaybillDelivery->save();
                //
                $OrderShipper = $Order->shipper;
                $OrderShipper->cp_code = $print_response->cp_code ?? null;
                //
                $OrderShipper->sheet_type = $Waybill->sheet_type ?? 1;
                $OrderShipper->waybill_id = $Waybill->id ?? null;
                $OrderShipper->express_corp_id = $Waybill->express_corp_id ?? null;
                $OrderShipper->delivery_id = $WaybillDelivery->id ?? null;
                //
                $OrderShipper->cp_name = $Waybill->cp_name ?? null;
                $OrderShipper->real_cp_code = $print_response->real_cp_code ?? null;
                $OrderShipper->master_express_no = $print_response->waybill_code ?? null;
                $OrderShipper->update();
                //
                $Order->is_waybill = 1;
                $Order->main_state = OrderStateEnum::OrderShipmentGetExpressNoSucceed();
                $Order->client_ip = request()->ip();
                $Order->update();
            }
        }
        //
        return $waybill_response;
    }

    /**
     * @param string $retail_ali1688_retail_id
     * @return mixed
     */
    public static function GetOrderPrintCustomTemplate($Store, $Order, $print_content, $print_describe, $OrderItems, $count_items, $sum_items)
    {
        //
        $title_state = in_array('title_state', $print_content);
        $retail_state = in_array('retail_state', $print_content);
        $shop_state = in_array('shop_state', $print_content);
        $time_state = in_array('time_state', $print_content);
        $no_state = in_array('no_state', $print_content);
        $buyer_message_state = in_array('message_state', $print_content);
        $seller_memo_state = in_array('memo_state', $print_content);
        $pay_state = in_array('pay_state', $print_content);
        $describe_state = in_array('describe_state', $print_content);
        $goods_state = in_array('goods_state', $print_content);
        $count_state = in_array('count_state', $print_content);
        //
        $retail_content = $retail_state ? '|1688' : null;
        $shop_content = $shop_state ? '|' . $Store->loginId : null;
        $time_content = $time_state ? $Order->create_time : null;
        $no_content = $no_state ? $Order->order_no : null;
        $buyer_message_content = $buyer_message_state ? $Order->buyerFeedback : null;
        $seller_memo_content = $seller_memo_state ? $Order->sellerMemo : null;
        $pay_content = $pay_state ? $Order->pay_time : null;
        $count_content = $count_state ? ($count_items ? $count_items . '/' : null) . ($sum_items ?? null) : null;
        //
        $goods_info_content = '';
        foreach ($OrderItems as $name => $quantity) {
            //
            $goods_info_content = $goods_info_content . '【' . $name . '】' . '*' . $quantity . '    ';
        }
        //
        $custom_templates = [
            'data' => array_filter([
                'waybill_info' => $retail_state || $shop_state ? "自" . ($retail_content ?? '') . ($shop_content ?? '') : null,
                'trade_info' => (($title_state && ($time_state || $no_state)) ? "交易信息：" : '') . ($time_state ? $time_content . '    ' : '') . ($no_state ? $no_content : ''),
                'buyer_message' => ($title_state && $buyer_message_state ? "用户留言：" : '') . ($buyer_message_state ? $buyer_message_content . '    ' : ''),
                'seller_memo' => ($title_state && $seller_memo_state ? "商家备注：" : '') . ($seller_memo_state ? $seller_memo_content . '    ' : ''),
                'pay_info' => ($title_state && $pay_state ? "付款时间：" : '') . ($pay_state ? $pay_content . '    ' : ''),
                'goods_info' => ($title_state && $goods_state ? "款式数量：" : '') . ($goods_state ? $goods_info_content . '    ' : ''),
                'pack_describe' => ($title_state && $describe_state ? "包裹描述：" : '') . $print_describe,
                'pack_count' => ($title_state && $count_state ? "包裹统计：" : '') . ($count_state ? $count_content . '    ' : ''),
                'consumer_info' => "   ****************************************************",
            ]),
            //
            'templateURL' => "https://cloudprint.cainiao.com/template/standard/616424/30",
        ];
        //
        return $custom_templates;
    }
}
