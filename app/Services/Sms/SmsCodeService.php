<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 短信验证码管理服务类
 *
 * 负责短信验证码的生成、存储、验证和管理，包括：
 * - 验证码生成和存储
 * - 验证码验证和清除
 * - 防重复发送机制
 * - 验证码过期管理
 * - 安全性检查
 *
 * @package App\Services\Sms
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class SmsCodeService
{
    /**
     * 验证码长度
     */
    private const CODE_LENGTH = 6;

    /**
     * 验证码过期时间（分钟）
     */
    private const CODE_EXPIRES_MINUTES = 5;

    /**
     * 验证码重发间隔（秒）
     */
    private const RESEND_INTERVAL_SECONDS = 60;

    /**
     * 最大验证尝试次数
     */
    private const MAX_VERIFY_ATTEMPTS = 5;

    /**
     * 生成短信验证码
     *
     * 为指定手机号和类型生成验证码并存储
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型（login, register, bind等）
     * @return string 返回生成的验证码
     * @throws \Exception 当生成失败时抛出异常
     */
    public function generateCode(string $mobile, string $type = 'login'): string
    {
        try {
            // 检查是否在重发间隔内
            $this->checkResendInterval($mobile, $type);

            // 生成验证码
            $code = $this->createRandomCode();

            // 存储验证码
            $this->storeCode($mobile, $type, $code);

            // 记录生成日志
            Log::info('短信验证码生成成功', [
                'mobile' => $mobile,
                'type' => $type,
                'code_length' => strlen($code),
                'expires_at' => now()->addMinutes(self::CODE_EXPIRES_MINUTES)->toDateTimeString()
            ]);

            return $code;

        } catch (\Exception $e) {
            Log::error('短信验证码生成失败', [
                'mobile' => $mobile,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 检查重发间隔
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @throws \Exception 当在重发间隔内时抛出异常
     */
    private function checkResendInterval(string $mobile, string $type): void
    {
        $remaining = $this->getResendRemainingSeconds($mobile, $type);

        if ($remaining > 0) {
            throw new \Exception("请等待 {$remaining} 秒后再重新发送");
        }
    }

    /**
     * 获取重发剩余时间（秒）
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return int 剩余秒数，0表示可以重发
     */
    public function getResendRemainingSeconds(string $mobile, string $type = 'login'): int
    {
        $key = $this->getResendCacheKey($mobile, $type);
        $lastSendTime = Cache::get($key);

        if (!$lastSendTime) {
            return 0;
        }

        $elapsed = now()->diffInSeconds($lastSendTime);
        $remaining = self::RESEND_INTERVAL_SECONDS - $elapsed;

        return max(0, $remaining);
    }

    /**
     * 获取重发时间缓存键
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return string 返回缓存键
     */
    private function getResendCacheKey(string $mobile, string $type): string
    {
        return "sms_resend:{$type}:{$mobile}";
    }

    /**
     * 创建随机验证码
     *
     * @return string 返回生成的验证码
     */
    private function createRandomCode(): string
    {
        // 生成纯数字验证码
        $code = '';
        for ($i = 0; $i < self::CODE_LENGTH; $i++) {
            $code .= random_int(0, 9);
        }

        // 确保验证码不是全相同数字（如：111111）
        if (strlen(array_unique(str_split($code))) === 1) {
            return $this->createRandomCode();
        }

        return $code;
    }

    /**
     * 存储验证码
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @param string $code 验证码
     */
    private function storeCode(string $mobile, string $type, string $code): void
    {
        $key = $this->getCodeCacheKey($mobile, $type);
        $resendKey = $this->getResendCacheKey($mobile, $type);

        $data = [
            'code' => $code,
            'mobile' => $mobile,
            'type' => $type,
            'created_at' => now(),
            'expires_at' => now()->addMinutes(self::CODE_EXPIRES_MINUTES),
        ];

        // 存储验证码，过期时间比实际过期时间多1分钟，用于日志记录
        Cache::put($key, $data, now()->addMinutes(self::CODE_EXPIRES_MINUTES + 1));

        // 存储重发时间戳
        Cache::put($resendKey, now(), now()->addSeconds(self::RESEND_INTERVAL_SECONDS));
    }

    /**
     * 获取验证码缓存键
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return string 返回缓存键
     */
    private function getCodeCacheKey(string $mobile, string $type): string
    {
        return "sms_code:{$type}:{$mobile}";
    }

    /**
     * 验证短信验证码
     *
     * 验证用户提交的验证码是否正确
     *
     * @param string $mobile 手机号
     * @param string $code 用户提交的验证码
     * @param string $type 验证码类型
     * @return bool 验证成功返回true，失败返回false
     */
    public function verifyCode(string $mobile, string $code, string $type = 'login'): bool
    {
        try {
            // 检查验证尝试次数
            if (!$this->checkVerifyAttempts($mobile, $type)) {
                Log::warning('短信验证码验证次数超限', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'attempts' => $this->getVerifyAttempts($mobile, $type)
                ]);
                return false;
            }

            // 获取存储的验证码信息
            $storedData = $this->getStoredCode($mobile, $type);

            if (!$storedData) {
                Log::warning('短信验证码不存在或已过期', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'submitted_code' => $code
                ]);

                $this->incrementVerifyAttempts($mobile, $type);
                return false;
            }

            // 验证码比较
            if (!hash_equals($storedData['code'], $code)) {
                Log::warning('短信验证码不匹配', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'submitted_code' => $code,
                    'attempts' => $this->incrementVerifyAttempts($mobile, $type)
                ]);
                return false;
            }

            // 检查是否过期
            if (now()->isAfter($storedData['expires_at'])) {
                Log::warning('短信验证码已过期', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'expires_at' => $storedData['expires_at'],
                    'current_time' => now()->toDateTimeString()
                ]);

                $this->clearCode($mobile, $type);
                return false;
            }

            // 验证成功，清除验证码和尝试次数
            $this->clearCode($mobile, $type);
            $this->clearVerifyAttempts($mobile, $type);

            Log::info('短信验证码验证成功', [
                'mobile' => $mobile,
                'type' => $type,
                'verified_at' => now()->toDateTimeString()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('短信验证码验证异常', [
                'mobile' => $mobile,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 检查验证尝试次数
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return bool 未超限返回true
     */
    private function checkVerifyAttempts(string $mobile, string $type): bool
    {
        $attempts = $this->getVerifyAttempts($mobile, $type);
        return $attempts < self::MAX_VERIFY_ATTEMPTS;
    }

    /**
     * 获取验证尝试次数
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return int 返回尝试次数
     */
    private function getVerifyAttempts(string $mobile, string $type): int
    {
        $key = $this->getAttemptsCacheKey($mobile, $type);
        return Cache::get($key, 0);
    }

    /**
     * 获取验证尝试次数缓存键
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return string 返回缓存键
     */
    private function getAttemptsCacheKey(string $mobile, string $type): string
    {
        return "sms_attempts:{$type}:{$mobile}";
    }

    /**
     * 获取存储的验证码数据
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return array|null 返回验证码数据或null
     */
    private function getStoredCode(string $mobile, string $type): ?array
    {
        $key = $this->getCodeCacheKey($mobile, $type);
        return Cache::get($key);
    }

    /**
     * 增加验证尝试次数
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return int 返回新的尝试次数
     */
    private function incrementVerifyAttempts(string $mobile, string $type): int
    {
        $key = $this->getAttemptsCacheKey($mobile, $type);
        $attempts = Cache::get($key, 0) + 1;

        // 尝试次数缓存1小时
        Cache::put($key, $attempts, now()->addHour());

        return $attempts;
    }

    /**
     * 清除验证码
     *
     * 删除指定手机号和类型的验证码
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     */
    public function clearCode(string $mobile, string $type = 'login'): void
    {
        $key = $this->getCodeCacheKey($mobile, $type);
        Cache::forget($key);

        Log::info('短信验证码已清除', [
            'mobile' => $mobile,
            'type' => $type
        ]);
    }

    /**
     * 清除验证尝试次数
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     */
    private function clearVerifyAttempts(string $mobile, string $type): void
    {
        $key = $this->getAttemptsCacheKey($mobile, $type);
        Cache::forget($key);
    }

    /**
     * 检查验证码是否存在且有效
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return bool 存在且有效返回true
     */
    public function hasValidCode(string $mobile, string $type = 'login'): bool
    {
        $storedData = $this->getStoredCode($mobile, $type);

        if (!$storedData) {
            return false;
        }

        return now()->isBefore($storedData['expires_at']);
    }

    /**
     * 获取验证码剩余有效时间（秒）
     *
     * @param string $mobile 手机号
     * @param string $type 验证码类型
     * @return int 剩余秒数，0表示已过期或不存在
     */
    public function getRemainingSeconds(string $mobile, string $type = 'login'): int
    {
        $storedData = $this->getStoredCode($mobile, $type);

        if (!$storedData) {
            return 0;
        }

        $remaining = now()->diffInSeconds($storedData['expires_at'], false);
        return max(0, $remaining);
    }

    /**
     * 批量清理过期验证码
     *
     * 清理所有过期的验证码缓存（可用于定时任务）
     */
    public function cleanupExpiredCodes(): void
    {
        // 这个方法可以在定时任务中调用
        // 由于使用了Cache的TTL机制，过期的验证码会自动清理
        // 这里主要用于记录清理日志

        Log::info('执行短信验证码清理任务', [
            'cleanup_time' => now()->toDateTimeString()
        ]);
    }
}
