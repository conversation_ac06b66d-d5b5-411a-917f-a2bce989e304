<?php

namespace App\Services\Wechat;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 微信公众号服务类
 *
 * 负责处理微信公众号相关的API调用，包括：
 * - 微信API认证和签名验证
 * - 获取访问令牌
 * - 用户信息获取
 * - 消息发送
 * - 事件处理
 *
 * @package App\Services\Wechat
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class WechatService
{
    /**
     * 微信API基础URL
     */
    private const API_BASE_URL = 'https://api.weixin.qq.com/cgi-bin';
    /**
     * 微信公众号配置
     *
     * @var array
     */
    protected array $config;

    /**
     * 构造函数
     *
     * 初始化微信服务配置
     */
    public function __construct()
    {
        $this->config = config('wechat.official_account', []);
    }

    /**
     * 验证微信服务器签名
     *
     * 验证来自微信服务器的请求签名
     *
     * @param Request $request HTTP请求对象
     * @return bool 验证成功返回true
     */
    public function verifySignature(Request $request): bool
    {
        $signature = $request->input('signature');
        $timestamp = $request->input('timestamp');
        $nonce = $request->input('nonce');
        $token = $this->config['token'] ?? '';

        if (!$signature || !$timestamp || !$nonce || !$token) {
            return false;
        }

        // 按字典序排序
        $tmpArr = [$token, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);

        // 拼接字符串并进行sha1加密
        $tmpStr = implode('', $tmpArr);
        $tmpStr = sha1($tmpStr);

        // 比较签名
        return $tmpStr === $signature;
    }

    /**
     * 解析微信消息
     *
     * 解析微信服务器推送的XML消息
     *
     * @param Request $request HTTP请求对象
     * @return array|null 返回解析后的消息数组或null
     */
    public function parseMessage(Request $request): ?array
    {
        try {
            $xmlContent = $request->getContent();

            if (empty($xmlContent)) {
                return null;
            }

            // 解析XML
            $xml = simplexml_load_string($xmlContent, 'SimpleXMLElement', LIBXML_NOCDATA);

            if ($xml === false) {
                Log::error('微信消息XML解析失败', ['content' => $xmlContent]);
                return null;
            }

            // 转换为数组
            $message = json_decode(json_encode($xml), true);

            Log::info('收到微信消息', [
                'message_type' => $message['MsgType'] ?? 'unknown',
                'from_user' => $message['FromUserName'] ?? 'unknown',
                'event' => $message['Event'] ?? null
            ]);

            return $message;

        } catch (\Exception $e) {
            Log::error('解析微信消息失败', [
                'error' => $e->getMessage(),
                'content' => $request->getContent()
            ]);

            return null;
        }
    }

    /**
     * 获取用户信息
     *
     * 根据OpenID获取微信用户信息
     *
     * @param string $openId 用户OpenID
     * @return array 返回用户信息
     */
    public function getUserInfo(string $openId): array
    {
        try {
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            // 请求用户信息
            $response = Http::timeout(30)->get(self::API_BASE_URL . '/user/info', [
                'access_token' => $accessToken,
                'openid' => $openId,
                'lang' => 'zh_CN',
            ]);

            if (!$response->successful()) {
                throw new \Exception('请求用户信息失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('获取用户信息失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            Log::info('获取微信用户信息成功', [
                'openid' => $openId,
                'nickname' => $data['nickname'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取微信用户信息失败', [
                'openid' => $openId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取访问令牌
     *
     * 获取微信API访问令牌，支持缓存
     *
     * @return string|null 返回访问令牌或null
     */
    public function getAccessToken(): ?string
    {
        $cacheKey = 'wechat_access_token';

        // 尝试从缓存获取
        $accessToken = Cache::get($cacheKey);

        if ($accessToken) {
            return $accessToken;
        }

        try {
            $appId = $this->config['app_id'] ?? '';
            $appSecret = $this->config['app_secret'] ?? '';

            if (!$appId || !$appSecret) {
                throw new \Exception('微信公众号配置不完整');
            }

            // 请求访问令牌
            $response = Http::timeout(30)->get(self::API_BASE_URL . '/token', [
                'grant_type' => 'client_credential',
                'appid' => $appId,
                'secret' => $appSecret,
            ]);

            if (!$response->successful()) {
                throw new \Exception('请求微信API失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('获取访问令牌失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            $accessToken = $data['access_token'] ?? null;
            $expiresIn = $data['expires_in'] ?? 7200;

            if (!$accessToken) {
                throw new \Exception('访问令牌为空');
            }

            // 缓存访问令牌，提前5分钟过期
            Cache::put($cacheKey, $accessToken, now()->addSeconds($expiresIn - 300));

            Log::info('微信访问令牌获取成功', [
                'expires_in' => $expiresIn,
                'cached_until' => now()->addSeconds($expiresIn - 300)->toDateTimeString()
            ]);

            return $accessToken;

        } catch (\Exception $e) {
            Log::error('获取微信访问令牌失败', [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 发送文本消息
     *
     * 向指定用户发送文本消息
     *
     * @param string $openId 用户OpenID
     * @param string $content 消息内容
     * @return array 返回发送结果
     */
    public function sendTextMessage(string $openId, string $content): array
    {
        try {
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            $messageData = [
                'touser' => $openId,
                'msgtype' => 'text',
                'text' => [
                    'content' => $content
                ]
            ];

            // 发送消息
            $response = Http::timeout(30)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post(self::API_BASE_URL . '/message/custom/send?access_token=' . $accessToken, $messageData);

            if (!$response->successful()) {
                throw new \Exception('发送消息失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('发送消息失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            Log::info('微信文本消息发送成功', [
                'openid' => $openId,
                'content_length' => strlen($content)
            ]);

            return [
                'success' => true,
                'message' => '消息发送成功'
            ];

        } catch (\Exception $e) {
            Log::error('微信文本消息发送失败', [
                'openid' => $openId,
                'content' => $content,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 发送图文消息
     *
     * 向指定用户发送图文消息
     *
     * @param string $openId 用户OpenID
     * @param array $articles 图文消息数组
     * @return array 返回发送结果
     */
    public function sendNewsMessage(string $openId, array $articles): array
    {
        try {
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            $messageData = [
                'touser' => $openId,
                'msgtype' => 'news',
                'news' => [
                    'articles' => $articles
                ]
            ];

            // 发送消息
            $response = Http::timeout(30)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post(self::API_BASE_URL . '/message/custom/send?access_token=' . $accessToken, $messageData);

            if (!$response->successful()) {
                throw new \Exception('发送图文消息失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('发送图文消息失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            Log::info('微信图文消息发送成功', [
                'openid' => $openId,
                'articles_count' => count($articles)
            ]);

            return [
                'success' => true,
                'message' => '图文消息发送成功'
            ];

        } catch (\Exception $e) {
            Log::error('微信图文消息发送失败', [
                'openid' => $openId,
                'articles_count' => count($articles),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取微信服务器IP列表
     *
     * 获取微信服务器的IP地址列表
     *
     * @return array 返回IP列表
     */
    public function getServerIpList(): array
    {
        try {
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('无法获取访问令牌');
            }

            $response = Http::timeout(30)->get(self::API_BASE_URL . '/getcallbackip', [
                'access_token' => $accessToken,
            ]);

            if (!$response->successful()) {
                throw new \Exception('获取服务器IP列表失败: ' . $response->status());
            }

            $data = $response->json();

            if (isset($data['errcode']) && $data['errcode'] !== 0) {
                throw new \Exception('获取服务器IP列表失败: ' . ($data['errmsg'] ?? 'Unknown error'));
            }

            return [
                'success' => true,
                'data' => $data['ip_list'] ?? []
            ];

        } catch (\Exception $e) {
            Log::error('获取微信服务器IP列表失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 清除访问令牌缓存
     *
     * 手动清除访问令牌缓存
     */
    public function clearAccessTokenCache(): void
    {
        Cache::forget('wechat_access_token');

        Log::info('微信访问令牌缓存已清除');
    }

    /**
     * 检查微信API配置
     *
     * 检查微信公众号配置是否完整
     *
     * @return array 返回检查结果
     */
    public function checkConfig(): array
    {
        $requiredFields = ['app_id', 'app_secret', 'token'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'valid' => false,
                'message' => '微信配置不完整，缺少字段: ' . implode(', ', $missingFields),
                'missing_fields' => $missingFields
            ];
        }

        return [
            'valid' => true,
            'message' => '微信配置完整'
        ];
    }
}
