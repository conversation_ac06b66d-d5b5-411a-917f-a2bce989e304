<?php

namespace App\Traits\Lesson;

use App\Models\ThemeCenter\Theme;
use App\Services\BaseService;

trait HandleThemeTrait
{
    /**
     * 处理主题逻辑，根据主题名称查找或创建主题
     * @param array|string|null $themeNames 主题名称，可能是数组或字符串
     * @param string $platformId 平台ID
     * @return Theme|null 返回Theme对象或null
     */
    public function handleTheme($themeNames, string $platformId)
    {
        //
        if (!empty($themeNames)) {
            // 将主题名称拼接成字符串
            $themeNameString = is_array($themeNames) ? implode(', ', $themeNames) : $themeNames;
            // 查找或创建主题
            return Theme::firstOrCreate(
                ['platform_id' => $platformId, 'theme_name' => $themeNameString],
                ['theme_no' => BaseService::BaseGetNo()]
            );
        }
        //
        return null;
    }
}
