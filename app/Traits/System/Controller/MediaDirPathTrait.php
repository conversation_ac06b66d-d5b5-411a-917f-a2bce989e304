<?php

namespace App\Traits\System\Controller;

use DateTime;
use Vinkla\Hashids\Facades\Hashids;

trait MediaDirPathTrait
{
    /**
     * 获取媒体文件的目录路径
     * @param string $scene 场景标识
     * @param int $id 店铺 ID
     * @return string 返回生成的目录路径
     */
    protected function getDirPathTrait(string $scene, int $id, string $formattedDate = null): string
    {
        // 使用提供的日期字符串创建 DateTime 对象，如果为空则使用当前日期
        $date = new DateTime($formattedDate ?? 'now');

        // 格式化日期为 "ym" 形式（例如，"2410" 表示2024年10月）
        // 并将格式化后的日期转换为整数，以确保可以被 Hashids 编码
        $formatted_date = (int)$date->format('ym'); // 转换为整数

        // 使用 Hashids 对格式化后的日期进行编码，以增加路径的复杂性和唯一性
        $datePath = Hashids::encode($formatted_date);

        // 使用 Base64 编码场景字符串，并去除末尾的 "=" 号（填充字符）
        $encoded_scene = rtrim(base64_encode($scene), '=');

        // 构建目录路径格式：平台ID/场景/店铺ID/日期编码
        // - Hashids::encode(config('app.platform_id')) 获取平台 ID 的编码值
        // - $encoded_scene 为编码后的场景字符串
        // - Hashids::encode($id) 获取店铺 ID 的编码值
        // - $datePath 为编码后的日期字符串
        return Hashids::encode(config('app.platform_id')) . '/' .  // 平台ID
            $encoded_scene . '/' .                              // 场景
            Hashids::encode($id) . '/' .                        // 店铺ID
            $datePath . '/';                                    // 日期路径
    }
}
