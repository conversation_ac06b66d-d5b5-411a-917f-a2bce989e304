<?php


namespace App\Traits\System\Controller;

use App\Models\MediaCenter\Media;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

trait MediaUploadTrait
{
    public function handleImageUpload($filePath, $md5Name, $entity, $params = [])
    {
        // 检查数据库中是否已经存在相同的媒体文件
        $Media = Media::where('platform_id', config('app.platform_id'))->where('name', $md5Name)->first();
        //
        if (is_null($Media)) {

            // 从 OSS（对象存储服务）中读取文件内容
            $fileContent = Storage::disk('oss')->readStream($filePath);

            // 使用 Intervention Image 对图片进行处理和调整尺寸
            $imageContent = Image::make($fileContent)->resize(1080, 1080, function ($constraint) {
                $constraint->aspectRatio(); // 保持图片的宽高比
                $constraint->upsize();      // 防止图片放大超过原尺寸
            })->encode(); // 将图片编码为其原始格式
            // 使用 Media Library 将处理后的图片保存到模型中
            $Media = $entity->addMediaFromStream($imageContent->stream()) // 使用 addMediaFromStream 保存图片流
            ->preservingOriginal()                                  // 保留原始文件
            ->usingName($md5Name)                                   // 使用 MD5 作为文件的唯一标识名称
            ->usingFileName(basename($filePath))                    // 保留原始文件名
            ->withCustomProperties([$params])                 // 将请求中的所有属性存储为自定义属性
            ->toMediaCollection($params['scene'], 'img');   // 存储到指定媒体集合中，默认为'img'
        }
        // 返回成功信息及媒体资源（包含原图或缩略图）
        return $Media;
    }

    public function handleVideoUpload($filePath, $md5Name, $entity, $params = [])
    {
        // 检查数据库中是否已经存在相同的媒体文件
        $Media = Media::where('platform_id', config('app.platform_id'))->where('name', $md5Name)->first();
        //
        if (is_null($Media)) {
            // 使用 Media Library 将视频直接从磁盘保存
            $Media = $entity->addMediaFromDisk($filePath, 'oss') // 从 OSS 磁盘存储中获取文件
            ->preservingOriginal()                          // 保留原始文件
            ->usingName($md5Name)                           // 使用 MD5 作为文件的唯一标识名称
            ->usingFileName(basename($filePath))            // 保留原始文件名
            ->withCustomProperties([$params])         // 将请求中的所有属性存储为自定义属性
            ->toMediaCollection($params['scene'], 'video'); // 存储到指定媒体集合，默认为'video'
        }
        // 返回成功信息及媒体资源（包含原图或缩略图）
        return $Media;
    }
}
