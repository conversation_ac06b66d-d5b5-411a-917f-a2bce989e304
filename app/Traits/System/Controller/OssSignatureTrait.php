<?php

namespace App\Traits\System\Controller;

trait OssSignatureTrait
{
    /**
     * 生成签名配置
     * @param string $prefix 文件前缀（上传目录）
     * @param string|null $callbackUrl 回调 URL
     * @param array $customData 自定义参数
     * @param int $expire 策略有效期（秒）
     * @param int $contentLengthRangeValue 文件大小限制（字节）
     * @param array $systemData 系统参数
     * @return array
     */
    public function signatureConfig(string $prefix = '', ?string $callbackUrl = null, array $params = [], array $customData = [], array $systemData = [], int $expire = 30, int $contentLengthRangeValue = 1048576000)
    {
        // 系统参数，占位符
        $systemFields = [
            'bucket' => '${bucket}',
            'etag' => '${etag}',
            'file_path' => '${object}',
            'size' => '${size}',
            'mime_type' => '${mimeType}',
            'height' => '${imageInfo.height}',
            'width' => '${imageInfo.width}',
            'format' => '${imageInfo.format}',
            'file_name' => strtolower($params['file_name'] ?? ''),
            'store_uuid' => $params['store_uuid'] ?? null,
            'describe' => $params['describe'] ?? null,
            'scene' => $params['scene'] ?? null,
            'md5' => $params['md5'] ?? null,
            'own' => $params['own'] ?? null,
        ];
        // 获取 OSS 配置信息
        $accessKeyId = config('filesystems.disks.oss.access_key_id');
        $accessKeySecret = config('filesystems.disks.oss.access_key_secret');
        $bucket = config('filesystems.disks.oss.bucket');
        $endpoint = config('filesystems.disks.oss.endpoint');

        // 构建上传地址
        $host = 'https://' . $bucket . '.' . $endpoint;

        // 自定义参数处理
        $callbackVar = [];
        $data = [];
        if (!empty($customData)) {
            foreach ($customData as $key => $value) {
                $callbackVar['x:' . $key] = $value;
                $data[$key] = '${x:' . $key . '}';
            }
        }

        // 构建回调参数
        $callbackParam = [
            'callbackUrl' => $callbackUrl,
            'callbackBody' => urldecode(http_build_query(array_merge($systemFields, $data))),
            'callbackBodyType' => 'application/x-www-form-urlencoded',
        ];
        $callbackString = json_encode($callbackParam);
        $base64CallbackBody = base64_encode($callbackString);

        // 策略失效时间
        $now = time();
        $end = $now + $expire; // 策略有效期
        $expiration = $this->gmtIso8601($end);

        // 上传限制条件
        $conditions = [];
        // 文件大小限制
        $conditions[] = ['content-length-range', 0, $contentLengthRangeValue];
        // 文件上传目录前缀
        $conditions[] = ['starts-with', '$key', $prefix];

        // 构建策略（Policy）
        $policy = [
            'expiration' => $expiration,
            'conditions' => $conditions,
        ];
        $policyString = json_encode($policy);
        $base64Policy = base64_encode($policyString);

        // 生成签名
        $signature = base64_encode(hash_hmac('sha1', $base64Policy, $accessKeySecret, true));

        // 返回上传所需的参数
        $response = [
            'accessid' => $accessKeyId,
            'host' => $host,
            'policy' => $base64Policy,
            'signature' => $signature,
            'expire' => $end,
            'callback' => $base64CallbackBody,
            'dir' => $prefix,
            'file_name' => strtolower($params['file_name'] ?? ''),
            'callback-var' => $callbackVar,
            'exist_type' => false,
        ];
        //
        return $response;
    }

    /**
     * 生成 ISO8601 格式的时间字符串
     * @param int $time 时间戳
     * @return string
     */
    private function gmtIso8601($time)
    {
        //
        return gmdate('Y-m-d\TH:i:s\Z', $time);
    }

    /**
     * 验证 OSS 回调签名
     * @return array [bool, array] 验证结果和数据
     */
    public function verify()
    {
        // 获取 OSS 的签名 header 和公钥 URL header
        $authorizationBase64 = '';
        $pubKeyUrlBase64 = '';

        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authorizationBase64 = $_SERVER['HTTP_AUTHORIZATION'];
        }
        if (isset($_SERVER['HTTP_X_OSS_PUB_KEY_URL'])) {
            $pubKeyUrlBase64 = $_SERVER['HTTP_X_OSS_PUB_KEY_URL'];
        }

        if ($authorizationBase64 == '' || $pubKeyUrlBase64 == '') {
            return [false, ['CallbackFailed' => '缺少签名信息']];
        }

        // 解码签名和公钥 URL
        $authorization = base64_decode($authorizationBase64);
        $pubKeyUrl = base64_decode($pubKeyUrlBase64);

        // 获取公钥
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $pubKeyUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        $pubKey = curl_exec($ch);
        curl_close($ch);

        if ($pubKey == '') {
            return [false, ['CallbackFailed' => '获取公钥失败']];
        }

        // 获取回调的 body 内容
        $body = file_get_contents('php://input');

        // 构造待签名的字符串
        $path = $_SERVER['REQUEST_URI'];
        $pos = strpos($path, '?');
        if ($pos === false) {
            $authStr = urldecode($path) . "\n" . $body;
        } else {
            $authStr = urldecode(substr($path, 0, $pos)) . substr($path, $pos) . "\n" . $body;
        }

        // 验证签名
        $ok = openssl_verify($authStr, $authorization, $pubKey, OPENSSL_ALGO_MD5);
        if ($ok == 1) {
            // 解析回调 body 中的参数
            parse_str($body, $data);
            return [true, $data];
        } else {
            return [false, ['CallbackFailed' => '签名验证失败']];
        }
    }
}
