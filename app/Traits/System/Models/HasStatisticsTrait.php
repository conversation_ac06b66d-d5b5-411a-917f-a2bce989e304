<?php

namespace App\Traits\System\Models;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * HasStatisticsTrait 提供模型的统计功能。
 */
trait HasStatisticsTrait
{
    /**
     * 获取按指定字段分组并映射键名的统计数据
     * @param string $field 要分组的字段
     * @param array $keyMap 映射数组，键为返回数组的键名，值为字段的值
     * @return array
     */
    public static function getMappedCounts(string $field, array $keyMap): array
    {
        // 提取需要统计的状态值（确保它们是标量值）
        $values = array_values($keyMap);

        // 获取分组统计结果
        $stateCounts = self::getGroupedStatistics($field, $values);

        // 构建返回的统计数组
        $counts = [];
        foreach ($keyMap as $key => $state) {
            // 如果状态值是对象，尝试获取其标量值
            if (is_object($state)) {
                if (method_exists($state, 'value')) {
                    $state = $state->value;
                } elseif (method_exists($state, '__toString')) {
                    $state = (string)$state;
                } else {
                    // 无法处理的对象类型，记录错误并跳过
                    Log::error("Cannot extract scalar value from object for state {$state}");
                    $counts[$key] = 0;
                    continue;
                }
            }

            $counts[$key] = $stateCounts[$state] ?? 0;
        }

        return $counts;
    }

    /**
     * 获取按指定字段分组的统计数据
     * @param string $field 要分组的字段
     * @param array|null $values 可选，指定要统计的具体值
     * @return array
     */
    public static function getGroupedStatistics(string $field, array $values = null): array
    {
        try {
            // 初始化查询，过滤当前平台
            $query = static::query()->where('platform_id', config('app.platform_id'));

            // 如果指定了特定值，则只统计这些值
            if (!is_null($values)) {
                $query->whereIn($field, $values);
            }

            // 执行分组统计
            $results = $query->select($field, DB::raw('COUNT(*) as count'))
                ->groupBy($field)
                ->get();

            // 转换结果为键值对数组
            $statistics = [];
            foreach ($results as $result) {
                //
                $key = $result->{$field};
                // 如果键是对象，尝试获取其标量值
                if (is_object($key)) {
                    if (method_exists($key, 'value')) {
                        $key = $key->value;
                    } elseif (method_exists($key, '__toString')) {
                        $key = (string)$key;
                    } else {
                        // 无法处理的对象类型，记录错误并跳过
                        Log::error("Cannot extract scalar value from object for field {$field}");
                        continue;
                    }
                }
                //
                $statistics[$key] = $result->count;
            }

            // 如果指定了特定值，确保所有值都有对应的统计数据
            if (!is_null($values)) {
                foreach ($values as $value) {
                    // 如果值是对象，获取其标量值
                    if (is_object($value)) {
                        if (method_exists($value, 'value')) {
                            $value = $value->value;
                        } elseif (method_exists($value, '__toString')) {
                            $value = (string)$value;
                        } else {
                            // 无法处理的对象类型，记录错误并跳过
                            Log::error("Cannot extract scalar value from object for field {$field}");
                            continue;
                        }
                    }

                    if (!isset($statistics[$value])) {
                        $statistics[$value] = 0;
                    }
                }
            }

            return $statistics;
        } catch (Exception $e) {
            // 记录错误日志
            Log::error("Error in getGroupedStatistics: " . $e->getMessage());

            // 返回空数组或默认值
            return [];
        }
    }

    /**
     * 获取多字段的统计数据
     * @param array $fields 要统计的字段列表
     * @param array|null $valuesMap 可选，每个字段对应的特定值列表
     * @return array
     */
    public static function getMultipleGroupedStatistics(array $fields, array $valuesMap = null): array
    {
        $statistics = [];

        foreach ($fields as $field) {
            $values = $valuesMap[$field] ?? null;
            $statistics[$field] = static::getGroupedStatistics($field, $values);
        }

        return $statistics;
    }
}
