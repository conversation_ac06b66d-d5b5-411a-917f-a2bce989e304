<?php

namespace App\Traits\System\Models;

use App\Models\ProductCenter\ProductSkuStock;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use InvalidArgumentException;

trait HasStockTrait
{
    /*
     |----------------------------------------------------------------------
     | 访问器
     |----------------------------------------------------------------------
     */

    /**
     * 获取库存属性。
     * @return int
     */
    public function getStockAttribute()
    {
        return $this->stock();
    }

    /*
     |----------------------------------------------------------------------
     | 方法
     |----------------------------------------------------------------------
     */

    /**
     * 计算库存。
     * @param mixed $date 传入日期，默认为当前时间。
     * @return int 返回指定时间点的库存数量。
     */
    public function stock($date = null)
    {
        $date = $date ?: Carbon::now();

        if (!$date instanceof DateTimeInterface) {
            $date = Carbon::create($date);
        }

        return (int)$this->stockMutations()
            ->where('created_at', '<=', $date->format('Y-m-d H:i:s'))
            ->sum('stock');
    }

    /**
     * 与 ProductSkuStock 关联。
     * @return HasMany
     */
    public function stockMutations()
    {
        return $this->hasMany(ProductSkuStock::class, 'product_sku_id', 'id');
    }

    /**
     * 增加库存。
     * @param int $stock 增加的库存量。
     * @param array $arguments 额外参数。
     * @return bool
     */
    public function increaseStock($stock = 1, $arguments = [])
    {
        return $this->createStockMutation($stock, $arguments);
    }

    /**
     * 创建库存变动。
     * @param int $stock 库存变化量。
     * @param array $arguments 额外参数。
     * @return bool
     * @throws InvalidArgumentException 如果 product_id 缺失。
     */
    protected function createStockMutation($stock, $arguments = [])
    {
        $reference = Arr::get($arguments, 'reference');
        $productId = $this->product_id;

        // 验证 product_id 是否存在
        if (empty($productId)) {
            throw new InvalidArgumentException("product_id is required for stock operations.");
        }

        $createArguments = collect([
            'uuid' => (string)Str::uuid(),  // 生成 UUID
            'stock' => $stock,
            'product_id' => $productId,  // 自动关联 product_id
            'description' => Arr::get($arguments, 'description'),
        ])->when($reference, function ($collection) use ($reference) {
            return $collection
                ->put('reference_type', $reference->getMorphClass())
                ->put('reference_id', $reference->getKey());
        })->toArray();

        return $this->stockMutations()->create($createArguments);
    }

    /**
     * 减少库存。
     * @param int $stock 减少的库存量。
     * @param array $arguments 额外参数。
     * @return bool
     */
    public function decreaseStock($stock = 1, $arguments = [])
    {
        return $this->createStockMutation(-1 * abs($stock), $arguments);
    }

    /**
     * 改变库存。
     * @param int $stock 库存变化量。
     * @param array $arguments 额外参数。
     * @return bool
     */
    public function mutateStock($stock = 1, $arguments = [])
    {
        return $this->createStockMutation($stock, $arguments);
    }

    /**
     * 清空库存并设置新的库存量。
     * @param int|null $newAmount 新库存值。
     * @param array $arguments 额外参数。
     * @return bool
     */
    public function clearStock($newAmount = null, $arguments = [])
    {
        DB::transaction(function () use ($newAmount, $arguments) {
            $this->stockMutations()->delete();

            if (!is_null($newAmount)) {
                $this->createStockMutation($newAmount, $arguments);
            }
        });

        return true;
    }

    /**
     * 设置库存值并基于差异创建库存变动。
     * @param int $newAmount 新库存值。
     * @param array $arguments 额外参数。
     * @return bool
     */
    public function setStock($newAmount, $arguments = [])
    {
        $currentStock = $this->stock;

        if ($deltaStock = $newAmount - $currentStock) {
            return $this->createStockMutation($deltaStock, $arguments);
        }

        return false;
    }

    /**
     * 判断库存是否足够。
     * @param int $stock 需要的库存量。
     * @return bool
     */
    public function inStock($stock = 1)
    {
        return $this->stock >= $stock;
    }

    /*
     |----------------------------------------------------------------------
     | 查询作用域
     |----------------------------------------------------------------------
     */

    /**
     * 判断是否缺货。
     * @return bool
     */
    public function outOfStock()
    {
        return $this->stock <= 0;
    }

    /**
     * 查询有库存的产品。
     * @param $query
     * @return mixed
     */
    public function scopeWhereInStock($query)
    {
        return $query->where(function ($query) {
            return $query->whereHas('stockMutations', function ($query) {
                return $query->select('product_sku_id')
                    ->groupBy('product_sku_id')
                    ->havingRaw('SUM(stock) > 0');
            });
        });
    }

    /*
     |----------------------------------------------------------------------
     | 关联关系
     |----------------------------------------------------------------------
     */

    /**
     * 查询缺货的产品。
     * @param $query
     * @return mixed
     */
    public function scopeWhereOutOfStock($query)
    {
        return $query->where(function ($query) {
            return $query->whereHas('stockMutations', function ($query) {
                return $query->select('product_sku_id')
                    ->groupBy('product_sku_id')
                    ->havingRaw('SUM(stock) <= 0');
            })->orWhereDoesntHave('stockMutations');
        });
    }
}
