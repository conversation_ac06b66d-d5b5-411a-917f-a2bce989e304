<?php

namespace App\Traits\System\Queue;

use Illuminate\Notifications\Notifiable as NotifiableTrait;

/**
 * QueueJobFailedNotifiableTrait 是一个用于处理队列任务失败时发送通知的 Trait。
 * 这个 Trait 使用了 Laravel 的 Notifiable trait，能够通过邮件和 Slack 发送通知。
 */
class QueueJobFailedNotifiableTrait
{
    use NotifiableTrait;

    /**
     * 获取邮件通知的接收者地址列表。
     * @return array 邮件接收者的数组
     */
    public function routeNotificationForMail(): array
    {
        // 从配置文件中获取接收邮件通知的邮箱地址
        $recipients = config('failed-job-monitor.mail.to');

        // 如果接收者是字符串（单个邮箱地址），则转换为数组
        if (is_string($recipients)) {
            return [$recipients];
        }

        // 返回接收者数组
        return $recipients;
    }

    /**
     * 获取 Slack 通知的 Webhook URL。
     * @return string Slack 通知的 Webhook URL
     */
    public function routeNotificationForSlack(): string
    {
        // 从配置文件中获取 Slack Webhook URL
        return config('failed-job-monitor.slack.webhook_url');
    }

    /**
     * 获取当前对象的主键。
     * @return int 返回固定的主键值 1
     */
    public function getKey(): int
    {
        // 这里返回的主键值是固定的 1，可能用于标识通知发送的对象
        return 1;
    }
}
