<?php

namespace App\Traits\User\User;

use App\Models\UserCenter\User;
use Illuminate\Support\Facades\Auth;

trait HasUserTrait
{
    /**
     * Get the key for the enum value.
     * @return string|null
     */
    public static function getUserIdByUser($user_id = null): ?User
    {
        //
        $id = Auth::id() ?? request()->user()->id ?? $user_id;
        $User = User::find($id);
        //
        return $User ?? null;
    }
}
