{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "aliyuncs/oss-sdk-php": "^2.7", "alphasnow/aliyun-oss-laravel": "^4.9", "barryvdh/laravel-dompdf": "^3.1.1", "bavix/laravel-wallet": "^11.4", "bilfeldt/laravel-http-client-logger": "^2.4", "bilfeldt/laravel-request-logger": "^3.6", "bilfeldt/laravel-route-statistics": "^4.2", "cmgmyr/messenger": "^2.31", "binafy/laravel-cart": "^1.2", "dyrynda/laravel-model-uuid": "^8.2", "easywechat-composer/easywechat-composer": "^1.4", "fukuball/jieba-php": "^0.34.0", "guanguans/laravel-exception-notify": "^5.2", "guanguans/notify": "^3.2.2", "guzzlehttp/guzzle": "^7.9", "inertiajs/inertia-laravel": "^2.0", "intervention/image": "^3.11", "jaybizzle/laravel-crawler-detect": "^1.3", "jdavidbakr/mail-tracker": "^7.18", "jenssegers/agent": "^2.6", "kalnoy/nestedset": "^6.0", "laracraft-tech/laravel-date-scopes": "^2.4", "laravel/breeze": "^2.3", "laravel/cashier-paddle": "^2.6", "laravel/fortify": "^1.26", "laravel/framework": "^12.18.0", "laravel/sanctum": "^4.1", "laravel/scout": "^10.15", "laravel/tinker": "^2.10", "laravolt/avatar": "^6.2", "league/flysystem-ftp": "^3.29", "leonis/easysms-notification-channel": "^3.0", "liliuwei/liliuwei-pscws4": "^1.0", "maatwebsite/excel": "^3.1", "mews/purifier": "^3.4", "mikebronner/laravel-model-caching": "^12.0", "naux/iplocation": "dev-master@dev", "oddvalue/laravel-drafts": "^2.1", "overtrue/laravel-filesystem-cos": "^3.6", "overtrue/laravel-follow": "^5.3", "overtrue/laravel-wechat": "^7.4", "overtrue/socialite": "^4.11", "pimple/pimple": "^3.5", "predis/predis": "^3.0", "qcloud/cos-sdk-v5": "^2.6", "qcloud_sts/qcloud-sts-sdk": "^3.0", "ralphjsmit/laravel-seo": "^1.7", "rappasoft/laravel-authentication-log": "^5.0", "santigarcor/laratrust": "^8.5", "spatie/async": "^1.6", "spatie/image": "^3.8", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-backup": "^9.2", "spatie/laravel-enum": "^3.2", "spatie/laravel-failed-job-monitor": "*", "spatie/laravel-medialibrary": "^11.12", "spatie/laravel-schedule-monitor": "^3.10", "spatie/laravel-sitemap": "^7.3", "spatie/laravel-sluggable": "^3.7", "spatie/laravel-translatable": "^6.11", "spatie/laravel-url-signer": "^3.2", "spatie/period": "^2.4", "symfony/http-client": "^7.3", "tightenco/ziggy": "^2.5", "torann/geoip": "^3.0", "wnx/laravel-backup-restore": "^1.6", "yansongda/laravel-pay": "^3.7"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/pail": "^1.2.2", "laravel/pint": "*", "laravel/sail": "^1.43", "laravel/telescope": "^5.9", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2", "spatie/laravel-ignition": "^2.9", "symfony/mailer": "^7.3", "laravel/breeze": "^2.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "easywechat-composer/easywechat-composer": true}}, "minimum-stability": "stable", "prefer-stable": true}