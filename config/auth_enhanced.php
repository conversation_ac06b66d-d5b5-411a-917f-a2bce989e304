<?php

/**
 * 增强认证系统配置文件
 *
 * 此配置文件包含了增强认证系统的所有配置选项
 * 包括安全设置、会话管理、密码策略等
 *
 * @package Config
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */

return [

    /*
    |--------------------------------------------------------------------------
    | 认证安全设置
    |--------------------------------------------------------------------------
    |
    | 这些设置控制认证系统的安全行为
    |
    */

    'security' => [
        // 是否启用增强安全检查
        'enhanced_security' => env('AUTH_ENHANCED_SECURITY', true),

        // 是否检查IP地址变化
        'check_ip_change' => env('AUTH_CHECK_IP_CHANGE', true),

        // 是否检查用户代理变化
        'check_user_agent_change' => env('AUTH_CHECK_USER_AGENT_CHANGE', false),

        // 可信任的IP地址列表
        'trusted_ips' => [
            '127.0.0.1',
            '::1',
            // 可以添加更多可信任的IP地址
        ],

        // 是否启用设备指纹识别
        'device_fingerprinting' => env('AUTH_DEVICE_FINGERPRINTING', false),

        // 是否启用地理位置检查
        'geo_location_check' => env('AUTH_GEO_LOCATION_CHECK', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | 会话管理设置
    |--------------------------------------------------------------------------
    |
    | 控制用户会话的行为和生命周期
    |
    */

    'session' => [
        // 会话超时时间（秒）
        'timeout' => env('AUTH_SESSION_TIMEOUT', 7200), // 2小时

        // 会话即将过期警告时间（分钟）
        'warning_minutes' => env('AUTH_SESSION_WARNING', 10),

        // 是否启用会话并发控制
        'concurrent_sessions' => env('AUTH_CONCURRENT_SESSIONS', false),

        // 最大并发会话数
        'max_concurrent_sessions' => env('AUTH_MAX_CONCURRENT_SESSIONS', 3),

        // 是否在新设备登录时踢出旧会话
        'kick_old_sessions' => env('AUTH_KICK_OLD_SESSIONS', false),

        // 会话数据清理间隔（小时）
        'cleanup_interval' => env('AUTH_SESSION_CLEANUP_INTERVAL', 24),
    ],

    /*
    |--------------------------------------------------------------------------
    | 登录限制设置
    |--------------------------------------------------------------------------
    |
    | 控制登录尝试的频率和限制
    |
    */

    'rate_limiting' => [
        // 最大登录尝试次数
        'max_attempts' => env('AUTH_MAX_LOGIN_ATTEMPTS', 5),

        // 锁定时间（秒）
        'lockout_duration' => env('AUTH_LOCKOUT_DURATION', 900), // 15分钟

        // 是否启用渐进式锁定（每次失败增加锁定时间）
        'progressive_lockout' => env('AUTH_PROGRESSIVE_LOCKOUT', false),

        // 渐进式锁定倍数
        'progressive_multiplier' => env('AUTH_PROGRESSIVE_MULTIPLIER', 2),

        // 是否启用IP级别的限制
        'ip_based_limiting' => env('AUTH_IP_BASED_LIMITING', true),

        // IP级别的最大尝试次数
        'ip_max_attempts' => env('AUTH_IP_MAX_ATTEMPTS', 20),
    ],

    /*
    |--------------------------------------------------------------------------
    | 密码策略设置
    |--------------------------------------------------------------------------
    |
    | 定义密码的复杂性要求和策略
    |
    */

    'password_policy' => [
        // 最小密码长度
        'min_length' => env('AUTH_PASSWORD_MIN_LENGTH', 8),

        // 最大密码长度
        'max_length' => env('AUTH_PASSWORD_MAX_LENGTH', 255),

        // 是否要求包含小写字母
        'require_lowercase' => env('AUTH_PASSWORD_REQUIRE_LOWERCASE', true),

        // 是否要求包含大写字母
        'require_uppercase' => env('AUTH_PASSWORD_REQUIRE_UPPERCASE', false),

        // 是否要求包含数字
        'require_numbers' => env('AUTH_PASSWORD_REQUIRE_NUMBERS', true),

        // 是否要求包含特殊字符
        'require_symbols' => env('AUTH_PASSWORD_REQUIRE_SYMBOLS', false),

        // 禁用的弱密码列表
        'forbidden_passwords' => [
            'password',
            '123456',
            'qwerty',
            'abc123',
            'password123',
            '12345678',
            'admin',
            'root',
        ],

        // 密码历史记录数量（防止重复使用旧密码）
        'history_count' => env('AUTH_PASSWORD_HISTORY_COUNT', 5),

        // 密码过期天数（0表示不过期）
        'expiry_days' => env('AUTH_PASSWORD_EXPIRY_DAYS', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | 双因子认证设置
    |--------------------------------------------------------------------------
    |
    | 双因子认证相关配置
    |
    */

    'two_factor' => [
        // 是否启用双因子认证
        'enabled' => env('AUTH_2FA_ENABLED', false),

        // 是否强制启用双因子认证
        'required' => env('AUTH_2FA_REQUIRED', false),

        // 支持的双因子认证方法
        'methods' => [
            'totp' => true,  // 基于时间的一次性密码
            'sms' => false,  // 短信验证码
            'email' => true, // 邮件验证码
        ],

        // TOTP设置
        'totp' => [
            'issuer' => env('APP_NAME', 'WorkHub Core'),
            'digits' => 6,
            'period' => 30,
            'algorithm' => 'sha1',
        ],

        // 备用恢复码数量
        'recovery_codes_count' => 8,
    ],

    /*
    |--------------------------------------------------------------------------
    | 邮箱验证设置
    |--------------------------------------------------------------------------
    |
    | 邮箱验证相关配置
    |
    */

    'email_verification' => [
        // 是否启用邮箱验证
        'enabled' => env('AUTH_EMAIL_VERIFICATION', true),

        // 是否要求邮箱验证后才能登录
        'required_for_login' => env('AUTH_EMAIL_VERIFICATION_REQUIRED', false),

        // 验证链接过期时间（分钟）
        'expiry_minutes' => env('AUTH_EMAIL_VERIFICATION_EXPIRY', 60),

        // 验证邮件发送间隔（分钟）
        'resend_interval' => env('AUTH_EMAIL_VERIFICATION_RESEND_INTERVAL', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | 记住我功能设置
    |--------------------------------------------------------------------------
    |
    | "记住我"功能相关配置
    |
    */

    'remember_me' => [
        // 是否启用记住我功能
        'enabled' => env('AUTH_REMEMBER_ME_ENABLED', true),

        // 记住我令牌过期时间（分钟）
        'expiry_minutes' => env('AUTH_REMEMBER_ME_EXPIRY', 525600), // 1年

        // 是否在新设备登录时使旧的记住我令牌失效
        'invalidate_on_new_device' => env('AUTH_REMEMBER_ME_INVALIDATE_NEW_DEVICE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | 日志记录设置
    |--------------------------------------------------------------------------
    |
    | 认证相关的日志记录配置
    |
    */

    'logging' => [
        // 是否启用认证日志
        'enabled' => env('AUTH_LOGGING_ENABLED', true),

        // 日志级别
        'level' => env('AUTH_LOGGING_LEVEL', 'info'),

        // 是否记录成功的登录
        'log_successful_logins' => env('AUTH_LOG_SUCCESSFUL_LOGINS', true),

        // 是否记录失败的登录尝试
        'log_failed_attempts' => env('AUTH_LOG_FAILED_ATTEMPTS', true),

        // 是否记录登出事件
        'log_logouts' => env('AUTH_LOG_LOGOUTS', true),

        // 是否记录用户注册
        'log_registrations' => env('AUTH_LOG_REGISTRATIONS', true),

        // 是否记录密码重置
        'log_password_resets' => env('AUTH_LOG_PASSWORD_RESETS', true),

        // 是否记录用户活动
        'log_user_activity' => env('AUTH_LOG_USER_ACTIVITY', false),

        // 日志保留天数
        'retention_days' => env('AUTH_LOG_RETENTION_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | API认证设置
    |--------------------------------------------------------------------------
    |
    | API认证相关配置
    |
    */

    'api' => [
        // 是否启用API认证
        'enabled' => env('AUTH_API_ENABLED', true),

        // API令牌过期时间（分钟）
        'token_expiry_minutes' => env('AUTH_API_TOKEN_EXPIRY', 60),

        // 是否启用API频率限制
        'rate_limiting' => env('AUTH_API_RATE_LIMITING', true),

        // API频率限制（每分钟请求数）
        'rate_limit_per_minute' => env('AUTH_API_RATE_LIMIT', 60),

        // 是否要求API密钥
        'require_api_key' => env('AUTH_API_REQUIRE_KEY', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | 通知设置
    |--------------------------------------------------------------------------
    |
    | 认证相关通知的配置
    |
    */

    'notifications' => [
        // 是否启用登录通知
        'login_notifications' => env('AUTH_LOGIN_NOTIFICATIONS', false),

        // 是否在新设备登录时发送通知
        'new_device_notifications' => env('AUTH_NEW_DEVICE_NOTIFICATIONS', true),

        // 是否在可疑活动时发送通知
        'suspicious_activity_notifications' => env('AUTH_SUSPICIOUS_ACTIVITY_NOTIFICATIONS', true),

        // 是否在密码更改时发送通知
        'password_change_notifications' => env('AUTH_PASSWORD_CHANGE_NOTIFICATIONS', true),

        // 通知渠道
        'channels' => [
            'email' => true,
            'sms' => false,
            'push' => false,
        ],
    ],
];
