<?php

/**
 * 短信服务配置文件
 *
 * 此配置文件包含了短信发送服务的所有配置选项
 * 包括多渠道支持、模板管理、频率限制等
 *
 * @package Config
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */

return [

    /*
    |--------------------------------------------------------------------------
    | 默认短信渠道
    |--------------------------------------------------------------------------
    |
    | 指定默认使用的短信发送渠道
    | 支持的渠道：aliyun, tencent, huawei, mock
    |
    */

    'default' => env('SMS_DEFAULT_CHANNEL', 'mock'),

    /*
    |--------------------------------------------------------------------------
    | 短信渠道配置
    |--------------------------------------------------------------------------
    |
    | 各个短信服务提供商的配置信息
    |
    */

    'channels' => [
        // 阿里云短信服务
        'aliyun' => [
            'access_key_id' => env('ALIYUN_SMS_ACCESS_KEY_ID'),
            'access_key_secret' => env('ALIYUN_SMS_ACCESS_KEY_SECRET'),
            'sign_name' => env('ALIYUN_SMS_SIGN_NAME', 'WorkHub'),
            'endpoint' => env('ALIYUN_SMS_ENDPOINT', 'https://dysmsapi.aliyuncs.com'),
            'region' => env('ALIYUN_SMS_REGION', 'cn-hangzhou'),
        ],

        // 腾讯云短信服务
        'tencent' => [
            'secret_id' => env('TENCENT_SMS_SECRET_ID'),
            'secret_key' => env('TENCENT_SMS_SECRET_KEY'),
            'sdk_app_id' => env('TENCENT_SMS_SDK_APP_ID'),
            'sign_name' => env('TENCENT_SMS_SIGN_NAME', 'WorkHub'),
            'region' => env('TENCENT_SMS_REGION', 'ap-beijing'),
        ],

        // 华为云短信服务
        'huawei' => [
            'app_key' => env('HUAWEI_SMS_APP_KEY'),
            'app_secret' => env('HUAWEI_SMS_APP_SECRET'),
            'sign_name' => env('HUAWEI_SMS_SIGN_NAME', 'WorkHub'),
            'sender' => env('HUAWEI_SMS_SENDER'),
            'endpoint' => env('HUAWEI_SMS_ENDPOINT', 'https://smsapi.cn-north-4.myhuaweicloud.com'),
        ],

        // 模拟短信（用于开发测试）
        'mock' => [
            'enabled' => env('SMS_MOCK_ENABLED', true),
            'log_codes' => env('SMS_MOCK_LOG_CODES', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 短信模板配置
    |--------------------------------------------------------------------------
    |
    | 各种类型短信的模板配置
    |
    */

    'templates' => [
        // 验证码短信模板
        'verification' => [
            'login' => [
                'template_code' => env('SMS_TEMPLATE_LOGIN', 'SMS_LOGIN_CODE'),
                'content' => '您的登录验证码是：{code}，{minutes}分钟内有效，请勿泄露。',
            ],
            'register' => [
                'template_code' => env('SMS_TEMPLATE_REGISTER', 'SMS_REGISTER_CODE'),
                'content' => '您的注册验证码是：{code}，{minutes}分钟内有效，请勿泄露。',
            ],
            'bind' => [
                'template_code' => env('SMS_TEMPLATE_BIND', 'SMS_BIND_CODE'),
                'content' => '您的绑定验证码是：{code}，{minutes}分钟内有效，请勿泄露。',
            ],
            'reset_password' => [
                'template_code' => env('SMS_TEMPLATE_RESET_PASSWORD', 'SMS_RESET_PASSWORD_CODE'),
                'content' => '您的密码重置验证码是：{code}，{minutes}分钟内有效，请勿泄露。',
            ],
            'default' => [
                'template_code' => env('SMS_TEMPLATE_DEFAULT', 'SMS_VERIFICATION_CODE'),
                'content' => '您的验证码是：{code}，{minutes}分钟内有效，请勿泄露。',
            ],
        ],

        // 通知短信模板
        'notification' => [
            'login_alert' => [
                'template_code' => env('SMS_TEMPLATE_LOGIN_ALERT', 'SMS_LOGIN_ALERT'),
                'content' => '您的账户在{time}从{location}登录，如非本人操作请及时修改密码。',
            ],
            'password_changed' => [
                'template_code' => env('SMS_TEMPLATE_PASSWORD_CHANGED', 'SMS_PASSWORD_CHANGED'),
                'content' => '您的账户密码已于{time}修改，如非本人操作请联系客服。',
            ],
            'account_locked' => [
                'template_code' => env('SMS_TEMPLATE_ACCOUNT_LOCKED', 'SMS_ACCOUNT_LOCKED'),
                'content' => '您的账户因安全原因被锁定，请联系客服解锁。',
            ],
            'default' => [
                'template_code' => env('SMS_TEMPLATE_NOTIFICATION_DEFAULT', 'SMS_NOTIFICATION'),
                'content' => '系统通知：{message}',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 验证码配置
    |--------------------------------------------------------------------------
    |
    | 短信验证码相关的配置选项
    |
    */

    'code_length' => env('SMS_CODE_LENGTH', 6),
    'code_expires_minutes' => env('SMS_CODE_EXPIRES_MINUTES', 5),
    'resend_interval_seconds' => env('SMS_RESEND_INTERVAL_SECONDS', 60),
    'max_verify_attempts' => env('SMS_MAX_VERIFY_ATTEMPTS', 5),

    /*
    |--------------------------------------------------------------------------
    | 频率限制配置
    |--------------------------------------------------------------------------
    |
    | 短信发送的频率限制设置
    |
    */

    'max_attempts_per_hour' => env('SMS_MAX_ATTEMPTS_PER_HOUR', 10),
    'max_attempts_per_day' => env('SMS_MAX_ATTEMPTS_PER_DAY', 50),
    'rate_limit_decay_minutes' => env('SMS_RATE_LIMIT_DECAY_MINUTES', 60),
    'max_code_attempts_per_minute' => env('SMS_MAX_CODE_ATTEMPTS_PER_MINUTE', 5),

    /*
    |--------------------------------------------------------------------------
    | 安全配置
    |--------------------------------------------------------------------------
    |
    | 短信安全相关的配置选项
    |
    */

    'security' => [
        // 是否启用IP白名单
        'ip_whitelist_enabled' => env('SMS_IP_WHITELIST_ENABLED', false),

        // IP白名单
        'ip_whitelist' => [
            '127.0.0.1',
            '::1',
        ],

        // 是否启用手机号黑名单
        'mobile_blacklist_enabled' => env('SMS_MOBILE_BLACKLIST_ENABLED', true),

        // 手机号黑名单
        'mobile_blacklist' => [
            // 可以添加需要屏蔽的手机号
        ],

        // 是否启用内容过滤
        'content_filter_enabled' => env('SMS_CONTENT_FILTER_ENABLED', true),

        // 敏感词列表
        'sensitive_words' => [
            // 可以添加敏感词
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 日志配置
    |--------------------------------------------------------------------------
    |
    | 短信发送日志相关配置
    |
    */

    'logging' => [
        // 是否启用短信日志
        'enabled' => env('SMS_LOGGING_ENABLED', true),

        // 日志级别
        'level' => env('SMS_LOGGING_LEVEL', 'info'),

        // 是否记录发送成功的短信
        'log_successful_sends' => env('SMS_LOG_SUCCESSFUL_SENDS', true),

        // 是否记录发送失败的短信
        'log_failed_sends' => env('SMS_LOG_FAILED_SENDS', true),

        // 是否记录验证码验证
        'log_code_verifications' => env('SMS_LOG_CODE_VERIFICATIONS', true),

        // 日志保留天数
        'retention_days' => env('SMS_LOG_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | 队列配置
    |--------------------------------------------------------------------------
    |
    | 短信发送队列相关配置
    |
    */

    'queue' => [
        // 是否启用队列发送
        'enabled' => env('SMS_QUEUE_ENABLED', false),

        // 队列名称
        'name' => env('SMS_QUEUE_NAME', 'sms'),

        // 队列连接
        'connection' => env('SMS_QUEUE_CONNECTION', 'default'),

        // 最大重试次数
        'max_tries' => env('SMS_QUEUE_MAX_TRIES', 3),

        // 重试延迟（秒）
        'retry_delay' => env('SMS_QUEUE_RETRY_DELAY', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | 缓存配置
    |--------------------------------------------------------------------------
    |
    | 短信相关缓存配置
    |
    */

    'cache' => [
        // 缓存前缀
        'prefix' => env('SMS_CACHE_PREFIX', 'sms'),

        // 缓存驱动
        'store' => env('SMS_CACHE_STORE', 'default'),

        // 验证码缓存TTL（分钟）
        'code_ttl' => env('SMS_CACHE_CODE_TTL', 10),

        // 频率限制缓存TTL（分钟）
        'rate_limit_ttl' => env('SMS_CACHE_RATE_LIMIT_TTL', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | 监控配置
    |--------------------------------------------------------------------------
    |
    | 短信发送监控相关配置
    |
    */

    'monitoring' => [
        // 是否启用监控
        'enabled' => env('SMS_MONITORING_ENABLED', false),

        // 监控指标
        'metrics' => [
            'send_count' => true,
            'success_rate' => true,
            'response_time' => true,
            'error_rate' => true,
        ],

        // 告警阈值
        'alerts' => [
            'error_rate_threshold' => env('SMS_ALERT_ERROR_RATE_THRESHOLD', 0.1), // 10%
            'response_time_threshold' => env('SMS_ALERT_RESPONSE_TIME_THRESHOLD', 5000), // 5秒
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 开发配置
    |--------------------------------------------------------------------------
    |
    | 开发环境相关配置
    |
    */

    'development' => [
        // 开发模式下是否实际发送短信
        'actually_send' => env('SMS_ACTUALLY_SEND', false),

        // 开发模式下的测试手机号
        'test_mobiles' => [
            '13800138000',
            '13800138001',
        ],

        // 开发模式下的固定验证码
        'fixed_code' => env('SMS_FIXED_CODE', null),

        // 是否在日志中显示验证码
        'log_codes_in_dev' => env('SMS_LOG_CODES_IN_DEV', true),
    ],

];
