// 批量创建剩余页面的脚本
// 这个脚本用于快速生成剩余的管理页面

const fs = require('fs');
const path = require('path');

// 页面配置
const pages = [
    // 计划管理模块
    { path: 'plan/contract', name: 'AdminPlanContractIndexPage', title: '合同管理', description: '管理合同信息和状态' },
    { path: 'plan/serve', name: 'AdminPlanServeIndexPage', title: '服务管理', description: '管理服务项目和质量' },
    { path: 'plan/functions', name: 'AdminPlanFunctionsIndexPage', title: '功能管理', description: '管理系统功能和权限' },
    
    // 供应商管理模块
    { path: 'provider/provider', name: 'AdminProviderProviderIndexPage', title: '供应商管理', description: '管理供应商信息和合作' },
    { path: 'provider/demo', name: 'AdminProviderDemoIndexPage', title: '供应商演示', description: '管理供应商演示和展示' },
    { path: 'provider/shipment', name: 'AdminProviderShipmentIndexPage', title: '发货管理', description: '管理发货流程和状态' },
    { path: 'supplier/supplier', name: 'AdminSupplierSupplierIndexPage', title: '供货商管理', description: '管理供货商信息和产品' },
    { path: 'supplier/demo', name: 'AdminSupplierDemoIndexPage', title: '供货商演示', description: '管理供货商演示和样品' },
    
    // 产品管理模块
    { path: 'product/draft', name: 'AdminProductDraftIndexPage', title: '产品草稿', description: '管理产品草稿和待发布产品' },
    { path: 'product/sku', name: 'AdminProductSkuIndexPage', title: 'SKU管理', description: '管理产品SKU和库存' },
    
    // 生产管理模块
    { path: 'produce/schema', name: 'AdminProduceSchemaIndexPage', title: '生产方案', description: '管理生产方案和流程' },
    { path: 'produce/group', name: 'AdminProduceGroupIndexPage', title: '生产分组', description: '管理生产分组和团队' },
    { path: 'produce/spec', name: 'AdminProduceSpecIndexPage', title: '生产规格', description: '管理生产规格和标准' },
    { path: 'produce/suit', name: 'AdminProduceSuitIndexPage', title: '生产套装', description: '管理生产套装和组合' },
    { path: 'produce/custom', name: 'AdminProduceCustomIndexPage', title: '生产定制', description: '管理定制生产和个性化' },
    { path: 'produce/param', name: 'AdminProduceParamIndexPage', title: '生产参数', description: '管理生产参数和配置' },
    { path: 'produce/fabric', name: 'AdminProduceFabricIndexPage', title: '面料管理', description: '管理面料信息和库存' },
    { path: 'produce/design', name: 'AdminProduceDesignIndexPage', title: '设计管理', description: '管理设计方案和创意' },
    { path: 'produce/trend', name: 'AdminProduceTrendIndexPage', title: '趋势管理', description: '管理流行趋势和预测' },
    { path: 'produce/craft', name: 'AdminProduceCraftIndexPage', title: '工艺管理', description: '管理生产工艺和技术' },
    { path: 'produce/shade', name: 'AdminProduceShadeIndexPage', title: '色调管理', description: '管理色彩搭配和色调' },
    { path: 'produce/purpose', name: 'AdminProducePurposeIndexPage', title: '用途管理', description: '管理产品用途和分类' },
    { path: 'produce/accessory', name: 'AdminProduceAccessoryIndexPage', title: '配件管理', description: '管理生产配件和辅料' },
    { path: 'produce/level', name: 'AdminProduceLevelIndexPage', title: '等级管理', description: '管理产品等级和质量' },
    { path: 'produce/tag', name: 'AdminProduceTagIndexPage', title: '生产标签', description: '管理生产标签和标识' },
    { path: 'produce/peg', name: 'AdminProducePegIndexPage', title: '挂钩管理', description: '管理挂钩和连接件' },
    { path: 'produce/property', name: 'AdminProducePropertyIndexPage', title: '生产属性', description: '管理生产属性和特性' },
    
    // 版权管理模块
    { path: 'copyright/copyright', name: 'AdminCopyrightCopyrightIndexPage', title: '版权管理', description: '管理版权信息和授权' },
    { path: 'copyright/copyright_agent', name: 'AdminCopyrightAgentIndexPage', title: '版权代理', description: '管理版权代理和服务' },
    { path: 'copyright/copyright_holder', name: 'AdminCopyrightHolderIndexPage', title: '版权持有人', description: '管理版权持有人信息' },
    { path: 'copyright/copyright_relate', name: 'AdminCopyrightRelateIndexPage', title: '版权关联', description: '管理版权关联和关系' },
    
    // 倡导者管理模块
    { path: 'advocate/advocate', name: 'AdminAdvocateAdvocateIndexPage', title: '倡导者管理', description: '管理倡导者信息和活动' },
    { path: 'advocate/permission', name: 'AdminAdvocatePermissionIndexPage', title: '倡导者权限', description: '管理倡导者权限和访问' },
    { path: 'advocate/role', name: 'AdminAdvocateRoleIndexPage', title: '倡导者角色', description: '管理倡导者角色和职责' },
    { path: 'advocate/assignment', name: 'AdminAdvocateAssignmentIndexPage', title: '倡导者分配', description: '管理倡导者分配和任务' },
    
    // 其他系统工具模块
    { path: 'duty/duty', name: 'AdminDutyDutyIndexPage', title: '职责管理', description: '管理职责分工和责任' },
    { path: 'think/think', name: 'AdminThinkThinkIndexPage', title: '思考管理', description: '管理思考记录和创意' }
];

// 生成页面模板
function generatePageTemplate(config) {
    return `<!--
/**
 * ${config.title}页面
 * 
 * 功能特性：
 * - ${config.description}
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * 
 * 路由路径：/admin/${config.path}
 * 页面标题：${config.title}
 * 
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="admin-${config.path.replace('/', '-')}-page">
        <BackendPageListLayout
            :loading="loading"
            :refreshing="refreshing"
            :show-empty-state="showEmptyState"
            empty-description="当前没有任何数据，您可以刷新页面或创建新的内容"
            empty-title="暂无数据"
            @create="handleCreate"
            @refresh="handleRefresh"
        >
            <div class="content-area">
                <el-card class="data-card" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>${config.title}</span>
                            <el-button size="small" type="primary" @click="handleCreate">
                                <i class="fas fa-plus"></i>
                                新增
                            </el-button>
                        </div>
                    </template>
                    
                    <!-- 搜索区域 -->
                    <div class="search-section">
                        <el-form 
                            :model="searchForm" 
                            :inline="true" 
                            class="search-form"
                            @submit.prevent="handleSearch"
                        >
                            <el-form-item label="关键词">
                                <el-input
                                    v-model="searchForm.keyword"
                                    placeholder="请输入关键词"
                                    clearable
                                    @keyup.enter="handleSearch"
                                    style="width: 200px"
                                />
                            </el-form-item>
                            <el-form-item label="状态">
                                <el-select 
                                    v-model="searchForm.status" 
                                    placeholder="请选择状态"
                                    clearable
                                    style="width: 120px"
                                >
                                    <el-option label="启用" value="1" />
                                    <el-option label="禁用" value="0" />
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button 
                                    type="primary" 
                                    @click="handleSearch"
                                    :loading="loading"
                                >
                                    搜索
                                </el-button>
                                <el-button @click="handleReset">
                                    重置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 表格工具栏 -->
                    <div class="table-toolbar">
                        <div class="toolbar-left">
                            <el-button 
                                type="warning" 
                                size="small"
                                @click="handleBatchDisable"
                                :disabled="!selectedRows.length"
                            >
                                <i class="fas fa-ban"></i>
                                批量禁用
                            </el-button>
                            <el-button 
                                type="info" 
                                size="small"
                                @click="handleExport"
                            >
                                <i class="fas fa-download"></i>
                                导出数据
                            </el-button>
                        </div>
                        <div class="toolbar-right">
                            <el-tooltip content="刷新数据" placement="top">
                                <el-button 
                                    size="small"
                                    circle 
                                    @click="handleRefresh"
                                >
                                    <i class="fas fa-sync-alt"></i>
                                </el-button>
                            </el-tooltip>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <el-table
                        :data="tableData"
                        :loading="loading"
                        @selection-change="handleSelectionChange"
                        stripe
                        border
                        class="admin-table"
                    >
                        <el-table-column type="selection" width="55" />
                        <el-table-column prop="id" label="ID" width="80" sortable />
                        <el-table-column 
                            prop="name" 
                            label="名称" 
                            min-width="200"
                            show-overflow-tooltip
                        />
                        <el-table-column 
                            prop="description" 
                            label="描述" 
                            min-width="200"
                            show-overflow-tooltip
                        />
                        <el-table-column 
                            prop="status" 
                            label="状态" 
                            width="100"
                            align="center"
                        >
                            <template #default="{ row }">
                                <el-tag 
                                    :type="row.status === 1 ? 'success' : 'danger'"
                                    size="small"
                                >
                                    {{ row.status === 1 ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="created_at" 
                            label="创建时间" 
                            width="180"
                            sortable
                        />
                        <el-table-column 
                            label="操作" 
                            width="250" 
                            fixed="right"
                            align="center"
                        >
                            <template #default="{ row }">
                                <el-button 
                                    type="primary" 
                                    size="small" 
                                    @click="handleView(row)"
                                >
                                    查看
                                </el-button>
                                <el-button 
                                    type="success" 
                                    size="small" 
                                    @click="handleEdit(row)"
                                >
                                    编辑
                                </el-button>
                                <el-button 
                                    :type="row.status === 1 ? 'warning' : 'info'"
                                    size="small" 
                                    @click="handleToggleStatus(row)"
                                >
                                    {{ row.status === 1 ? '禁用' : '启用' }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="pagination-wrapper">
                        <el-pagination
                            v-model:current-page="pagination.currentPage"
                            v-model:page-size="pagination.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="pagination.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </el-card>
            </div>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'

export default {
    name: '${config.name}',
    components: {
        BackendPageListLayout
    },
    data() {
        return {
            loading: false,
            refreshing: false,
            showEmptyState: false,
            tableData: [],
            selectedRows: [],
            searchForm: {
                keyword: '',
                status: ''
            },
            pagination: {
                currentPage: 1,
                pageSize: 20,
                total: 0
            }
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.loading = true
                
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))
                
                // 模拟数据
                const mockData = [
                    {
                        id: 1,
                        name: '示例数据1',
                        description: '这是一个示例数据描述',
                        status: 1,
                        created_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '示例数据2',
                        description: '这是另一个示例数据描述',
                        status: 0,
                        created_at: '2024-01-14 16:20:00'
                    }
                ]
                
                this.tableData = mockData
                this.pagination.total = 50 // 模拟总数
                this.showEmptyState = this.tableData.length === 0
                
            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.loading = false
            }
        },

        // 刷新数据
        async handleRefresh() {
            try {
                this.refreshing = true
                await this.loadData()
                this.$message.success('刷新成功')
            } catch (error) {
                console.error('刷新失败:', error)
                this.$message.error('刷新失败，请重试')
            } finally {
                this.refreshing = false
            }
        },

        // 创建
        handleCreate() {
            this.$message.info('新增功能待实现')
        },

        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1
            this.loadData()
        },

        // 重置搜索
        handleReset() {
            this.searchForm.keyword = ''
            this.searchForm.status = ''
            this.handleSearch()
        },

        // 查看详情
        handleView(row) {
            this.$message.info(\`查看详情: \${row.name}\`)
        },

        // 编辑
        handleEdit(row) {
            this.$message.info(\`编辑: \${row.name}\`)
        },

        // 切换状态
        async handleToggleStatus(row) {
            const action = row.status === 1 ? '禁用' : '启用'
            try {
                await this.$confirm(
                    \`确定要\${action} "\${row.name}" 吗？\`,
                    \`确认\${action}\`,
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                this.$message.success(\`\${action}成功\`)
                this.loadData()
            } catch {
                this.$message.info(\`已取消\${action}\`)
            }
        },

        // 批量禁用
        async handleBatchDisable() {
            if (!this.selectedRows.length) {
                this.$message.warning('请选择要禁用的项目')
                return
            }

            try {
                await this.$confirm(
                    \`确定要禁用选中的 \${this.selectedRows.length} 个项目吗？\`,
                    '确认批量禁用',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )
                
                this.$message.success('批量禁用成功')
                this.selectedRows = []
                this.loadData()
            } catch {
                this.$message.info('已取消禁用')
            }
        },

        // 导出数据
        handleExport() {
            this.$message.info('导出数据功能待实现')
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        // 分页大小变化
        handleSizeChange(size) {
            this.pagination.pageSize = size
            this.loadData()
        },

        // 当前页变化
        handleCurrentChange(page) {
            this.pagination.currentPage = page
            this.loadData()
        }
    }
}
</script>

<style lang="scss" scoped>
.admin-${config.path.replace('/', '-')}-page {
    .content-area {
        .data-card {
            border-radius: 8px;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                color: #1f2937;
            }

            .search-section {
                margin-bottom: 16px;
                padding: 16px;
                background-color: #f8f9fa;
                border-radius: 6px;

                .search-form {
                    margin-bottom: 0;
                }
            }

            .table-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 16px;
                border-bottom: 1px solid #ebeef5;

                .toolbar-left,
                .toolbar-right {
                    display: flex;
                    gap: 8px;
                }
            }

            .admin-table {
                margin-bottom: 20px;
            }

            .pagination-wrapper {
                display: flex;
                justify-content: flex-end;
                padding-top: 16px;
                border-top: 1px solid #ebeef5;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .admin-${config.path.replace('/', '-')}-page {
        .search-form {
            .el-form-item {
                width: 100%;
                margin-right: 0;
            }
        }

        .table-toolbar {
            flex-direction: column;
            gap: 12px;
            align-items: stretch !important;

            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
        }

        .pagination-wrapper {
            justify-content: center;
        }
    }
}
</style>`;
}

// 创建目录结构
function ensureDirectoryExists(filePath) {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
}

// 批量生成页面
function generatePages() {
    const baseDir = 'resources/js/admin/pages';
    
    pages.forEach(config => {
        const filePath = path.join(baseDir, config.path, 'index.vue');
        const content = generatePageTemplate(config);
        
        ensureDirectoryExists(filePath);
        fs.writeFileSync(filePath, content, 'utf8');
        
        console.log(`✅ 已创建: ${filePath}`);
    });
    
    console.log(`\n🎉 成功创建 ${pages.length} 个页面文件！`);
}

// 如果直接运行此脚本
if (require.main === module) {
    generatePages();
}

module.exports = { generatePages, generatePageTemplate };
