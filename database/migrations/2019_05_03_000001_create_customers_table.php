<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
	/**
	 * Run the migrations.
	 */
	public function up(): void {
		Schema::create('customers', function (Blueprint $table) {
			$table->id();
			$table->morphs('billable');
			$table->string('paddle_id')->unique();
			$table->string('name');
			$table->string('email');
			$table->timestamp('trial_ends_at')->nullable();
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 */
	public function down(): void {
		Schema::dropIfExists('customers');
	}
};
