# Backend Layout 刷新功能使用示例

## 版本信息
- **版本**: V1.0
- **日期**: 2025-01-22
- **文件**: 刷新功能使用示例和最佳实践

## 基本使用方法

### 1. 通过刷新按钮（推荐）

用户可以直接点击页面右上角的刷新按钮来刷新当前页面内容：

```vue
<!-- 刷新按钮已内置在 BackendMainLayout 中 -->
<el-tooltip content="刷新页面" placement="bottom">
    <el-button :loading="isRefreshing" circle class="refresh-btn" size="small"
               @click="refreshPage">
        <el-icon>
            <Refresh/>
        </el-icon>
    </el-button>
</el-tooltip>
```

### 2. 通过全局方法调用

#### 快速内容刷新
```javascript
// 快速刷新内容区域，不显示加载状态
window.BackendLayout.refreshContent()
```

#### 完整页面刷新
```javascript
// 完整的页面刷新，包含加载状态和路由更新
window.BackendLayout.refreshPage()
```

### 3. 在 Vue 组件中调用

#### 方法一：通过父组件引用
```vue
<template>
    <div class="my-component">
        <el-button @click="handleRefresh">刷新页面</el-button>
    </div>
</template>

<script>
export default {
    methods: {
        handleRefresh() {
            // 调用父组件的刷新方法
            this.$parent.refreshPage()
        }
    }
}
</script>
```

#### 方法二：通过事件总线
```javascript
// 在组件中触发刷新事件
this.$emit('refresh-content')

// 或者使用全局事件
window.dispatchEvent(new CustomEvent('backend-refresh'))
```

#### 方法三：通过 Pinia Store
```javascript
// 在 store 中定义刷新方法
import { defineStore } from 'pinia'

export const usePageStore = defineStore('page', {
    actions: {
        refreshCurrentPage() {
            if (window.BackendLayout) {
                window.BackendLayout.refreshPage()
            }
        }
    }
})

// 在组件中使用
const pageStore = usePageStore()
pageStore.refreshCurrentPage()
```

## 高级使用场景

### 1. 条件刷新

```javascript
// 检查是否需要刷新
if (window.BackendLayout && typeof window.BackendLayout.shouldAutoRefresh === 'function') {
    const shouldRefresh = window.BackendLayout.shouldAutoRefresh()
    if (shouldRefresh) {
        window.BackendLayout.refreshPage()
    }
}
```

### 2. 批量操作后刷新

```vue
<script>
export default {
    methods: {
        async handleBatchDelete() {
            try {
                // 执行批量删除操作
                await this.deleteBatchItems()
                
                // 操作成功后刷新页面
                this.$nextTick(() => {
                    window.BackendLayout.refreshContent()
                })
                
            } catch (error) {
                console.error('批量删除失败:', error)
            }
        }
    }
}
</script>
```

### 3. 定时自动刷新

```javascript
// 设置定时刷新（每5分钟）
setInterval(() => {
    if (window.BackendLayout) {
        window.BackendLayout.refreshContent()
    }
}, 5 * 60 * 1000)
```

### 4. 网络重连后刷新

```javascript
// 监听网络状态变化
window.addEventListener('online', () => {
    console.log('网络已恢复，刷新页面内容')
    if (window.BackendLayout) {
        window.BackendLayout.refreshPage()
    }
})
```

## 自定义刷新组件

### 创建自定义刷新按钮

```vue
<template>
    <div class="custom-refresh">
        <el-button 
            :loading="isRefreshing" 
            :disabled="!canRefresh"
            type="primary" 
            @click="handleRefresh"
        >
            <el-icon v-if="!isRefreshing">
                <Refresh />
            </el-icon>
            {{ isRefreshing ? '刷新中...' : '刷新数据' }}
        </el-button>
        
        <span v-if="lastRefreshTime" class="refresh-time">
            上次刷新: {{ lastRefreshTime }}
        </span>
    </div>
</template>

<script>
export default {
    name: 'CustomRefreshButton',
    data() {
        return {
            isRefreshing: false,
            lastRefreshTime: null,
            canRefresh: true
        }
    },
    methods: {
        async handleRefresh() {
            if (!this.canRefresh || this.isRefreshing) return
            
            this.isRefreshing = true
            this.canRefresh = false
            
            try {
                // 调用全局刷新方法
                if (window.BackendLayout) {
                    await window.BackendLayout.refreshPage()
                }
                
                // 更新刷新时间
                this.lastRefreshTime = new Date().toLocaleTimeString()
                
                // 触发自定义事件
                this.$emit('refresh-completed')
                
            } catch (error) {
                console.error('刷新失败:', error)
                this.$emit('refresh-failed', error)
            } finally {
                this.isRefreshing = false
                
                // 防止频繁刷新，设置冷却时间
                setTimeout(() => {
                    this.canRefresh = true
                }, 2000)
            }
        }
    }
}
</script>

<style scoped>
.custom-refresh {
    display: flex;
    align-items: center;
    gap: 12px;
}

.refresh-time {
    font-size: 12px;
    color: #909399;
}
</style>
```

### 创建刷新状态指示器

```vue
<template>
    <div class="refresh-indicator" :class="{ active: isRefreshing }">
        <div class="indicator-dot"></div>
        <span class="indicator-text">{{ statusText }}</span>
    </div>
</template>

<script>
export default {
    name: 'RefreshIndicator',
    data() {
        return {
            isRefreshing: false,
            statusText: '就绪'
        }
    },
    mounted() {
        // 监听全局刷新状态
        this.watchRefreshStatus()
    },
    methods: {
        watchRefreshStatus() {
            // 监听刷新开始事件
            window.addEventListener('backend-refresh-start', () => {
                this.isRefreshing = true
                this.statusText = '刷新中...'
            })
            
            // 监听刷新完成事件
            window.addEventListener('backend-refresh-end', () => {
                this.isRefreshing = false
                this.statusText = '已更新'
                
                // 3秒后重置状态
                setTimeout(() => {
                    this.statusText = '就绪'
                }, 3000)
            })
        }
    }
}
</script>

<style scoped>
.refresh-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #909399;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #67c23a;
    transition: all 0.3s ease;
}

.refresh-indicator.active .indicator-dot {
    background-color: #409eff;
    animation: pulse 1.5s infinite;
}

.indicator-text {
    transition: color 0.3s ease;
}

.refresh-indicator.active .indicator-text {
    color: #409eff;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}
</style>
```

## 最佳实践

### 1. 错误处理

```javascript
async function safeRefresh() {
    try {
        if (!window.BackendLayout) {
            throw new Error('BackendLayout 未初始化')
        }
        
        await window.BackendLayout.refreshPage()
        
    } catch (error) {
        console.error('刷新失败:', error)
        
        // 显示用户友好的错误信息
        ElMessage.error('页面刷新失败，请稍后重试')
        
        // 可选：回退到页面重载
        if (confirm('是否重新加载整个页面？')) {
            window.location.reload()
        }
    }
}
```

### 2. 防抖处理

```javascript
import { debounce } from 'lodash-es'

// 创建防抖刷新函数
const debouncedRefresh = debounce(() => {
    if (window.BackendLayout) {
        window.BackendLayout.refreshContent()
    }
}, 1000)

// 使用防抖刷新
debouncedRefresh()
```

### 3. 状态管理

```javascript
// 在 Pinia store 中管理刷新状态
export const useRefreshStore = defineStore('refresh', {
    state: () => ({
        isRefreshing: false,
        lastRefreshTime: null,
        refreshCount: 0
    }),
    
    actions: {
        async performRefresh() {
            if (this.isRefreshing) return
            
            this.isRefreshing = true
            
            try {
                await window.BackendLayout.refreshPage()
                this.lastRefreshTime = Date.now()
                this.refreshCount++
            } finally {
                this.isRefreshing = false
            }
        }
    }
})
```

## 注意事项

1. **组件状态丢失**: 刷新会完全重新渲染组件，组件内部状态会丢失
2. **网络请求**: 确保在网络请求完成后再进行刷新操作
3. **用户体验**: 避免频繁刷新，建议设置合理的冷却时间
4. **错误处理**: 始终包含适当的错误处理逻辑
5. **性能考虑**: 大量数据的页面刷新可能影响性能

## 兼容性说明

- ✅ 支持所有现代浏览器
- ✅ 兼容 Vue 3 + Composition API
- ✅ 支持 TypeScript（需要类型声明）
- ✅ 移动端友好

## 故障排除

### 常见问题

1. **刷新按钮无响应**
   - 检查 `window.BackendLayout` 是否存在
   - 确认组件已正确挂载

2. **刷新后数据未更新**
   - 检查组件的 `key` 属性是否正确更新
   - 确认数据请求逻辑在组件重新挂载时执行

3. **刷新状态卡住**
   - 检查网络连接
   - 查看浏览器控制台错误信息

### 调试方法

```javascript
// 开启调试模式
window.BackendLayoutDebug = true

// 监听刷新事件
window.addEventListener('backend-refresh-start', (e) => {
    console.log('刷新开始:', e.detail)
})

window.addEventListener('backend-refresh-end', (e) => {
    console.log('刷新完成:', e.detail)
})
```
