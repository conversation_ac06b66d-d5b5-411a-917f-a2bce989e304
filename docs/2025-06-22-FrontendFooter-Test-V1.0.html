<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FrontendFooter.vue 组件测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #5247ef;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #303133;
            border-bottom: 2px solid #5247ef;
            padding-bottom: 10px;
        }
        .config-example {
            background: #f8f9fb;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #5247ef;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-item {
            background: #f1f3f6;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #04c717;
        }
        .feature-item h4 {
            margin: 0 0 8px 0;
            color: #303133;
        }
        .feature-item p {
            margin: 0;
            color: #606266;
            font-size: 14px;
        }
        .responsive-demo {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .device-frame {
            border: 2px solid #e4e7ed;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            background: white;
        }
        .device-frame h4 {
            margin: 0 0 10px 0;
            color: #5247ef;
        }
        .device-screen {
            background: #f8f9fb;
            height: 120px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
            font-size: 12px;
        }
        .usage-example {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 20px;
            margin-top: 15px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin-top: 10px;
        }
        .test-checklist {
            list-style: none;
            padding: 0;
        }
        .test-checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
            padding-left: 25px;
        }
        .test-checklist li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #04c717;
            font-weight: bold;
        }
        .integration-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .integration-note h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        @media (max-width: 768px) {
            .responsive-demo {
                grid-template-columns: 1fr;
            }
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 FrontendFooter.vue 企业级页脚组件</h1>
            <p>企业级电商供应链系统 - 前台页脚组件测试文档</p>
        </div>

        <div class="test-content">
            <!-- 组件概述 -->
            <div class="test-section">
                <h3>📋 组件概述</h3>
                <p>FrontendFooter.vue 是一个功能完整的企业级页脚组件，参考 FrontendHeader.vue 的设计模式，提供响应式布局、完整的配置选项和无障碍访问支持。</p>
                
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>🎨 企业级设计</h4>
                        <p>与 FrontendHeader.vue 保持一致的设计语言，使用全局设计系统</p>
                    </div>
                    <div class="feature-item">
                        <h4>📱 响应式布局</h4>
                        <p>桌面端网格布局，移动端手风琴式折叠设计</p>
                    </div>
                    <div class="feature-item">
                        <h4>⚙️ 高度可配置</h4>
                        <p>通过 props 传递配置，支持灵活的内容定制</p>
                    </div>
                    <div class="feature-item">
                        <h4>♿ 无障碍访问</h4>
                        <p>完整的 ARIA 支持，键盘导航友好</p>
                    </div>
                </div>
            </div>

            <!-- 响应式设计演示 -->
            <div class="test-section">
                <h3>📱 响应式设计</h3>
                <p>组件在不同设备上的布局适配：</p>
                
                <div class="responsive-demo">
                    <div class="device-frame">
                        <h4>🖥️ 桌面端 (1200px+)</h4>
                        <div class="device-screen">
                            4列网格布局<br>
                            品牌 | 快速链接 | 支持服务 | 联系我们
                        </div>
                    </div>
                    <div class="device-frame">
                        <h4>📱 平板端 (768px-1199px)</h4>
                        <div class="device-screen">
                            2列网格布局<br>
                            品牌 | 快速链接<br>
                            支持服务 | 联系我们
                        </div>
                    </div>
                    <div class="device-frame">
                        <h4>📱 移动端 (768px以下)</h4>
                        <div class="device-screen">
                            手风琴式折叠<br>
                            ▼ 快速链接<br>
                            ▼ 支持服务<br>
                            ▼ 联系我们
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置示例 -->
            <div class="test-section">
                <h3>⚙️ 配置示例</h3>
                <p>footerConfig 配置对象的完整结构：</p>
                
                <div class="config-example">
const footerConfig = {
  // 基础信息
  company: 'WorkHub 企业级供应链系统',
  subtitle: '现代化电商供应链管理平台',
  description: '为企业提供完整的供应链解决方案，提升运营效率，优化业务流程。',
  
  // Logo配置
  logo: { icon: 'fas fa-cube' },
  
  // 区域标题
  quickLinksTitle: '产品与服务',
  supportTitle: '帮助与支持',
  contactTitle: '联系我们',
  
  // 联系信息
  contact: {
    address: '北京市朝阳区xxx大厦xxx室',
    phone: '************',
    email: '<EMAIL>',
    workingHours: '工作时间：周一至周五 9:00-18:00'
  },
  
  // 社交媒体
  socialLinks: [
    { url: 'https://github.com/company', icon: 'fab fa-github', label: 'GitHub' },
    { url: 'https://weibo.com/company', icon: 'fab fa-weibo', label: '微博' }
  ],
  
  // 版权信息
  copyrightText: '保留所有权利',
  icp: '京ICP备12345678号',
  version: '2.1.0',
  showVersion: true
};
                </div>
            </div>

            <!-- 使用示例 -->
            <div class="test-section">
                <h3>🔧 使用示例</h3>
                
                <div class="usage-example">
                    <h4>在 Vue 组件中使用：</h4>
                    <div class="code-block">
&lt;template&gt;
  &lt;div class="page-layout"&gt;
    &lt;!-- 页面内容 --&gt;
    &lt;main&gt;
      &lt;!-- 主要内容区域 --&gt;
    &lt;/main&gt;
    
    &lt;!-- 页脚组件 --&gt;
    &lt;FrontendFooter :footer-config="footerConfig" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
import FrontendFooter from '@/layouts/FrontendLayout/components/FrontendFooter.vue'

export default {
  components: { FrontendFooter },
  data() {
    return {
      footerConfig: {
        company: '我的公司',
        description: '公司描述信息...',
        // 更多配置选项...
      }
    }
  }
}
&lt;/script&gt;
                    </div>
                </div>

                <div class="integration-note">
                    <h4>💡 集成提示</h4>
                    <p>组件已完美集成到 FrontendMainLayout.vue 中，可以通过 footerConfig prop 传递配置。建议在应用的全局配置中定义页脚配置，确保整个应用的一致性。</p>
                </div>
            </div>

            <!-- 测试清单 -->
            <div class="test-section">
                <h3>✅ 测试清单</h3>
                <p>建议进行以下测试以确保组件正常工作：</p>
                
                <ul class="test-checklist">
                    <li>验证桌面端4列网格布局显示正确</li>
                    <li>验证平板端2列网格布局适配正常</li>
                    <li>验证移动端手风琴式折叠功能</li>
                    <li>测试所有内部链接的路由跳转</li>
                    <li>测试所有外部链接的新窗口打开</li>
                    <li>验证电话号码和邮箱的可点击性</li>
                    <li>测试社交媒体链接的正确跳转</li>
                    <li>验证版权信息的年份自动更新</li>
                    <li>测试键盘导航的完整性</li>
                    <li>验证屏幕阅读器的兼容性</li>
                    <li>测试高对比度模式下的显示效果</li>
                    <li>验证打印样式的优化效果</li>
                </ul>
            </div>

            <!-- 技术规格 -->
            <div class="test-section">
                <h3>🔧 技术规格</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>Vue 版本</h4>
                        <p>Vue 3 Composition API + Options API</p>
                    </div>
                    <div class="feature-item">
                        <h4>UI 框架</h4>
                        <p>Element Plus (Collapse 组件)</p>
                    </div>
                    <div class="feature-item">
                        <h4>样式系统</h4>
                        <p>SCSS + 企业级设计系统变量</p>
                    </div>
                    <div class="feature-item">
                        <h4>响应式</h4>
                        <p>CSS Grid + Flexbox + 媒体查询</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互演示
        console.log('🎯 FrontendFooter.vue 测试页面已加载');
        console.log('📋 组件特性：');
        console.log('  ✅ 企业级设计系统集成');
        console.log('  ✅ 完整的响应式布局');
        console.log('  ✅ 高度可配置的架构');
        console.log('  ✅ 无障碍访问支持');
        console.log('  ✅ Element Plus 集成');
        
        // 模拟响应式测试
        function checkResponsive() {
            const width = window.innerWidth;
            let layout = '';
            
            if (width >= 1200) {
                layout = '桌面端 - 4列网格布局';
            } else if (width >= 768) {
                layout = '平板端 - 2列网格布局';
            } else {
                layout = '移动端 - 手风琴式布局';
            }
            
            console.log(`📱 当前屏幕宽度: ${width}px - ${layout}`);
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', checkResponsive);
        checkResponsive(); // 初始检查
    </script>
</body>
</html>
