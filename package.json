{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "build:analyze": "ANALYZE=true vite build", "build:prod": "NODE_ENV=production vite build", "dev": "vite", "preview": "vite preview"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.10", "@vitejs/plugin-vue": "^5.2.4", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "alpinejs": "^3.14.9", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "concurrently": "^9.1.2", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-regex": "^1.10.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-vue": "^10.2.0", "laravel-vite-plugin": "^1.3.0", "postcss": "^8.5.5", "postcss-html": "^1.8.0", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.20.0", "stylelint-config-idiomatic-order": "^10.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-use-logical-spec": "^5.0.1", "tailwindcss": "^3.4.17", "terser": "^5.42.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vue": "^3.5.16"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/vue": "^2.2.2", "@floating-ui/dom": "^1.7.1", "@vueuse/core": "^13.3.0", "@vueuse/math": "^13.3.0", "apexcharts-clevision": "^3.28.5", "chart.js": "^4.4.9", "core-js": "^3.43.0", "element-plus": "^2.10.1", "eslint-import-resolver-alias": "^1.1.2", "js-md5": "^0.8.3", "jwt-decode": "^4.0.0", "pinia": "^3.0.3", "prismjs": "^1.30.0", "roboto-fontface": "^0.10.0", "unocss": "^66.2.0", "unplugin-vue-define-options": "^3.0.0-beta.14", "vite-plugin-image-optimizer": "^1.1.8", "vue-chartjs": "^5.3.2", "vue-flatpickr-component": "^12.0.0", "vue-prism-component": "^2.0.0", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0", "vue3-perfect-scrollbar": "^2.0.0", "webfontloader": "^1.6.28"}}