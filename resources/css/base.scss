// ========================================
// Workhub Core 企业级样式系统主入口文件
// 技术栈: Laravel Blade + Vue 3 + Element Plus + Tailwind CSS + Vite + SCSS
// 设计风格: Modern, Clean, Professional, Enterprise-grade
// 中文注释用于中国合规
// ========================================

// 1. 所有 @use 规则必须在最前面 (SCSS 要求)
@use 'variables' as *;
@use 'global' as *;
@use 'element-plus-theme';

// 2. 导入 Tailwind CSS 核心样式
@tailwind base;
@tailwind components;
@tailwind utilities;

// ========================================
// Tailwind CSS 自定义层级 - 企业级增强
// ========================================

@layer base {
    /* 企业级基础样式增强 - 覆盖 Tailwind 默认值 */
    html {
        font-size: 12px; // 12px 企业级基础字体大小 - 作为rem计算基础
    }

    /* Tailwind 容器增强 - 1760px 最大宽度 */
    .container {
        @apply mx-auto px-4 sm:px-6 lg:px-8;
        max-width: #{$container-max-width}; // 1760px
        min-width: #{$container-min-width}; // 640px
    }
}

@layer components {
    /* 企业级 Tailwind 组件增强 */
    /* 布局组件 - 企业级 1760px 最大宽度 */
    .page-container {
        @apply mx-auto px-4 sm:px-6 lg:px-8;
        max-width: #{$container-max-width}; // 1760px
        min-width: #{$container-min-width}; // 640px
    }

    .container-1760 {
        @apply mx-auto px-4 sm:px-6 lg:px-8;
        max-width: #{$container-max-width}; // 1760px
        min-width: #{$desktop-min-width}; // 1200px
    }

    /* 企业级网格系统 - Tailwind 增强 */
    .grid-responsive {
        @apply grid gap-4;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));

        &.grid-responsive-sm {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        &.grid-responsive-lg {
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        }
    }

    /* 企业级按钮组件 - Tailwind 集成 & Element Plus 兼容 */
    .btn-tw {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md;
        font-size: #{$font-size-xs}; // 12px 基础 (1rem = 12px)
        font-family: #{$font-family-primary};
        border-radius: #{$border-radius-md}; // 8px 统一圆角
        min-height: 2rem; // 24px 统一高度 (2rem) - 基于12px
        transition: none; // 企业级无动画
        border: 1px solid transparent;
        cursor: pointer;

        &:focus {
            @apply outline-none;
            box-shadow: none; // 企业级无阴影
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        // 尺寸变体
        &.btn-small {
            min-height: 1.5rem; // 18px (1.5rem) - 基于12px
            font-size: #{$font-size-xxs}; // 8px
            padding: #{$spacing-xs} #{$spacing-sm}; // 4px 8px
        }

        &.btn-large {
            min-height: 2.5rem; // 30px (2.5rem) - 基于12px
            font-size: #{$font-size-sm}; // 14px
            padding: #{$spacing-sm} #{$spacing-lg}; // 8px 20px
        }

        // 颜色变体
        &.btn-primary-tw {
            background-color: #{$primary-color};
            color: #{$white};
            border-color: #{$primary-color};

            &:hover:not(:disabled) {
                background-color: #{$primary-dark};
                border-color: #{$primary-dark};
            }
        }

        &.btn-secondary-tw {
            background-color: #{$secondary-color};
            color: #{$white};
            border-color: #{$secondary-color};

            &:hover:not(:disabled) {
                background-color: #{$secondary-dark};
                border-color: #{$secondary-dark};
            }
        }

        &.btn-success-tw {
            background-color: #{$success-color};
            color: #{$white};
            border-color: #{$success-color};

            &:hover:not(:disabled) {
                background-color: #{$success-dark};
                border-color: #{$success-dark};
            }
        }

        &.btn-warning-tw {
            background-color: #{$warning-color};
            color: #{$white};
            border-color: #{$warning-color};

            &:hover:not(:disabled) {
                background-color: #{$warning-dark};
                border-color: #{$warning-dark};
            }
        }

        &.btn-danger-tw {
            background-color: #{$danger-color};
            color: #{$white};
            border-color: #{$danger-color};

            &:hover:not(:disabled) {
                background-color: #{$danger-dark};
                border-color: #{$danger-dark};
            }
        }

        &.btn-outline-tw {
            @apply bg-transparent border;
            border-color: #{$border-color};
            color: #{$text-primary};

            &:hover:not(:disabled) {
                background-color: #{$bg-hover};
                border-color: #{$border-color-dark};
            }
        }

        &.btn-text-tw {
            @apply bg-transparent border-transparent;
            color: #{$primary-color};

            &:hover:not(:disabled) {
                background-color: #{$primary-alpha-10};
            }
        }
    }

    /* 企业级表单组件 - Tailwind 集成 & Element Plus 兼容 */
    .form-input-tw {
        @apply block w-full px-3 py-2 border rounded-md;
        border-color: #{$border-color};
        font-size: #{$font-size-xs}; // 12px 基础 (1rem = 12px)
        font-family: #{$font-family-primary};
        border-radius: #{$border-radius-md}; // 8px 统一圆角
        min-height: 2rem; // 24px 统一高度 (2rem) - 基于12px
        background-color: #{$white};
        color: #{$text-primary};
        transition: none; // 企业级无动画

        &:focus {
            @apply outline-none;
            border-color: #{$primary-color};
            box-shadow: none; // 企业级无阴影
        }

        &::placeholder {
            color: #{$text-placeholder};
            opacity: 1;
        }

        &:disabled {
            background-color: #{$bg-lighter};
            color: #{$text-muted};
            cursor: not-allowed;
        }

        &:read-only {
            background-color: #{$gray-50};
            cursor: default;
        }

        &:invalid {
            border-color: #{$danger-color};
        }

        // 尺寸变体
        &.form-input-small {
            min-height: 1.5rem; // 18px (1.5rem) - 基于12px
            font-size: #{$font-size-xxs}; // 8px
            padding: #{$spacing-xs} #{$spacing-sm}; // 4px 8px
        }

        &.form-input-large {
            min-height: 2.5rem; // 30px (2.5rem) - 基于12px
            font-size: #{$font-size-sm}; // 14px
            padding: #{$spacing-sm} #{$spacing-base}; // 8px 12px
        }

        // 状态变体
        &.form-input-error {
            border-color: #{$danger-color};

            &:focus {
                border-color: #{$danger-color};
            }
        }

        &.form-input-success {
            border-color: #{$success-color};

            &:focus {
                border-color: #{$success-color};
            }
        }

        &.form-input-warning {
            border-color: #{$warning-color};

            &:focus {
                border-color: #{$warning-color};
            }
        }
    }

    /* 企业级选择框组件 */
    .form-select-tw {
        @extend .form-input-tw;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right #{$spacing-sm} center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: #{$spacing-xl}; // 为箭头留空间
        cursor: pointer;

        &:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%235247ef' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }
    }

    /* 企业级文本域组件 */
    .form-textarea-tw {
        @extend .form-input-tw;
        min-height: #{$spacing-4xl}; // 64px
        resize: vertical;
        line-height: #{$line-height-relaxed};
    }

    /* 企业级复选框和单选框 */
    .form-checkbox-tw,
    .form-radio-tw {
        width: #{$spacing-base}; // 12px
        height: #{$spacing-base}; // 12px
        border: 1px solid #{$border-color};
        background-color: #{$white};
        transition: none; // 企业级无动画

        &:checked {
            background-color: #{$primary-color};
            border-color: #{$primary-color};
        }

        &:focus {
            outline: none;
            box-shadow: none; // 企业级无阴影
        }

        &:disabled {
            background-color: #{$bg-lighter};
            cursor: not-allowed;
        }
    }

    .form-checkbox-tw {
        border-radius: #{$border-radius-xs}; // 2px
    }

    .form-radio-tw {
        border-radius: 50%;
    }

    /* 企业级加载状态 - 最小化动画 */
    .loading-tw {
        @apply flex items-center justify-center p-4 text-gray-500;

        &::before {
            content: '';
            @apply w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full mr-2;
            animation: spin 1s linear infinite;
        }
    }

    .skeleton-tw {
        @apply bg-gray-200 rounded;
        animation: pulse 1.5s ease-in-out infinite;
    }

    /* 企业级表格组件 - Tailwind 集成 */
    .table-tw {
        @apply w-full border-collapse;
        border-spacing: 0;
        font-size: #{$font-size-xs}; // 12px 基础
        font-family: #{$font-family-primary};
        border-radius: #{$border-radius-md}; // 8px 统一圆角
        overflow: hidden;
        border: 1px solid #{$border-color};

        th {
            background-color: #{$table-header-bg};
            color: #{$table-header-text};
            font-weight: #{$font-weight-medium};
            padding: #{$table-cell-padding};
            border-bottom: 1px solid #{$border-color};
            text-align: left;
        }

        td {
            padding: #{$table-cell-padding};
            border-bottom: 1px solid #{$border-color};
            color: #{$text-primary};
        }

        tbody tr {
            transition: none; // 企业级无动画

            &:hover {
                background-color: #{$table-bg-hover};
            }

            &:nth-child(even) {
                background-color: #{$table-bg-striped};
            }

            &.selected {
                background-color: #{$table-bg-selected};
            }
        }

        &.table-bordered {
            th, td {
                border-right: 1px solid #{$border-color};

                &:last-child {
                    border-right: none;
                }
            }
        }

        &.table-compact {
            th, td {
                padding: #{$spacing-xs} #{$spacing-sm};
            }
        }

        &.table-large {
            th, td {
                padding: #{$spacing-base} #{$spacing-lg};
            }
        }
    }
}

// ========================================
// 企业级工具类 - Tailwind 增强
// ========================================

@layer utilities {
    /* 企业级无动画类 - 符合用户偏好 */
    .no-animation,
    .no-animation * {
        animation: none !important;
        transition: none !important;
    }

    /* 企业级焦点样式移除 - 符合用户偏好 */
    .no-focus-outline,
    .no-focus-outline:focus,
    .no-focus-outline:active,
    .no-focus-outline:focus-visible {
        outline: none !important;
        box-shadow: none !important;
    }

    /* 企业级文本截断 */
    .truncate-120 {
        @apply truncate;
        max-width: 120px;
    }

    .truncate-180 {
        @apply truncate;
        max-width: 180px;
    }

    .truncate-240 {
        @apply truncate;
        max-width: 240px;
    }

    /* 企业级宽度类 */
    .min-w-mobile {
        min-width: #{$container-min-width}; // 640px
    }

    .min-w-desktop {
        min-width: #{$desktop-min-width}; // 1200px
    }

    .max-w-enterprise {
        max-width: #{$container-max-width}; // 1760px
    }

    /* 企业级字体大小 - 12px 基础 (基于HTML根元素12px计算) */
    .text-enterprise-xs {
        font-size: #{$font-size-xxs}; // 8px (0.667rem)
    }

    .text-enterprise-sm {
        font-size: #{$font-size-xs}; // 12px 基础 (1rem)
    }

    .text-enterprise-base {
        font-size: #{$font-size-sm}; // 14px (1.167rem)
    }

    .text-enterprise-lg {
        font-size: #{$font-size-base}; // 16px (1.333rem)
    }

    /* 企业级颜色工具类 */
    .text-enterprise-primary {
        color: #{$primary-color};
    }

    .text-enterprise-secondary {
        color: #{$secondary-color};
    }

    .text-enterprise-muted {
        color: #{$text-muted};
    }

    .bg-enterprise-primary {
        background-color: #{$primary-color};
    }

    .bg-enterprise-light {
        background-color: #{$body-bg};
    }

    .border-enterprise {
        border-color: #{$border-color};
    }

    /* 企业级响应式显示/隐藏 */
    @media (max-width: #{$breakpoint-lg - 1px}) {
        .hidden-mobile {
            @apply hidden;
        }

        .show-mobile {
            @apply block;
        }
    }

    @media (min-width: #{$breakpoint-lg}) {
        .hidden-desktop {
            @apply hidden;
        }

        .show-desktop {
            @apply block;
        }
    }

    /* 移动端响应式工具类 */
    @media (max-width: #{$breakpoint-sm - 1px}) {
        /* 文本大小调整 */
        .text-mobile-xs {
            font-size: #{$font-size-xxs}; // 8px
        }

        .text-mobile-sm {
            font-size: #{$font-size-xs}; // 12px
        }

        /* 间距调整 */
        .p-mobile-sm {
            padding: #{$spacing-sm};
        }

        .p-mobile-md {
            padding: #{$spacing-md};
        }

        .m-mobile-sm {
            margin: #{$spacing-sm};
        }

        .m-mobile-md {
            margin: #{$spacing-md};
        }

        /* 容器调整 */
        .container-mobile {
            @apply max-w-full px-4;
        }

        /* 网格布局调整 */
        .grid-mobile-1 {
            @apply grid-cols-1;
        }

        .grid-mobile-2 {
            @apply grid-cols-2;
        }

        /* Flex 布局调整 */
        .flex-mobile-col {
            @apply flex-col;
        }

        .flex-mobile-wrap {
            @apply flex-wrap;
        }
    }

    /* 超小屏幕优化 (640px以下兼容) */
    @media (max-width: #{$breakpoint-xs - 1px}) {
        /* 基础布局调整 */
        .layout-mobile-xs {
            min-width: 320px; // 最小支持宽度
            padding: #{$spacing-xs};
        }

        /* 字体大小调整 */
        html {
            font-size: 12px; // 12px 企业级基础字体大小 - 保持rem计算基础
        }

        /* 触摸友好 */
        .touch-target {
            min-height: 48px;
            min-width: 48px;
            @apply flex items-center justify-center;
        }

        /* 简化显示 */
        .hide-text-xs {
            @apply sr-only;
        }

        .icon-only-xs {
            .text {
                @apply sr-only;
            }

            .icon {
                font-size: 1.25em;
            }
        }
    }

    /* 文本截断工具类 */
    .text-truncate {
        @apply truncate;
    }

    .text-truncate-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .text-truncate-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 滚动条样式 */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: #{$gray-400} transparent;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: #{$gray-400};
            border-radius: 3px;

            &:hover {
                background-color: #{$gray-500};
            }
        }
    }

    /* 布局修复工具类 */
    .overflow-x-hidden {
        overflow-x: hidden !important;
    }

    .max-w-screen {
        max-width: 100vw !important;
    }

    .w-screen-safe {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    /* 容器安全类 */
    .container-safe {
        width: 100%;
        max-width: min(#{$container-max-width}, 100vw);
        overflow-x: hidden;
        box-sizing: border-box;
    }

    /* 安全区域支持 */
    .safe-top {
        padding-top: env(safe-area-inset-top);
    }

    .safe-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .safe-left {
        padding-left: env(safe-area-inset-left);
    }

    .safe-right {
        padding-right: env(safe-area-inset-right);
    }

    /* 企业级间距工具类 */
    .space-enterprise-xs {
        gap: #{$spacing-xs};
    }

    .space-enterprise-sm {
        gap: #{$spacing-sm};
    }

    .space-enterprise-md {
        gap: #{$spacing-md};
    }

    .space-enterprise-lg {
        gap: #{$spacing-lg};
    }

    /* 企业级模态框组件 - Tailwind 集成 */
    .modal-tw {
        @apply fixed inset-0 z-50 flex items-center justify-center;
        background-color: #{$modal-overlay-bg};

        .modal-content {
            @apply relative max-w-lg w-full mx-4;
            background-color: #{$modal-bg};
            border-radius: #{$border-radius-lg}; // 12px 统一圆角
            box-shadow: #{$modal-shadow};
            border: 1px solid #{$modal-border};
            max-height: 90vh;
            overflow-y: auto;

            .modal-header {
                padding: #{$spacing-lg};
                border-bottom: 1px solid #{$modal-header-border};
                background-color: #{$modal-header-bg};
                border-radius: #{$border-radius-lg} #{$border-radius-lg} 0 0;

                .modal-title {
                    font-size: #{$font-size-base}; // 16px
                    font-weight: #{$font-weight-medium};
                    color: #{$text-primary};
                    margin: 0;
                }

                .modal-close {
                    @apply absolute top-4 right-4 p-1;
                    background: none;
                    border: none;
                    color: #{$text-muted};
                    cursor: pointer;
                    font-size: #{$font-size-lg};

                    &:hover {
                        color: #{$text-primary};
                    }
                }
            }

            .modal-body {
                padding: #{$spacing-lg};
                font-size: #{$font-size-xs}; // 12px 基础
            }

            .modal-footer {
                padding: #{$spacing-lg};
                border-top: 1px solid #{$modal-footer-border};
                background-color: #{$modal-footer-bg};
                border-radius: 0 0 #{$border-radius-lg} #{$border-radius-lg};
                @apply flex justify-end gap-3;
            }
        }

        &.modal-sm .modal-content {
            @apply max-w-sm;
        }

        &.modal-lg .modal-content {
            @apply max-w-2xl;
        }

        &.modal-xl .modal-content {
            @apply max-w-4xl;
        }

        &.modal-fullscreen .modal-content {
            @apply max-w-none w-full h-full mx-0;
            border-radius: 0;
        }
    }

    /* 企业级阴影工具类 */
    .shadow-enterprise-sm {
        box-shadow: #{$box-shadow-sm};
    }

    .shadow-enterprise-md {
        box-shadow: #{$box-shadow-md};
    }

    .shadow-enterprise-lg {
        box-shadow: #{$box-shadow-lg};
    }

    /* 企业级状态工具类 */
    .status-online {
        color: #{$status-online};
    }

    .status-offline {
        color: #{$status-offline};
    }

    .status-away {
        color: #{$status-away};
    }

    .status-busy {
        color: #{$status-busy};
    }

    /* 企业级优先级工具类 */
    .priority-critical {
        color: #{$priority-critical};
    }

    .priority-high {
        color: #{$priority-high};
    }

    .priority-medium {
        color: #{$priority-medium};
    }

    .priority-low {
        color: #{$priority-low};
    }

    /* 企业级进度工具类 */
    .progress-complete {
        color: #{$progress-complete};
    }

    .progress-in-progress {
        color: #{$progress-in-progress};
    }

    .progress-pending {
        color: #{$progress-pending};
    }

    .progress-blocked {
        color: #{$progress-blocked};
    }

    /* 企业级标签颜色工具类 */
    .tag-blue {
        background-color: #{$tag-blue-light};
        color: #{$tag-blue};
    }

    .tag-green {
        background-color: #{$tag-green-light};
        color: #{$tag-green};
    }

    .tag-yellow {
        background-color: #{$tag-yellow-light};
        color: #{$tag-yellow};
    }

    .tag-red {
        background-color: #{$tag-red-light};
        color: #{$tag-red};
    }

    .tag-purple {
        background-color: #{$tag-purple-light};
        color: #{$tag-purple};
    }

    .tag-pink {
        background-color: #{$tag-pink-light};
        color: #{$tag-pink};
    }

    .tag-indigo {
        background-color: #{$tag-indigo-light};
        color: #{$tag-indigo};
    }

    .tag-teal {
        background-color: #{$tag-teal-light};
        color: #{$tag-teal};
    }

    .tag-orange {
        background-color: #{$tag-orange-light};
        color: #{$tag-orange};
    }

    .tag-cyan {
        background-color: #{$tag-cyan-light};
        color: #{$tag-cyan};
    }

    /* 企业级状态指示器工具类 */
    .status-indicator {
        @include enterprise-status-indicator();

        &.status-indicator-online {
            @include enterprise-status-indicator('online');
        }

        &.status-indicator-offline {
            @include enterprise-status-indicator('offline');
        }

        &.status-indicator-away {
            @include enterprise-status-indicator('away');
        }

        &.status-indicator-busy {
            @include enterprise-status-indicator('busy');
        }

        &.status-indicator-idle {
            @include enterprise-status-indicator('idle');
        }
    }

    /* 企业级优先级指示器工具类 */
    .priority-indicator {
        @include enterprise-priority-indicator();

        &.priority-indicator-critical {
            @include enterprise-priority-indicator('critical');
        }

        &.priority-indicator-high {
            @include enterprise-priority-indicator('high');
        }

        &.priority-indicator-medium {
            @include enterprise-priority-indicator('medium');
        }

        &.priority-indicator-low {
            @include enterprise-priority-indicator('low');
        }
    }

    /* 企业级标签工具类 */
    .tag-tw {
        @include enterprise-tag();

        &.tag-tw-sm {
            @include enterprise-tag('primary', 'sm');
        }

        &.tag-tw-lg {
            @include enterprise-tag('primary', 'lg');
        }

        &.tag-tw-primary {
            @include enterprise-tag('primary');
        }

        &.tag-tw-secondary {
            @include enterprise-tag('secondary');
        }

        &.tag-tw-success {
            @include enterprise-tag('success');
        }

        &.tag-tw-warning {
            @include enterprise-tag('warning');
        }

        &.tag-tw-danger {
            @include enterprise-tag('danger');
        }

        &.tag-tw-gray {
            @include enterprise-tag('gray');
        }
    }

    /* 企业级通知工具类 */
    .notification-tw {
        @include enterprise-notification();

        &.notification-tw-success {
            @include enterprise-notification('success');
        }

        &.notification-tw-warning {
            @include enterprise-notification('warning');
        }

        &.notification-tw-error {
            @include enterprise-notification('error');
        }
    }

    /* 企业级进度条工具类 */
    .progress-tw {
        @include enterprise-progress();

        &.progress-tw-sm {
            @include enterprise-progress(4px);
        }

        &.progress-tw-lg {
            @include enterprise-progress(12px);
        }

        &.progress-tw-xl {
            @include enterprise-progress(16px);
        }

        &.progress-tw-success .progress-bar {
            background-color: #{$success-color};
        }

        &.progress-tw-warning .progress-bar {
            background-color: #{$warning-color};
        }

        &.progress-tw-danger .progress-bar {
            background-color: #{$danger-color};
        }
    }
}

// ========================================
// 企业级可访问性和用户体验增强
// ========================================

/* 企业级减少动画模式支持 - 符合用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-tw::before,
    .skeleton-tw {
        animation: none !important;
    }
}

/* 企业级高对比度模式支持 */
@media (prefers-contrast: more) {
    body {
        background-color: #{$white} !important;
        color: #{$black} !important;
    }

    a {
        color: #0000ff !important;
        text-decoration: underline !important;
    }

    button,
    .btn,
    .btn-tw {
        border: 2px solid currentColor !important;
    }

    .card,
    .card-tw {
        border: 2px solid #{$black} !important;
    }
}

/* 企业级暗色模式偏好支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --body-bg: #{$dark-color};
        --text-primary: #{$white};
        --card-bg: #{$dark-light};
        --border-color: #{$dark-lighter};
    }
}

/* 企业级打印优化 */
@media print {
    .no-print,
    .btn,
    .btn-tw,
    .loading-tw {
        display: none !important;
    }

    body {
        background: #{$white} !important;
        color: #{$black} !important;
        font-size: 12pt !important;
    }

    .container,
    .container-1760,
    .page-container {
        max-width: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #{$gray-600};
    }
}
