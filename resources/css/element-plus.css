@charset "UTF-8";
:root {
    --el-color-white: #ffffff;
    --el-color-black: #000000;
    /* ↓↓↓ 仅更新了这 6 行 RGB 数值 ↓↓↓ */
    --el-color-primary-rgb: 82, 71, 239; /* #5247EF */
    --el-color-success-rgb: 4, 199, 23; /* #04C717 */
    --el-color-warning-rgb: 255, 80, 0; /* #FF5000 */
    --el-color-danger-rgb: 255, 0, 54; /* #FF0036 */
    --el-color-error-rgb: 255, 0, 54; /* 同 danger */
    --el-color-info-rgb: 151, 166, 186; /* #97A6BA */
    /* ↑↑↑ 其余保持官方 2.9.1 顺序 ↑↑↑ */
    --el-font-size-extra-large: 20px;
    --el-font-size-large: 18px;
    --el-font-size-medium: 16px;
    --el-font-size-base: 14px;
    --el-font-size-small: 13px;
    --el-font-size-extra-small: 12px;
    --el-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", <PERSON><PERSON>, sans-serif;
    --el-font-weight-primary: 500;
    --el-font-line-height-primary: 24px;
    --el-index-normal: 1;
    --el-index-top: 1000;
    --el-index-popper: 2000;
    --el-border-radius-base: 4px;
    --el-border-radius-small: 2px;
    --el-border-radius-round: 20px;
    --el-border-radius-circle: 100%;
    --el-transition-duration: 0.3s;
    --el-transition-duration-fast: 0.2s;
    --el-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
    --el-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
    --el-transition-all: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-fade: opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-md-fade: transform var(--el-transition-duration) var(--el-transition-function-fast-bezier), opacity var(--el-transition-duration) var(--el-transition-function-fast-bezier);
    --el-transition-fade-linear: opacity var(--el-transition-duration-fast) linear;
    --el-transition-border: border-color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-box-shadow: box-shadow var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-transition-color: color var(--el-transition-duration-fast) var(--el-transition-function-ease-in-out-bezier);
    --el-component-size-large: 40px;
    --el-component-size: 32px;
    --el-component-size-small: 24px;
    color-scheme: light;
    /* 主色 #5247EF */
    --el-color-primary: #5247ef;
    --el-color-primary-light-3: rgb(134, 126, 244); /* +30 % */
    --el-color-primary-light-5: rgb(168, 163, 247); /* +50 % */
    --el-color-primary-light-7: rgb(203, 200, 250); /* +70 % */
    --el-color-primary-light-8: rgb(220, 218, 252); /* +80 % */
    --el-color-primary-light-9: rgb(238, 237, 253); /* +90 % */
    --el-color-primary-dark-2: rgb(66, 57, 191); /* –20 % */

    /* 成功色 #04C717 */
    --el-color-success: #04c717;
    --el-color-success-light-3: rgb(79, 216, 93);
    --el-color-success-light-5: rgb(130, 227, 139);
    --el-color-success-light-7: rgb(180, 238, 185);
    --el-color-success-light-8: rgb(205, 244, 209);
    --el-color-success-light-9: rgb(230, 249, 232);
    --el-color-success-dark-2: rgb(3, 159, 18);

    /* 警告色 #FF5000 */
    --el-color-warning: #ff5000;
    --el-color-warning-light-3: rgb(255, 132, 76);
    --el-color-warning-light-5: rgb(255, 168, 128);
    --el-color-warning-light-7: rgb(255, 202, 178);
    --el-color-warning-light-8: rgb(255, 220, 204);
    --el-color-warning-light-9: rgb(255, 238, 230);
    --el-color-warning-dark-2: rgb(204, 64, 0);

    /* 危险/错误色 #FF0036 */
    --el-color-danger: #ff0036;
    --el-color-danger-light-3: rgb(255, 76, 114);
    --el-color-danger-light-5: rgb(255, 128, 154);
    --el-color-danger-light-7: rgb(255, 178, 195);
    --el-color-danger-light-8: rgb(255, 204, 215);
    --el-color-danger-light-9: rgb(255, 230, 235);
    --el-color-danger-dark-2: rgb(204, 0, 43);

    /* 同步 error 变量（若需要与 danger 分离，可单独设色）*/
    --el-color-error: var(--el-color-danger);
    --el-color-error-light-3: var(--el-color-danger-light-3);
    --el-color-error-light-5: var(--el-color-danger-light-5);
    --el-color-error-light-7: var(--el-color-danger-light-7);
    --el-color-error-light-8: var(--el-color-danger-light-8);
    --el-color-error-light-9: var(--el-color-danger-light-9);
    --el-color-error-dark-2: var(--el-color-danger-dark-2);

    /* 信息色 #97A6BA */
    --el-color-info: #97a6ba;
    --el-color-info-light-3: rgb(182, 193, 207);
    --el-color-info-light-5: rgb(203, 210, 220);
    --el-color-info-light-7: rgb(224, 228, 234);
    --el-color-info-light-8: rgb(234, 237, 241);
    --el-color-info-light-9: rgb(245, 246, 248);
    --el-color-info-dark-2: rgb(121, 133, 149);
    --el-bg-color: #ffffff;
    --el-bg-color-page: #f2f3f5;
    --el-bg-color-overlay: #ffffff;
    --el-text-color-primary: #999;
    --el-text-color-regular: #606266;
    --el-text-color-secondary: #909399;
    --el-text-color-placeholder: #a8abb2;
    --el-text-color-disabled: #c0c4cc;
    --el-border-color: #dcdfe6;
    --el-border-color-light: #e4e7ed;
    --el-border-color-lighter: #ebeef5;
    --el-border-color-extra-light: #f2f6fc;
    --el-border-color-dark: #d4d7de;
    --el-border-color-darker: #cdd0d6;
    --el-fill-color: #f0f2f5;
    --el-fill-color-light: #f5f7fa;
    --el-fill-color-lighter: #fafafa;
    --el-fill-color-extra-light: #fafcff;
    --el-fill-color-dark: #ebedf0;
    --el-fill-color-darker: #e6e8eb;
    --el-fill-color-blank: #ffffff;
    --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
    --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
    --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
    --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
    --el-disabled-bg-color: var(--el-fill-color-light);
    --el-disabled-text-color: var(--el-text-color-placeholder);
    --el-disabled-border-color: var(--el-border-color-light);
    --el-overlay-color: rgba(0, 0, 0, 0.8);
    --el-overlay-color-light: rgba(0, 0, 0, 0.7);
    --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
    --el-mask-color: rgba(255, 255, 255, 0.9);
    --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
    --el-border-width: 1px;
    --el-border-style: solid;
    --el-border-color-hover: var(--el-text-color-disabled);
    --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
    --el-svg-monochrome-grey: var(--el-border-color)
}

.fade-in-linear-enter-active, .fade-in-linear-leave-active {
    transition: var(--el-transition-fade-linear)
}

.fade-in-linear-enter-from, .fade-in-linear-leave-to {
    opacity: 0
}

.el-fade-in-linear-enter-active, .el-fade-in-linear-leave-active {
    transition: var(--el-transition-fade-linear)
}

.el-fade-in-linear-enter-from, .el-fade-in-linear-leave-to {
    opacity: 0
}

.el-fade-in-enter-active, .el-fade-in-leave-active {
    transition: all var(--el-transition-duration) cubic-bezier(.55, 0, .1, 1)
}

.el-fade-in-enter-from, .el-fade-in-leave-active {
    opacity: 0
}

.el-zoom-in-center-enter-active, .el-zoom-in-center-leave-active {
    transition: all var(--el-transition-duration) cubic-bezier(.55, 0, .1, 1)
}

.el-zoom-in-center-enter-from, .el-zoom-in-center-leave-active {
    opacity: 0;
    transform: scaleX(0)
}

.el-zoom-in-top-enter-active, .el-zoom-in-top-leave-active {
    opacity: 1;
    transform: scaleY(1);
    transform-origin: center top;
    transition: var(--el-transition-md-fade)
}

.el-zoom-in-top-enter-active[data-popper-placement^=top], .el-zoom-in-top-leave-active[data-popper-placement^=top] {
    transform-origin: center bottom
}

.el-zoom-in-top-enter-from, .el-zoom-in-top-leave-active {
    opacity: 0;
    transform: scaleY(0)
}

.el-zoom-in-bottom-enter-active, .el-zoom-in-bottom-leave-active {
    opacity: 1;
    transform: scaleY(1);
    transform-origin: center bottom;
    transition: var(--el-transition-md-fade)
}

.el-zoom-in-bottom-enter-from, .el-zoom-in-bottom-leave-active {
    opacity: 0;
    transform: scaleY(0)
}

.el-zoom-in-left-enter-active, .el-zoom-in-left-leave-active {
    opacity: 1;
    transform: scale(1);
    transform-origin: top left;
    transition: var(--el-transition-md-fade)
}

.el-zoom-in-left-enter-from, .el-zoom-in-left-leave-active {
    opacity: 0;
    transform: scale(.45)
}

.collapse-transition {
    transition: var(--el-transition-duration) height ease-in-out, var(--el-transition-duration) padding-top ease-in-out, var(--el-transition-duration) padding-bottom ease-in-out
}

.el-collapse-transition-enter-active, .el-collapse-transition-leave-active {
    transition: var(--el-transition-duration) max-height ease-in-out, var(--el-transition-duration) padding-top ease-in-out, var(--el-transition-duration) padding-bottom ease-in-out
}

.horizontal-collapse-transition {
    transition: var(--el-transition-duration) width ease-in-out, var(--el-transition-duration) padding-left ease-in-out, var(--el-transition-duration) padding-right ease-in-out
}

.el-list-enter-active, .el-list-leave-active {
    transition: all 1s
}

.el-list-enter-from, .el-list-leave-to {
    opacity: 0;
    transform: translateY(-30px)
}

.el-list-leave-active {
    position: absolute !important
}

.el-opacity-transition {
    transition: opacity var(--el-transition-duration) cubic-bezier(.55, 0, .1, 1)
}

.el-icon-loading {
    animation: rotating 2s linear infinite
}

.el-icon--right {
    margin-left: 5px
}

.el-icon--left {
    margin-right: 5px
}

@keyframes rotating {
    0% {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(1turn)
    }
}

.el-icon {
    --color: inherit;
    align-items: center;
    display: inline-flex;
    height: 1em;
    justify-content: center;
    line-height: 1em;
    position: relative;
    width: 1em;
    fill: currentColor;
    color: var(--color);
    font-size: inherit
}

.el-icon.is-loading {
    animation: rotating 2s linear infinite
}

.el-icon svg {
    height: 1em;
    width: 1em
}

.el-affix--fixed {
    position: fixed
}

.el-alert {
    --el-alert-padding: 8px 16px;
    --el-alert-border-radius-base: var(--el-border-radius-base);
    --el-alert-title-font-size: 14px;
    --el-alert-title-with-description-font-size: 16px;
    --el-alert-description-font-size: 14px;
    --el-alert-close-font-size: 16px;
    --el-alert-close-customed-font-size: 14px;
    --el-alert-icon-size: 16px;
    --el-alert-icon-large-size: 28px;
    align-items: center;
    background-color: var(--el-color-white);
    border-radius: var(--el-alert-border-radius-base);
    box-sizing: border-box;
    display: flex;
    margin: 0;
    opacity: 1;
    overflow: hidden;
    padding: var(--el-alert-padding);
    position: relative;
    transition: opacity var(--el-transition-duration-fast);
    width: 100%
}

.el-alert.is-light .el-alert__close-btn {
    color: var(--el-text-color-placeholder)
}

.el-alert.is-dark .el-alert__close-btn, .el-alert.is-dark .el-alert__description {
    color: var(--el-color-white)
}

.el-alert.is-center {
    justify-content: center
}

.el-alert--primary {
    --el-alert-bg-color: var(--el-color-primary-light-9)
}

.el-alert--primary.is-light {
    background-color: var(--el-alert-bg-color)
}

.el-alert--primary.is-light, .el-alert--primary.is-light .el-alert__description {
    color: var(--el-color-primary)
}

.el-alert--primary.is-dark {
    background-color: var(--el-color-primary);
    color: var(--el-color-white)
}

.el-alert--success {
    --el-alert-bg-color: var(--el-color-success-light-9)
}

.el-alert--success.is-light {
    background-color: var(--el-alert-bg-color)
}

.el-alert--success.is-light, .el-alert--success.is-light .el-alert__description {
    color: var(--el-color-success)
}

.el-alert--success.is-dark {
    background-color: var(--el-color-success);
    color: var(--el-color-white)
}

.el-alert--info {
    --el-alert-bg-color: var(--el-color-info-light-9)
}

.el-alert--info.is-light {
    background-color: var(--el-alert-bg-color)
}

.el-alert--info.is-light, .el-alert--info.is-light .el-alert__description {
    color: var(--el-color-info)
}

.el-alert--info.is-dark {
    background-color: var(--el-color-info);
    color: var(--el-color-white)
}

.el-alert--warning {
    --el-alert-bg-color: var(--el-color-warning-light-9)
}

.el-alert--warning.is-light {
    background-color: var(--el-alert-bg-color)
}

.el-alert--warning.is-light, .el-alert--warning.is-light .el-alert__description {
    color: var(--el-color-warning)
}

.el-alert--warning.is-dark {
    background-color: var(--el-color-warning);
    color: var(--el-color-white)
}

.el-alert--error {
    --el-alert-bg-color: var(--el-color-error-light-9)
}

.el-alert--error.is-light {
    background-color: var(--el-alert-bg-color)
}

.el-alert--error.is-light, .el-alert--error.is-light .el-alert__description {
    color: var(--el-color-error)
}

.el-alert--error.is-dark {
    background-color: var(--el-color-error);
    color: var(--el-color-white)
}

.el-alert__content {
    display: flex;
    flex-direction: column;
    gap: 4px
}

.el-alert .el-alert__icon {
    font-size: var(--el-alert-icon-size);
    margin-right: 8px;
    width: var(--el-alert-icon-size)
}

.el-alert .el-alert__icon.is-big {
    font-size: var(--el-alert-icon-large-size);
    margin-right: 12px;
    width: var(--el-alert-icon-large-size)
}

.el-alert__title {
    font-size: var(--el-alert-title-font-size);
    line-height: 24px
}

.el-alert__title.with-description {
    font-size: var(--el-alert-title-with-description-font-size)
}

.el-alert .el-alert__description {
    font-size: var(--el-alert-description-font-size);
    margin: 0
}

.el-alert .el-alert__close-btn {
    cursor: pointer;
    font-size: var(--el-alert-close-font-size);
    opacity: 1;
    position: absolute;
    right: 16px;
    top: 12px
}

.el-alert .el-alert__close-btn.is-customed {
    font-size: var(--el-alert-close-customed-font-size);
    font-style: normal;
    line-height: 24px;
    top: 8px
}

.el-alert-fade-enter-from, .el-alert-fade-leave-active {
    opacity: 0
}

.el-aside {
    box-sizing: border-box;
    flex-shrink: 0;
    overflow: auto;
    width: var(--el-aside-width, 300px)
}

.el-autocomplete {
    --el-input-text-color: var(--el-text-color-regular);
    --el-input-border: var(--el-border);
    --el-input-hover-border: var(--el-border-color-hover);
    --el-input-focus-border: var(--el-color-primary);
    --el-input-transparent-border: 0 0 0 1px transparent inset;
    --el-input-border-color: var(--el-border-color);
    --el-input-border-radius: var(--el-border-radius-base);
    --el-input-bg-color: var(--el-fill-color-blank);
    --el-input-icon-color: var(--el-text-color-placeholder);
    --el-input-placeholder-color: var(--el-text-color-placeholder);
    --el-input-hover-border-color: var(--el-border-color-hover);
    --el-input-clear-hover-color: var(--el-text-color-secondary);
    --el-input-focus-border-color: var(--el-color-primary);
    --el-input-width: 100%;
    display: inline-block;
    position: relative;
    width: var(--el-input-width)
}

.el-autocomplete__popper.el-popper {
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light)
}

.el-autocomplete__popper.el-popper, .el-autocomplete__popper.el-popper .el-popper__arrow:before {
    border: 1px solid var(--el-border-color-light)
}

.el-autocomplete__popper.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-autocomplete__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-autocomplete__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-autocomplete__popper.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-autocomplete-suggestion {
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box
}

.el-autocomplete-suggestion__wrap {
    box-sizing: border-box;
    max-height: 280px;
    padding: 10px 0
}

.el-autocomplete-suggestion__list {
    margin: 0;
    padding: 0
}

.el-autocomplete-suggestion li {
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: var(--el-font-size-base);
    line-height: 34px;
    list-style: none;
    margin: 0;
    overflow: hidden;
    padding: 0 20px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-autocomplete-suggestion li.highlighted, .el-autocomplete-suggestion li:hover {
    background-color: var(--el-fill-color-light)
}

.el-autocomplete-suggestion li.divider {
    border-top: 1px solid var(--el-color-black);
    margin-top: 6px
}

.el-autocomplete-suggestion li.divider:last-child {
    margin-bottom: -6px
}

.el-autocomplete-suggestion.is-loading li {
    color: var(--el-text-color-secondary);
    font-size: 20px;
    height: 100px;
    line-height: 100px;
    text-align: center
}

.el-autocomplete-suggestion.is-loading li:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.el-autocomplete-suggestion.is-loading li:hover {
    background-color: var(--el-bg-color-overlay)
}

.el-autocomplete-suggestion.is-loading .el-icon-loading {
    vertical-align: middle
}

.el-avatar {
    --el-avatar-text-color: var(--el-color-white);
    --el-avatar-bg-color: var(--el-text-color-disabled);
    --el-avatar-text-size: 14px;
    --el-avatar-icon-size: 18px;
    --el-avatar-border-radius: var(--el-border-radius-base);
    --el-avatar-size-large: 56px;
    --el-avatar-size-small: 24px;
    --el-avatar-size: 40px;
    align-items: center;
    background: var(--el-avatar-bg-color);
    box-sizing: border-box;
    color: var(--el-avatar-text-color);
    display: inline-flex;
    font-size: var(--el-avatar-text-size);
    height: var(--el-avatar-size);
    justify-content: center;
    outline: none;
    overflow: hidden;
    text-align: center;
    width: var(--el-avatar-size)
}

.el-avatar > img {
    display: block;
    height: 100%;
    width: 100%
}

.el-avatar--circle {
    border-radius: 50%
}

.el-avatar--square {
    border-radius: var(--el-avatar-border-radius)
}

.el-avatar--icon {
    font-size: var(--el-avatar-icon-size)
}

.el-avatar--small {
    --el-avatar-size: 24px
}

.el-avatar--large {
    --el-avatar-size: 56px
}

.el-backtop {
    --el-backtop-bg-color: var(--el-bg-color-overlay);
    --el-backtop-text-color: var(--el-color-primary);
    --el-backtop-hover-bg-color: var(--el-border-color-extra-light);
    align-items: center;
    background-color: var(--el-backtop-bg-color);
    border-radius: 50%;
    box-shadow: var(--el-box-shadow-lighter);
    color: var(--el-backtop-text-color);
    cursor: pointer;
    display: flex;
    font-size: 20px;
    height: 40px;
    justify-content: center;
    position: fixed;
    width: 40px;
    z-index: 5
}

.el-backtop:hover {
    background-color: var(--el-backtop-hover-bg-color)
}

.el-backtop__icon {
    font-size: 20px
}

.el-badge {
    --el-badge-bg-color: var(--el-color-danger);
    --el-badge-radius: 10px;
    --el-badge-font-size: 12px;
    --el-badge-padding: 6px;
    --el-badge-size: 18px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    width: -moz-fit-content;
    width: fit-content
}

.el-badge__content {
    align-items: center;
    background-color: var(--el-badge-bg-color);
    border: 1px solid var(--el-bg-color);
    border-radius: var(--el-badge-radius);
    color: var(--el-color-white);
    display: inline-flex;
    font-size: var(--el-badge-font-size);
    height: var(--el-badge-size);
    justify-content: center;
    padding: 0 var(--el-badge-padding);
    white-space: nowrap
}

.el-badge__content.is-fixed {
    position: absolute;
    right: calc(1px + var(--el-badge-size) / 2);
    top: 0;
    transform: translateY(-50%) translateX(100%);
    z-index: var(--el-index-normal)
}

.el-badge__content.is-fixed.is-dot {
    right: 5px
}

.el-badge__content.is-dot {
    border-radius: 50%;
    height: 8px;
    padding: 0;
    right: 0;
    width: 8px
}

.el-badge__content.is-hide-zero {
    display: none
}

.el-badge__content--primary {
    background-color: var(--el-color-primary)
}

.el-badge__content--success {
    background-color: var(--el-color-success)
}

.el-badge__content--warning {
    background-color: var(--el-color-warning)
}

.el-badge__content--info {
    background-color: var(--el-color-info)
}

.el-badge__content--danger {
    background-color: var(--el-color-danger)
}

.el-breadcrumb {
    font-size: 14px;
    line-height: 1
}

.el-breadcrumb:after, .el-breadcrumb:before {
    content: "";
    display: table
}

.el-breadcrumb:after {
    clear: both
}

.el-breadcrumb__separator {
    color: var(--el-text-color-placeholder);
    font-weight: bold;
    margin: 0 9px
}

.el-breadcrumb__separator.el-icon {
    font-weight: normal;
    margin: 0 6px
}

.el-breadcrumb__separator.el-icon svg {
    vertical-align: middle
}

.el-breadcrumb__item {
    align-items: center;
    display: inline-flex;
    float: left
}

.el-breadcrumb__inner {
    color: var(--el-text-color-regular)
}

.el-breadcrumb__inner a, .el-breadcrumb__inner.is-link {
    color: var(--el-text-color-primary);
    font-weight: bold;
    text-decoration: none;
    transition: var(--el-transition-color)
}

.el-breadcrumb__inner a:hover, .el-breadcrumb__inner.is-link:hover {
    color: var(--el-color-primary);
    cursor: pointer
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner, .el-breadcrumb__item:last-child .el-breadcrumb__inner a, .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover, .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
    color: var(--el-text-color-regular);
    cursor: text;
    font-weight: normal
}

.el-breadcrumb__item:last-child .el-breadcrumb__separator {
    display: none
}

.el-button-group {
    display: inline-block;
    vertical-align: middle
}

.el-button-group:after, .el-button-group:before {
    content: "";
    display: table
}

.el-button-group:after {
    clear: both
}

.el-button-group > .el-button {
    float: left;
    position: relative
}

.el-button-group > .el-button + .el-button {
    margin-left: 0
}

.el-button-group > .el-button:first-child {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.el-button-group > .el-button:last-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.el-button-group > .el-button:first-child:last-child {
    border-bottom-left-radius: var(--el-border-radius-base);
    border-bottom-right-radius: var(--el-border-radius-base);
    border-top-left-radius: var(--el-border-radius-base);
    border-top-right-radius: var(--el-border-radius-base)
}

.el-button-group > .el-button:first-child:last-child.is-round {
    border-radius: var(--el-border-radius-round)
}

.el-button-group > .el-button:first-child:last-child.is-circle {
    border-radius: 50%
}

.el-button-group > .el-button:not(:first-child):not(:last-child) {
    border-radius: 0
}

.el-button-group > .el-button:not(:last-child) {
    margin-right: -1px
}

.el-button-group > .el-button.is-active, .el-button-group > .el-button:active, .el-button-group > .el-button:focus, .el-button-group > .el-button:hover {
    z-index: 1
}

.el-button-group > .el-dropdown > .el-button {
    border-bottom-left-radius: 0;
    border-left-color: var(--el-button-divide-border-color);
    border-top-left-radius: 0
}

.el-button-group .el-button--primary:first-child {
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--primary:last-child {
    border-left-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--primary:not(:first-child):not(:last-child) {
    border-left-color: var(--el-button-divide-border-color);
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--success:first-child {
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--success:last-child {
    border-left-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--success:not(:first-child):not(:last-child) {
    border-left-color: var(--el-button-divide-border-color);
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--warning:first-child {
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--warning:last-child {
    border-left-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--warning:not(:first-child):not(:last-child) {
    border-left-color: var(--el-button-divide-border-color);
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--danger:first-child {
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--danger:last-child {
    border-left-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--danger:not(:first-child):not(:last-child) {
    border-left-color: var(--el-button-divide-border-color);
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--info:first-child {
    border-right-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--info:last-child {
    border-left-color: var(--el-button-divide-border-color)
}

.el-button-group .el-button--info:not(:first-child):not(:last-child) {
    border-left-color: var(--el-button-divide-border-color);
    border-right-color: var(--el-button-divide-border-color)
}

.el-button {
    --el-button-font-weight: var(--el-font-weight-primary);
    --el-button-border-color: var(--el-border-color);
    --el-button-bg-color: var(--el-fill-color-blank);
    --el-button-text-color: var(--el-text-color-regular);
    --el-button-disabled-text-color: var(--el-disabled-text-color);
    --el-button-disabled-bg-color: var(--el-fill-color-blank);
    --el-button-disabled-border-color: var(--el-border-color-light);
    --el-button-divide-border-color: rgba(255, 255, 255, 0.5);
    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-color-primary-light-9);
    --el-button-hover-border-color: var(--el-color-primary-light-7);
    --el-button-active-text-color: var(--el-button-hover-text-color);
    --el-button-active-border-color: var(--el-color-primary);
    --el-button-active-bg-color: var(--el-button-hover-bg-color);
    --el-button-outline-color: var(--el-color-primary-light-5);
    --el-button-hover-link-text-color: var(--el-text-color-secondary);
    --el-button-active-color: var(--el-text-color-primary);
    align-items: center;
    -webkit-appearance: none;
    background-color: var(--el-button-bg-color);
    border: var(--el-border);
    border-color: var(--el-button-border-color);
    box-sizing: border-box;
    color: var(--el-button-text-color);
    cursor: pointer;
    display: inline-flex;
    font-weight: var(--el-button-font-weight);
    height: 32px;
    justify-content: center;
    line-height: 1;
    outline: none;
    text-align: center;
    transition: .1s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap
}

.el-button:hover {
    background-color: var(--el-button-hover-bg-color);
    border-color: var(--el-button-hover-border-color);
    color: var(--el-button-hover-text-color);
    outline: none
}

.el-button:active {
    background-color: var(--el-button-active-bg-color);
    border-color: var(--el-button-active-border-color);
    color: var(--el-button-active-text-color);
    outline: none
}

.el-button:focus-visible {
    outline: 2px solid var(--el-button-outline-color);
    outline-offset: 1px;
    transition: outline-offset 0s, outline 0s
}

.el-button > span {
    align-items: center;
    display: inline-flex
}

.el-button + .el-button {
    margin-left: 12px
}

.el-button {
    border-radius: var(--el-border-radius-base);
    font-size: var(--el-font-size-base)
}

.el-button, .el-button.is-round {
    padding: 8px 15px
}

.el-button::-moz-focus-inner {
    border: 0
}

.el-button [class*=el-icon] + span {
    margin-left: 6px
}

.el-button [class*=el-icon] svg {
    vertical-align: bottom
}

.el-button.is-plain {
    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-fill-color-blank);
    --el-button-hover-border-color: var(--el-color-primary)
}

.el-button.is-active {
    background-color: var(--el-button-active-bg-color);
    border-color: var(--el-button-active-border-color);
    color: var(--el-button-active-text-color);
    outline: none
}

.el-button.is-disabled, .el-button.is-disabled:hover {
    background-color: var(--el-button-disabled-bg-color);
    background-image: none;
    border-color: var(--el-button-disabled-border-color);
    color: var(--el-button-disabled-text-color);
    cursor: not-allowed
}

.el-button.is-loading {
    pointer-events: none;
    position: relative
}

.el-button.is-loading:before {
    background-color: var(--el-mask-color-extra-light);
    border-radius: inherit;
    bottom: -1px;
    content: "";
    left: -1px;
    pointer-events: none;
    position: absolute;
    right: -1px;
    top: -1px;
    z-index: 1
}

.el-button.is-round {
    border-radius: var(--el-border-radius-round)
}

.el-button.is-circle {
    border-radius: 50%;
    padding: 8px;
    width: 32px
}

.el-button.is-text {
    background-color: transparent;
    border: 0 solid transparent;
    color: var(--el-button-text-color)
}

.el-button.is-text.is-disabled {
    background-color: transparent !important;
    color: var(--el-button-disabled-text-color)
}

.el-button.is-text:not(.is-disabled):hover {
    background-color: var(--el-fill-color-light)
}

.el-button.is-text:not(.is-disabled):focus-visible {
    outline: 2px solid var(--el-button-outline-color);
    outline-offset: 1px;
    transition: outline-offset 0s, outline 0s
}

.el-button.is-text:not(.is-disabled):active {
    background-color: var(--el-fill-color)
}

.el-button.is-text:not(.is-disabled).is-has-bg {
    background-color: var(--el-fill-color-light)
}

.el-button.is-text:not(.is-disabled).is-has-bg:hover {
    background-color: var(--el-fill-color)
}

.el-button.is-text:not(.is-disabled).is-has-bg:active {
    background-color: var(--el-fill-color-dark)
}

.el-button__text--expand {
    letter-spacing: .3em;
    margin-right: -.3em
}

.el-button.is-link {
    background: transparent;
    border-color: transparent;
    color: var(--el-button-text-color);
    height: auto;
    padding: 2px
}

.el-button.is-link:hover {
    color: var(--el-button-hover-link-text-color)
}

.el-button.is-link.is-disabled {
    background-color: transparent !important;
    border-color: transparent !important;
    color: var(--el-button-disabled-text-color)
}

.el-button.is-link:not(.is-disabled):active, .el-button.is-link:not(.is-disabled):hover {
    background-color: transparent;
    border-color: transparent
}

.el-button.is-link:not(.is-disabled):active {
    color: var(--el-button-active-color)
}

.el-button--text {
    background: transparent;
    border-color: transparent;
    color: var(--el-color-primary);
    padding-left: 0;
    padding-right: 0
}

.el-button--text.is-disabled {
    background-color: transparent !important;
    border-color: transparent !important;
    color: var(--el-button-disabled-text-color)
}

.el-button--text:not(.is-disabled):hover {
    background-color: transparent;
    border-color: transparent;
    color: var(--el-color-primary-light-3)
}

.el-button--text:not(.is-disabled):active {
    background-color: transparent;
    border-color: transparent;
    color: var(--el-color-primary-dark-2)
}

.el-button__link--expand {
    letter-spacing: .3em;
    margin-right: -.3em
}

.el-button--primary {
    --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-primary);
    --el-button-border-color: var(--el-color-primary);
    --el-button-outline-color: var(--el-color-primary-light-5);
    --el-button-active-color: var(--el-color-primary-dark-2);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-primary-light-5);
    --el-button-hover-bg-color: var(--el-color-primary-light-3);
    --el-button-hover-border-color: var(--el-color-primary-light-3);
    --el-button-active-bg-color: var(--el-color-primary-dark-2);
    --el-button-active-border-color: var(--el-color-primary-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--el-color-primary-light-5);
    --el-button-disabled-border-color: var(--el-color-primary-light-5)
}

.el-button--primary.is-link, .el-button--primary.is-plain, .el-button--primary.is-text {
    --el-button-text-color: var(--el-color-primary);
    --el-button-bg-color: var(--el-color-primary-light-9);
    --el-button-border-color: var(--el-color-primary-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-bg-color: var(--el-color-primary);
    --el-button-hover-border-color: var(--el-color-primary);
    --el-button-active-text-color: var(--el-color-white)
}

.el-button--primary.is-link.is-disabled, .el-button--primary.is-link.is-disabled:active, .el-button--primary.is-link.is-disabled:focus, .el-button--primary.is-link.is-disabled:hover, .el-button--primary.is-plain.is-disabled, .el-button--primary.is-plain.is-disabled:active, .el-button--primary.is-plain.is-disabled:focus, .el-button--primary.is-plain.is-disabled:hover, .el-button--primary.is-text.is-disabled, .el-button--primary.is-text.is-disabled:active, .el-button--primary.is-text.is-disabled:focus, .el-button--primary.is-text.is-disabled:hover {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary-light-5)
}

.el-button--success {
    --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-success);
    --el-button-border-color: var(--el-color-success);
    --el-button-outline-color: var(--el-color-success-light-5);
    --el-button-active-color: var(--el-color-success-dark-2);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-success-light-5);
    --el-button-hover-bg-color: var(--el-color-success-light-3);
    --el-button-hover-border-color: var(--el-color-success-light-3);
    --el-button-active-bg-color: var(--el-color-success-dark-2);
    --el-button-active-border-color: var(--el-color-success-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--el-color-success-light-5);
    --el-button-disabled-border-color: var(--el-color-success-light-5)
}

.el-button--success.is-link, .el-button--success.is-plain, .el-button--success.is-text {
    --el-button-text-color: var(--el-color-success);
    --el-button-bg-color: var(--el-color-success-light-9);
    --el-button-border-color: var(--el-color-success-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-bg-color: var(--el-color-success);
    --el-button-hover-border-color: var(--el-color-success);
    --el-button-active-text-color: var(--el-color-white)
}

.el-button--success.is-link.is-disabled, .el-button--success.is-link.is-disabled:active, .el-button--success.is-link.is-disabled:focus, .el-button--success.is-link.is-disabled:hover, .el-button--success.is-plain.is-disabled, .el-button--success.is-plain.is-disabled:active, .el-button--success.is-plain.is-disabled:focus, .el-button--success.is-plain.is-disabled:hover, .el-button--success.is-text.is-disabled, .el-button--success.is-text.is-disabled:active, .el-button--success.is-text.is-disabled:focus, .el-button--success.is-text.is-disabled:hover {
    background-color: var(--el-color-success-light-9);
    border-color: var(--el-color-success-light-8);
    color: var(--el-color-success-light-5)
}

.el-button--warning {
    --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-warning);
    --el-button-border-color: var(--el-color-warning);
    --el-button-outline-color: var(--el-color-warning-light-5);
    --el-button-active-color: var(--el-color-warning-dark-2);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-warning-light-5);
    --el-button-hover-bg-color: var(--el-color-warning-light-3);
    --el-button-hover-border-color: var(--el-color-warning-light-3);
    --el-button-active-bg-color: var(--el-color-warning-dark-2);
    --el-button-active-border-color: var(--el-color-warning-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--el-color-warning-light-5);
    --el-button-disabled-border-color: var(--el-color-warning-light-5)
}

.el-button--warning.is-link, .el-button--warning.is-plain, .el-button--warning.is-text {
    --el-button-text-color: var(--el-color-warning);
    --el-button-bg-color: var(--el-color-warning-light-9);
    --el-button-border-color: var(--el-color-warning-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-bg-color: var(--el-color-warning);
    --el-button-hover-border-color: var(--el-color-warning);
    --el-button-active-text-color: var(--el-color-white)
}

.el-button--warning.is-link.is-disabled, .el-button--warning.is-link.is-disabled:active, .el-button--warning.is-link.is-disabled:focus, .el-button--warning.is-link.is-disabled:hover, .el-button--warning.is-plain.is-disabled, .el-button--warning.is-plain.is-disabled:active, .el-button--warning.is-plain.is-disabled:focus, .el-button--warning.is-plain.is-disabled:hover, .el-button--warning.is-text.is-disabled, .el-button--warning.is-text.is-disabled:active, .el-button--warning.is-text.is-disabled:focus, .el-button--warning.is-text.is-disabled:hover {
    background-color: var(--el-color-warning-light-9);
    border-color: var(--el-color-warning-light-8);
    color: var(--el-color-warning-light-5)
}

.el-button--danger {
    --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-danger);
    --el-button-border-color: var(--el-color-danger);
    --el-button-outline-color: var(--el-color-danger-light-5);
    --el-button-active-color: var(--el-color-danger-dark-2);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-danger-light-5);
    --el-button-hover-bg-color: var(--el-color-danger-light-3);
    --el-button-hover-border-color: var(--el-color-danger-light-3);
    --el-button-active-bg-color: var(--el-color-danger-dark-2);
    --el-button-active-border-color: var(--el-color-danger-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--el-color-danger-light-5);
    --el-button-disabled-border-color: var(--el-color-danger-light-5)
}

.el-button--danger.is-link, .el-button--danger.is-plain, .el-button--danger.is-text {
    --el-button-text-color: var(--el-color-danger);
    --el-button-bg-color: var(--el-color-danger-light-9);
    --el-button-border-color: var(--el-color-danger-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-bg-color: var(--el-color-danger);
    --el-button-hover-border-color: var(--el-color-danger);
    --el-button-active-text-color: var(--el-color-white)
}

.el-button--danger.is-link.is-disabled, .el-button--danger.is-link.is-disabled:active, .el-button--danger.is-link.is-disabled:focus, .el-button--danger.is-link.is-disabled:hover, .el-button--danger.is-plain.is-disabled, .el-button--danger.is-plain.is-disabled:active, .el-button--danger.is-plain.is-disabled:focus, .el-button--danger.is-plain.is-disabled:hover, .el-button--danger.is-text.is-disabled, .el-button--danger.is-text.is-disabled:active, .el-button--danger.is-text.is-disabled:focus, .el-button--danger.is-text.is-disabled:hover {
    background-color: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger-light-8);
    color: var(--el-color-danger-light-5)
}

.el-button--info {
    --el-button-text-color: var(--el-color-white);
    --el-button-bg-color: var(--el-color-info);
    --el-button-border-color: var(--el-color-info);
    --el-button-outline-color: var(--el-color-info-light-5);
    --el-button-active-color: var(--el-color-info-dark-2);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-link-text-color: var(--el-color-info-light-5);
    --el-button-hover-bg-color: var(--el-color-info-light-3);
    --el-button-hover-border-color: var(--el-color-info-light-3);
    --el-button-active-bg-color: var(--el-color-info-dark-2);
    --el-button-active-border-color: var(--el-color-info-dark-2);
    --el-button-disabled-text-color: var(--el-color-white);
    --el-button-disabled-bg-color: var(--el-color-info-light-5);
    --el-button-disabled-border-color: var(--el-color-info-light-5)
}

.el-button--info.is-link, .el-button--info.is-plain, .el-button--info.is-text {
    --el-button-text-color: var(--el-color-info);
    --el-button-bg-color: var(--el-color-info-light-9);
    --el-button-border-color: var(--el-color-info-light-5);
    --el-button-hover-text-color: var(--el-color-white);
    --el-button-hover-bg-color: var(--el-color-info);
    --el-button-hover-border-color: var(--el-color-info);
    --el-button-active-text-color: var(--el-color-white)
}

.el-button--info.is-link.is-disabled, .el-button--info.is-link.is-disabled:active, .el-button--info.is-link.is-disabled:focus, .el-button--info.is-link.is-disabled:hover, .el-button--info.is-plain.is-disabled, .el-button--info.is-plain.is-disabled:active, .el-button--info.is-plain.is-disabled:focus, .el-button--info.is-plain.is-disabled:hover, .el-button--info.is-text.is-disabled, .el-button--info.is-text.is-disabled:active, .el-button--info.is-text.is-disabled:focus, .el-button--info.is-text.is-disabled:hover {
    background-color: var(--el-color-info-light-9);
    border-color: var(--el-color-info-light-8);
    color: var(--el-color-info-light-5)
}

.el-button--large {
    --el-button-size: 40px;
    height: var(--el-button-size)
}

.el-button--large [class*=el-icon] + span {
    margin-left: 8px
}

.el-button--large {
    border-radius: var(--el-border-radius-base);
    font-size: var(--el-font-size-base);
    padding: 12px 19px
}

.el-button--large.is-round {
    padding: 12px 19px
}

.el-button--large.is-circle {
    padding: 12px;
    width: var(--el-button-size)
}

.el-button--small {
    --el-button-size: 24px;
    height: var(--el-button-size)
}

.el-button--small [class*=el-icon] + span {
    margin-left: 4px
}

.el-button--small {
    border-radius: calc(var(--el-border-radius-base) - 1px);
    font-size: 12px;
    padding: 5px 11px
}

.el-button--small.is-round {
    padding: 5px 11px
}

.el-button--small.is-circle {
    padding: 5px;
    width: var(--el-button-size)
}

.el-calendar {
    --el-calendar-border: var(--el-table-border, 1px solid var(--el-border-color-lighter));
    --el-calendar-header-border-bottom: var(--el-calendar-border);
    --el-calendar-selected-bg-color: var(--el-color-primary-light-9);
    --el-calendar-cell-width: 85px;
    background-color: var(--el-fill-color-blank)
}

.el-calendar__header {
    border-bottom: var(--el-calendar-header-border-bottom);
    display: flex;
    justify-content: space-between;
    padding: 12px 20px
}

.el-calendar__title {
    align-self: center;
    color: var(--el-text-color)
}

.el-calendar__body {
    padding: 12px 20px 35px
}

.el-calendar-table {
    table-layout: fixed;
    width: 100%
}

.el-calendar-table thead th {
    color: var(--el-text-color-regular);
    font-weight: normal;
    padding: 12px 0
}

.el-calendar-table:not(.is-range) td.next, .el-calendar-table:not(.is-range) td.prev {
    color: var(--el-text-color-placeholder)
}

.el-calendar-table td {
    border-bottom: var(--el-calendar-border);
    border-right: var(--el-calendar-border);
    transition: background-color var(--el-transition-duration-fast) ease;
    vertical-align: top
}

.el-calendar-table td.is-selected {
    background-color: var(--el-calendar-selected-bg-color)
}

.el-calendar-table td.is-today {
    color: var(--el-color-primary)
}

.el-calendar-table tr:first-child td {
    border-top: var(--el-calendar-border)
}

.el-calendar-table tr td:first-child {
    border-left: var(--el-calendar-border)
}

.el-calendar-table tr.el-calendar-table__row--hide-border td {
    border-top: none
}

.el-calendar-table .el-calendar-day {
    box-sizing: border-box;
    height: var(--el-calendar-cell-width);
    padding: 8px
}

.el-calendar-table .el-calendar-day:hover {
    background-color: var(--el-calendar-selected-bg-color);
    cursor: pointer
}

.el-card {
    --el-card-border-color: var(--el-border-color-light);
    --el-card-border-radius: 4px;
    --el-card-padding: 20px;
    --el-card-bg-color: var(--el-fill-color-blank);
    background-color: var(--el-card-bg-color);
    border-radius: var(--el-card-border-radius);
    color: var(--el-text-color-primary);
    overflow: hidden;
    transition: var(--el-transition-duration)
}

.el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
    box-shadow: var(--el-box-shadow-light)
}

.el-card__header {
    border-bottom: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
    padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding)
}

.el-card__body {
    padding: calc(var(--el-card-padding) - 8px)
}

.el-card__footer {
    border-top: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
    padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding)
}

.el-carousel__item {
    display: inline-block;
    height: 100%;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%
}

.el-carousel__item, .el-carousel__item.is-active {
    z-index: calc(var(--el-index-normal) - 1)
}

.el-carousel__item--card, .el-carousel__item.is-animating {
    transition: transform .4s ease-in-out
}

.el-carousel__item--card {
    width: 50%
}

.el-carousel__item--card.is-in-stage {
    cursor: pointer;
    z-index: var(--el-index-normal)
}

.el-carousel__item--card.is-in-stage.is-hover .el-carousel__mask, .el-carousel__item--card.is-in-stage:hover .el-carousel__mask {
    opacity: .12
}

.el-carousel__item--card.is-active {
    z-index: calc(var(--el-index-normal) + 1)
}

.el-carousel__item--card-vertical {
    height: 50%;
    width: 100%
}

.el-carousel__mask {
    background-color: var(--el-color-white);
    height: 100%;
    left: 0;
    opacity: .24;
    position: absolute;
    top: 0;
    transition: var(--el-transition-duration-fast);
    width: 100%
}

.el-carousel {
    --el-carousel-arrow-font-size: 12px;
    --el-carousel-arrow-size: 36px;
    --el-carousel-arrow-background: rgba(31, 45, 61, 0.11);
    --el-carousel-arrow-hover-background: rgba(31, 45, 61, 0.23);
    --el-carousel-indicator-width: 30px;
    --el-carousel-indicator-height: 2px;
    --el-carousel-indicator-padding-horizontal: 4px;
    --el-carousel-indicator-padding-vertical: 12px;
    --el-carousel-indicator-out-color: var(--el-border-color-hover);
    position: relative
}

.el-carousel--horizontal, .el-carousel--vertical {
    overflow: hidden
}

.el-carousel__container {
    height: 300px;
    position: relative
}

.el-carousel__arrow {
    align-items: center;
    background-color: var(--el-carousel-arrow-background);
    border: none;
    border-radius: 50%;
    color: #ffffff;
    cursor: pointer;
    display: inline-flex;
    font-size: var(--el-carousel-arrow-font-size);
    height: var(--el-carousel-arrow-size);
    justify-content: center;
    margin: 0;
    outline: none;
    padding: 0;
    position: absolute;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
    transition: var(--el-transition-duration);
    width: var(--el-carousel-arrow-size);
    z-index: 10
}

.el-carousel__arrow--left {
    left: 16px
}

.el-carousel__arrow--right {
    right: 16px
}

.el-carousel__arrow:hover {
    background-color: var(--el-carousel-arrow-hover-background)
}

.el-carousel__arrow i {
    cursor: pointer
}

.el-carousel__indicators {
    list-style: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: calc(var(--el-index-normal) + 1)
}

.el-carousel__indicators--horizontal {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

.el-carousel__indicators--vertical {
    right: 0;
    top: 50%;
    transform: translateY(-50%)
}

.el-carousel__indicators--outside {
    bottom: calc(var(--el-carousel-indicator-height) + var(--el-carousel-indicator-padding-vertical) * 2);
    position: static;
    text-align: center;
    transform: none
}

.el-carousel__indicators--outside .el-carousel__indicator:hover button {
    opacity: .64
}

.el-carousel__indicators--outside button {
    background-color: var(--el-carousel-indicator-out-color);
    opacity: .24
}

.el-carousel__indicators--right {
    right: 0
}

.el-carousel__indicators--labels {
    left: 0;
    right: 0;
    text-align: center;
    transform: none
}

.el-carousel__indicators--labels .el-carousel__button {
    color: #000000;
    font-size: 12px;
    height: auto;
    padding: 2px 18px;
    width: auto
}

.el-carousel__indicators--labels .el-carousel__indicator {
    padding: 6px 4px
}

.el-carousel__indicator {
    background-color: transparent;
    cursor: pointer
}

.el-carousel__indicator:hover button {
    opacity: .72
}

.el-carousel__indicator--horizontal {
    display: inline-block;
    padding: var(--el-carousel-indicator-padding-vertical) var(--el-carousel-indicator-padding-horizontal)
}

.el-carousel__indicator--vertical {
    padding: var(--el-carousel-indicator-padding-horizontal) var(--el-carousel-indicator-padding-vertical)
}

.el-carousel__indicator--vertical .el-carousel__button {
    height: calc(var(--el-carousel-indicator-width) / 2);
    width: var(--el-carousel-indicator-height)
}

.el-carousel__indicator.is-active button {
    opacity: 1
}

.el-carousel__button {
    background-color: #ffffff;
    border: none;
    cursor: pointer;
    display: block;
    height: var(--el-carousel-indicator-height);
    margin: 0;
    opacity: .48;
    outline: none;
    padding: 0;
    transition: var(--el-transition-duration);
    width: var(--el-carousel-indicator-width)
}

.carousel-arrow-left-enter-from, .carousel-arrow-left-leave-active {
    opacity: 0;
    transform: translateY(-50%) translateX(-10px)
}

.carousel-arrow-right-enter-from, .carousel-arrow-right-leave-active {
    opacity: 0;
    transform: translateY(-50%) translateX(10px)
}

.el-transitioning {
    filter: url(#elCarouselHorizontal)
}

.el-transitioning-vertical {
    filter: url(#elCarouselVertical)
}

.el-cascader-panel {
    --el-cascader-menu-text-color: var(--el-text-color-regular);
    --el-cascader-menu-selected-text-color: var(--el-color-primary);
    --el-cascader-menu-fill: var(--el-bg-color-overlay);
    --el-cascader-menu-font-size: var(--el-font-size-base);
    --el-cascader-menu-radius: var(--el-border-radius-base);
    --el-cascader-menu-border: solid 1px var(--el-border-color-light);
    --el-cascader-menu-shadow: var(--el-box-shadow-light);
    --el-cascader-node-background-hover: var(--el-fill-color-light);
    --el-cascader-node-color-disabled: var(--el-text-color-placeholder);
    --el-cascader-color-empty: var(--el-text-color-placeholder);
    --el-cascader-tag-background: var(--el-fill-color);
    border-radius: var(--el-cascader-menu-radius);
    display: flex;
    font-size: var(--el-cascader-menu-font-size)
}

.el-cascader-panel.is-bordered {
    border: var(--el-cascader-menu-border);
    border-radius: var(--el-cascader-menu-radius)
}

.el-cascader-menu {
    border-right: var(--el-cascader-menu-border);
    box-sizing: border-box;
    color: var(--el-cascader-menu-text-color);
    min-width: 180px
}

.el-cascader-menu:last-child {
    border-right: none
}

.el-cascader-menu:last-child .el-cascader-node {
    padding-right: 20px
}

.el-cascader-menu__wrap.el-scrollbar__wrap {
    height: 204px
}

.el-cascader-menu__list {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    min-height: 100%;
    padding: 6px 0;
    position: relative
}

.el-cascader-menu__hover-zone {
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%
}

.el-cascader-menu__empty-text {
    align-items: center;
    color: var(--el-cascader-color-empty);
    display: flex;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.el-cascader-menu__empty-text .is-loading {
    margin-right: 2px
}

.el-cascader-node {
    align-items: center;
    display: flex;
    height: 34px;
    line-height: 34px;
    outline: none;
    padding: 0 30px 0 20px;
    position: relative
}

.el-cascader-node.is-selectable.in-active-path {
    color: var(--el-cascader-menu-text-color)
}

.el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
    color: var(--el-cascader-menu-selected-text-color);
    font-weight: bold
}

.el-cascader-node:not(.is-disabled) {
    cursor: pointer
}

.el-cascader-node:not(.is-disabled):focus, .el-cascader-node:not(.is-disabled):hover {
    background: var(--el-cascader-node-background-hover)
}

.el-cascader-node.is-disabled {
    color: var(--el-cascader-node-color-disabled);
    cursor: not-allowed
}

.el-cascader-node__prefix {
    left: 10px;
    position: absolute
}

.el-cascader-node__postfix {
    position: absolute;
    right: 10px
}

.el-cascader-node__label {
    flex: 1;
    overflow: hidden;
    padding: 0 8px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-cascader-node > .el-checkbox, .el-cascader-node > .el-radio {
    margin-right: 0
}

.el-cascader-node > .el-radio .el-radio__label {
    padding-left: 0
}

.el-cascader {
    --el-cascader-menu-text-color: var(--el-text-color-regular);
    --el-cascader-menu-selected-text-color: var(--el-color-primary);
    --el-cascader-menu-fill: var(--el-bg-color-overlay);
    --el-cascader-menu-font-size: var(--el-font-size-base);
    --el-cascader-menu-radius: var(--el-border-radius-base);
    --el-cascader-menu-border: solid 1px var(--el-border-color-light);
    --el-cascader-menu-shadow: var(--el-box-shadow-light);
    --el-cascader-node-background-hover: var(--el-fill-color-light);
    --el-cascader-node-color-disabled: var(--el-text-color-placeholder);
    --el-cascader-color-empty: var(--el-text-color-placeholder);
    --el-cascader-tag-background: var(--el-fill-color);
    display: inline-block;
    font-size: var(--el-font-size-base);
    line-height: 32px;
    outline: none;
    position: relative;
    vertical-align: middle
}

.el-cascader:not(.is-disabled):hover .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
    cursor: pointer
}

.el-cascader .el-input {
    cursor: pointer;
    display: flex
}

.el-cascader .el-input .el-input__inner {
    cursor: pointer;
    text-overflow: ellipsis
}

.el-cascader .el-input .el-input__suffix-inner .el-icon svg {
    vertical-align: middle
}

.el-cascader .el-input .icon-arrow-down {
    font-size: 14px;
    transition: transform var(--el-transition-duration)
}

.el-cascader .el-input .icon-arrow-down.is-reverse {
    transform: rotate(180deg)
}

.el-cascader .el-input .icon-circle-close:hover {
    color: var(--el-input-clear-hover-color, var(--el-text-color-secondary))
}

.el-cascader .el-input.is-focus .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color, var(--el-color-primary)) inset
}

.el-cascader--large {
    font-size: 14px;
    line-height: 40px
}

.el-cascader--large .el-cascader__tags {
    gap: 6px;
    padding: 8px
}

.el-cascader--large .el-cascader__search-input {
    height: 24px;
    margin-left: 7px
}

.el-cascader--small {
    font-size: 12px;
    line-height: 24px
}

.el-cascader--small .el-cascader__tags {
    gap: 4px;
    padding: 2px
}

.el-cascader--small .el-cascader__search-input {
    height: 20px;
    margin-left: 5px
}

.el-cascader.is-disabled .el-cascader__label {
    color: var(--el-disabled-text-color);
    z-index: calc(var(--el-index-normal) + 1)
}

.el-cascader__dropdown {
    --el-cascader-menu-text-color: var(--el-text-color-regular);
    --el-cascader-menu-selected-text-color: var(--el-color-primary);
    --el-cascader-menu-fill: var(--el-bg-color-overlay);
    --el-cascader-menu-font-size: var(--el-font-size-base);
    --el-cascader-menu-radius: var(--el-border-radius-base);
    --el-cascader-menu-border: solid 1px var(--el-border-color-light);
    --el-cascader-menu-shadow: var(--el-box-shadow-light);
    --el-cascader-node-background-hover: var(--el-fill-color-light);
    --el-cascader-node-color-disabled: var(--el-text-color-placeholder);
    --el-cascader-color-empty: var(--el-text-color-placeholder);
    --el-cascader-tag-background: var(--el-fill-color);
    border-radius: var(--el-cascader-menu-radius);
    font-size: var(--el-cascader-menu-font-size)
}

.el-cascader__dropdown.el-popper {
    background: var(--el-cascader-menu-fill)
}

.el-cascader__dropdown.el-popper, .el-cascader__dropdown.el-popper .el-popper__arrow:before {
    border: var(--el-cascader-menu-border)
}

.el-cascader__dropdown.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-cascader__dropdown.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-cascader__dropdown.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-cascader__dropdown.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-cascader__dropdown.el-popper {
    box-shadow: var(--el-cascader-menu-shadow)
}

.el-cascader__tags {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    left: 0;
    line-height: normal;
    padding: 4px;
    position: absolute;
    right: 30px;
    text-align: left;
    top: 50%;
    transform: translateY(-50%)
}

.el-cascader__tags .el-tag {
    align-items: center;
    background: var(--el-cascader-tag-background);
    display: inline-flex;
    max-width: 100%;
    text-overflow: ellipsis
}

.el-cascader__tags .el-tag.el-tag--dark, .el-cascader__tags .el-tag.el-tag--plain {
    background-color: var(--el-tag-bg-color)
}

.el-cascader__tags .el-tag:not(.is-hit) {
    border-color: transparent
}

.el-cascader__tags .el-tag:not(.is-hit).el-tag--dark, .el-cascader__tags .el-tag:not(.is-hit).el-tag--plain {
    border-color: var(--el-tag-border-color)
}

.el-cascader__tags .el-tag > span {
    flex: 1;
    line-height: normal;
    overflow: hidden;
    text-overflow: ellipsis
}

.el-cascader__tags .el-tag .el-icon-close {
    background-color: var(--el-text-color-placeholder);
    color: var(--el-color-white);
    flex: none
}

.el-cascader__tags .el-tag .el-icon-close:hover {
    background-color: var(--el-text-color-secondary)
}

.el-cascader__tags .el-tag + input {
    margin-left: 0
}

.el-cascader__tags.is-validate {
    right: 55px
}

.el-cascader__collapse-tags {
    white-space: normal;
    z-index: var(--el-index-normal)
}

.el-cascader__collapse-tags .el-tag {
    align-items: center;
    background: var(--el-fill-color);
    display: inline-flex;
    max-width: 100%;
    text-overflow: ellipsis
}

.el-cascader__collapse-tags .el-tag.el-tag--dark, .el-cascader__collapse-tags .el-tag.el-tag--plain {
    background-color: var(--el-tag-bg-color)
}

.el-cascader__collapse-tags .el-tag:not(.is-hit) {
    border-color: transparent
}

.el-cascader__collapse-tags .el-tag:not(.is-hit).el-tag--dark, .el-cascader__collapse-tags .el-tag:not(.is-hit).el-tag--plain {
    border-color: var(--el-tag-border-color)
}

.el-cascader__collapse-tags .el-tag > span {
    flex: 1;
    line-height: normal;
    overflow: hidden;
    text-overflow: ellipsis
}

.el-cascader__collapse-tags .el-tag .el-icon-close {
    background-color: var(--el-text-color-placeholder);
    color: var(--el-color-white);
    flex: none
}

.el-cascader__collapse-tags .el-tag .el-icon-close:hover {
    background-color: var(--el-text-color-secondary)
}

.el-cascader__collapse-tags .el-tag + input {
    margin-left: 0
}

.el-cascader__collapse-tags .el-tag {
    margin: 2px 0
}

.el-cascader__suggestion-panel {
    border-radius: var(--el-cascader-menu-radius)
}

.el-cascader__suggestion-list {
    color: var(--el-cascader-menu-text-color);
    font-size: var(--el-font-size-base);
    margin: 0;
    max-height: 204px;
    padding: 6px 0;
    text-align: center
}

.el-cascader__suggestion-item {
    align-items: center;
    cursor: pointer;
    display: flex;
    height: 34px;
    justify-content: space-between;
    outline: none;
    padding: 0 15px;
    text-align: left
}

.el-cascader__suggestion-item:focus, .el-cascader__suggestion-item:hover {
    background: var(--el-cascader-node-background-hover)
}

.el-cascader__suggestion-item.is-checked {
    color: var(--el-cascader-menu-selected-text-color);
    font-weight: bold
}

.el-cascader__suggestion-item > span {
    margin-right: 10px
}

.el-cascader__empty-text {
    color: var(--el-cascader-color-empty);
    margin: 10px 0
}

.el-cascader__search-input {
    background: transparent;
    border: none;
    box-sizing: border-box;
    color: var(--el-cascader-menu-text-color);
    flex: 1;
    height: 24px;
    margin-left: 7px;
    min-width: 60px;
    outline: none;
    padding: 0
}

.el-cascader__search-input::-moz-placeholder {
    color: transparent
}

.el-cascader__search-input::placeholder {
    color: transparent
}

.el-check-tag {
    background-color: var(--el-color-info-light-9);
    border-radius: var(--el-border-radius-base);
    color: var(--el-color-info);
    cursor: pointer;
    display: inline-block;
    font-size: var(--el-font-size-base);
    font-weight: bold;
    line-height: var(--el-font-size-base);
    padding: 7px 15px;
    transition: var(--el-transition-all)
}

.el-check-tag:hover {
    background-color: var(--el-color-info-light-7)
}

.el-check-tag.el-check-tag--primary.is-checked {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary)
}

.el-check-tag.el-check-tag--primary.is-checked:hover {
    background-color: var(--el-color-primary-light-7)
}

.el-check-tag.el-check-tag--primary.is-checked.is-disabled {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--primary.is-checked.is-disabled:hover {
    background-color: var(--el-color-primary-light-8)
}

.el-check-tag.el-check-tag--primary.is-disabled {
    background-color: var(--el-color-info-light-9);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--primary.is-disabled:hover {
    background-color: var(--el-color-info-light-9)
}

.el-check-tag.el-check-tag--success.is-checked {
    background-color: var(--el-color-success-light-8);
    color: var(--el-color-success)
}

.el-check-tag.el-check-tag--success.is-checked:hover {
    background-color: var(--el-color-success-light-7)
}

.el-check-tag.el-check-tag--success.is-checked.is-disabled {
    background-color: var(--el-color-success-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--success.is-checked.is-disabled:hover {
    background-color: var(--el-color-success-light-8)
}

.el-check-tag.el-check-tag--success.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--success.is-disabled, .el-check-tag.el-check-tag--success.is-disabled:hover {
    background-color: var(--el-color-success-light-9)
}

.el-check-tag.el-check-tag--warning.is-checked {
    background-color: var(--el-color-warning-light-8);
    color: var(--el-color-warning)
}

.el-check-tag.el-check-tag--warning.is-checked:hover {
    background-color: var(--el-color-warning-light-7)
}

.el-check-tag.el-check-tag--warning.is-checked.is-disabled {
    background-color: var(--el-color-warning-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--warning.is-checked.is-disabled:hover {
    background-color: var(--el-color-warning-light-8)
}

.el-check-tag.el-check-tag--warning.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--warning.is-disabled, .el-check-tag.el-check-tag--warning.is-disabled:hover {
    background-color: var(--el-color-warning-light-9)
}

.el-check-tag.el-check-tag--danger.is-checked {
    background-color: var(--el-color-danger-light-8);
    color: var(--el-color-danger)
}

.el-check-tag.el-check-tag--danger.is-checked:hover {
    background-color: var(--el-color-danger-light-7)
}

.el-check-tag.el-check-tag--danger.is-checked.is-disabled {
    background-color: var(--el-color-danger-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--danger.is-checked.is-disabled:hover {
    background-color: var(--el-color-danger-light-8)
}

.el-check-tag.el-check-tag--danger.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--danger.is-disabled, .el-check-tag.el-check-tag--danger.is-disabled:hover {
    background-color: var(--el-color-danger-light-9)
}

.el-check-tag.el-check-tag--error.is-checked {
    background-color: var(--el-color-error-light-8);
    color: var(--el-color-error)
}

.el-check-tag.el-check-tag--error.is-checked:hover {
    background-color: var(--el-color-error-light-7)
}

.el-check-tag.el-check-tag--error.is-checked.is-disabled {
    background-color: var(--el-color-error-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--error.is-checked.is-disabled:hover {
    background-color: var(--el-color-error-light-8)
}

.el-check-tag.el-check-tag--error.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--error.is-disabled, .el-check-tag.el-check-tag--error.is-disabled:hover {
    background-color: var(--el-color-error-light-9)
}

.el-check-tag.el-check-tag--info.is-checked {
    background-color: var(--el-color-info-light-8);
    color: var(--el-color-info)
}

.el-check-tag.el-check-tag--info.is-checked:hover {
    background-color: var(--el-color-info-light-7)
}

.el-check-tag.el-check-tag--info.is-checked.is-disabled {
    background-color: var(--el-color-info-light-8);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--info.is-checked.is-disabled:hover {
    background-color: var(--el-color-info-light-8)
}

.el-check-tag.el-check-tag--info.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-check-tag.el-check-tag--info.is-disabled, .el-check-tag.el-check-tag--info.is-disabled:hover {
    background-color: var(--el-color-info-light-9)
}

.el-checkbox-button {
    --el-checkbox-button-checked-bg-color: var(--el-color-primary);
    --el-checkbox-button-checked-text-color: var(--el-color-white);
    --el-checkbox-button-checked-border-color: var(--el-color-primary);
    display: inline-block;
    position: relative
}

.el-checkbox-button__inner {
    -webkit-appearance: none;
    background: var(--el-button-bg-color, var(--el-fill-color-blank));
    border: var(--el-border);
    border-left-color: transparent;
    border-radius: 0;
    box-sizing: border-box;
    color: var(--el-button-text-color, var(--el-text-color-regular));
    cursor: pointer;
    display: inline-block;
    font-size: var(--el-font-size-base);
    font-weight: var(--el-checkbox-font-weight);
    line-height: 1;
    margin: 0;
    outline: none;
    padding: 8px 15px;
    position: relative;
    text-align: center;
    transition: var(--el-transition-all);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap
}

.el-checkbox-button__inner.is-round {
    padding: 8px 15px
}

.el-checkbox-button__inner:hover {
    color: var(--el-color-primary)
}

.el-checkbox-button__inner [class*=el-icon-] {
    line-height: .9
}

.el-checkbox-button__inner [class*=el-icon-] + span {
    margin-left: 5px
}

.el-checkbox-button__original {
    margin: 0;
    opacity: 0;
    outline: none;
    position: absolute;
    z-index: -1
}

.el-checkbox-button.is-checked .el-checkbox-button__inner {
    background-color: var(--el-checkbox-button-checked-bg-color);
    border-color: var(--el-checkbox-button-checked-border-color);
    box-shadow: -1px 0 0 0 var(--el-color-primary-light-7);
    color: var(--el-checkbox-button-checked-text-color)
}

.el-checkbox-button.is-checked:first-child .el-checkbox-button__inner {
    border-left-color: var(--el-checkbox-button-checked-border-color)
}

.el-checkbox-button.is-disabled .el-checkbox-button__inner {
    background-color: var(--el-button-disabled-bg-color, var(--el-fill-color-blank));
    background-image: none;
    border-color: var(--el-button-disabled-border-color, var(--el-border-color-light));
    box-shadow: none;
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-checkbox-button.is-disabled:first-child .el-checkbox-button__inner {
    border-left-color: var(--el-button-disabled-border-color, var(--el-border-color-light))
}

.el-checkbox-button:first-child .el-checkbox-button__inner {
    border-bottom-left-radius: var(--el-border-radius-base);
    border-left: var(--el-border);
    border-top-left-radius: var(--el-border-radius-base);
    box-shadow: none !important
}

.el-checkbox-button.is-focus .el-checkbox-button__inner {
    border-color: var(--el-checkbox-button-checked-border-color)
}

.el-checkbox-button:last-child .el-checkbox-button__inner {
    border-bottom-right-radius: var(--el-border-radius-base);
    border-top-right-radius: var(--el-border-radius-base)
}

.el-checkbox-button--large .el-checkbox-button__inner {
    border-radius: 0;
    font-size: var(--el-font-size-base);
    padding: 12px 19px
}

.el-checkbox-button--large .el-checkbox-button__inner.is-round {
    padding: 12px 19px
}

.el-checkbox-button--small .el-checkbox-button__inner {
    border-radius: 0;
    font-size: 12px;
    padding: 5px 11px
}

.el-checkbox-button--small .el-checkbox-button__inner.is-round {
    padding: 5px 11px
}

.el-checkbox-group {
    font-size: 0;
    line-height: 0
}

.el-checkbox {
    --el-checkbox-font-size: 14px;
    --el-checkbox-font-weight: var(--el-font-weight-primary);
    --el-checkbox-text-color: var(--el-text-color-regular);
    --el-checkbox-input-height: 14px;
    --el-checkbox-input-width: 14px;
    --el-checkbox-border-radius: var(--el-border-radius-small);
    --el-checkbox-bg-color: var(--el-fill-color-blank);
    --el-checkbox-input-border: var(--el-border);
    --el-checkbox-disabled-border-color: var(--el-border-color);
    --el-checkbox-disabled-input-fill: var(--el-fill-color-light);
    --el-checkbox-disabled-icon-color: var(--el-text-color-placeholder);
    --el-checkbox-disabled-checked-input-fill: var(--el-border-color-extra-light);
    --el-checkbox-disabled-checked-input-border-color: var(--el-border-color);
    --el-checkbox-disabled-checked-icon-color: var(--el-text-color-placeholder);
    --el-checkbox-checked-text-color: var(--el-color-primary);
    --el-checkbox-checked-input-border-color: var(--el-color-primary);
    --el-checkbox-checked-bg-color: var(--el-color-primary);
    --el-checkbox-checked-icon-color: var(--el-color-white);
    --el-checkbox-input-border-color-hover: var(--el-color-primary);
    align-items: center;
    color: var(--el-checkbox-text-color);
    cursor: pointer;
    display: inline-flex;
    font-size: var(--el-font-size-base);
    font-weight: var(--el-checkbox-font-weight);
    height: var(--el-checkbox-height, 32px);
    margin-right: 30px;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap
}

.el-checkbox.is-disabled {
    cursor: not-allowed
}

.el-checkbox.is-bordered {
    border: var(--el-border);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    padding: 0 15px 0 9px
}

.el-checkbox.is-bordered.is-checked {
    border-color: var(--el-color-primary)
}

.el-checkbox.is-bordered.is-disabled {
    border-color: var(--el-border-color-lighter)
}

.el-checkbox.is-bordered.el-checkbox--large {
    border-radius: var(--el-border-radius-base);
    padding: 0 19px 0 11px
}

.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__label {
    font-size: var(--el-font-size-base)
}

.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__inner {
    height: 14px;
    width: 14px
}

.el-checkbox.is-bordered.el-checkbox--small {
    border-radius: calc(var(--el-border-radius-base) - 1px);
    padding: 0 11px 0 7px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__label {
    font-size: 12px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner {
    height: 12px;
    width: 12px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner:after {
    height: 6px;
    width: 2px
}

.el-checkbox input:focus-visible + .el-checkbox__inner {
    border-radius: var(--el-checkbox-border-radius);
    outline: 2px solid var(--el-checkbox-input-border-color-hover);
    outline-offset: 1px
}

.el-checkbox__input {
    cursor: pointer;
    display: inline-flex;
    outline: none;
    position: relative;
    white-space: nowrap
}

.el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: var(--el-checkbox-disabled-input-fill);
    border-color: var(--el-checkbox-disabled-border-color);
    cursor: not-allowed
}

.el-checkbox__input.is-disabled .el-checkbox__inner:after {
    border-color: var(--el-checkbox-disabled-icon-color);
    cursor: not-allowed
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: var(--el-checkbox-disabled-checked-input-fill);
    border-color: var(--el-checkbox-disabled-checked-input-border-color)
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner:after {
    border-color: var(--el-checkbox-disabled-checked-icon-color)
}

.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
    background-color: var(--el-checkbox-disabled-checked-input-fill);
    border-color: var(--el-checkbox-disabled-checked-input-border-color)
}

.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner:before {
    background-color: var(--el-checkbox-disabled-checked-icon-color);
    border-color: var(--el-checkbox-disabled-checked-icon-color)
}

.el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: var(--el-checkbox-checked-bg-color);
    border-color: var(--el-checkbox-checked-input-border-color)
}

.el-checkbox__input.is-checked .el-checkbox__inner:after {
    border-color: var(--el-checkbox-checked-icon-color);
    transform: rotate(45deg) scaleY(1)
}

.el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--el-checkbox-checked-text-color)
}

.el-checkbox__input.is-focus:not(.is-checked) .el-checkbox__original:not(:focus-visible) {
    border-color: var(--el-checkbox-input-border-color-hover)
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--el-checkbox-checked-bg-color);
    border-color: var(--el-checkbox-checked-input-border-color)
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
    background-color: var(--el-checkbox-checked-icon-color);
    content: "";
    display: block;
    height: 2px;
    left: 0;
    position: absolute;
    right: 0;
    top: 5px;
    transform: scale(.5)
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner:after {
    display: none
}

.el-checkbox__inner {
    background-color: var(--el-checkbox-bg-color);
    border: var(--el-checkbox-input-border);
    border-radius: var(--el-checkbox-border-radius);
    box-sizing: border-box;
    display: inline-block;
    height: var(--el-checkbox-input-height);
    position: relative;
    transition: border-color .25s cubic-bezier(.71, -.46, .29, 1.46), background-color .25s cubic-bezier(.71, -.46, .29, 1.46), outline .25s cubic-bezier(.71, -.46, .29, 1.46);
    width: var(--el-checkbox-input-width);
    z-index: var(--el-index-normal)
}

.el-checkbox__inner:hover {
    border-color: var(--el-checkbox-input-border-color-hover)
}

.el-checkbox__inner:after {
    border: 1px solid transparent;
    border-left: 0;
    border-top: 0;
    box-sizing: content-box;
    content: "";
    height: 7px;
    left: 4px;
    position: absolute;
    top: 1px;
    transform: rotate(45deg) scaleY(0);
    transform-origin: center;
    transition: transform .15s ease-in .05s;
    width: 3px
}

.el-checkbox__original {
    height: 0;
    margin: 0;
    opacity: 0;
    outline: none;
    position: absolute;
    width: 0;
    z-index: -1
}

.el-checkbox__label {
    display: inline-block;
    font-size: var(--el-checkbox-font-size);
    line-height: 1;
    padding-left: 8px
}

.el-checkbox.el-checkbox--large {
    height: 40px
}

.el-checkbox.el-checkbox--large .el-checkbox__label {
    font-size: 14px
}

.el-checkbox.el-checkbox--large .el-checkbox__inner {
    height: 14px;
    width: 14px
}

.el-checkbox.el-checkbox--small {
    height: 24px
}

.el-checkbox.el-checkbox--small .el-checkbox__label {
    font-size: 12px
}

.el-checkbox.el-checkbox--small .el-checkbox__inner {
    height: 12px;
    width: 12px
}

.el-checkbox.el-checkbox--small .el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
    top: 4px
}

.el-checkbox.el-checkbox--small .el-checkbox__inner:after {
    height: 6px;
    width: 2px
}

.el-checkbox:last-of-type {
    margin-right: 0
}

[class*=el-col-] {
    box-sizing: border-box
}

[class*=el-col-].is-guttered {
    display: block;
    min-height: 1px
}

.el-col-0 {
    flex: 0 0 0%;
    max-width: 0
}

.el-col-0, .el-col-0.is-guttered {
    display: none
}

.el-col-offset-0 {
    margin-left: 0
}

.el-col-pull-0 {
    position: relative;
    right: 0
}

.el-col-push-0 {
    left: 0;
    position: relative
}

.el-col-1 {
    flex: 0 0 4.1666666667%;
    max-width: 4.1666666667%
}

.el-col-1, .el-col-1.is-guttered {
    display: block
}

.el-col-offset-1 {
    margin-left: 4.1666666667%
}

.el-col-pull-1 {
    position: relative;
    right: 4.1666666667%
}

.el-col-push-1 {
    left: 4.1666666667%;
    position: relative
}

.el-col-2 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%
}

.el-col-2, .el-col-2.is-guttered {
    display: block
}

.el-col-offset-2 {
    margin-left: 8.3333333333%
}

.el-col-pull-2 {
    position: relative;
    right: 8.3333333333%
}

.el-col-push-2 {
    left: 8.3333333333%;
    position: relative
}

.el-col-3 {
    flex: 0 0 12.5%;
    max-width: 12.5%
}

.el-col-3, .el-col-3.is-guttered {
    display: block
}

.el-col-offset-3 {
    margin-left: 12.5%
}

.el-col-pull-3 {
    position: relative;
    right: 12.5%
}

.el-col-push-3 {
    left: 12.5%;
    position: relative
}

.el-col-4 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%
}

.el-col-4, .el-col-4.is-guttered {
    display: block
}

.el-col-offset-4 {
    margin-left: 16.6666666667%
}

.el-col-pull-4 {
    position: relative;
    right: 16.6666666667%
}

.el-col-push-4 {
    left: 16.6666666667%;
    position: relative
}

.el-col-5 {
    flex: 0 0 20.8333333333%;
    max-width: 20.8333333333%
}

.el-col-5, .el-col-5.is-guttered {
    display: block
}

.el-col-offset-5 {
    margin-left: 20.8333333333%
}

.el-col-pull-5 {
    position: relative;
    right: 20.8333333333%
}

.el-col-push-5 {
    left: 20.8333333333%;
    position: relative
}

.el-col-6 {
    flex: 0 0 25%;
    max-width: 25%
}

.el-col-6, .el-col-6.is-guttered {
    display: block
}

.el-col-offset-6 {
    margin-left: 25%
}

.el-col-pull-6 {
    position: relative;
    right: 25%
}

.el-col-push-6 {
    left: 25%;
    position: relative
}

.el-col-7 {
    flex: 0 0 29.1666666667%;
    max-width: 29.1666666667%
}

.el-col-7, .el-col-7.is-guttered {
    display: block
}

.el-col-offset-7 {
    margin-left: 29.1666666667%
}

.el-col-pull-7 {
    position: relative;
    right: 29.1666666667%
}

.el-col-push-7 {
    left: 29.1666666667%;
    position: relative
}

.el-col-8 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%
}

.el-col-8, .el-col-8.is-guttered {
    display: block
}

.el-col-offset-8 {
    margin-left: 33.3333333333%
}

.el-col-pull-8 {
    position: relative;
    right: 33.3333333333%
}

.el-col-push-8 {
    left: 33.3333333333%;
    position: relative
}

.el-col-9 {
    flex: 0 0 37.5%;
    max-width: 37.5%
}

.el-col-9, .el-col-9.is-guttered {
    display: block
}

.el-col-offset-9 {
    margin-left: 37.5%
}

.el-col-pull-9 {
    position: relative;
    right: 37.5%
}

.el-col-push-9 {
    left: 37.5%;
    position: relative
}

.el-col-10 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%
}

.el-col-10, .el-col-10.is-guttered {
    display: block
}

.el-col-offset-10 {
    margin-left: 41.6666666667%
}

.el-col-pull-10 {
    position: relative;
    right: 41.6666666667%
}

.el-col-push-10 {
    left: 41.6666666667%;
    position: relative
}

.el-col-11 {
    flex: 0 0 45.8333333333%;
    max-width: 45.8333333333%
}

.el-col-11, .el-col-11.is-guttered {
    display: block
}

.el-col-offset-11 {
    margin-left: 45.8333333333%
}

.el-col-pull-11 {
    position: relative;
    right: 45.8333333333%
}

.el-col-push-11 {
    left: 45.8333333333%;
    position: relative
}

.el-col-12 {
    flex: 0 0 50%;
    max-width: 50%
}

.el-col-12, .el-col-12.is-guttered {
    display: block
}

.el-col-offset-12 {
    margin-left: 50%
}

.el-col-pull-12 {
    position: relative;
    right: 50%
}

.el-col-push-12 {
    left: 50%;
    position: relative
}

.el-col-13 {
    flex: 0 0 54.1666666667%;
    max-width: 54.1666666667%
}

.el-col-13, .el-col-13.is-guttered {
    display: block
}

.el-col-offset-13 {
    margin-left: 54.1666666667%
}

.el-col-pull-13 {
    position: relative;
    right: 54.1666666667%
}

.el-col-push-13 {
    left: 54.1666666667%;
    position: relative
}

.el-col-14 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%
}

.el-col-14, .el-col-14.is-guttered {
    display: block
}

.el-col-offset-14 {
    margin-left: 58.3333333333%
}

.el-col-pull-14 {
    position: relative;
    right: 58.3333333333%
}

.el-col-push-14 {
    left: 58.3333333333%;
    position: relative
}

.el-col-15 {
    flex: 0 0 62.5%;
    max-width: 62.5%
}

.el-col-15, .el-col-15.is-guttered {
    display: block
}

.el-col-offset-15 {
    margin-left: 62.5%
}

.el-col-pull-15 {
    position: relative;
    right: 62.5%
}

.el-col-push-15 {
    left: 62.5%;
    position: relative
}

.el-col-16 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%
}

.el-col-16, .el-col-16.is-guttered {
    display: block
}

.el-col-offset-16 {
    margin-left: 66.6666666667%
}

.el-col-pull-16 {
    position: relative;
    right: 66.6666666667%
}

.el-col-push-16 {
    left: 66.6666666667%;
    position: relative
}

.el-col-17 {
    flex: 0 0 70.8333333333%;
    max-width: 70.8333333333%
}

.el-col-17, .el-col-17.is-guttered {
    display: block
}

.el-col-offset-17 {
    margin-left: 70.8333333333%
}

.el-col-pull-17 {
    position: relative;
    right: 70.8333333333%
}

.el-col-push-17 {
    left: 70.8333333333%;
    position: relative
}

.el-col-18 {
    flex: 0 0 75%;
    max-width: 75%
}

.el-col-18, .el-col-18.is-guttered {
    display: block
}

.el-col-offset-18 {
    margin-left: 75%
}

.el-col-pull-18 {
    position: relative;
    right: 75%
}

.el-col-push-18 {
    left: 75%;
    position: relative
}

.el-col-19 {
    flex: 0 0 79.1666666667%;
    max-width: 79.1666666667%
}

.el-col-19, .el-col-19.is-guttered {
    display: block
}

.el-col-offset-19 {
    margin-left: 79.1666666667%
}

.el-col-pull-19 {
    position: relative;
    right: 79.1666666667%
}

.el-col-push-19 {
    left: 79.1666666667%;
    position: relative
}

.el-col-20 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%
}

.el-col-20, .el-col-20.is-guttered {
    display: block
}

.el-col-offset-20 {
    margin-left: 83.3333333333%
}

.el-col-pull-20 {
    position: relative;
    right: 83.3333333333%
}

.el-col-push-20 {
    left: 83.3333333333%;
    position: relative
}

.el-col-21 {
    flex: 0 0 87.5%;
    max-width: 87.5%
}

.el-col-21, .el-col-21.is-guttered {
    display: block
}

.el-col-offset-21 {
    margin-left: 87.5%
}

.el-col-pull-21 {
    position: relative;
    right: 87.5%
}

.el-col-push-21 {
    left: 87.5%;
    position: relative
}

.el-col-22 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%
}

.el-col-22, .el-col-22.is-guttered {
    display: block
}

.el-col-offset-22 {
    margin-left: 91.6666666667%
}

.el-col-pull-22 {
    position: relative;
    right: 91.6666666667%
}

.el-col-push-22 {
    left: 91.6666666667%;
    position: relative
}

.el-col-23 {
    flex: 0 0 95.8333333333%;
    max-width: 95.8333333333%
}

.el-col-23, .el-col-23.is-guttered {
    display: block
}

.el-col-offset-23 {
    margin-left: 95.8333333333%
}

.el-col-pull-23 {
    position: relative;
    right: 95.8333333333%
}

.el-col-push-23 {
    left: 95.8333333333%;
    position: relative
}

.el-col-24 {
    flex: 0 0 100%;
    max-width: 100%
}

.el-col-24, .el-col-24.is-guttered {
    display: block
}

.el-col-offset-24 {
    margin-left: 100%
}

.el-col-pull-24 {
    position: relative;
    right: 100%
}

.el-col-push-24 {
    left: 100%;
    position: relative
}

@media only screen and (max-width: 767px) {
    .el-col-xs-0 {
        display: none;
        flex: 0 0 0%;
        max-width: 0
    }

    .el-col-xs-0.is-guttered {
        display: none
    }

    .el-col-xs-offset-0 {
        margin-left: 0
    }

    .el-col-xs-pull-0 {
        position: relative;
        right: 0
    }

    .el-col-xs-push-0 {
        left: 0;
        position: relative
    }

    .el-col-xs-1 {
        flex: 0 0 4.1666666667%;
        max-width: 4.1666666667%
    }

    .el-col-xs-1, .el-col-xs-1.is-guttered {
        display: block
    }

    .el-col-xs-offset-1 {
        margin-left: 4.1666666667%
    }

    .el-col-xs-pull-1 {
        position: relative;
        right: 4.1666666667%
    }

    .el-col-xs-push-1 {
        left: 4.1666666667%;
        position: relative
    }

    .el-col-xs-2 {
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }

    .el-col-xs-2, .el-col-xs-2.is-guttered {
        display: block
    }

    .el-col-xs-offset-2 {
        margin-left: 8.3333333333%
    }

    .el-col-xs-pull-2 {
        position: relative;
        right: 8.3333333333%
    }

    .el-col-xs-push-2 {
        left: 8.3333333333%;
        position: relative
    }

    .el-col-xs-3 {
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .el-col-xs-3, .el-col-xs-3.is-guttered {
        display: block
    }

    .el-col-xs-offset-3 {
        margin-left: 12.5%
    }

    .el-col-xs-pull-3 {
        position: relative;
        right: 12.5%
    }

    .el-col-xs-push-3 {
        left: 12.5%;
        position: relative
    }

    .el-col-xs-4 {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }

    .el-col-xs-4, .el-col-xs-4.is-guttered {
        display: block
    }

    .el-col-xs-offset-4 {
        margin-left: 16.6666666667%
    }

    .el-col-xs-pull-4 {
        position: relative;
        right: 16.6666666667%
    }

    .el-col-xs-push-4 {
        left: 16.6666666667%;
        position: relative
    }

    .el-col-xs-5 {
        flex: 0 0 20.8333333333%;
        max-width: 20.8333333333%
    }

    .el-col-xs-5, .el-col-xs-5.is-guttered {
        display: block
    }

    .el-col-xs-offset-5 {
        margin-left: 20.8333333333%
    }

    .el-col-xs-pull-5 {
        position: relative;
        right: 20.8333333333%
    }

    .el-col-xs-push-5 {
        left: 20.8333333333%;
        position: relative
    }

    .el-col-xs-6 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .el-col-xs-6, .el-col-xs-6.is-guttered {
        display: block
    }

    .el-col-xs-offset-6 {
        margin-left: 25%
    }

    .el-col-xs-pull-6 {
        position: relative;
        right: 25%
    }

    .el-col-xs-push-6 {
        left: 25%;
        position: relative
    }

    .el-col-xs-7 {
        flex: 0 0 29.1666666667%;
        max-width: 29.1666666667%
    }

    .el-col-xs-7, .el-col-xs-7.is-guttered {
        display: block
    }

    .el-col-xs-offset-7 {
        margin-left: 29.1666666667%
    }

    .el-col-xs-pull-7 {
        position: relative;
        right: 29.1666666667%
    }

    .el-col-xs-push-7 {
        left: 29.1666666667%;
        position: relative
    }

    .el-col-xs-8 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }

    .el-col-xs-8, .el-col-xs-8.is-guttered {
        display: block
    }

    .el-col-xs-offset-8 {
        margin-left: 33.3333333333%
    }

    .el-col-xs-pull-8 {
        position: relative;
        right: 33.3333333333%
    }

    .el-col-xs-push-8 {
        left: 33.3333333333%;
        position: relative
    }

    .el-col-xs-9 {
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .el-col-xs-9, .el-col-xs-9.is-guttered {
        display: block
    }

    .el-col-xs-offset-9 {
        margin-left: 37.5%
    }

    .el-col-xs-pull-9 {
        position: relative;
        right: 37.5%
    }

    .el-col-xs-push-9 {
        left: 37.5%;
        position: relative
    }

    .el-col-xs-10 {
        display: block;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }

    .el-col-xs-10.is-guttered {
        display: block
    }

    .el-col-xs-offset-10 {
        margin-left: 41.6666666667%
    }

    .el-col-xs-pull-10 {
        position: relative;
        right: 41.6666666667%
    }

    .el-col-xs-push-10 {
        left: 41.6666666667%;
        position: relative
    }

    .el-col-xs-11 {
        display: block;
        flex: 0 0 45.8333333333%;
        max-width: 45.8333333333%
    }

    .el-col-xs-11.is-guttered {
        display: block
    }

    .el-col-xs-offset-11 {
        margin-left: 45.8333333333%
    }

    .el-col-xs-pull-11 {
        position: relative;
        right: 45.8333333333%
    }

    .el-col-xs-push-11 {
        left: 45.8333333333%;
        position: relative
    }

    .el-col-xs-12 {
        display: block;
        flex: 0 0 50%;
        max-width: 50%
    }

    .el-col-xs-12.is-guttered {
        display: block
    }

    .el-col-xs-offset-12 {
        margin-left: 50%
    }

    .el-col-xs-pull-12 {
        position: relative;
        right: 50%
    }

    .el-col-xs-push-12 {
        left: 50%;
        position: relative
    }

    .el-col-xs-13 {
        display: block;
        flex: 0 0 54.1666666667%;
        max-width: 54.1666666667%
    }

    .el-col-xs-13.is-guttered {
        display: block
    }

    .el-col-xs-offset-13 {
        margin-left: 54.1666666667%
    }

    .el-col-xs-pull-13 {
        position: relative;
        right: 54.1666666667%
    }

    .el-col-xs-push-13 {
        left: 54.1666666667%;
        position: relative
    }

    .el-col-xs-14 {
        display: block;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }

    .el-col-xs-14.is-guttered {
        display: block
    }

    .el-col-xs-offset-14 {
        margin-left: 58.3333333333%
    }

    .el-col-xs-pull-14 {
        position: relative;
        right: 58.3333333333%
    }

    .el-col-xs-push-14 {
        left: 58.3333333333%;
        position: relative
    }

    .el-col-xs-15 {
        display: block;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .el-col-xs-15.is-guttered {
        display: block
    }

    .el-col-xs-offset-15 {
        margin-left: 62.5%
    }

    .el-col-xs-pull-15 {
        position: relative;
        right: 62.5%
    }

    .el-col-xs-push-15 {
        left: 62.5%;
        position: relative
    }

    .el-col-xs-16 {
        display: block;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }

    .el-col-xs-16.is-guttered {
        display: block
    }

    .el-col-xs-offset-16 {
        margin-left: 66.6666666667%
    }

    .el-col-xs-pull-16 {
        position: relative;
        right: 66.6666666667%
    }

    .el-col-xs-push-16 {
        left: 66.6666666667%;
        position: relative
    }

    .el-col-xs-17 {
        display: block;
        flex: 0 0 70.8333333333%;
        max-width: 70.8333333333%
    }

    .el-col-xs-17.is-guttered {
        display: block
    }

    .el-col-xs-offset-17 {
        margin-left: 70.8333333333%
    }

    .el-col-xs-pull-17 {
        position: relative;
        right: 70.8333333333%
    }

    .el-col-xs-push-17 {
        left: 70.8333333333%;
        position: relative
    }

    .el-col-xs-18 {
        display: block;
        flex: 0 0 75%;
        max-width: 75%
    }

    .el-col-xs-18.is-guttered {
        display: block
    }

    .el-col-xs-offset-18 {
        margin-left: 75%
    }

    .el-col-xs-pull-18 {
        position: relative;
        right: 75%
    }

    .el-col-xs-push-18 {
        left: 75%;
        position: relative
    }

    .el-col-xs-19 {
        display: block;
        flex: 0 0 79.1666666667%;
        max-width: 79.1666666667%
    }

    .el-col-xs-19.is-guttered {
        display: block
    }

    .el-col-xs-offset-19 {
        margin-left: 79.1666666667%
    }

    .el-col-xs-pull-19 {
        position: relative;
        right: 79.1666666667%
    }

    .el-col-xs-push-19 {
        left: 79.1666666667%;
        position: relative
    }

    .el-col-xs-20 {
        display: block;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }

    .el-col-xs-20.is-guttered {
        display: block
    }

    .el-col-xs-offset-20 {
        margin-left: 83.3333333333%
    }

    .el-col-xs-pull-20 {
        position: relative;
        right: 83.3333333333%
    }

    .el-col-xs-push-20 {
        left: 83.3333333333%;
        position: relative
    }

    .el-col-xs-21 {
        display: block;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .el-col-xs-21.is-guttered {
        display: block
    }

    .el-col-xs-offset-21 {
        margin-left: 87.5%
    }

    .el-col-xs-pull-21 {
        position: relative;
        right: 87.5%
    }

    .el-col-xs-push-21 {
        left: 87.5%;
        position: relative
    }

    .el-col-xs-22 {
        display: block;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }

    .el-col-xs-22.is-guttered {
        display: block
    }

    .el-col-xs-offset-22 {
        margin-left: 91.6666666667%
    }

    .el-col-xs-pull-22 {
        position: relative;
        right: 91.6666666667%
    }

    .el-col-xs-push-22 {
        left: 91.6666666667%;
        position: relative
    }

    .el-col-xs-23 {
        display: block;
        flex: 0 0 95.8333333333%;
        max-width: 95.8333333333%
    }

    .el-col-xs-23.is-guttered {
        display: block
    }

    .el-col-xs-offset-23 {
        margin-left: 95.8333333333%
    }

    .el-col-xs-pull-23 {
        position: relative;
        right: 95.8333333333%
    }

    .el-col-xs-push-23 {
        left: 95.8333333333%;
        position: relative
    }

    .el-col-xs-24 {
        display: block;
        flex: 0 0 100%;
        max-width: 100%
    }

    .el-col-xs-24.is-guttered {
        display: block
    }

    .el-col-xs-offset-24 {
        margin-left: 100%
    }

    .el-col-xs-pull-24 {
        position: relative;
        right: 100%
    }

    .el-col-xs-push-24 {
        left: 100%;
        position: relative
    }
}

@media only screen and (min-width: 768px) {
    .el-col-sm-0 {
        display: none;
        flex: 0 0 0%;
        max-width: 0
    }

    .el-col-sm-0.is-guttered {
        display: none
    }

    .el-col-sm-offset-0 {
        margin-left: 0
    }

    .el-col-sm-pull-0 {
        position: relative;
        right: 0
    }

    .el-col-sm-push-0 {
        left: 0;
        position: relative
    }

    .el-col-sm-1 {
        flex: 0 0 4.1666666667%;
        max-width: 4.1666666667%
    }

    .el-col-sm-1, .el-col-sm-1.is-guttered {
        display: block
    }

    .el-col-sm-offset-1 {
        margin-left: 4.1666666667%
    }

    .el-col-sm-pull-1 {
        position: relative;
        right: 4.1666666667%
    }

    .el-col-sm-push-1 {
        left: 4.1666666667%;
        position: relative
    }

    .el-col-sm-2 {
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }

    .el-col-sm-2, .el-col-sm-2.is-guttered {
        display: block
    }

    .el-col-sm-offset-2 {
        margin-left: 8.3333333333%
    }

    .el-col-sm-pull-2 {
        position: relative;
        right: 8.3333333333%
    }

    .el-col-sm-push-2 {
        left: 8.3333333333%;
        position: relative
    }

    .el-col-sm-3 {
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .el-col-sm-3, .el-col-sm-3.is-guttered {
        display: block
    }

    .el-col-sm-offset-3 {
        margin-left: 12.5%
    }

    .el-col-sm-pull-3 {
        position: relative;
        right: 12.5%
    }

    .el-col-sm-push-3 {
        left: 12.5%;
        position: relative
    }

    .el-col-sm-4 {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }

    .el-col-sm-4, .el-col-sm-4.is-guttered {
        display: block
    }

    .el-col-sm-offset-4 {
        margin-left: 16.6666666667%
    }

    .el-col-sm-pull-4 {
        position: relative;
        right: 16.6666666667%
    }

    .el-col-sm-push-4 {
        left: 16.6666666667%;
        position: relative
    }

    .el-col-sm-5 {
        flex: 0 0 20.8333333333%;
        max-width: 20.8333333333%
    }

    .el-col-sm-5, .el-col-sm-5.is-guttered {
        display: block
    }

    .el-col-sm-offset-5 {
        margin-left: 20.8333333333%
    }

    .el-col-sm-pull-5 {
        position: relative;
        right: 20.8333333333%
    }

    .el-col-sm-push-5 {
        left: 20.8333333333%;
        position: relative
    }

    .el-col-sm-6 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .el-col-sm-6, .el-col-sm-6.is-guttered {
        display: block
    }

    .el-col-sm-offset-6 {
        margin-left: 25%
    }

    .el-col-sm-pull-6 {
        position: relative;
        right: 25%
    }

    .el-col-sm-push-6 {
        left: 25%;
        position: relative
    }

    .el-col-sm-7 {
        flex: 0 0 29.1666666667%;
        max-width: 29.1666666667%
    }

    .el-col-sm-7, .el-col-sm-7.is-guttered {
        display: block
    }

    .el-col-sm-offset-7 {
        margin-left: 29.1666666667%
    }

    .el-col-sm-pull-7 {
        position: relative;
        right: 29.1666666667%
    }

    .el-col-sm-push-7 {
        left: 29.1666666667%;
        position: relative
    }

    .el-col-sm-8 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }

    .el-col-sm-8, .el-col-sm-8.is-guttered {
        display: block
    }

    .el-col-sm-offset-8 {
        margin-left: 33.3333333333%
    }

    .el-col-sm-pull-8 {
        position: relative;
        right: 33.3333333333%
    }

    .el-col-sm-push-8 {
        left: 33.3333333333%;
        position: relative
    }

    .el-col-sm-9 {
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .el-col-sm-9, .el-col-sm-9.is-guttered {
        display: block
    }

    .el-col-sm-offset-9 {
        margin-left: 37.5%
    }

    .el-col-sm-pull-9 {
        position: relative;
        right: 37.5%
    }

    .el-col-sm-push-9 {
        left: 37.5%;
        position: relative
    }

    .el-col-sm-10 {
        display: block;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }

    .el-col-sm-10.is-guttered {
        display: block
    }

    .el-col-sm-offset-10 {
        margin-left: 41.6666666667%
    }

    .el-col-sm-pull-10 {
        position: relative;
        right: 41.6666666667%
    }

    .el-col-sm-push-10 {
        left: 41.6666666667%;
        position: relative
    }

    .el-col-sm-11 {
        display: block;
        flex: 0 0 45.8333333333%;
        max-width: 45.8333333333%
    }

    .el-col-sm-11.is-guttered {
        display: block
    }

    .el-col-sm-offset-11 {
        margin-left: 45.8333333333%
    }

    .el-col-sm-pull-11 {
        position: relative;
        right: 45.8333333333%
    }

    .el-col-sm-push-11 {
        left: 45.8333333333%;
        position: relative
    }

    .el-col-sm-12 {
        display: block;
        flex: 0 0 50%;
        max-width: 50%
    }

    .el-col-sm-12.is-guttered {
        display: block
    }

    .el-col-sm-offset-12 {
        margin-left: 50%
    }

    .el-col-sm-pull-12 {
        position: relative;
        right: 50%
    }

    .el-col-sm-push-12 {
        left: 50%;
        position: relative
    }

    .el-col-sm-13 {
        display: block;
        flex: 0 0 54.1666666667%;
        max-width: 54.1666666667%
    }

    .el-col-sm-13.is-guttered {
        display: block
    }

    .el-col-sm-offset-13 {
        margin-left: 54.1666666667%
    }

    .el-col-sm-pull-13 {
        position: relative;
        right: 54.1666666667%
    }

    .el-col-sm-push-13 {
        left: 54.1666666667%;
        position: relative
    }

    .el-col-sm-14 {
        display: block;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }

    .el-col-sm-14.is-guttered {
        display: block
    }

    .el-col-sm-offset-14 {
        margin-left: 58.3333333333%
    }

    .el-col-sm-pull-14 {
        position: relative;
        right: 58.3333333333%
    }

    .el-col-sm-push-14 {
        left: 58.3333333333%;
        position: relative
    }

    .el-col-sm-15 {
        display: block;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .el-col-sm-15.is-guttered {
        display: block
    }

    .el-col-sm-offset-15 {
        margin-left: 62.5%
    }

    .el-col-sm-pull-15 {
        position: relative;
        right: 62.5%
    }

    .el-col-sm-push-15 {
        left: 62.5%;
        position: relative
    }

    .el-col-sm-16 {
        display: block;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }

    .el-col-sm-16.is-guttered {
        display: block
    }

    .el-col-sm-offset-16 {
        margin-left: 66.6666666667%
    }

    .el-col-sm-pull-16 {
        position: relative;
        right: 66.6666666667%
    }

    .el-col-sm-push-16 {
        left: 66.6666666667%;
        position: relative
    }

    .el-col-sm-17 {
        display: block;
        flex: 0 0 70.8333333333%;
        max-width: 70.8333333333%
    }

    .el-col-sm-17.is-guttered {
        display: block
    }

    .el-col-sm-offset-17 {
        margin-left: 70.8333333333%
    }

    .el-col-sm-pull-17 {
        position: relative;
        right: 70.8333333333%
    }

    .el-col-sm-push-17 {
        left: 70.8333333333%;
        position: relative
    }

    .el-col-sm-18 {
        display: block;
        flex: 0 0 75%;
        max-width: 75%
    }

    .el-col-sm-18.is-guttered {
        display: block
    }

    .el-col-sm-offset-18 {
        margin-left: 75%
    }

    .el-col-sm-pull-18 {
        position: relative;
        right: 75%
    }

    .el-col-sm-push-18 {
        left: 75%;
        position: relative
    }

    .el-col-sm-19 {
        display: block;
        flex: 0 0 79.1666666667%;
        max-width: 79.1666666667%
    }

    .el-col-sm-19.is-guttered {
        display: block
    }

    .el-col-sm-offset-19 {
        margin-left: 79.1666666667%
    }

    .el-col-sm-pull-19 {
        position: relative;
        right: 79.1666666667%
    }

    .el-col-sm-push-19 {
        left: 79.1666666667%;
        position: relative
    }

    .el-col-sm-20 {
        display: block;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }

    .el-col-sm-20.is-guttered {
        display: block
    }

    .el-col-sm-offset-20 {
        margin-left: 83.3333333333%
    }

    .el-col-sm-pull-20 {
        position: relative;
        right: 83.3333333333%
    }

    .el-col-sm-push-20 {
        left: 83.3333333333%;
        position: relative
    }

    .el-col-sm-21 {
        display: block;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .el-col-sm-21.is-guttered {
        display: block
    }

    .el-col-sm-offset-21 {
        margin-left: 87.5%
    }

    .el-col-sm-pull-21 {
        position: relative;
        right: 87.5%
    }

    .el-col-sm-push-21 {
        left: 87.5%;
        position: relative
    }

    .el-col-sm-22 {
        display: block;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }

    .el-col-sm-22.is-guttered {
        display: block
    }

    .el-col-sm-offset-22 {
        margin-left: 91.6666666667%
    }

    .el-col-sm-pull-22 {
        position: relative;
        right: 91.6666666667%
    }

    .el-col-sm-push-22 {
        left: 91.6666666667%;
        position: relative
    }

    .el-col-sm-23 {
        display: block;
        flex: 0 0 95.8333333333%;
        max-width: 95.8333333333%
    }

    .el-col-sm-23.is-guttered {
        display: block
    }

    .el-col-sm-offset-23 {
        margin-left: 95.8333333333%
    }

    .el-col-sm-pull-23 {
        position: relative;
        right: 95.8333333333%
    }

    .el-col-sm-push-23 {
        left: 95.8333333333%;
        position: relative
    }

    .el-col-sm-24 {
        display: block;
        flex: 0 0 100%;
        max-width: 100%
    }

    .el-col-sm-24.is-guttered {
        display: block
    }

    .el-col-sm-offset-24 {
        margin-left: 100%
    }

    .el-col-sm-pull-24 {
        position: relative;
        right: 100%
    }

    .el-col-sm-push-24 {
        left: 100%;
        position: relative
    }
}

@media only screen and (min-width: 992px) {
    .el-col-md-0 {
        display: none;
        flex: 0 0 0%;
        max-width: 0
    }

    .el-col-md-0.is-guttered {
        display: none
    }

    .el-col-md-offset-0 {
        margin-left: 0
    }

    .el-col-md-pull-0 {
        position: relative;
        right: 0
    }

    .el-col-md-push-0 {
        left: 0;
        position: relative
    }

    .el-col-md-1 {
        flex: 0 0 4.1666666667%;
        max-width: 4.1666666667%
    }

    .el-col-md-1, .el-col-md-1.is-guttered {
        display: block
    }

    .el-col-md-offset-1 {
        margin-left: 4.1666666667%
    }

    .el-col-md-pull-1 {
        position: relative;
        right: 4.1666666667%
    }

    .el-col-md-push-1 {
        left: 4.1666666667%;
        position: relative
    }

    .el-col-md-2 {
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }

    .el-col-md-2, .el-col-md-2.is-guttered {
        display: block
    }

    .el-col-md-offset-2 {
        margin-left: 8.3333333333%
    }

    .el-col-md-pull-2 {
        position: relative;
        right: 8.3333333333%
    }

    .el-col-md-push-2 {
        left: 8.3333333333%;
        position: relative
    }

    .el-col-md-3 {
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .el-col-md-3, .el-col-md-3.is-guttered {
        display: block
    }

    .el-col-md-offset-3 {
        margin-left: 12.5%
    }

    .el-col-md-pull-3 {
        position: relative;
        right: 12.5%
    }

    .el-col-md-push-3 {
        left: 12.5%;
        position: relative
    }

    .el-col-md-4 {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }

    .el-col-md-4, .el-col-md-4.is-guttered {
        display: block
    }

    .el-col-md-offset-4 {
        margin-left: 16.6666666667%
    }

    .el-col-md-pull-4 {
        position: relative;
        right: 16.6666666667%
    }

    .el-col-md-push-4 {
        left: 16.6666666667%;
        position: relative
    }

    .el-col-md-5 {
        flex: 0 0 20.8333333333%;
        max-width: 20.8333333333%
    }

    .el-col-md-5, .el-col-md-5.is-guttered {
        display: block
    }

    .el-col-md-offset-5 {
        margin-left: 20.8333333333%
    }

    .el-col-md-pull-5 {
        position: relative;
        right: 20.8333333333%
    }

    .el-col-md-push-5 {
        left: 20.8333333333%;
        position: relative
    }

    .el-col-md-6 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .el-col-md-6, .el-col-md-6.is-guttered {
        display: block
    }

    .el-col-md-offset-6 {
        margin-left: 25%
    }

    .el-col-md-pull-6 {
        position: relative;
        right: 25%
    }

    .el-col-md-push-6 {
        left: 25%;
        position: relative
    }

    .el-col-md-7 {
        flex: 0 0 29.1666666667%;
        max-width: 29.1666666667%
    }

    .el-col-md-7, .el-col-md-7.is-guttered {
        display: block
    }

    .el-col-md-offset-7 {
        margin-left: 29.1666666667%
    }

    .el-col-md-pull-7 {
        position: relative;
        right: 29.1666666667%
    }

    .el-col-md-push-7 {
        left: 29.1666666667%;
        position: relative
    }

    .el-col-md-8 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }

    .el-col-md-8, .el-col-md-8.is-guttered {
        display: block
    }

    .el-col-md-offset-8 {
        margin-left: 33.3333333333%
    }

    .el-col-md-pull-8 {
        position: relative;
        right: 33.3333333333%
    }

    .el-col-md-push-8 {
        left: 33.3333333333%;
        position: relative
    }

    .el-col-md-9 {
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .el-col-md-9, .el-col-md-9.is-guttered {
        display: block
    }

    .el-col-md-offset-9 {
        margin-left: 37.5%
    }

    .el-col-md-pull-9 {
        position: relative;
        right: 37.5%
    }

    .el-col-md-push-9 {
        left: 37.5%;
        position: relative
    }

    .el-col-md-10 {
        display: block;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }

    .el-col-md-10.is-guttered {
        display: block
    }

    .el-col-md-offset-10 {
        margin-left: 41.6666666667%
    }

    .el-col-md-pull-10 {
        position: relative;
        right: 41.6666666667%
    }

    .el-col-md-push-10 {
        left: 41.6666666667%;
        position: relative
    }

    .el-col-md-11 {
        display: block;
        flex: 0 0 45.8333333333%;
        max-width: 45.8333333333%
    }

    .el-col-md-11.is-guttered {
        display: block
    }

    .el-col-md-offset-11 {
        margin-left: 45.8333333333%
    }

    .el-col-md-pull-11 {
        position: relative;
        right: 45.8333333333%
    }

    .el-col-md-push-11 {
        left: 45.8333333333%;
        position: relative
    }

    .el-col-md-12 {
        display: block;
        flex: 0 0 50%;
        max-width: 50%
    }

    .el-col-md-12.is-guttered {
        display: block
    }

    .el-col-md-offset-12 {
        margin-left: 50%
    }

    .el-col-md-pull-12 {
        position: relative;
        right: 50%
    }

    .el-col-md-push-12 {
        left: 50%;
        position: relative
    }

    .el-col-md-13 {
        display: block;
        flex: 0 0 54.1666666667%;
        max-width: 54.1666666667%
    }

    .el-col-md-13.is-guttered {
        display: block
    }

    .el-col-md-offset-13 {
        margin-left: 54.1666666667%
    }

    .el-col-md-pull-13 {
        position: relative;
        right: 54.1666666667%
    }

    .el-col-md-push-13 {
        left: 54.1666666667%;
        position: relative
    }

    .el-col-md-14 {
        display: block;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }

    .el-col-md-14.is-guttered {
        display: block
    }

    .el-col-md-offset-14 {
        margin-left: 58.3333333333%
    }

    .el-col-md-pull-14 {
        position: relative;
        right: 58.3333333333%
    }

    .el-col-md-push-14 {
        left: 58.3333333333%;
        position: relative
    }

    .el-col-md-15 {
        display: block;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .el-col-md-15.is-guttered {
        display: block
    }

    .el-col-md-offset-15 {
        margin-left: 62.5%
    }

    .el-col-md-pull-15 {
        position: relative;
        right: 62.5%
    }

    .el-col-md-push-15 {
        left: 62.5%;
        position: relative
    }

    .el-col-md-16 {
        display: block;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }

    .el-col-md-16.is-guttered {
        display: block
    }

    .el-col-md-offset-16 {
        margin-left: 66.6666666667%
    }

    .el-col-md-pull-16 {
        position: relative;
        right: 66.6666666667%
    }

    .el-col-md-push-16 {
        left: 66.6666666667%;
        position: relative
    }

    .el-col-md-17 {
        display: block;
        flex: 0 0 70.8333333333%;
        max-width: 70.8333333333%
    }

    .el-col-md-17.is-guttered {
        display: block
    }

    .el-col-md-offset-17 {
        margin-left: 70.8333333333%
    }

    .el-col-md-pull-17 {
        position: relative;
        right: 70.8333333333%
    }

    .el-col-md-push-17 {
        left: 70.8333333333%;
        position: relative
    }

    .el-col-md-18 {
        display: block;
        flex: 0 0 75%;
        max-width: 75%
    }

    .el-col-md-18.is-guttered {
        display: block
    }

    .el-col-md-offset-18 {
        margin-left: 75%
    }

    .el-col-md-pull-18 {
        position: relative;
        right: 75%
    }

    .el-col-md-push-18 {
        left: 75%;
        position: relative
    }

    .el-col-md-19 {
        display: block;
        flex: 0 0 79.1666666667%;
        max-width: 79.1666666667%
    }

    .el-col-md-19.is-guttered {
        display: block
    }

    .el-col-md-offset-19 {
        margin-left: 79.1666666667%
    }

    .el-col-md-pull-19 {
        position: relative;
        right: 79.1666666667%
    }

    .el-col-md-push-19 {
        left: 79.1666666667%;
        position: relative
    }

    .el-col-md-20 {
        display: block;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }

    .el-col-md-20.is-guttered {
        display: block
    }

    .el-col-md-offset-20 {
        margin-left: 83.3333333333%
    }

    .el-col-md-pull-20 {
        position: relative;
        right: 83.3333333333%
    }

    .el-col-md-push-20 {
        left: 83.3333333333%;
        position: relative
    }

    .el-col-md-21 {
        display: block;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .el-col-md-21.is-guttered {
        display: block
    }

    .el-col-md-offset-21 {
        margin-left: 87.5%
    }

    .el-col-md-pull-21 {
        position: relative;
        right: 87.5%
    }

    .el-col-md-push-21 {
        left: 87.5%;
        position: relative
    }

    .el-col-md-22 {
        display: block;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }

    .el-col-md-22.is-guttered {
        display: block
    }

    .el-col-md-offset-22 {
        margin-left: 91.6666666667%
    }

    .el-col-md-pull-22 {
        position: relative;
        right: 91.6666666667%
    }

    .el-col-md-push-22 {
        left: 91.6666666667%;
        position: relative
    }

    .el-col-md-23 {
        display: block;
        flex: 0 0 95.8333333333%;
        max-width: 95.8333333333%
    }

    .el-col-md-23.is-guttered {
        display: block
    }

    .el-col-md-offset-23 {
        margin-left: 95.8333333333%
    }

    .el-col-md-pull-23 {
        position: relative;
        right: 95.8333333333%
    }

    .el-col-md-push-23 {
        left: 95.8333333333%;
        position: relative
    }

    .el-col-md-24 {
        display: block;
        flex: 0 0 100%;
        max-width: 100%
    }

    .el-col-md-24.is-guttered {
        display: block
    }

    .el-col-md-offset-24 {
        margin-left: 100%
    }

    .el-col-md-pull-24 {
        position: relative;
        right: 100%
    }

    .el-col-md-push-24 {
        left: 100%;
        position: relative
    }
}

@media only screen and (min-width: 1200px) {
    .el-col-lg-0 {
        display: none;
        flex: 0 0 0%;
        max-width: 0
    }

    .el-col-lg-0.is-guttered {
        display: none
    }

    .el-col-lg-offset-0 {
        margin-left: 0
    }

    .el-col-lg-pull-0 {
        position: relative;
        right: 0
    }

    .el-col-lg-push-0 {
        left: 0;
        position: relative
    }

    .el-col-lg-1 {
        flex: 0 0 4.1666666667%;
        max-width: 4.1666666667%
    }

    .el-col-lg-1, .el-col-lg-1.is-guttered {
        display: block
    }

    .el-col-lg-offset-1 {
        margin-left: 4.1666666667%
    }

    .el-col-lg-pull-1 {
        position: relative;
        right: 4.1666666667%
    }

    .el-col-lg-push-1 {
        left: 4.1666666667%;
        position: relative
    }

    .el-col-lg-2 {
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }

    .el-col-lg-2, .el-col-lg-2.is-guttered {
        display: block
    }

    .el-col-lg-offset-2 {
        margin-left: 8.3333333333%
    }

    .el-col-lg-pull-2 {
        position: relative;
        right: 8.3333333333%
    }

    .el-col-lg-push-2 {
        left: 8.3333333333%;
        position: relative
    }

    .el-col-lg-3 {
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .el-col-lg-3, .el-col-lg-3.is-guttered {
        display: block
    }

    .el-col-lg-offset-3 {
        margin-left: 12.5%
    }

    .el-col-lg-pull-3 {
        position: relative;
        right: 12.5%
    }

    .el-col-lg-push-3 {
        left: 12.5%;
        position: relative
    }

    .el-col-lg-4 {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }

    .el-col-lg-4, .el-col-lg-4.is-guttered {
        display: block
    }

    .el-col-lg-offset-4 {
        margin-left: 16.6666666667%
    }

    .el-col-lg-pull-4 {
        position: relative;
        right: 16.6666666667%
    }

    .el-col-lg-push-4 {
        left: 16.6666666667%;
        position: relative
    }

    .el-col-lg-5 {
        flex: 0 0 20.8333333333%;
        max-width: 20.8333333333%
    }

    .el-col-lg-5, .el-col-lg-5.is-guttered {
        display: block
    }

    .el-col-lg-offset-5 {
        margin-left: 20.8333333333%
    }

    .el-col-lg-pull-5 {
        position: relative;
        right: 20.8333333333%
    }

    .el-col-lg-push-5 {
        left: 20.8333333333%;
        position: relative
    }

    .el-col-lg-6 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .el-col-lg-6, .el-col-lg-6.is-guttered {
        display: block
    }

    .el-col-lg-offset-6 {
        margin-left: 25%
    }

    .el-col-lg-pull-6 {
        position: relative;
        right: 25%
    }

    .el-col-lg-push-6 {
        left: 25%;
        position: relative
    }

    .el-col-lg-7 {
        flex: 0 0 29.1666666667%;
        max-width: 29.1666666667%
    }

    .el-col-lg-7, .el-col-lg-7.is-guttered {
        display: block
    }

    .el-col-lg-offset-7 {
        margin-left: 29.1666666667%
    }

    .el-col-lg-pull-7 {
        position: relative;
        right: 29.1666666667%
    }

    .el-col-lg-push-7 {
        left: 29.1666666667%;
        position: relative
    }

    .el-col-lg-8 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }

    .el-col-lg-8, .el-col-lg-8.is-guttered {
        display: block
    }

    .el-col-lg-offset-8 {
        margin-left: 33.3333333333%
    }

    .el-col-lg-pull-8 {
        position: relative;
        right: 33.3333333333%
    }

    .el-col-lg-push-8 {
        left: 33.3333333333%;
        position: relative
    }

    .el-col-lg-9 {
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .el-col-lg-9, .el-col-lg-9.is-guttered {
        display: block
    }

    .el-col-lg-offset-9 {
        margin-left: 37.5%
    }

    .el-col-lg-pull-9 {
        position: relative;
        right: 37.5%
    }

    .el-col-lg-push-9 {
        left: 37.5%;
        position: relative
    }

    .el-col-lg-10 {
        display: block;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }

    .el-col-lg-10.is-guttered {
        display: block
    }

    .el-col-lg-offset-10 {
        margin-left: 41.6666666667%
    }

    .el-col-lg-pull-10 {
        position: relative;
        right: 41.6666666667%
    }

    .el-col-lg-push-10 {
        left: 41.6666666667%;
        position: relative
    }

    .el-col-lg-11 {
        display: block;
        flex: 0 0 45.8333333333%;
        max-width: 45.8333333333%
    }

    .el-col-lg-11.is-guttered {
        display: block
    }

    .el-col-lg-offset-11 {
        margin-left: 45.8333333333%
    }

    .el-col-lg-pull-11 {
        position: relative;
        right: 45.8333333333%
    }

    .el-col-lg-push-11 {
        left: 45.8333333333%;
        position: relative
    }

    .el-col-lg-12 {
        display: block;
        flex: 0 0 50%;
        max-width: 50%
    }

    .el-col-lg-12.is-guttered {
        display: block
    }

    .el-col-lg-offset-12 {
        margin-left: 50%
    }

    .el-col-lg-pull-12 {
        position: relative;
        right: 50%
    }

    .el-col-lg-push-12 {
        left: 50%;
        position: relative
    }

    .el-col-lg-13 {
        display: block;
        flex: 0 0 54.1666666667%;
        max-width: 54.1666666667%
    }

    .el-col-lg-13.is-guttered {
        display: block
    }

    .el-col-lg-offset-13 {
        margin-left: 54.1666666667%
    }

    .el-col-lg-pull-13 {
        position: relative;
        right: 54.1666666667%
    }

    .el-col-lg-push-13 {
        left: 54.1666666667%;
        position: relative
    }

    .el-col-lg-14 {
        display: block;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }

    .el-col-lg-14.is-guttered {
        display: block
    }

    .el-col-lg-offset-14 {
        margin-left: 58.3333333333%
    }

    .el-col-lg-pull-14 {
        position: relative;
        right: 58.3333333333%
    }

    .el-col-lg-push-14 {
        left: 58.3333333333%;
        position: relative
    }

    .el-col-lg-15 {
        display: block;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .el-col-lg-15.is-guttered {
        display: block
    }

    .el-col-lg-offset-15 {
        margin-left: 62.5%
    }

    .el-col-lg-pull-15 {
        position: relative;
        right: 62.5%
    }

    .el-col-lg-push-15 {
        left: 62.5%;
        position: relative
    }

    .el-col-lg-16 {
        display: block;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }

    .el-col-lg-16.is-guttered {
        display: block
    }

    .el-col-lg-offset-16 {
        margin-left: 66.6666666667%
    }

    .el-col-lg-pull-16 {
        position: relative;
        right: 66.6666666667%
    }

    .el-col-lg-push-16 {
        left: 66.6666666667%;
        position: relative
    }

    .el-col-lg-17 {
        display: block;
        flex: 0 0 70.8333333333%;
        max-width: 70.8333333333%
    }

    .el-col-lg-17.is-guttered {
        display: block
    }

    .el-col-lg-offset-17 {
        margin-left: 70.8333333333%
    }

    .el-col-lg-pull-17 {
        position: relative;
        right: 70.8333333333%
    }

    .el-col-lg-push-17 {
        left: 70.8333333333%;
        position: relative
    }

    .el-col-lg-18 {
        display: block;
        flex: 0 0 75%;
        max-width: 75%
    }

    .el-col-lg-18.is-guttered {
        display: block
    }

    .el-col-lg-offset-18 {
        margin-left: 75%
    }

    .el-col-lg-pull-18 {
        position: relative;
        right: 75%
    }

    .el-col-lg-push-18 {
        left: 75%;
        position: relative
    }

    .el-col-lg-19 {
        display: block;
        flex: 0 0 79.1666666667%;
        max-width: 79.1666666667%
    }

    .el-col-lg-19.is-guttered {
        display: block
    }

    .el-col-lg-offset-19 {
        margin-left: 79.1666666667%
    }

    .el-col-lg-pull-19 {
        position: relative;
        right: 79.1666666667%
    }

    .el-col-lg-push-19 {
        left: 79.1666666667%;
        position: relative
    }

    .el-col-lg-20 {
        display: block;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }

    .el-col-lg-20.is-guttered {
        display: block
    }

    .el-col-lg-offset-20 {
        margin-left: 83.3333333333%
    }

    .el-col-lg-pull-20 {
        position: relative;
        right: 83.3333333333%
    }

    .el-col-lg-push-20 {
        left: 83.3333333333%;
        position: relative
    }

    .el-col-lg-21 {
        display: block;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .el-col-lg-21.is-guttered {
        display: block
    }

    .el-col-lg-offset-21 {
        margin-left: 87.5%
    }

    .el-col-lg-pull-21 {
        position: relative;
        right: 87.5%
    }

    .el-col-lg-push-21 {
        left: 87.5%;
        position: relative
    }

    .el-col-lg-22 {
        display: block;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }

    .el-col-lg-22.is-guttered {
        display: block
    }

    .el-col-lg-offset-22 {
        margin-left: 91.6666666667%
    }

    .el-col-lg-pull-22 {
        position: relative;
        right: 91.6666666667%
    }

    .el-col-lg-push-22 {
        left: 91.6666666667%;
        position: relative
    }

    .el-col-lg-23 {
        display: block;
        flex: 0 0 95.8333333333%;
        max-width: 95.8333333333%
    }

    .el-col-lg-23.is-guttered {
        display: block
    }

    .el-col-lg-offset-23 {
        margin-left: 95.8333333333%
    }

    .el-col-lg-pull-23 {
        position: relative;
        right: 95.8333333333%
    }

    .el-col-lg-push-23 {
        left: 95.8333333333%;
        position: relative
    }

    .el-col-lg-24 {
        display: block;
        flex: 0 0 100%;
        max-width: 100%
    }

    .el-col-lg-24.is-guttered {
        display: block
    }

    .el-col-lg-offset-24 {
        margin-left: 100%
    }

    .el-col-lg-pull-24 {
        position: relative;
        right: 100%
    }

    .el-col-lg-push-24 {
        left: 100%;
        position: relative
    }
}

@media only screen and (min-width: 1920px) {
    .el-col-xl-0 {
        display: none;
        flex: 0 0 0%;
        max-width: 0
    }

    .el-col-xl-0.is-guttered {
        display: none
    }

    .el-col-xl-offset-0 {
        margin-left: 0
    }

    .el-col-xl-pull-0 {
        position: relative;
        right: 0
    }

    .el-col-xl-push-0 {
        left: 0;
        position: relative
    }

    .el-col-xl-1 {
        flex: 0 0 4.1666666667%;
        max-width: 4.1666666667%
    }

    .el-col-xl-1, .el-col-xl-1.is-guttered {
        display: block
    }

    .el-col-xl-offset-1 {
        margin-left: 4.1666666667%
    }

    .el-col-xl-pull-1 {
        position: relative;
        right: 4.1666666667%
    }

    .el-col-xl-push-1 {
        left: 4.1666666667%;
        position: relative
    }

    .el-col-xl-2 {
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }

    .el-col-xl-2, .el-col-xl-2.is-guttered {
        display: block
    }

    .el-col-xl-offset-2 {
        margin-left: 8.3333333333%
    }

    .el-col-xl-pull-2 {
        position: relative;
        right: 8.3333333333%
    }

    .el-col-xl-push-2 {
        left: 8.3333333333%;
        position: relative
    }

    .el-col-xl-3 {
        flex: 0 0 12.5%;
        max-width: 12.5%
    }

    .el-col-xl-3, .el-col-xl-3.is-guttered {
        display: block
    }

    .el-col-xl-offset-3 {
        margin-left: 12.5%
    }

    .el-col-xl-pull-3 {
        position: relative;
        right: 12.5%
    }

    .el-col-xl-push-3 {
        left: 12.5%;
        position: relative
    }

    .el-col-xl-4 {
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }

    .el-col-xl-4, .el-col-xl-4.is-guttered {
        display: block
    }

    .el-col-xl-offset-4 {
        margin-left: 16.6666666667%
    }

    .el-col-xl-pull-4 {
        position: relative;
        right: 16.6666666667%
    }

    .el-col-xl-push-4 {
        left: 16.6666666667%;
        position: relative
    }

    .el-col-xl-5 {
        flex: 0 0 20.8333333333%;
        max-width: 20.8333333333%
    }

    .el-col-xl-5, .el-col-xl-5.is-guttered {
        display: block
    }

    .el-col-xl-offset-5 {
        margin-left: 20.8333333333%
    }

    .el-col-xl-pull-5 {
        position: relative;
        right: 20.8333333333%
    }

    .el-col-xl-push-5 {
        left: 20.8333333333%;
        position: relative
    }

    .el-col-xl-6 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .el-col-xl-6, .el-col-xl-6.is-guttered {
        display: block
    }

    .el-col-xl-offset-6 {
        margin-left: 25%
    }

    .el-col-xl-pull-6 {
        position: relative;
        right: 25%
    }

    .el-col-xl-push-6 {
        left: 25%;
        position: relative
    }

    .el-col-xl-7 {
        flex: 0 0 29.1666666667%;
        max-width: 29.1666666667%
    }

    .el-col-xl-7, .el-col-xl-7.is-guttered {
        display: block
    }

    .el-col-xl-offset-7 {
        margin-left: 29.1666666667%
    }

    .el-col-xl-pull-7 {
        position: relative;
        right: 29.1666666667%
    }

    .el-col-xl-push-7 {
        left: 29.1666666667%;
        position: relative
    }

    .el-col-xl-8 {
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }

    .el-col-xl-8, .el-col-xl-8.is-guttered {
        display: block
    }

    .el-col-xl-offset-8 {
        margin-left: 33.3333333333%
    }

    .el-col-xl-pull-8 {
        position: relative;
        right: 33.3333333333%
    }

    .el-col-xl-push-8 {
        left: 33.3333333333%;
        position: relative
    }

    .el-col-xl-9 {
        flex: 0 0 37.5%;
        max-width: 37.5%
    }

    .el-col-xl-9, .el-col-xl-9.is-guttered {
        display: block
    }

    .el-col-xl-offset-9 {
        margin-left: 37.5%
    }

    .el-col-xl-pull-9 {
        position: relative;
        right: 37.5%
    }

    .el-col-xl-push-9 {
        left: 37.5%;
        position: relative
    }

    .el-col-xl-10 {
        display: block;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }

    .el-col-xl-10.is-guttered {
        display: block
    }

    .el-col-xl-offset-10 {
        margin-left: 41.6666666667%
    }

    .el-col-xl-pull-10 {
        position: relative;
        right: 41.6666666667%
    }

    .el-col-xl-push-10 {
        left: 41.6666666667%;
        position: relative
    }

    .el-col-xl-11 {
        display: block;
        flex: 0 0 45.8333333333%;
        max-width: 45.8333333333%
    }

    .el-col-xl-11.is-guttered {
        display: block
    }

    .el-col-xl-offset-11 {
        margin-left: 45.8333333333%
    }

    .el-col-xl-pull-11 {
        position: relative;
        right: 45.8333333333%
    }

    .el-col-xl-push-11 {
        left: 45.8333333333%;
        position: relative
    }

    .el-col-xl-12 {
        display: block;
        flex: 0 0 50%;
        max-width: 50%
    }

    .el-col-xl-12.is-guttered {
        display: block
    }

    .el-col-xl-offset-12 {
        margin-left: 50%
    }

    .el-col-xl-pull-12 {
        position: relative;
        right: 50%
    }

    .el-col-xl-push-12 {
        left: 50%;
        position: relative
    }

    .el-col-xl-13 {
        display: block;
        flex: 0 0 54.1666666667%;
        max-width: 54.1666666667%
    }

    .el-col-xl-13.is-guttered {
        display: block
    }

    .el-col-xl-offset-13 {
        margin-left: 54.1666666667%
    }

    .el-col-xl-pull-13 {
        position: relative;
        right: 54.1666666667%
    }

    .el-col-xl-push-13 {
        left: 54.1666666667%;
        position: relative
    }

    .el-col-xl-14 {
        display: block;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }

    .el-col-xl-14.is-guttered {
        display: block
    }

    .el-col-xl-offset-14 {
        margin-left: 58.3333333333%
    }

    .el-col-xl-pull-14 {
        position: relative;
        right: 58.3333333333%
    }

    .el-col-xl-push-14 {
        left: 58.3333333333%;
        position: relative
    }

    .el-col-xl-15 {
        display: block;
        flex: 0 0 62.5%;
        max-width: 62.5%
    }

    .el-col-xl-15.is-guttered {
        display: block
    }

    .el-col-xl-offset-15 {
        margin-left: 62.5%
    }

    .el-col-xl-pull-15 {
        position: relative;
        right: 62.5%
    }

    .el-col-xl-push-15 {
        left: 62.5%;
        position: relative
    }

    .el-col-xl-16 {
        display: block;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }

    .el-col-xl-16.is-guttered {
        display: block
    }

    .el-col-xl-offset-16 {
        margin-left: 66.6666666667%
    }

    .el-col-xl-pull-16 {
        position: relative;
        right: 66.6666666667%
    }

    .el-col-xl-push-16 {
        left: 66.6666666667%;
        position: relative
    }

    .el-col-xl-17 {
        display: block;
        flex: 0 0 70.8333333333%;
        max-width: 70.8333333333%
    }

    .el-col-xl-17.is-guttered {
        display: block
    }

    .el-col-xl-offset-17 {
        margin-left: 70.8333333333%
    }

    .el-col-xl-pull-17 {
        position: relative;
        right: 70.8333333333%
    }

    .el-col-xl-push-17 {
        left: 70.8333333333%;
        position: relative
    }

    .el-col-xl-18 {
        display: block;
        flex: 0 0 75%;
        max-width: 75%
    }

    .el-col-xl-18.is-guttered {
        display: block
    }

    .el-col-xl-offset-18 {
        margin-left: 75%
    }

    .el-col-xl-pull-18 {
        position: relative;
        right: 75%
    }

    .el-col-xl-push-18 {
        left: 75%;
        position: relative
    }

    .el-col-xl-19 {
        display: block;
        flex: 0 0 79.1666666667%;
        max-width: 79.1666666667%
    }

    .el-col-xl-19.is-guttered {
        display: block
    }

    .el-col-xl-offset-19 {
        margin-left: 79.1666666667%
    }

    .el-col-xl-pull-19 {
        position: relative;
        right: 79.1666666667%
    }

    .el-col-xl-push-19 {
        left: 79.1666666667%;
        position: relative
    }

    .el-col-xl-20 {
        display: block;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }

    .el-col-xl-20.is-guttered {
        display: block
    }

    .el-col-xl-offset-20 {
        margin-left: 83.3333333333%
    }

    .el-col-xl-pull-20 {
        position: relative;
        right: 83.3333333333%
    }

    .el-col-xl-push-20 {
        left: 83.3333333333%;
        position: relative
    }

    .el-col-xl-21 {
        display: block;
        flex: 0 0 87.5%;
        max-width: 87.5%
    }

    .el-col-xl-21.is-guttered {
        display: block
    }

    .el-col-xl-offset-21 {
        margin-left: 87.5%
    }

    .el-col-xl-pull-21 {
        position: relative;
        right: 87.5%
    }

    .el-col-xl-push-21 {
        left: 87.5%;
        position: relative
    }

    .el-col-xl-22 {
        display: block;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }

    .el-col-xl-22.is-guttered {
        display: block
    }

    .el-col-xl-offset-22 {
        margin-left: 91.6666666667%
    }

    .el-col-xl-pull-22 {
        position: relative;
        right: 91.6666666667%
    }

    .el-col-xl-push-22 {
        left: 91.6666666667%;
        position: relative
    }

    .el-col-xl-23 {
        display: block;
        flex: 0 0 95.8333333333%;
        max-width: 95.8333333333%
    }

    .el-col-xl-23.is-guttered {
        display: block
    }

    .el-col-xl-offset-23 {
        margin-left: 95.8333333333%
    }

    .el-col-xl-pull-23 {
        position: relative;
        right: 95.8333333333%
    }

    .el-col-xl-push-23 {
        left: 95.8333333333%;
        position: relative
    }

    .el-col-xl-24 {
        display: block;
        flex: 0 0 100%;
        max-width: 100%
    }

    .el-col-xl-24.is-guttered {
        display: block
    }

    .el-col-xl-offset-24 {
        margin-left: 100%
    }

    .el-col-xl-pull-24 {
        position: relative;
        right: 100%
    }

    .el-col-xl-push-24 {
        left: 100%;
        position: relative
    }
}

.el-collapse {
    --el-collapse-border-color: var(--el-border-color-lighter);
    --el-collapse-header-height: 48px;
    --el-collapse-header-bg-color: var(--el-fill-color-blank);
    --el-collapse-header-text-color: var(--el-text-color-primary);
    --el-collapse-header-font-size: 13px;
    --el-collapse-content-bg-color: var(--el-fill-color-blank);
    --el-collapse-content-font-size: 13px;
    --el-collapse-content-text-color: var(--el-text-color-primary);
    border-bottom: 1px solid var(--el-collapse-border-color);
    border-top: 1px solid var(--el-collapse-border-color)
}

.el-collapse-item.is-disabled .el-collapse-item__header {
    color: var(--el-text-color-disabled);
    cursor: not-allowed
}

.el-collapse-item__header {
    align-items: center;
    background-color: var(--el-collapse-header-bg-color);
    border: none;
    border-bottom: 1px solid var(--el-collapse-border-color);
    color: var(--el-collapse-header-text-color);
    cursor: pointer;
    display: flex;
    font-size: var(--el-collapse-header-font-size);
    font-weight: 500;
    height: var(--el-collapse-header-height);
    line-height: var(--el-collapse-header-height);
    outline: none;
    padding: 0;
    transition: border-bottom-color var(--el-transition-duration);
    width: 100%
}

.el-collapse-item__arrow {
    font-weight: 300;
    transition: transform var(--el-transition-duration)
}

.el-collapse-item__arrow.is-active {
    transform: rotate(90deg)
}

.el-collapse-item__title {
    flex: auto;
    text-align: left
}

.el-collapse-item__header.focusing:focus:not(:hover) {
    color: var(--el-color-primary)
}

.el-collapse-item__header.is-active {
    border-bottom-color: transparent
}

.el-collapse-item__wrap {
    background-color: var(--el-collapse-content-bg-color);
    border-bottom: 1px solid var(--el-collapse-border-color);
    box-sizing: border-box;
    overflow: hidden;
    will-change: height
}

.el-collapse-item__content {
    color: var(--el-collapse-content-text-color);
    font-size: var(--el-collapse-content-font-size);
    line-height: 1.7692307692;
    padding-bottom: 25px
}

.el-collapse-item:last-child {
    margin-bottom: -1px
}

.el-collapse-icon-position-left .el-collapse-item__header {
    gap: 8px
}

.el-collapse-icon-position-left .el-collapse-item__title {
    order: 1
}

.el-collapse-icon-position-right .el-collapse-item__header {
    padding-right: 8px
}

.el-color-predefine {
    display: flex;
    font-size: 12px;
    margin-top: 8px;
    width: 280px
}

.el-color-predefine__colors {
    display: flex;
    flex: 1;
    flex-wrap: wrap
}

.el-color-predefine__color-selector {
    border-radius: 4px;
    cursor: pointer;
    height: 20px;
    margin: 0 0 8px 8px;
    width: 20px
}

.el-color-predefine__color-selector:nth-child(10n+1) {
    margin-left: 0
}

.el-color-predefine__color-selector.selected {
    box-shadow: 0 0 3px 2px var(--el-color-primary)
}

.el-color-predefine__color-selector > div {
    border-radius: 3px;
    display: flex;
    height: 100%
}

.el-color-predefine__color-selector.is-alpha {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==)
}

.el-color-hue-slider {
    background-color: #f00;
    box-sizing: border-box;
    float: right;
    height: 12px;
    padding: 0 2px;
    position: relative;
    width: 280px
}

.el-color-hue-slider__bar {
    background: linear-gradient(90deg, #f00 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00);
    height: 100%;
    position: relative
}

.el-color-hue-slider__thumb {
    background: #fff;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 1px;
    box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    box-sizing: border-box;
    cursor: pointer;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 4px;
    z-index: 1
}

.el-color-hue-slider__thumb:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 1px
}

.el-color-hue-slider.is-vertical {
    height: 180px;
    padding: 2px 0;
    width: 12px
}

.el-color-hue-slider.is-vertical .el-color-hue-slider__bar {
    background: linear-gradient(180deg, #f00 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00)
}

.el-color-hue-slider.is-vertical .el-color-hue-slider__thumb {
    height: 4px;
    left: 0;
    top: 0;
    width: 100%
}

.el-color-svpanel {
    height: 180px;
    position: relative;
    width: 280px
}

.el-color-svpanel__black, .el-color-svpanel__white {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.el-color-svpanel__white {
    background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0))
}

.el-color-svpanel__black {
    background: linear-gradient(0deg, #000, rgba(0, 0, 0, 0))
}

.el-color-svpanel__cursor {
    position: absolute
}

.el-color-svpanel__cursor > div {
    border-radius: 50%;
    box-shadow: 0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0, 0, 0, .3), 0 0 1px 2px rgba(0, 0, 0, .4);
    cursor: head;
    height: 4px;
    transform: translate(-2px, -2px);
    width: 4px
}

.el-color-alpha-slider {
    background-image: linear-gradient(45deg, var(--el-color-picker-alpha-bg-a) 25%, var(--el-color-picker-alpha-bg-b) 25%), linear-gradient(135deg, var(--el-color-picker-alpha-bg-a) 25%, var(--el-color-picker-alpha-bg-b) 25%), linear-gradient(45deg, var(--el-color-picker-alpha-bg-b) 75%, var(--el-color-picker-alpha-bg-a) 75%), linear-gradient(135deg, var(--el-color-picker-alpha-bg-b) 75%, var(--el-color-picker-alpha-bg-a) 75%);
    background-position: 0 0, 6px 0, 6px -6px, 0 6px;
    background-size: 12px 12px;
    box-sizing: border-box;
    height: 12px;
    position: relative;
    width: 280px
}

.el-color-alpha-slider__bar {
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0, var(--el-bg-color) 100%);
    height: 100%;
    position: relative
}

.el-color-alpha-slider__thumb {
    background: #fff;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 1px;
    box-shadow: 0 0 2px rgba(0, 0, 0, .6);
    box-sizing: border-box;
    cursor: pointer;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 4px;
    z-index: 1
}

.el-color-alpha-slider__thumb:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 1px
}

.el-color-alpha-slider.is-vertical {
    height: 180px;
    width: 20px
}

.el-color-alpha-slider.is-vertical .el-color-alpha-slider__bar {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0, rgb(255, 255, 255))
}

.el-color-alpha-slider.is-vertical .el-color-alpha-slider__thumb {
    height: 4px;
    left: 0;
    top: 0;
    width: 100%
}

.el-color-dropdown {
    width: 300px
}

.el-color-dropdown__main-wrapper {
    margin-bottom: 6px
}

.el-color-dropdown__main-wrapper:after {
    clear: both;
    content: "";
    display: table
}

.el-color-dropdown__btns {
    margin-top: 12px;
    text-align: right
}

.el-color-dropdown__value {
    color: #000000;
    float: left;
    font-size: 12px;
    line-height: 26px;
    width: 160px
}

.el-color-picker {
    display: inline-block;
    height: 32px;
    line-height: normal;
    outline: none;
    position: relative;
    width: 32px
}

.el-color-picker:hover:not(.is-disabled,.is-focused) .el-color-picker__trigger {
    border-color: var(--el-border-color-hover)
}

.el-color-picker:focus-visible:not(.is-disabled) .el-color-picker__trigger {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 1px
}

.el-color-picker.is-focused .el-color-picker__trigger {
    border-color: var(--el-color-primary)
}

.el-color-picker.is-disabled .el-color-picker__trigger {
    background-color: var(--el-fill-color-light);
    cursor: not-allowed
}

.el-color-picker.is-disabled .el-color-picker__color {
    opacity: .3
}

.el-color-picker--large {
    height: 40px;
    width: 40px
}

.el-color-picker--small {
    height: 24px;
    width: 24px
}

.el-color-picker--small .el-color-picker__empty, .el-color-picker--small .el-color-picker__icon {
    transform: scale(.8)
}

.el-color-picker__trigger {
    align-items: center;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    font-size: 0;
    justify-content: center;
    padding: 4px
}

.el-color-picker__color, .el-color-picker__trigger {
    box-sizing: border-box;
    height: 100%;
    position: relative;
    width: 100%
}

.el-color-picker__color {
    border: 1px solid var(--el-text-color-secondary);
    border-radius: var(--el-border-radius-small);
    display: block;
    text-align: center
}

.el-color-picker__color.is-alpha {
    background-image: linear-gradient(45deg, var(--el-color-picker-alpha-bg-a) 25%, var(--el-color-picker-alpha-bg-b) 25%), linear-gradient(135deg, var(--el-color-picker-alpha-bg-a) 25%, var(--el-color-picker-alpha-bg-b) 25%), linear-gradient(45deg, var(--el-color-picker-alpha-bg-b) 75%, var(--el-color-picker-alpha-bg-a) 75%), linear-gradient(135deg, var(--el-color-picker-alpha-bg-b) 75%, var(--el-color-picker-alpha-bg-a) 75%);
    background-position: 0 0, 6px 0, 6px -6px, 0 6px;
    background-size: 12px 12px
}

.el-color-picker__color-inner {
    align-items: center;
    display: inline-flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.el-color-picker .el-color-picker__empty {
    color: var(--el-text-color-secondary);
    font-size: 12px
}

.el-color-picker .el-color-picker__icon {
    align-items: center;
    color: #ffffff;
    display: inline-flex;
    font-size: 12px;
    justify-content: center
}

.el-color-picker__panel {
    background-color: #ffffff;
    border-radius: var(--el-border-radius-base);
    box-shadow: var(--el-box-shadow-light);
    box-sizing: content-box;
    padding: 6px;
    position: absolute;
    z-index: 10
}

.el-color-picker__panel.el-popper {
    border: 1px solid var(--el-border-color-lighter)
}

.el-color-picker, .el-color-picker__panel {
    --el-color-picker-alpha-bg-a: #ccc;
    --el-color-picker-alpha-bg-b: transparent
}

.dark .el-color-picker, .dark .el-color-picker__panel {
    --el-color-picker-alpha-bg-a: #333333
}

.el-container {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-basis: auto;
    flex-direction: row;
    min-width: 0
}

.el-container.is-vertical {
    flex-direction: column
}

.el-date-table {
    font-size: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-date-table.is-week-mode .el-date-table__row:hover .el-date-table-cell {
    background-color: var(--el-datepicker-inrange-bg-color)
}

.el-date-table.is-week-mode .el-date-table__row:hover td.available:hover {
    color: var(--el-datepicker-text-color)
}

.el-date-table.is-week-mode .el-date-table__row:hover td:first-child .el-date-table-cell {
    border-bottom-left-radius: 15px;
    border-top-left-radius: 15px;
    margin-left: 5px
}

.el-date-table.is-week-mode .el-date-table__row:hover td:last-child .el-date-table-cell {
    border-bottom-right-radius: 15px;
    border-top-right-radius: 15px;
    margin-right: 5px
}

.el-date-table.is-week-mode .el-date-table__row.current .el-date-table-cell {
    background-color: var(--el-datepicker-inrange-bg-color)
}

.el-date-table td {
    box-sizing: border-box;
    cursor: pointer;
    height: 30px;
    padding: 4px 0;
    position: relative;
    text-align: center;
    width: 32px
}

.el-date-table td .el-date-table-cell {
    box-sizing: border-box;
    height: 30px;
    padding: 3px 0
}

.el-date-table td .el-date-table-cell .el-date-table-cell__text {
    border-radius: 50%;
    display: block;
    height: 24px;
    left: 50%;
    line-height: 24px;
    margin: 0 auto;
    position: absolute;
    transform: translateX(-50%);
    width: 24px
}

.el-date-table td.next-month, .el-date-table td.prev-month {
    color: var(--el-datepicker-off-text-color)
}

.el-date-table td.today {
    position: relative
}

.el-date-table td.today .el-date-table-cell__text {
    color: var(--el-color-primary);
    font-weight: bold
}

.el-date-table td.today.end-date .el-date-table-cell__text, .el-date-table td.today.start-date .el-date-table-cell__text {
    color: #ffffff
}

.el-date-table td.available:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-date-table td.in-range .el-date-table-cell {
    background-color: var(--el-datepicker-inrange-bg-color)
}

.el-date-table td.in-range .el-date-table-cell:hover {
    background-color: var(--el-datepicker-inrange-hover-bg-color)
}

.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    color: #ffffff
}

.el-date-table td.current:not(.disabled):focus-visible .el-date-table-cell__text {
    outline: 2px solid var(--el-datepicker-active-color);
    outline-offset: 1px
}

.el-date-table td.end-date .el-date-table-cell, .el-date-table td.start-date .el-date-table-cell {
    color: #ffffff
}

.el-date-table td.end-date .el-date-table-cell__text, .el-date-table td.start-date .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color)
}

.el-date-table td.start-date .el-date-table-cell {
    border-bottom-left-radius: 15px;
    border-top-left-radius: 15px;
    margin-left: 5px
}

.el-date-table td.end-date .el-date-table-cell {
    border-bottom-right-radius: 15px;
    border-top-right-radius: 15px;
    margin-right: 5px
}

.el-date-table td.disabled .el-date-table-cell {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-placeholder);
    cursor: not-allowed;
    opacity: 1
}

.el-date-table td.selected .el-date-table-cell {
    border-radius: 15px;
    margin-left: 5px;
    margin-right: 5px
}

.el-date-table td.selected .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    border-radius: 15px;
    color: #ffffff
}

.el-date-table td.week {
    color: var(--el-datepicker-header-text-color);
    font-size: 80%
}

.el-date-table td:focus {
    outline: none
}

.el-date-table th {
    border-bottom: 1px solid var(--el-border-color-lighter);
    color: var(--el-datepicker-header-text-color);
    font-weight: 400;
    padding: 5px
}

.el-month-table {
    border-collapse: collapse;
    font-size: 12px;
    margin: -1px
}

.el-month-table td {
    cursor: pointer;
    padding: 8px 0;
    position: relative;
    text-align: center;
    width: 68px
}

.el-month-table td .el-date-table-cell {
    box-sizing: border-box;
    height: 48px;
    padding: 6px 0
}

.el-month-table td.today .el-date-table-cell__text {
    color: var(--el-color-primary);
    font-weight: bold
}

.el-month-table td.today.end-date .el-date-table-cell__text, .el-month-table td.today.start-date .el-date-table-cell__text {
    color: #ffffff
}

.el-month-table td.disabled .el-date-table-cell__text {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-month-table td.disabled .el-date-table-cell__text:hover {
    color: var(--el-text-color-placeholder)
}

.el-month-table td .el-date-table-cell__text {
    border-radius: 18px;
    color: var(--el-datepicker-text-color);
    display: block;
    height: 36px;
    left: 50%;
    line-height: 36px;
    margin: 0 auto;
    position: absolute;
    transform: translateX(-50%);
    width: 54px
}

.el-month-table td .el-date-table-cell__text:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-month-table td.in-range .el-date-table-cell {
    background-color: var(--el-datepicker-inrange-bg-color)
}

.el-month-table td.in-range .el-date-table-cell:hover {
    background-color: var(--el-datepicker-inrange-hover-bg-color)
}

.el-month-table td.end-date .el-date-table-cell, .el-month-table td.start-date .el-date-table-cell {
    color: #ffffff
}

.el-month-table td.end-date .el-date-table-cell__text, .el-month-table td.start-date .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    color: #ffffff
}

.el-month-table td.start-date .el-date-table-cell {
    border-bottom-left-radius: 24px;
    border-top-left-radius: 24px;
    margin-left: 3px
}

.el-month-table td.end-date .el-date-table-cell {
    border-bottom-right-radius: 24px;
    border-top-right-radius: 24px;
    margin-right: 3px
}

.el-month-table td.current:not(.disabled) .el-date-table-cell {
    border-radius: 24px;
    margin-left: 3px;
    margin-right: 3px
}

.el-month-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    color: #ffffff
}

.el-month-table td:focus-visible {
    outline: none
}

.el-month-table td:focus-visible .el-date-table-cell__text {
    outline: 2px solid var(--el-datepicker-active-color);
    outline-offset: 1px
}

.el-year-table {
    border-collapse: collapse;
    font-size: 12px;
    margin: -1px
}

.el-year-table .el-icon {
    color: var(--el-datepicker-icon-color)
}

.el-year-table td {
    cursor: pointer;
    padding: 8px 0;
    position: relative;
    text-align: center;
    width: 68px
}

.el-year-table td .el-date-table-cell {
    box-sizing: border-box;
    height: 48px;
    padding: 6px 0
}

.el-year-table td.today .el-date-table-cell__text {
    color: var(--el-color-primary);
    font-weight: bold
}

.el-year-table td.today.end-date .el-date-table-cell__text, .el-year-table td.today.start-date .el-date-table-cell__text {
    color: #ffffff
}

.el-year-table td.disabled .el-date-table-cell__text {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-year-table td.disabled .el-date-table-cell__text:hover {
    color: var(--el-text-color-placeholder)
}

.el-year-table td .el-date-table-cell__text {
    border-radius: 18px;
    color: var(--el-datepicker-text-color);
    display: block;
    height: 36px;
    left: 50%;
    line-height: 36px;
    margin: 0 auto;
    position: absolute;
    transform: translateX(-50%);
    width: 60px
}

.el-year-table td .el-date-table-cell__text:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-year-table td.in-range .el-date-table-cell {
    background-color: var(--el-datepicker-inrange-bg-color)
}

.el-year-table td.in-range .el-date-table-cell:hover {
    background-color: var(--el-datepicker-inrange-hover-bg-color)
}

.el-year-table td.end-date .el-date-table-cell, .el-year-table td.start-date .el-date-table-cell {
    color: #ffffff
}

.el-year-table td.end-date .el-date-table-cell__text, .el-year-table td.start-date .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    color: #ffffff
}

.el-year-table td.start-date .el-date-table-cell {
    border-bottom-left-radius: 24px;
    border-top-left-radius: 24px
}

.el-year-table td.end-date .el-date-table-cell {
    border-bottom-right-radius: 24px;
    border-top-right-radius: 24px
}

.el-year-table td.current:not(.disabled) .el-date-table-cell__text {
    background-color: var(--el-datepicker-active-color);
    color: #ffffff
}

.el-year-table td:focus-visible {
    outline: none
}

.el-year-table td:focus-visible .el-date-table-cell__text {
    outline: 2px solid var(--el-datepicker-active-color);
    outline-offset: 1px
}

.el-time-spinner.has-seconds .el-time-spinner__wrapper {
    width: 33.3%
}

.el-time-spinner__wrapper {
    display: inline-block;
    max-height: 192px;
    overflow: auto;
    position: relative;
    vertical-align: top;
    width: 50%
}

.el-time-spinner__wrapper.el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
    padding-bottom: 15px
}

.el-time-spinner__wrapper.is-arrow {
    box-sizing: border-box;
    overflow: hidden;
    text-align: center
}

.el-time-spinner__wrapper.is-arrow .el-time-spinner__list {
    transform: translateY(-32px)
}

.el-time-spinner__wrapper.is-arrow .el-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
    background: var(--el-fill-color-light);
    cursor: default
}

.el-time-spinner__arrow {
    color: var(--el-text-color-secondary);
    cursor: pointer;
    font-size: 12px;
    height: 30px;
    left: 0;
    line-height: 30px;
    position: absolute;
    text-align: center;
    width: 100%;
    z-index: var(--el-index-normal)
}

.el-time-spinner__arrow:hover {
    color: var(--el-color-primary)
}

.el-time-spinner__arrow.arrow-up {
    top: 10px
}

.el-time-spinner__arrow.arrow-down {
    bottom: 10px
}

.el-time-spinner__input.el-input {
    width: 70%
}

.el-time-spinner__input.el-input .el-input__inner, .el-time-spinner__list {
    padding: 0;
    text-align: center
}

.el-time-spinner__list {
    list-style: none;
    margin: 0
}

.el-time-spinner__list:after, .el-time-spinner__list:before {
    content: "";
    display: block;
    height: 80px;
    width: 100%
}

.el-time-spinner__item {
    color: var(--el-text-color-regular);
    font-size: 12px;
    height: 32px;
    line-height: 32px
}

.el-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
    background: var(--el-fill-color-light);
    cursor: pointer
}

.el-time-spinner__item.is-active:not(.is-disabled) {
    color: var(--el-text-color-primary);
    font-weight: bold
}

.el-time-spinner__item.is-disabled {
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-picker__popper {
    --el-datepicker-border-color: var(--el-disabled-border-color)
}

.el-picker__popper.el-popper {
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light)
}

.el-picker__popper.el-popper, .el-picker__popper.el-popper .el-popper__arrow:before {
    border: 1px solid var(--el-datepicker-border-color)
}

.el-picker__popper.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-picker__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-picker__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-picker__popper.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-date-editor {
    --el-date-editor-width: 220px;
    --el-date-editor-monthrange-width: 300px;
    --el-date-editor-daterange-width: 350px;
    --el-date-editor-datetimerange-width: 400px;
    --el-input-text-color: var(--el-text-color-regular);
    --el-input-border: var(--el-border);
    --el-input-hover-border: var(--el-border-color-hover);
    --el-input-focus-border: var(--el-color-primary);
    --el-input-transparent-border: 0 0 0 1px transparent inset;
    --el-input-border-color: var(--el-border-color);
    --el-input-border-radius: var(--el-border-radius-base);
    --el-input-bg-color: var(--el-fill-color-blank);
    --el-input-icon-color: var(--el-text-color-placeholder);
    --el-input-placeholder-color: var(--el-text-color-placeholder);
    --el-input-hover-border-color: var(--el-border-color-hover);
    --el-input-clear-hover-color: var(--el-text-color-secondary);
    --el-input-focus-border-color: var(--el-color-primary);
    --el-input-width: 100%;
    position: relative;
    text-align: left;
    vertical-align: middle
}

.el-date-editor.el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset
}

.el-date-editor.el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset
}

.el-date-editor.el-input, .el-date-editor.el-input__wrapper {
    height: var(--el-input-height, var(--el-component-size));
    width: var(--el-date-editor-width)
}

.el-date-editor--monthrange {
    --el-date-editor-width: var(--el-date-editor-monthrange-width)
}

.el-date-editor--daterange, .el-date-editor--timerange {
    --el-date-editor-width: var(--el-date-editor-daterange-width)
}

.el-date-editor--datetimerange {
    --el-date-editor-width: var(--el-date-editor-datetimerange-width)
}

.el-date-editor--dates .el-input__wrapper {
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-date-editor .clear-icon, .el-date-editor .close-icon {
    cursor: pointer
}

.el-date-editor .clear-icon:hover {
    color: var(--el-input-clear-hover-color)
}

.el-date-editor .el-range__icon {
    color: var(--el-text-color-placeholder);
    float: left;
    font-size: 14px;
    height: inherit
}

.el-date-editor .el-range__icon svg {
    vertical-align: middle
}

.el-date-editor .el-range-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: transparent;
    border: none;
    color: var(--el-text-color-regular);
    display: inline-block;
    font-size: var(--el-font-size-base);
    height: 30px;
    line-height: 30px;
    margin: 0;
    outline: none;
    padding: 0;
    text-align: center;
    width: 39%
}

.el-date-editor .el-range-input::-moz-placeholder {
    color: var(--el-text-color-placeholder)
}

.el-date-editor .el-range-input::placeholder {
    color: var(--el-text-color-placeholder)
}

.el-date-editor .el-range-separator {
    align-items: center;
    color: var(--el-text-color-primary);
    display: inline-flex;
    flex: 1;
    font-size: 14px;
    height: 100%;
    justify-content: center;
    margin: 0;
    overflow-wrap: break-word;
    padding: 0 5px
}

.el-date-editor .el-range__close-icon {
    color: var(--el-text-color-placeholder);
    cursor: pointer;
    font-size: 14px;
    height: inherit;
    width: unset
}

.el-date-editor .el-range__close-icon:hover {
    color: var(--el-input-clear-hover-color)
}

.el-date-editor .el-range__close-icon svg {
    vertical-align: middle
}

.el-date-editor .el-range__close-icon--hidden {
    opacity: 0;
    visibility: hidden
}

.el-range-editor.el-input__wrapper {
    align-items: center;
    display: inline-flex;
    padding: 0 10px;
    vertical-align: middle
}

.el-range-editor.is-active, .el-range-editor.is-active:hover {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset
}

.el-range-editor--large {
    line-height: var(--el-component-size-large)
}

.el-range-editor--large.el-input__wrapper {
    height: var(--el-component-size-large)
}

.el-range-editor--large .el-range-separator {
    font-size: 14px;
    line-height: 40px
}

.el-range-editor--large .el-range-input {
    font-size: 14px;
    height: 38px;
    line-height: 38px
}

.el-range-editor--small {
    line-height: var(--el-component-size-small)
}

.el-range-editor--small.el-input__wrapper {
    height: var(--el-component-size-small)
}

.el-range-editor--small .el-range-separator {
    font-size: 12px;
    line-height: 24px
}

.el-range-editor--small .el-range-input {
    font-size: 12px;
    height: 22px;
    line-height: 22px
}

.el-range-editor.is-disabled {
    background-color: var(--el-disabled-bg-color);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-range-editor.is-disabled, .el-range-editor.is-disabled:focus, .el-range-editor.is-disabled:hover {
    border-color: var(--el-disabled-border-color)
}

.el-range-editor.is-disabled input {
    background-color: var(--el-disabled-bg-color);
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-range-editor.is-disabled input::-moz-placeholder {
    color: var(--el-text-color-placeholder)
}

.el-range-editor.is-disabled input::placeholder {
    color: var(--el-text-color-placeholder)
}

.el-range-editor.is-disabled .el-range-separator {
    color: var(--el-disabled-text-color)
}

.el-picker-panel {
    background: var(--el-bg-color-overlay);
    border-radius: var(--el-popper-border-radius, var(--el-border-radius-base));
    color: var(--el-text-color-regular);
    line-height: 30px
}

.el-picker-panel .el-time-panel {
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-datepicker-border-color);
    box-shadow: var(--el-box-shadow-light);
    margin: 5px 0
}

.el-picker-panel__body-wrapper:after, .el-picker-panel__body:after {
    clear: both;
    content: "";
    display: table
}

.el-picker-panel__content {
    margin: 15px;
    position: relative
}

.el-picker-panel__footer {
    background-color: var(--el-bg-color-overlay);
    border-top: 1px solid var(--el-datepicker-inner-border-color);
    font-size: 0;
    padding: 4px 12px;
    position: relative;
    text-align: right
}

.el-picker-panel__shortcut {
    background-color: transparent;
    border: 0;
    color: var(--el-datepicker-text-color);
    cursor: pointer;
    display: block;
    font-size: 14px;
    line-height: 28px;
    outline: none;
    padding-left: 12px;
    text-align: left;
    width: 100%
}

.el-picker-panel__shortcut:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-picker-panel__shortcut.active {
    background-color: #e6f1fe;
    color: var(--el-datepicker-active-color)
}

.el-picker-panel__btn {
    background-color: transparent;
    border: 1px solid var(--el-fill-color-darker);
    border-radius: 2px;
    color: var(--el-text-color-primary);
    cursor: pointer;
    font-size: 12px;
    line-height: 24px;
    outline: none;
    padding: 0 20px
}

.el-picker-panel__btn[disabled] {
    color: var(--el-text-color-disabled);
    cursor: not-allowed
}

.el-picker-panel__icon-btn {
    background: transparent;
    border: 0;
    color: var(--el-datepicker-icon-color);
    cursor: pointer;
    font-size: 12px;
    margin-top: 8px;
    outline: none
}

.el-picker-panel__icon-btn:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-picker-panel__icon-btn:focus-visible {
    color: var(--el-datepicker-hover-text-color)
}

.el-picker-panel__icon-btn.is-disabled {
    color: var(--el-text-color-disabled)
}

.el-picker-panel__icon-btn.is-disabled:hover {
    cursor: not-allowed
}

.el-picker-panel__icon-btn .el-icon {
    cursor: pointer;
    font-size: inherit
}

.el-picker-panel__link-btn {
    vertical-align: middle
}

.el-picker-panel [slot=sidebar], .el-picker-panel__sidebar {
    border-right: 1px solid var(--el-datepicker-inner-border-color);
    bottom: 0;
    box-sizing: border-box;
    overflow: auto;
    padding-top: 6px;
    position: absolute;
    top: 0;
    width: 110px
}

.el-picker-panel [slot=sidebar] + .el-picker-panel__body, .el-picker-panel__sidebar + .el-picker-panel__body {
    margin-left: 110px
}

.el-date-picker {
    --el-datepicker-text-color: var(--el-text-color-regular);
    --el-datepicker-off-text-color: var(--el-text-color-placeholder);
    --el-datepicker-header-text-color: var(--el-text-color-regular);
    --el-datepicker-icon-color: var(--el-text-color-primary);
    --el-datepicker-border-color: var(--el-disabled-border-color);
    --el-datepicker-inner-border-color: var(--el-border-color-light);
    --el-datepicker-inrange-bg-color: var(--el-border-color-extra-light);
    --el-datepicker-inrange-hover-bg-color: var(--el-border-color-extra-light);
    --el-datepicker-active-color: var(--el-color-primary);
    --el-datepicker-hover-text-color: var(--el-color-primary);
    width: 322px
}

.el-date-picker.has-sidebar.has-time {
    width: 434px
}

.el-date-picker.has-sidebar {
    width: 438px
}

.el-date-picker.has-time .el-picker-panel__body-wrapper {
    position: relative
}

.el-date-picker .el-picker-panel__content {
    width: 292px
}

.el-date-picker table {
    table-layout: fixed;
    width: 100%
}

.el-date-picker__editor-wrap {
    display: table-cell;
    padding: 0 5px;
    position: relative
}

.el-date-picker__time-header {
    border-bottom: 1px solid var(--el-datepicker-inner-border-color);
    box-sizing: border-box;
    display: table;
    font-size: 12px;
    padding: 8px 5px 5px;
    position: relative;
    width: 100%
}

.el-date-picker__header {
    padding: 12px 12px 0;
    text-align: center
}

.el-date-picker__header--bordered {
    border-bottom: 1px solid var(--el-border-color-lighter);
    margin-bottom: 0;
    padding-bottom: 12px
}

.el-date-picker__header--bordered + .el-picker-panel__content {
    margin-top: 0
}

.el-date-picker__header-label {
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    padding: 0 5px;
    text-align: center
}

.el-date-picker__header-label:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-date-picker__header-label:focus-visible {
    color: var(--el-datepicker-hover-text-color);
    outline: none
}

.el-date-picker__header-label.active {
    color: var(--el-datepicker-active-color)
}

.el-date-picker__prev-btn {
    float: left
}

.el-date-picker__next-btn {
    float: right
}

.el-date-picker__time-wrap {
    padding: 10px;
    text-align: center
}

.el-date-picker__time-label {
    cursor: pointer;
    float: left;
    line-height: 30px;
    margin-left: 10px
}

.el-date-picker .el-time-panel {
    position: absolute
}

.el-date-range-picker {
    --el-datepicker-text-color: var(--el-text-color-regular);
    --el-datepicker-off-text-color: var(--el-text-color-placeholder);
    --el-datepicker-header-text-color: var(--el-text-color-regular);
    --el-datepicker-icon-color: var(--el-text-color-primary);
    --el-datepicker-border-color: var(--el-disabled-border-color);
    --el-datepicker-inner-border-color: var(--el-border-color-light);
    --el-datepicker-inrange-bg-color: var(--el-border-color-extra-light);
    --el-datepicker-inrange-hover-bg-color: var(--el-border-color-extra-light);
    --el-datepicker-active-color: var(--el-color-primary);
    --el-datepicker-hover-text-color: var(--el-color-primary);
    width: 646px
}

.el-date-range-picker.has-sidebar {
    width: 756px
}

.el-date-range-picker.has-time .el-picker-panel__body-wrapper {
    position: relative
}

.el-date-range-picker table {
    table-layout: fixed;
    width: 100%
}

.el-date-range-picker .el-picker-panel__body {
    min-width: 513px
}

.el-date-range-picker .el-picker-panel__content {
    margin: 0
}

.el-date-range-picker__header {
    height: 28px;
    position: relative;
    text-align: center
}

.el-date-range-picker__header [class*=arrow-left] {
    float: left
}

.el-date-range-picker__header [class*=arrow-right] {
    float: right
}

.el-date-range-picker__header div {
    font-size: 16px;
    font-weight: 500;
    margin-right: 50px
}

.el-date-range-picker__header-label {
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    padding: 0 5px;
    text-align: center
}

.el-date-range-picker__header-label:hover {
    color: var(--el-datepicker-hover-text-color)
}

.el-date-range-picker__header-label:focus-visible {
    color: var(--el-datepicker-hover-text-color);
    outline: none
}

.el-date-range-picker__header-label.active {
    color: var(--el-datepicker-active-color)
}

.el-date-range-picker__content {
    box-sizing: border-box;
    float: left;
    margin: 0;
    padding: 16px;
    width: 50%
}

.el-date-range-picker__content.is-left {
    border-right: 1px solid var(--el-datepicker-inner-border-color)
}

.el-date-range-picker__content .el-date-range-picker__header div {
    margin-left: 50px;
    margin-right: 50px
}

.el-date-range-picker__editors-wrap {
    box-sizing: border-box;
    display: table-cell
}

.el-date-range-picker__editors-wrap.is-right {
    text-align: right
}

.el-date-range-picker__time-header {
    border-bottom: 1px solid var(--el-datepicker-inner-border-color);
    box-sizing: border-box;
    display: table;
    font-size: 12px;
    padding: 8px 5px 5px;
    position: relative;
    width: 100%
}

.el-date-range-picker__time-header > .el-icon-arrow-right {
    color: var(--el-datepicker-icon-color);
    display: table-cell;
    font-size: 20px;
    vertical-align: middle
}

.el-date-range-picker__time-picker-wrap {
    display: table-cell;
    padding: 0 5px;
    position: relative
}

.el-date-range-picker__time-picker-wrap .el-picker-panel {
    background: #ffffff;
    position: absolute;
    right: 0;
    top: 13px;
    z-index: 1
}

.el-date-range-picker__time-picker-wrap .el-time-panel {
    position: absolute
}

.el-time-range-picker {
    overflow: visible;
    width: 354px
}

.el-time-range-picker__content {
    padding: 10px;
    position: relative;
    text-align: center;
    z-index: 1
}

.el-time-range-picker__cell {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    padding: 4px 7px 7px;
    width: 50%
}

.el-time-range-picker__header {
    font-size: 14px;
    margin-bottom: 5px;
    text-align: center
}

.el-time-range-picker__body {
    border: 1px solid var(--el-datepicker-border-color);
    border-radius: 2px
}

.el-time-panel {
    border-radius: 2px;
    box-sizing: content-box;
    left: 0;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 180px;
    z-index: var(--el-index-top)
}

.el-time-panel__content {
    font-size: 0;
    overflow: hidden;
    position: relative
}

.el-time-panel__content:after, .el-time-panel__content:before {
    box-sizing: border-box;
    content: "";
    height: 32px;
    left: 0;
    margin-top: -16px;
    padding-top: 6px;
    position: absolute;
    right: 0;
    text-align: left;
    top: 50%;
    z-index: -1
}

.el-time-panel__content:after {
    left: 50%;
    margin-left: 12%;
    margin-right: 12%
}

.el-time-panel__content:before {
    border-bottom: 1px solid var(--el-border-color-light);
    border-top: 1px solid var(--el-border-color-light);
    margin-left: 12%;
    margin-right: 12%;
    padding-left: 50%
}

.el-time-panel__content.has-seconds:after {
    left: 66.6666666667%
}

.el-time-panel__content.has-seconds:before {
    padding-left: 33.3333333333%
}

.el-time-panel__footer {
    border-top: 1px solid var(--el-timepicker-inner-border-color, var(--el-border-color-light));
    box-sizing: border-box;
    height: 36px;
    line-height: 25px;
    padding: 4px;
    text-align: right
}

.el-time-panel__btn {
    background-color: transparent;
    border: none;
    color: var(--el-text-color-primary);
    cursor: pointer;
    font-size: 12px;
    line-height: 28px;
    margin: 0 5px;
    outline: none;
    padding: 0 5px
}

.el-time-panel__btn.confirm {
    color: var(--el-timepicker-active-color, var(--el-color-primary));
    font-weight: 800
}

.el-descriptions {
    --el-descriptions-table-border: 1px solid var(--el-border-color-lighter);
    --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
    box-sizing: border-box;
    color: var(--el-text-color-primary);
    font-size: var(--el-font-size-base)
}

.el-descriptions__header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px
}

.el-descriptions__title {
    color: var(--el-text-color-primary);
    font-size: 16px;
    font-weight: bold
}

.el-descriptions__body {
    background-color: var(--el-fill-color-blank)
}

.el-descriptions__body .el-descriptions__table {
    border-collapse: collapse;
    width: 100%
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell {
    box-sizing: border-box;
    font-size: 14px;
    line-height: 23px;
    text-align: left
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-left {
    text-align: left
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
    text-align: center
}

.el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-right {
    text-align: right
}

.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
    border: var(--el-descriptions-table-border);
    padding: 8px 11px
}

.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    padding-bottom: 12px
}

.el-descriptions--large {
    font-size: 14px
}

.el-descriptions--large .el-descriptions__header {
    margin-bottom: 20px
}

.el-descriptions--large .el-descriptions__header .el-descriptions__title {
    font-size: 16px
}

.el-descriptions--large .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
    font-size: 14px
}

.el-descriptions--large .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
    padding: 12px 15px
}

.el-descriptions--large .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    padding-bottom: 16px
}

.el-descriptions--small {
    font-size: 12px
}

.el-descriptions--small .el-descriptions__header {
    margin-bottom: 12px
}

.el-descriptions--small .el-descriptions__header .el-descriptions__title {
    font-size: 14px
}

.el-descriptions--small .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
    font-size: 12px
}

.el-descriptions--small .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
    padding: 4px 7px
}

.el-descriptions--small .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    padding-bottom: 8px
}

.el-descriptions__label.el-descriptions__cell.is-bordered-label {
    background: var(--el-descriptions-item-bordered-label-background);
    color: var(--el-text-color-regular);
    font-weight: bold
}

.el-descriptions__label:not(.is-bordered-label) {
    color: var(--el-text-color-primary);
    margin-right: 16px
}

.el-descriptions__label.el-descriptions__cell:not(.is-bordered-label).is-vertical-label {
    padding-bottom: 6px
}

.el-descriptions__content.el-descriptions__cell.is-bordered-content {
    color: var(--el-text-color-primary)
}

.el-descriptions__content:not(.is-bordered-label) {
    color: var(--el-text-color-regular)
}

.el-descriptions--large .el-descriptions__label:not(.is-bordered-label) {
    margin-right: 16px
}

.el-descriptions--large .el-descriptions__label.el-descriptions__cell:not(.is-bordered-label).is-vertical-label {
    padding-bottom: 8px
}

.el-descriptions--small .el-descriptions__label:not(.is-bordered-label) {
    margin-right: 12px
}

.el-descriptions--small .el-descriptions__label.el-descriptions__cell:not(.is-bordered-label).is-vertical-label {
    padding-bottom: 4px
}

:root {
    --el-popup-modal-bg-color: var(--el-color-black);
    --el-popup-modal-opacity: 0.5
}

.v-modal-enter {
    animation: v-modal-in var(--el-transition-duration-fast) ease
}

.v-modal-leave {
    animation: v-modal-out var(--el-transition-duration-fast) ease forwards
}

@keyframes v-modal-in {
    0% {
        opacity: 0
    }
}

@keyframes v-modal-out {
    to {
        opacity: 0
    }
}

.v-modal {
    background: var(--el-popup-modal-bg-color);
    height: 100%;
    left: 0;
    opacity: var(--el-popup-modal-opacity);
    position: fixed;
    top: 0;
    width: 100%
}

.el-popup-parent--hidden {
    overflow: hidden
}

.el-dialog {
    --el-dialog-width: 50%;
    --el-dialog-margin-top: 15vh;
    --el-dialog-bg-color: var(--el-bg-color);
    --el-dialog-box-shadow: var(--el-box-shadow);
    --el-dialog-title-font-size: var(--el-font-size-large);
    --el-dialog-content-font-size: 14px;
    --el-dialog-font-line-height: var(--el-font-line-height-primary);
    --el-dialog-padding-primary: 16px;
    --el-dialog-border-radius: var(--el-border-radius-base);
    background: var(--el-dialog-bg-color);
    border-radius: var(--el-dialog-border-radius);
    box-shadow: var(--el-dialog-box-shadow);
    box-sizing: border-box;
    margin: var(--el-dialog-margin-top, 15vh) auto 50px;
    overflow-wrap: break-word;
    padding: var(--el-dialog-padding-primary);
    position: relative;
    width: var(--el-dialog-width, 50%)
}

.el-dialog:focus {
    outline: none !important
}

.el-dialog.is-align-center {
    margin: auto
}

.el-dialog.is-fullscreen {
    --el-dialog-width: 100%;
    --el-dialog-margin-top: 0;
    border-radius: 0;
    height: 100%;
    margin-bottom: 0;
    overflow: auto
}

.el-dialog__wrapper {
    bottom: 0;
    left: 0;
    margin: 0;
    overflow: auto;
    position: fixed;
    right: 0;
    top: 0
}

.el-dialog.is-draggable .el-dialog__header {
    cursor: move;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-dialog__header {
    padding-bottom: var(--el-dialog-padding-primary)
}

.el-dialog__header.show-close {
    padding-right: calc(var(--el-dialog-padding-primary) + var(--el-message-close-size, 16px))
}

.el-dialog__headerbtn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: var(--el-message-close-size, 16px);
    height: 48px;
    outline: none;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 48px
}

.el-dialog__headerbtn .el-dialog__close {
    color: var(--el-color-info);
    font-size: inherit
}

.el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
    color: var(--el-color-primary)
}

.el-dialog__title {
    color: var(--el-text-color-primary);
    font-size: var(--el-dialog-title-font-size);
    line-height: var(--el-dialog-font-line-height)
}

.el-dialog__body {
    color: var(--el-text-color-regular);
    font-size: var(--el-dialog-content-font-size)
}

.el-dialog__footer {
    box-sizing: border-box;
    padding-top: var(--el-dialog-padding-primary);
    text-align: right
}

.el-dialog--center {
    text-align: center
}

.el-dialog--center .el-dialog__body {
    text-align: initial
}

.el-dialog--center .el-dialog__footer {
    text-align: inherit
}

.el-overlay-dialog {
    bottom: 0;
    left: 0;
    overflow: auto;
    position: fixed;
    right: 0;
    top: 0
}

.dialog-fade-enter-active {
    animation: modal-fade-in var(--el-transition-duration)
}

.dialog-fade-enter-active .el-overlay-dialog {
    animation: dialog-fade-in var(--el-transition-duration)
}

.dialog-fade-leave-active {
    animation: modal-fade-out var(--el-transition-duration)
}

.dialog-fade-leave-active .el-overlay-dialog {
    animation: dialog-fade-out var(--el-transition-duration)
}

@keyframes dialog-fade-in {
    0% {
        opacity: 0;
        transform: translate3d(0, -20px, 0)
    }
    to {
        opacity: 1;
        transform: translateZ(0)
    }
}

@keyframes dialog-fade-out {
    0% {
        opacity: 1;
        transform: translateZ(0)
    }
    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0)
    }
}

@keyframes modal-fade-in {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes modal-fade-out {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

.el-divider {
    position: relative
}

.el-divider--horizontal {
    border-top: 1px var(--el-border-color) var(--el-border-style);
    display: block;
    height: 1px;
    margin: 24px 0;
    width: 100%
}

.el-divider--vertical {
    border-left: 1px var(--el-border-color) var(--el-border-style);
    display: inline-block;
    height: 1em;
    margin: 0 8px;
    position: relative;
    vertical-align: middle;
    width: 1px
}

.el-divider__text {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
    font-size: 14px;
    font-weight: 500;
    padding: 0 20px;
    position: absolute
}

.el-divider__text.is-left {
    left: 20px;
    transform: translateY(-50%)
}

.el-divider__text.is-center {
    left: 50%;
    transform: translateX(-50%) translateY(-50%)
}

.el-divider__text.is-right {
    right: 20px;
    transform: translateY(-50%)
}

.el-drawer {
    --el-drawer-bg-color: var(--el-dialog-bg-color, var(--el-bg-color));
    --el-drawer-padding-primary: var(--el-dialog-padding-primary, 20px);
    background-color: var(--el-drawer-bg-color);
    box-shadow: var(--el-box-shadow-dark);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    transition: all var(--el-transition-duration)
}

.el-drawer .btt, .el-drawer .ltr, .el-drawer .rtl, .el-drawer .ttb {
    transform: translate(0)
}

.el-drawer__sr-focus:focus {
    outline: none !important
}

.el-drawer__header {
    align-items: center;
    color: rgb(114, 118, 123);
    display: flex;
    margin-bottom: 32px;
    padding: var(--el-drawer-padding-primary);
    padding-bottom: 0
}

.el-drawer__header > :first-child {
    flex: 1
}

.el-drawer__title {
    flex: 1;
    font-size: 16px;
    line-height: inherit;
    margin: 0
}

.el-drawer__footer {
    padding: var(--el-drawer-padding-primary);
    padding-top: 10px;
    text-align: right
}

.el-drawer__close-btn {
    background-color: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    display: inline-flex;
    font-size: var(--el-font-size-extra-large);
    outline: none
}

.el-drawer__close-btn:focus i, .el-drawer__close-btn:hover i {
    color: var(--el-color-primary)
}

.el-drawer__body {
    flex: 1;
    overflow: auto;
    padding: var(--el-drawer-padding-primary)
}

.el-drawer__body > * {
    box-sizing: border-box
}

.el-drawer.ltr, .el-drawer.rtl {
    bottom: 0;
    height: 100%;
    top: 0
}

.el-drawer.btt, .el-drawer.ttb {
    left: 0;
    right: 0;
    width: 100%
}

.el-drawer.ltr {
    left: 0
}

.el-drawer.rtl {
    right: 0
}

.el-drawer.ttb {
    top: 0
}

.el-drawer.btt {
    bottom: 0
}

.el-drawer-fade-enter-active, .el-drawer-fade-leave-active {
    transition: all var(--el-transition-duration)
}

.el-drawer-fade-enter-active, .el-drawer-fade-enter-from, .el-drawer-fade-enter-to, .el-drawer-fade-leave-active, .el-drawer-fade-leave-from, .el-drawer-fade-leave-to {
    overflow: hidden !important
}

.el-drawer-fade-enter-from, .el-drawer-fade-leave-to {
    background-color: transparent !important
}

.el-drawer-fade-enter-from .rtl, .el-drawer-fade-leave-to .rtl {
    transform: translateX(100%)
}

.el-drawer-fade-enter-from .ltr, .el-drawer-fade-leave-to .ltr {
    transform: translateX(-100%)
}

.el-drawer-fade-enter-from .ttb, .el-drawer-fade-leave-to .ttb {
    transform: translateY(-100%)
}

.el-drawer-fade-enter-from .btt, .el-drawer-fade-leave-to .btt {
    transform: translateY(100%)
}

.el-dropdown {
    --el-dropdown-menu-box-shadow: var(--el-box-shadow-light);
    --el-dropdown-menuItem-hover-fill: var(--el-color-primary-light-9);
    --el-dropdown-menuItem-hover-color: var(--el-color-primary);
    --el-dropdown-menu-index: 10;
    color: var(--el-text-color-regular);
    display: inline-flex;
    font-size: var(--el-font-size-base);
    line-height: 1;
    position: relative;
    vertical-align: top
}

.el-dropdown.is-disabled {
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-dropdown__popper {
    --el-dropdown-menu-box-shadow: var(--el-box-shadow-light);
    --el-dropdown-menuItem-hover-fill: var(--el-color-primary-light-9);
    --el-dropdown-menuItem-hover-color: var(--el-color-primary);
    --el-dropdown-menu-index: 10
}

.el-dropdown__popper.el-popper {
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-dropdown-menu-box-shadow)
}

.el-dropdown__popper.el-popper, .el-dropdown__popper.el-popper .el-popper__arrow:before {
    border: 1px solid var(--el-border-color-light)
}

.el-dropdown__popper.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-dropdown__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-dropdown__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-dropdown__popper.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-dropdown__popper .el-dropdown-menu {
    border: none
}

.el-dropdown__popper .el-dropdown__popper-selfdefine {
    outline: none
}

.el-dropdown__popper .el-scrollbar__bar {
    z-index: calc(var(--el-dropdown-menu-index) + 1)
}

.el-dropdown__popper .el-dropdown__list {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0
}

.el-dropdown .el-dropdown__caret-button {
    align-items: center;
    border-left: none;
    display: inline-flex;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
    width: 32px
}

.el-dropdown .el-dropdown__caret-button > span {
    display: inline-flex
}

.el-dropdown .el-dropdown__caret-button:before {
    background: var(--el-overlay-color-lighter);
    bottom: -1px;
    content: "";
    display: block;
    left: 0;
    position: absolute;
    top: -1px;
    width: 1px
}

.el-dropdown .el-dropdown__caret-button.el-button:before {
    background: var(--el-border-color);
    opacity: .5
}

.el-dropdown .el-dropdown__caret-button .el-dropdown__icon {
    font-size: inherit;
    padding-left: 0
}

.el-dropdown .el-dropdown-selfdefine {
    outline: none
}

.el-dropdown--large .el-dropdown__caret-button {
    width: 40px
}

.el-dropdown--small .el-dropdown__caret-button {
    width: 24px
}

.el-dropdown-menu {
    background-color: var(--el-bg-color-overlay);
    border: none;
    border-radius: var(--el-border-radius-base);
    box-shadow: none;
    left: 0;
    list-style: none;
    margin: 0;
    padding: 5px 0;
    position: relative;
    top: 0;
    z-index: var(--el-dropdown-menu-index)
}

.el-dropdown-menu__item {
    align-items: center;
    color: var(--el-text-color-regular);
    cursor: pointer;
    display: flex;
    font-size: var(--el-font-size-base);
    line-height: 22px;
    list-style: none;
    margin: 0;
    outline: none;
    padding: 5px 16px;
    white-space: nowrap
}

.el-dropdown-menu__item:not(.is-disabled):focus, .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: var(--el-dropdown-menuItem-hover-fill);
    color: var(--el-dropdown-menuItem-hover-color)
}

.el-dropdown-menu__item i {
    margin-right: 5px
}

.el-dropdown-menu__item--divided {
    border-top: 1px solid var(--el-border-color-lighter);
    margin: 6px 0
}

.el-dropdown-menu__item.is-disabled {
    color: var(--el-text-color-disabled);
    cursor: not-allowed
}

.el-dropdown-menu--large {
    padding: 7px 0
}

.el-dropdown-menu--large .el-dropdown-menu__item {
    font-size: 14px;
    line-height: 22px;
    padding: 7px 20px
}

.el-dropdown-menu--large .el-dropdown-menu__item--divided {
    margin: 8px 0
}

.el-dropdown-menu--small {
    padding: 3px 0
}

.el-dropdown-menu--small .el-dropdown-menu__item {
    font-size: 12px;
    line-height: 20px;
    padding: 2px 12px
}

.el-dropdown-menu--small .el-dropdown-menu__item--divided {
    margin: 4px 0
}

.el-empty {
    --el-empty-padding: 40px 0;
    --el-empty-image-width: 160px;
    --el-empty-description-margin-top: 20px;
    --el-empty-bottom-margin-top: 20px;
    --el-empty-fill-color-0: var(--el-color-white);
    --el-empty-fill-color-1: #fcfcfd;
    --el-empty-fill-color-2: #f8f9fb;
    --el-empty-fill-color-3: #f7f8fc;
    --el-empty-fill-color-4: #eeeff3;
    --el-empty-fill-color-5: #edeef2;
    --el-empty-fill-color-6: #e9ebef;
    --el-empty-fill-color-7: #e5e7e9;
    --el-empty-fill-color-8: #e0e3e9;
    --el-empty-fill-color-9: #d5d7de;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--el-empty-padding);
    text-align: center
}

.el-empty__image {
    width: var(--el-empty-image-width)
}

.el-empty__image img {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: top;
    width: 100%
}

.el-empty__image svg {
    color: var(--el-svg-monochrome-grey);
    fill: currentColor;
    height: 100%;
    vertical-align: top;
    width: 100%
}

.el-empty__description {
    margin-top: var(--el-empty-description-margin-top)
}

.el-empty__description p {
    color: var(--el-text-color-secondary);
    font-size: var(--el-font-size-base);
    margin: 0
}

.el-empty__bottom {
    margin-top: var(--el-empty-bottom-margin-top)
}

.el-footer {
    --el-footer-padding: 0 20px;
    --el-footer-height: 60px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: var(--el-footer-height);
    padding: var(--el-footer-padding)
}

.el-form {
    --el-form-label-font-size: var(--el-font-size-base);
    --el-form-inline-content-width: 220px
}

.el-form--inline .el-form-item {
    display: inline-flex;
    margin-right: 32px;
    vertical-align: middle
}

.el-form--inline.el-form--label-top {
    display: flex;
    flex-wrap: wrap
}

.el-form--inline.el-form--label-top .el-form-item {
    display: block
}

.el-form-item {
    display: flex;
    --font-size: 14px;
    margin-bottom: 18px
}

.el-form-item .el-form-item {
    margin-bottom: 0
}

.el-form-item .el-input__validateIcon {
    display: none
}

.el-form-item--large {
    --font-size: 14px;
    --el-form-label-font-size: var(--font-size);
    margin-bottom: 22px
}

.el-form-item--large .el-form-item__label {
    height: 40px;
    line-height: 40px
}

.el-form-item--large .el-form-item__content {
    line-height: 40px
}

.el-form-item--large .el-form-item__error {
    padding-top: 4px
}

.el-form-item--default {
    --font-size: 14px;
    --el-form-label-font-size: var(--font-size);
    margin-bottom: 18px
}

.el-form-item--default .el-form-item__label {
    height: 32px;
    line-height: 32px
}

.el-form-item--default .el-form-item__content {
    line-height: 32px
}

.el-form-item--default .el-form-item__error {
    padding-top: 2px
}

.el-form-item--small {
    --font-size: 12px;
    --el-form-label-font-size: var(--font-size);
    margin-bottom: 18px
}

.el-form-item--small .el-form-item__label {
    height: 24px;
    line-height: 24px
}

.el-form-item--small .el-form-item__content {
    line-height: 24px
}

.el-form-item--small .el-form-item__error {
    padding-top: 2px
}

.el-form-item--label-left .el-form-item__label {
    justify-content: flex-start;
    text-align: left
}

.el-form-item--label-right .el-form-item__label {
    justify-content: flex-end;
    text-align: right
}

.el-form-item--label-top {
    display: block
}

.el-form-item--label-top .el-form-item__label {
    display: block;
    height: auto;
    line-height: 22px;
    margin-bottom: 8px;
    text-align: left
}

.el-form-item__label-wrap {
    display: flex
}

.el-form-item__label {
    align-items: flex-start;
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    display: inline-flex;
    flex: 0 0 auto;
    font-size: var(--el-form-label-font-size);
    height: 32px;
    line-height: 32px;
    padding: 0 12px 0 0
}

.el-form-item__content {
    align-items: center;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    font-size: var(--font-size);
    line-height: 32px;
    min-width: 0;
    position: relative
}

.el-form-item__content .el-input-group {
    vertical-align: top
}

.el-form-item__error {
    color: var(--el-color-danger);
    font-size: 12px;
    left: 0;
    line-height: 1;
    padding-top: 2px;
    position: absolute;
    top: 100%
}

.el-form-item__error--inline {
    display: inline-block;
    left: auto;
    margin-left: 10px;
    position: relative;
    top: auto
}

.el-form-item.is-required:not(.is-no-asterisk).asterisk-left > .el-form-item__label-wrap > .el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk).asterisk-left > .el-form-item__label:before {
    color: var(--el-color-danger);
    content: "*";
    margin-right: 4px
}

.el-form-item.is-required:not(.is-no-asterisk).asterisk-right > .el-form-item__label-wrap > .el-form-item__label:after, .el-form-item.is-required:not(.is-no-asterisk).asterisk-right > .el-form-item__label:after {
    color: var(--el-color-danger);
    content: "*";
    margin-left: 4px
}

.el-form-item.is-error .el-input-tag__wrapper, .el-form-item.is-error .el-input-tag__wrapper.is-focus, .el-form-item.is-error .el-input-tag__wrapper:focus, .el-form-item.is-error .el-input-tag__wrapper:hover, .el-form-item.is-error .el-input__wrapper, .el-form-item.is-error .el-input__wrapper.is-focus, .el-form-item.is-error .el-input__wrapper:focus, .el-form-item.is-error .el-input__wrapper:hover, .el-form-item.is-error .el-select__wrapper, .el-form-item.is-error .el-select__wrapper.is-focus, .el-form-item.is-error .el-select__wrapper:focus, .el-form-item.is-error .el-select__wrapper:hover, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner.is-focus, .el-form-item.is-error .el-textarea__inner:focus, .el-form-item.is-error .el-textarea__inner:hover {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset
}

.el-form-item.is-error .el-input-group__append .el-input__wrapper, .el-form-item.is-error .el-input-group__prepend .el-input__wrapper {
    box-shadow: inset 0 0 0 1px transparent
}

.el-form-item.is-error .el-input-group__append .el-input__validateIcon, .el-form-item.is-error .el-input-group__prepend .el-input__validateIcon {
    display: none
}

.el-form-item.is-error .el-input__validateIcon {
    color: var(--el-color-danger)
}

.el-form-item--feedback .el-input__validateIcon {
    display: inline-flex
}

.el-header {
    --el-header-padding: 0 20px;
    --el-header-height: 60px;
    box-sizing: border-box;
    flex-shrink: 0;
    height: var(--el-header-height);
    padding: var(--el-header-padding)
}

.el-image-viewer__wrapper {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0
}

.el-image-viewer__wrapper:focus {
    outline: none !important
}

.el-image-viewer__btn {
    align-items: center;
    border-radius: 50%;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    justify-content: center;
    opacity: .8;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    z-index: 1
}

.el-image-viewer__btn .el-icon {
    cursor: pointer
}

.el-image-viewer__close {
    font-size: 40px;
    height: 40px;
    right: 40px;
    top: 40px;
    width: 40px
}

.el-image-viewer__canvas {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    position: static;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 100%
}

.el-image-viewer__actions {
    background-color: var(--el-text-color-regular);
    border-color: #fff;
    border-radius: 22px;
    bottom: 30px;
    height: 44px;
    left: 50%;
    padding: 0 23px;
    transform: translateX(-50%)
}

.el-image-viewer__actions__inner {
    align-items: center;
    color: #fff;
    cursor: default;
    display: flex;
    font-size: 23px;
    gap: 22px;
    height: 100%;
    justify-content: space-around;
    padding: 0 6px;
    width: 100%
}

.el-image-viewer__actions__divider {
    margin: 0 -6px
}

.el-image-viewer__progress {
    bottom: 90px;
    color: #fff;
    cursor: default;
    left: 50%;
    transform: translateX(-50%)
}

.el-image-viewer__prev {
    left: 40px
}

.el-image-viewer__next, .el-image-viewer__prev {
    background-color: var(--el-text-color-regular);
    border-color: #fff;
    color: #fff;
    font-size: 24px;
    height: 44px;
    top: 50%;
    transform: translateY(-50%);
    width: 44px
}

.el-image-viewer__next {
    right: 40px;
    text-indent: 2px
}

.el-image-viewer__close {
    background-color: var(--el-text-color-regular);
    border-color: #fff;
    color: #fff;
    font-size: 24px;
    height: 44px;
    width: 44px
}

.el-image-viewer__mask {
    background: #000;
    height: 100%;
    left: 0;
    opacity: .5;
    position: absolute;
    top: 0;
    width: 100%
}

.viewer-fade-enter-active {
    animation: viewer-fade-in var(--el-transition-duration)
}

.viewer-fade-leave-active {
    animation: viewer-fade-out var(--el-transition-duration)
}

@keyframes viewer-fade-in {
    0% {
        opacity: 0;
        transform: translate3d(0, -20px, 0)
    }
    to {
        opacity: 1;
        transform: translateZ(0)
    }
}

@keyframes viewer-fade-out {
    0% {
        opacity: 1;
        transform: translateZ(0)
    }
    to {
        opacity: 0;
        transform: translate3d(0, -20px, 0)
    }
}

.el-image__error, .el-image__inner, .el-image__placeholder, .el-image__wrapper {
    height: 100%;
    width: 100%
}

.el-image {
    display: inline-block;
    overflow: hidden;
    position: relative
}

.el-image__inner {
    opacity: 1;
    vertical-align: top
}

.el-image__inner.is-loading {
    opacity: 0
}

.el-image__wrapper {
    left: 0;
    position: absolute;
    top: 0
}

.el-image__error, .el-image__placeholder {
    background: var(--el-fill-color-light)
}

.el-image__error {
    align-items: center;
    color: var(--el-text-color-placeholder);
    display: flex;
    font-size: 14px;
    justify-content: center;
    vertical-align: middle
}

.el-image__preview {
    cursor: pointer
}

.el-textarea {
    --el-input-text-color: var(--el-text-color-regular);
    --el-input-border: var(--el-border);
    --el-input-hover-border: var(--el-border-color-hover);
    --el-input-focus-border: var(--el-color-primary);
    --el-input-transparent-border: 0 0 0 1px transparent inset;
    --el-input-border-color: var(--el-border-color);
    --el-input-border-radius: var(--el-border-radius-base);
    --el-input-bg-color: var(--el-fill-color-blank);
    --el-input-icon-color: var(--el-text-color-placeholder);
    --el-input-placeholder-color: var(--el-text-color-placeholder);
    --el-input-hover-border-color: var(--el-border-color-hover);
    --el-input-clear-hover-color: var(--el-text-color-secondary);
    --el-input-focus-border-color: var(--el-color-primary);
    --el-input-width: 100%;
    display: inline-block;
    font-size: var(--el-font-size-base);
    position: relative;
    vertical-align: bottom;
    width: 100%
}

.el-textarea__inner {
    -webkit-appearance: none;
    background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
    background-image: none;
    border: none;
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    box-sizing: border-box;
    color: var(--el-input-text-color, var(--el-text-color-regular));
    display: block;
    font-family: inherit;
    font-size: inherit;
    line-height: 1.5;
    padding: 5px 11px;
    position: relative;
    resize: vertical;
    transition: var(--el-transition-box-shadow);
    width: 100%
}

.el-textarea__inner::-moz-placeholder {
    color: var(--el-input-placeholder-color, var(--el-text-color-placeholder))
}

.el-textarea__inner::placeholder {
    color: var(--el-input-placeholder-color, var(--el-text-color-placeholder))
}

.el-textarea__inner:hover {
    box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset
}

.el-textarea__inner:focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
    outline: none
}

.el-textarea .el-input__count {
    background: var(--el-fill-color-blank);
    bottom: 5px;
    color: var(--el-color-info);
    font-size: 12px;
    line-height: 14px;
    position: absolute;
    right: 10px
}

.el-textarea.is-disabled .el-textarea__inner {
    background-color: var(--el-disabled-bg-color);
    box-shadow: 0 0 0 1px var(--el-disabled-border-color) inset;
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-textarea.is-disabled .el-textarea__inner::-moz-placeholder {
    color: var(--el-text-color-placeholder)
}

.el-textarea.is-disabled .el-textarea__inner::placeholder {
    color: var(--el-text-color-placeholder)
}

.el-textarea.is-exceed .el-textarea__inner {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset
}

.el-textarea.is-exceed .el-input__count {
    color: var(--el-color-danger)
}

.el-input {
    --el-input-text-color: var(--el-text-color-regular);
    --el-input-border: var(--el-border);
    --el-input-hover-border: var(--el-border-color-hover);
    --el-input-focus-border: var(--el-color-primary);
    --el-input-transparent-border: 0 0 0 1px transparent inset;
    --el-input-border-color: var(--el-border-color);
    --el-input-border-radius: var(--el-border-radius-base);
    --el-input-bg-color: var(--el-fill-color-blank);
    --el-input-icon-color: var(--el-text-color-placeholder);
    --el-input-placeholder-color: var(--el-text-color-placeholder);
    --el-input-hover-border-color: var(--el-border-color-hover);
    --el-input-clear-hover-color: var(--el-text-color-secondary);
    --el-input-focus-border-color: var(--el-color-primary);
    --el-input-width: 100%;
    --el-input-height: var(--el-component-size);
    box-sizing: border-box;
    display: inline-flex;
    font-size: var(--el-font-size-base);
    line-height: var(--el-input-height);
    position: relative;
    vertical-align: middle;
    width: var(--el-input-width)
}

.el-input::-webkit-scrollbar {
    width: 6px;
    z-index: 11
}

.el-input::-webkit-scrollbar:horizontal {
    height: 6px
}

.el-input::-webkit-scrollbar-thumb {
    background: var(--el-text-color-disabled);
    border-radius: 5px;
    width: 6px
}

.el-input::-webkit-scrollbar-corner, .el-input::-webkit-scrollbar-track {
    background: var(--el-fill-color-blank)
}

.el-input::-webkit-scrollbar-track-piece {
    background: var(--el-fill-color-blank);
    width: 6px
}

.el-input .el-input__clear, .el-input .el-input__password {
    color: var(--el-input-icon-color);
    cursor: pointer;
    font-size: 14px
}

.el-input .el-input__clear:hover, .el-input .el-input__password:hover {
    color: var(--el-input-clear-hover-color)
}

.el-input .el-input__count {
    align-items: center;
    color: var(--el-color-info);
    display: inline-flex;
    font-size: 12px;
    height: 100%
}

.el-input .el-input__count .el-input__count-inner {
    background: var(--el-fill-color-blank);
    display: inline-block;
    line-height: normal;
    padding-left: 8px
}

.el-input__wrapper {
    align-items: center;
    background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
    background-image: none;
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
    cursor: text;
    display: inline-flex;
    flex-grow: 1;
    justify-content: center;
    padding: 1px 11px;
    transform: translateZ(0);
    transition: var(--el-transition-box-shadow)
}

.el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset
}

.el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset
}

.el-input {
    --el-input-inner-height: calc(var(--el-input-height, 32px) - 2px)
}

.el-input__inner {
    -webkit-appearance: none;
    background: none;
    border: none;
    box-sizing: border-box;
    color: var(--el-input-text-color, var(--el-text-color-regular));
    flex-grow: 1;
    font-size: inherit;
    height: var(--el-input-inner-height);
    line-height: var(--el-input-inner-height);
    outline: none;
    padding: 0;
    width: 100%
}

.el-input__inner:focus {
    outline: none
}

.el-input__inner::-moz-placeholder {
    color: var(--el-input-placeholder-color, var(--el-text-color-placeholder))
}

.el-input__inner::placeholder {
    color: var(--el-input-placeholder-color, var(--el-text-color-placeholder))
}

.el-input__inner[type=password]::-ms-reveal {
    display: none
}

.el-input__inner[type=number] {
    line-height: 1
}

.el-input__prefix {
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    display: inline-flex;
    flex-shrink: 0;
    flex-wrap: nowrap;
    height: 100%;
    line-height: var(--el-input-inner-height);
    pointer-events: none;
    text-align: center;
    transition: all var(--el-transition-duration);
    white-space: nowrap
}

.el-input__prefix-inner {
    align-items: center;
    display: inline-flex;
    justify-content: center;
    pointer-events: all
}

.el-input__prefix-inner > :last-child {
    margin-right: 8px
}

.el-input__prefix-inner > :first-child, .el-input__prefix-inner > :first-child.el-input__icon {
    margin-left: 0
}

.el-input__suffix {
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    display: inline-flex;
    flex-shrink: 0;
    flex-wrap: nowrap;
    height: 100%;
    line-height: var(--el-input-inner-height);
    pointer-events: none;
    text-align: center;
    transition: all var(--el-transition-duration);
    white-space: nowrap
}

.el-input__suffix-inner {
    align-items: center;
    display: inline-flex;
    justify-content: center;
    pointer-events: all
}

.el-input__suffix-inner > :first-child {
    margin-left: 8px
}

.el-input .el-input__icon {
    align-items: center;
    display: flex;
    height: inherit;
    justify-content: center;
    line-height: inherit;
    margin-left: 8px;
    transition: all var(--el-transition-duration)
}

.el-input__validateIcon {
    pointer-events: none
}

.el-input.is-active .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-focus-color,) inset
}

.el-input.is-disabled {
    cursor: not-allowed
}

.el-input.is-disabled .el-input__wrapper {
    background-color: var(--el-disabled-bg-color);
    box-shadow: 0 0 0 1px var(--el-disabled-border-color) inset;
    cursor: not-allowed
}

.el-input.is-disabled .el-input__inner {
    color: var(--el-disabled-text-color);
    -webkit-text-fill-color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-input.is-disabled .el-input__inner::-moz-placeholder {
    color: var(--el-text-color-placeholder)
}

.el-input.is-disabled .el-input__inner::placeholder {
    color: var(--el-text-color-placeholder)
}

.el-input.is-disabled .el-input__icon {
    cursor: not-allowed
}

.el-input.is-exceed .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset
}

.el-input.is-exceed .el-input__suffix .el-input__count {
    color: var(--el-color-danger)
}

.el-input--large {
    --el-input-height: var(--el-component-size-large);
    font-size: 14px
}

.el-input--large .el-input__wrapper {
    padding: 1px 15px
}

.el-input--large {
    --el-input-inner-height: calc(var(--el-input-height, 40px) - 2px)
}

.el-input--small {
    --el-input-height: var(--el-component-size-small);
    font-size: 12px
}

.el-input--small .el-input__wrapper {
    padding: 1px 7px
}

.el-input--small {
    --el-input-inner-height: calc(var(--el-input-height, 24px) - 2px)
}

.el-input-group {
    align-items: stretch;
    display: inline-flex;
    width: 100%
}

.el-input-group__append, .el-input-group__prepend {
    align-items: center;
    background-color: var(--el-fill-color-light);
    border-radius: var(--el-input-border-radius);
    color: var(--el-color-info);
    display: inline-flex;
    justify-content: center;
    min-height: 100%;
    padding: 0 20px;
    position: relative;
    white-space: nowrap
}

.el-input-group__append:focus, .el-input-group__prepend:focus {
    outline: none
}

.el-input-group__append .el-button, .el-input-group__append .el-select, .el-input-group__prepend .el-button, .el-input-group__prepend .el-select {
    display: inline-block;
    margin: 0 -20px
}

.el-input-group__append button.el-button, .el-input-group__append button.el-button:hover, .el-input-group__append div.el-select .el-select__wrapper, .el-input-group__append div.el-select:hover .el-select__wrapper, .el-input-group__prepend button.el-button, .el-input-group__prepend button.el-button:hover, .el-input-group__prepend div.el-select .el-select__wrapper, .el-input-group__prepend div.el-select:hover .el-select__wrapper {
    background-color: transparent;
    border-color: transparent;
    color: inherit
}

.el-input-group__append .el-button, .el-input-group__append .el-input, .el-input-group__prepend .el-button, .el-input-group__prepend .el-input {
    font-size: inherit
}

.el-input-group__prepend {
    border-bottom-right-radius: 0;
    border-right: 0;
    border-top-right-radius: 0;
    box-shadow: 1px 0 0 0 var(--el-input-border-color) inset, 0 1px 0 0 var(--el-input-border-color) inset, 0 -1px 0 0 var(--el-input-border-color) inset
}

.el-input-group__append {
    border-left: 0;
    box-shadow: 0 1px 0 0 var(--el-input-border-color) inset, 0 -1px 0 0 var(--el-input-border-color) inset, -1px 0 0 0 var(--el-input-border-color) inset
}

.el-input-group--prepend > .el-input__wrapper, .el-input-group__append {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.el-input-group--prepend .el-input-group__prepend .el-select .el-select__wrapper {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    box-shadow: 1px 0 0 0 var(--el-input-border-color) inset, 0 1px 0 0 var(--el-input-border-color) inset, 0 -1px 0 0 var(--el-input-border-color) inset
}

.el-input-group--append > .el-input__wrapper {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.el-input-group--append .el-input-group__append .el-select .el-select__wrapper {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    box-shadow: 0 1px 0 0 var(--el-input-border-color) inset, 0 -1px 0 0 var(--el-input-border-color) inset, -1px 0 0 0 var(--el-input-border-color) inset
}

.el-input-hidden {
    display: none !important
}

.el-input-number {
    display: inline-flex;
    line-height: 30px;
    position: relative;
    vertical-align: middle;
    width: 150px
}

.el-input-number .el-input__wrapper {
    padding-left: 42px;
    padding-right: 42px
}

.el-input-number .el-input__inner {
    -webkit-appearance: none;
    -moz-appearance: textfield;
    line-height: 1;
    text-align: center
}

.el-input-number .el-input__inner::-webkit-inner-spin-button, .el-input-number .el-input__inner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.el-input-number__decrease, .el-input-number__increase {
    align-items: center;
    background: var(--el-fill-color-light);
    bottom: 1px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    display: flex;
    font-size: 13px;
    height: auto;
    justify-content: center;
    position: absolute;
    top: 1px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 32px;
    z-index: 1
}

.el-input-number__decrease:hover, .el-input-number__increase:hover {
    color: var(--el-color-primary)
}

.el-input-number__decrease:hover ~ .el-input:not(.is-disabled) .el-input__wrapper, .el-input-number__increase:hover ~ .el-input:not(.is-disabled) .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color, var(--el-color-primary)) inset
}

.el-input-number__decrease.is-disabled, .el-input-number__increase.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-input-number__increase {
    border-left: var(--el-border);
    border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
    right: 1px
}

.el-input-number__decrease {
    border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
    border-right: var(--el-border);
    left: 1px
}

.el-input-number.is-disabled .el-input-number__decrease, .el-input-number.is-disabled .el-input-number__increase {
    border-color: var(--el-disabled-border-color);
    color: var(--el-disabled-border-color)
}

.el-input-number.is-disabled .el-input-number__decrease:hover, .el-input-number.is-disabled .el-input-number__increase:hover {
    color: var(--el-disabled-border-color);
    cursor: not-allowed
}

.el-input-number--large {
    line-height: 38px;
    width: 180px
}

.el-input-number--large .el-input-number__decrease, .el-input-number--large .el-input-number__increase {
    font-size: 14px;
    width: 40px
}

.el-input-number--large.is-controls-right .el-input--large .el-input__wrapper {
    padding-right: 47px
}

.el-input-number--large .el-input--large .el-input__wrapper {
    padding-left: 47px;
    padding-right: 47px
}

.el-input-number--small {
    line-height: 22px;
    width: 120px
}

.el-input-number--small .el-input-number__decrease, .el-input-number--small .el-input-number__increase {
    font-size: 12px;
    width: 24px
}

.el-input-number--small.is-controls-right .el-input--small .el-input__wrapper {
    padding-right: 31px
}

.el-input-number--small .el-input--small .el-input__wrapper {
    padding-left: 31px;
    padding-right: 31px
}

.el-input-number--small .el-input-number__decrease [class*=el-icon], .el-input-number--small .el-input-number__increase [class*=el-icon] {
    transform: scale(.9)
}

.el-input-number.is-without-controls .el-input__wrapper {
    padding-left: 15px;
    padding-right: 15px
}

.el-input-number.is-controls-right .el-input__wrapper {
    padding-left: 15px;
    padding-right: 42px
}

.el-input-number.is-controls-right .el-input-number__decrease, .el-input-number.is-controls-right .el-input-number__increase {
    --el-input-number-controls-height: 15px;
    height: var(--el-input-number-controls-height);
    line-height: var(--el-input-number-controls-height)
}

.el-input-number.is-controls-right .el-input-number__decrease [class*=el-icon], .el-input-number.is-controls-right .el-input-number__increase [class*=el-icon] {
    transform: scale(.8)
}

.el-input-number.is-controls-right .el-input-number__increase {
    border-bottom: var(--el-border);
    border-radius: 0 var(--el-border-radius-base) 0 0;
    bottom: auto;
    left: auto
}

.el-input-number.is-controls-right .el-input-number__decrease {
    border-left: var(--el-border);
    border-radius: 0 0 var(--el-border-radius-base) 0;
    border-right: none;
    left: auto;
    right: 1px;
    top: auto
}

.el-input-number.is-controls-right[class*=large] [class*=decrease], .el-input-number.is-controls-right[class*=large] [class*=increase] {
    --el-input-number-controls-height: 19px
}

.el-input-number.is-controls-right[class*=small] [class*=decrease], .el-input-number.is-controls-right[class*=small] [class*=increase] {
    --el-input-number-controls-height: 11px
}

.el-input-tag {
    --el-input-tag-border-color-hover: var(--el-border-color-hover);
    --el-input-tag-placeholder-color: var(--el-text-color-placeholder);
    --el-input-tag-disabled-color: var(--el-disabled-text-color);
    --el-input-tag-disabled-border: var(--el-disabled-border-color);
    --el-input-tag-font-size: var(--el-font-size-base);
    --el-input-tag-close-hover-color: var(--el-text-color-secondary);
    --el-input-tag-text-color: var(--el-text-color-regular);
    --el-input-tag-input-focus-border-color: var(--el-color-primary);
    --el-input-tag-width: 100%;
    --el-input-tag-mini-height: var(--el-component-size);
    --el-input-tag-gap: 6px;
    --el-input-tag-padding: 4px;
    --el-input-tag-inner-padding: 8px;
    --el-input-tag-line-height: 24px;
    align-items: center;
    background-color: var(--el-fill-color-blank);
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-size: var(--el-input-tag-font-size);
    line-height: var(--el-input-tag-line-height);
    min-height: var(--el-input-tag-mini-height);
    padding: var(--el-input-tag-padding);
    transform: translateZ(0);
    transition: var(--el-transition-duration);
    width: var(--el-input-tag-width)
}

.el-input-tag.is-focused {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset
}

.el-input-tag.is-hovering:not(.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color-hover) inset
}

.el-input-tag.is-disabled {
    background-color: var(--el-fill-color-light);
    cursor: not-allowed
}

.el-input-tag.is-disabled, .el-input-tag.is-disabled:hover {
    box-shadow: 0 0 0 1px var(--el-input-tag-disabled-border) inset
}

.el-input-tag.is-disabled.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset
}

.el-input-tag.is-disabled .el-input-tag__inner .el-input-tag__input, .el-input-tag.is-disabled .el-input-tag__inner .el-tag {
    cursor: not-allowed
}

.el-input-tag__prefix, .el-input-tag__suffix {
    align-items: center;
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    display: flex;
    flex-shrink: 0;
    padding: 0 var(--el-input-tag-inner-padding)
}

.el-input-tag__suffix {
    gap: 8px
}

.el-input-tag__inner {
    align-items: center;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: var(--el-input-tag-gap);
    max-width: 100%;
    min-width: 0;
    position: relative
}

.el-input-tag__inner.is-left-space {
    margin-left: var(--el-input-tag-inner-padding)
}

.el-input-tag__inner.is-right-space {
    margin-right: var(--el-input-tag-inner-padding)
}

.el-input-tag__inner.is-draggable .el-tag {
    cursor: move;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-input-tag__drop-indicator {
    background-color: var(--el-color-primary);
    height: var(--el-input-tag-line-height);
    position: absolute;
    top: 0;
    width: 1px
}

.el-input-tag__inner .el-tag {
    border-color: transparent;
    cursor: pointer;
    max-width: 100%
}

.el-input-tag__inner .el-tag.el-tag--plain {
    border-color: var(--el-tag-border-color)
}

.el-input-tag__inner .el-tag .el-tag__content {
    line-height: normal;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-input-tag__input-wrapper {
    flex: 1
}

.el-input-tag__input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: transparent;
    border: none;
    color: var(--el-input-tag-text-color);
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    outline: none;
    padding: 0;
    width: 100%
}

.el-input-tag__input::-moz-placeholder {
    color: var(--el-input-tag-placeholder-color)
}

.el-input-tag__input::placeholder {
    color: var(--el-input-tag-placeholder-color)
}

.el-input-tag__input-calculator {
    left: 0;
    max-width: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    visibility: hidden;
    white-space: pre
}

.el-input-tag--large {
    --el-input-tag-gap: 6px;
    --el-input-tag-padding: 8px;
    --el-input-tag-padding-left: 8px;
    --el-input-tag-font-size: 14px
}

.el-input-tag--small {
    --el-input-tag-gap: 4px;
    --el-input-tag-padding: 2px;
    --el-input-tag-padding-left: 6px;
    --el-input-tag-font-size: 12px;
    --el-input-tag-line-height: 20px;
    --el-input-tag-mini-height: var(--el-component-size-small)
}

.el-link {
    --el-link-font-size: var(--el-font-size-base);
    --el-link-font-weight: var(--el-font-weight-primary);
    --el-link-text-color: var(--el-text-color-regular);
    --el-link-hover-text-color: var(--el-color-primary);
    --el-link-disabled-text-color: var(--el-text-color-placeholder);
    align-items: center;
    color: var(--el-link-text-color);
    cursor: pointer;
    display: inline-flex;
    flex-direction: row;
    font-size: var(--el-link-font-size);
    font-weight: var(--el-link-font-weight);
    justify-content: center;
    outline: none;
    padding: 0;
    position: relative;
    text-decoration: none;
    vertical-align: middle
}

.el-link.is-hover-underline:hover:after {
    border-bottom: 1px solid var(--el-link-hover-text-color);
    bottom: 0;
    content: "";
    height: 0;
    left: 0;
    position: absolute;
    right: 0
}

.el-link.is-underline:after {
    border-bottom: 1px solid var(--el-link-text-color);
    bottom: 0;
    content: "";
    height: 0;
    left: 0;
    position: absolute;
    right: 0
}

.el-link:hover {
    color: var(--el-link-hover-text-color)
}

.el-link:hover:after {
    border-color: var(--el-link-hover-text-color)
}

.el-link [class*=el-icon-] + span {
    margin-left: 5px
}

.el-link__inner {
    align-items: center;
    display: inline-flex;
    justify-content: center
}

.el-link.el-link--primary {
    --el-link-text-color: var(--el-color-primary);
    --el-link-hover-text-color: var(--el-color-primary-light-3);
    --el-link-disabled-text-color: var(--el-color-primary-light-5)
}

.el-link.el-link--success {
    --el-link-text-color: var(--el-color-success);
    --el-link-hover-text-color: var(--el-color-success-light-3);
    --el-link-disabled-text-color: var(--el-color-success-light-5)
}

.el-link.el-link--warning {
    --el-link-text-color: var(--el-color-warning);
    --el-link-hover-text-color: var(--el-color-warning-light-3);
    --el-link-disabled-text-color: var(--el-color-warning-light-5)
}

.el-link.el-link--danger {
    --el-link-text-color: var(--el-color-danger);
    --el-link-hover-text-color: var(--el-color-danger-light-3);
    --el-link-disabled-text-color: var(--el-color-danger-light-5)
}

.el-link.el-link--error {
    --el-link-text-color: var(--el-color-error);
    --el-link-hover-text-color: var(--el-color-error-light-3);
    --el-link-disabled-text-color: var(--el-color-error-light-5)
}

.el-link.el-link--info {
    --el-link-text-color: var(--el-color-info);
    --el-link-hover-text-color: var(--el-color-info-light-3);
    --el-link-disabled-text-color: var(--el-color-info-light-5)
}

.el-link.is-disabled {
    color: var(--el-link-disabled-text-color);
    cursor: not-allowed
}

.el-link.is-disabled:after {
    border-color: var(--el-link-disabled-text-color)
}

:root {
    --el-loading-spinner-size: 42px;
    --el-loading-fullscreen-spinner-size: 50px
}

.el-loading-parent--relative {
    position: relative !important
}

.el-loading-parent--hidden {
    overflow: hidden !important
}

.el-loading-mask {
    background-color: var(--el-mask-color);
    bottom: 0;
    left: 0;
    margin: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity var(--el-transition-duration);
    z-index: 2000
}

.el-loading-mask.is-fullscreen {
    position: fixed
}

.el-loading-mask.is-fullscreen .el-loading-spinner {
    margin-top: calc((0px - var(--el-loading-fullscreen-spinner-size)) / 2)
}

.el-loading-mask.is-fullscreen .el-loading-spinner .circular {
    height: var(--el-loading-fullscreen-spinner-size);
    width: var(--el-loading-fullscreen-spinner-size)
}

.el-loading-spinner {
    margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
    position: absolute;
    text-align: center;
    top: 50%;
    width: 100%
}

.el-loading-spinner .el-loading-text {
    color: var(--el-color-primary);
    font-size: 14px;
    margin: 3px 0
}

.el-loading-spinner .circular {
    animation: loading-rotate 2s linear infinite;
    display: inline;
    height: var(--el-loading-spinner-size);
    width: var(--el-loading-spinner-size)
}

.el-loading-spinner .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: var(--el-color-primary);
    stroke-linecap: round
}

.el-loading-spinner i {
    color: var(--el-color-primary)
}

.el-loading-fade-enter-from, .el-loading-fade-leave-to {
    opacity: 0
}

@keyframes loading-rotate {
    to {
        transform: rotate(1turn)
    }
}

@keyframes loading-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -40px
    }
    to {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -120px
    }
}

.el-main {
    --el-main-padding: 20px;
    box-sizing: border-box;
    display: block;
    flex: 1;
    flex-basis: auto;
    overflow: auto;
    padding: var(--el-main-padding)
}

:root {
    --el-menu-active-color: var(--el-color-primary);
    --el-menu-text-color: var(--el-text-color-primary);
    --el-menu-hover-text-color: var(--el-color-primary);
    --el-menu-bg-color: var(--el-fill-color-blank);
    --el-menu-hover-bg-color: var(--el-color-primary-light-9);
    --el-menu-item-height: 56px;
    --el-menu-sub-item-height: calc(var(--el-menu-item-height) - 6px);
    --el-menu-horizontal-height: 60px;
    --el-menu-horizontal-sub-item-height: 36px;
    --el-menu-item-font-size: var(--el-font-size-base);
    --el-menu-item-hover-fill: var(--el-color-primary-light-9);
    --el-menu-border-color: var(--el-border-color);
    --el-menu-base-level-padding: 20px;
    --el-menu-level-padding: 20px;
    --el-menu-icon-width: 24px
}

.el-menu {
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-menu-border-color);
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding-left: 0;
    position: relative
}

.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item, .el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item-group__title, .el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-sub-menu__title {
    padding-left: calc(var(--el-menu-base-level-padding) + var(--el-menu-level) * var(--el-menu-level-padding));
    white-space: nowrap
}

.el-menu:not(.el-menu--collapse) .el-sub-menu__title {
    padding-right: calc(var(--el-menu-base-level-padding) + var(--el-menu-icon-width))
}

.el-menu--horizontal {
    border-right: none;
    display: flex;
    flex-wrap: nowrap;
    height: var(--el-menu-horizontal-height)
}

.el-menu--horizontal.el-menu--popup-container {
    height: unset
}

.el-menu--horizontal.el-menu {
    border-bottom: 1px solid var(--el-menu-border-color)
}

.el-menu--horizontal > .el-menu-item {
    align-items: center;
    border-bottom: 2px solid transparent;
    color: var(--el-menu-text-color);
    display: inline-flex;
    height: 100%;
    justify-content: center;
    margin: 0
}

.el-menu--horizontal > .el-menu-item a, .el-menu--horizontal > .el-menu-item a:hover {
    color: inherit
}

.el-menu--horizontal > .el-sub-menu:focus, .el-menu--horizontal > .el-sub-menu:hover {
    outline: none
}

.el-menu--horizontal > .el-sub-menu:hover .el-sub-menu__title {
    color: var(--el-menu-hover-text-color)
}

.el-menu--horizontal > .el-sub-menu.is-active .el-sub-menu__title {
    border-bottom: 2px solid var(--el-menu-active-color);
    color: var(--el-menu-active-color)
}

.el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
    border-bottom: 2px solid transparent;
    color: var(--el-menu-text-color);
    height: 100%
}

.el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover {
    background-color: var(--el-menu-bg-color)
}

.el-menu--horizontal .el-menu .el-menu-item, .el-menu--horizontal .el-menu .el-sub-menu__title {
    align-items: center;
    background-color: var(--el-menu-bg-color);
    color: var(--el-menu-text-color);
    display: flex;
    height: var(--el-menu-horizontal-sub-item-height);
    line-height: var(--el-menu-horizontal-sub-item-height);
    padding: 0 10px
}

.el-menu--horizontal .el-menu .el-sub-menu__title {
    padding-right: 40px
}

.el-menu--horizontal .el-menu .el-menu-item.is-active, .el-menu--horizontal .el-menu .el-menu-item.is-active:hover, .el-menu--horizontal .el-menu .el-sub-menu.is-active > .el-sub-menu__title, .el-menu--horizontal .el-menu .el-sub-menu.is-active > .el-sub-menu__title:hover {
    color: var(--el-menu-active-color)
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    background-color: var(--el-menu-hover-bg-color);
    color: var(--el-menu-active-color, var(--el-menu-hover-text-color));
    outline: none
}

.el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: 2px solid var(--el-menu-active-color);
    color: var(--el-menu-active-color)
}

.el-menu--collapse {
    width: calc(var(--el-menu-icon-width) + var(--el-menu-base-level-padding) * 2)
}

.el-menu--collapse > .el-menu-item [class^=el-icon], .el-menu--collapse > .el-menu-item-group > ul > .el-sub-menu > .el-sub-menu__title [class^=el-icon], .el-menu--collapse > .el-sub-menu > .el-sub-menu__title [class^=el-icon] {
    margin: 0;
    text-align: center;
    vertical-align: middle;
    width: var(--el-menu-icon-width)
}

.el-menu--collapse > .el-menu-item .el-sub-menu__icon-arrow, .el-menu--collapse > .el-menu-item-group > ul > .el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow, .el-menu--collapse > .el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow {
    display: none
}

.el-menu--collapse > .el-menu-item-group > ul > .el-sub-menu > .el-sub-menu__title > span, .el-menu--collapse > .el-menu-item > span, .el-menu--collapse > .el-sub-menu > .el-sub-menu__title > span {
    display: inline-block;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    width: 0
}

.el-menu--collapse > .el-menu-item.is-active i {
    color: inherit
}

.el-menu--collapse .el-menu .el-sub-menu {
    min-width: 200px
}

.el-menu--collapse .el-sub-menu.is-active .el-sub-menu__title {
    color: var(--el-menu-active-color)
}

.el-menu--popup {
    border: none;
    border-radius: var(--el-border-radius-small);
    box-shadow: var(--el-box-shadow-light);
    min-width: 200px;
    padding: 5px 0;
    z-index: 100
}

.el-menu .el-icon {
    flex-shrink: 0
}

.el-menu-item {
    align-items: center;
    box-sizing: border-box;
    color: var(--el-menu-text-color);
    cursor: pointer;
    display: flex;
    font-size: var(--el-menu-item-font-size);
    height: var(--el-menu-item-height);
    line-height: var(--el-menu-item-height);
    list-style: none;
    padding: 0 var(--el-menu-base-level-padding);
    position: relative;
    transition: border-color var(--el-transition-duration), background-color var(--el-transition-duration), color var(--el-transition-duration);
    white-space: nowrap
}

.el-menu-item * {
    vertical-align: bottom
}

.el-menu-item i {
    color: inherit
}

.el-menu-item:focus, .el-menu-item:hover {
    outline: none
}

.el-menu-item:hover {
    background-color: var(--el-menu-hover-bg-color)
}

.el-menu-item.is-disabled {
    background: none !important;
    cursor: not-allowed;
    opacity: .25
}

.el-menu-item [class^=el-icon] {
    font-size: 18px;
    margin-right: 5px;
    text-align: center;
    vertical-align: middle;
    width: var(--el-menu-icon-width)
}

.el-menu-item.is-active {
    color: var(--el-menu-active-color)
}

.el-menu-item.is-active i {
    color: inherit
}

.el-menu-item .el-menu-tooltip__trigger {
    align-items: center;
    box-sizing: border-box;
    display: inline-flex;
    height: 100%;
    left: 0;
    padding: 0 var(--el-menu-base-level-padding);
    position: absolute;
    top: 0;
    width: 100%
}

.el-sub-menu {
    list-style: none;
    margin: 0;
    padding-left: 0
}

.el-sub-menu__title {
    align-items: center;
    box-sizing: border-box;
    color: var(--el-menu-text-color);
    cursor: pointer;
    display: flex;
    font-size: var(--el-menu-item-font-size);
    height: var(--el-menu-item-height);
    line-height: var(--el-menu-item-height);
    list-style: none;
    padding: 0 var(--el-menu-base-level-padding);
    position: relative;
    transition: border-color var(--el-transition-duration), background-color var(--el-transition-duration), color var(--el-transition-duration);
    white-space: nowrap
}

.el-sub-menu__title * {
    vertical-align: bottom
}

.el-sub-menu__title i {
    color: inherit
}

.el-sub-menu__title:focus, .el-sub-menu__title:hover {
    outline: none
}

.el-sub-menu__title.is-disabled {
    background: none !important;
    cursor: not-allowed;
    opacity: .25
}

.el-sub-menu__title:hover {
    background-color: var(--el-menu-hover-bg-color)
}

.el-sub-menu .el-menu {
    border: none
}

.el-sub-menu .el-menu-item {
    height: var(--el-menu-sub-item-height);
    line-height: var(--el-menu-sub-item-height)
}

.el-sub-menu__hide-arrow .el-sub-menu__icon-arrow {
    display: none !important
}

.el-sub-menu.is-active .el-sub-menu__title {
    border-bottom-color: var(--el-menu-active-color)
}

.el-sub-menu.is-disabled .el-menu-item, .el-sub-menu.is-disabled .el-sub-menu__title {
    background: none !important;
    cursor: not-allowed;
    opacity: .25
}

.el-sub-menu .el-icon {
    font-size: 18px;
    margin-right: 5px;
    text-align: center;
    vertical-align: middle;
    width: var(--el-menu-icon-width)
}

.el-sub-menu .el-icon.el-sub-menu__icon-more {
    margin-right: 0 !important
}

.el-sub-menu .el-sub-menu__icon-arrow {
    font-size: 12px;
    margin-right: 0;
    margin-top: -6px;
    position: absolute;
    right: var(--el-menu-base-level-padding);
    top: 50%;
    transition: transform var(--el-transition-duration);
    width: inherit
}

.el-menu-item-group > ul {
    padding: 0
}

.el-menu-item-group__title {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    line-height: normal;
    padding: 7px 0 7px var(--el-menu-base-level-padding)
}

.horizontal-collapse-transition .el-sub-menu__title .el-sub-menu__icon-arrow {
    opacity: 0;
    transition: var(--el-transition-duration-fast)
}

.el-message-box {
    --el-messagebox-title-color: var(--el-text-color-primary);
    --el-messagebox-width: 420px;
    --el-messagebox-border-radius: 4px;
    --el-messagebox-box-shadow: var(--el-box-shadow);
    --el-messagebox-font-size: var(--el-font-size-large);
    --el-messagebox-content-font-size: var(--el-font-size-base);
    --el-messagebox-content-color: var(--el-text-color-regular);
    --el-messagebox-error-font-size: 12px;
    --el-messagebox-padding-primary: 12px;
    --el-messagebox-font-line-height: var(--el-font-line-height-primary);
    backface-visibility: hidden;
    background-color: var(--el-bg-color);
    border-radius: var(--el-messagebox-border-radius);
    box-shadow: var(--el-messagebox-box-shadow);
    box-sizing: border-box;
    display: inline-block;
    font-size: var(--el-messagebox-font-size);
    max-width: var(--el-messagebox-width);
    overflow: hidden;
    overflow-wrap: break-word;
    padding: var(--el-messagebox-padding-primary);
    position: relative;
    text-align: left;
    vertical-align: middle;
    width: 100%
}

.el-message-box:focus {
    outline: none !important
}

.el-overlay.is-message-box .el-overlay-message-box {
    bottom: 0;
    left: 0;
    overflow: auto;
    padding: 16px;
    position: fixed;
    right: 0;
    text-align: center;
    top: 0
}

.el-overlay.is-message-box .el-overlay-message-box:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    width: 0
}

.el-message-box.is-draggable .el-message-box__header {
    cursor: move;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-message-box__header {
    padding-bottom: var(--el-messagebox-padding-primary)
}

.el-message-box__header.show-close {
    padding-right: calc(var(--el-messagebox-padding-primary) + var(--el-message-close-size, 16px))
}

.el-message-box__title {
    color: var(--el-messagebox-title-color);
    font-size: var(--el-messagebox-font-size);
    line-height: var(--el-messagebox-font-line-height)
}

.el-message-box__headerbtn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: var(--el-message-close-size, 16px);
    height: 40px;
    outline: none;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 40px
}

.el-message-box__headerbtn .el-message-box__close {
    color: var(--el-color-info);
    font-size: inherit
}

.el-message-box__headerbtn:focus .el-message-box__close, .el-message-box__headerbtn:hover .el-message-box__close {
    color: var(--el-color-primary)
}

.el-message-box__content {
    color: var(--el-messagebox-content-color);
    font-size: var(--el-messagebox-content-font-size)
}

.el-message-box__container {
    align-items: center;
    display: flex;
    gap: 12px
}

.el-message-box__input {
    padding-top: 12px
}

.el-message-box__input div.invalid > input, .el-message-box__input div.invalid > input:focus {
    border-color: var(--el-color-error)
}

.el-message-box__status {
    font-size: 24px
}

.el-message-box__status.el-message-box-icon--primary {
    --el-messagebox-color: var(--el-color-primary);
    color: var(--el-messagebox-color)
}

.el-message-box__status.el-message-box-icon--success {
    --el-messagebox-color: var(--el-color-success);
    color: var(--el-messagebox-color)
}

.el-message-box__status.el-message-box-icon--info {
    --el-messagebox-color: var(--el-color-info);
    color: var(--el-messagebox-color)
}

.el-message-box__status.el-message-box-icon--warning {
    --el-messagebox-color: var(--el-color-warning);
    color: var(--el-messagebox-color)
}

.el-message-box__status.el-message-box-icon--error {
    --el-messagebox-color: var(--el-color-error);
    color: var(--el-messagebox-color)
}

.el-message-box__message {
    margin: 0;
    min-width: 0
}

.el-message-box__message p {
    line-height: var(--el-messagebox-font-line-height);
    margin: 0
}

.el-message-box__errormsg {
    color: var(--el-color-error);
    font-size: var(--el-messagebox-error-font-size);
    line-height: var(--el-messagebox-font-line-height)
}

.el-message-box__btns {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    padding-top: var(--el-messagebox-padding-primary)
}

.el-message-box--center .el-message-box__title {
    align-items: center;
    display: flex;
    gap: 6px;
    justify-content: center
}

.el-message-box--center .el-message-box__status {
    font-size: inherit
}

.el-message-box--center .el-message-box__btns, .el-message-box--center .el-message-box__container {
    justify-content: center
}

.fade-in-linear-enter-active .el-overlay-message-box {
    animation: msgbox-fade-in var(--el-transition-duration)
}

.fade-in-linear-leave-active .el-overlay-message-box {
    animation: msgbox-fade-in var(--el-transition-duration) reverse
}

@keyframes msgbox-fade-in {
    0% {
        opacity: 0;
        transform: translate3d(0, -20px, 0)
    }
    to {
        opacity: 1;
        transform: translateZ(0)
    }
}

.el-message {
    --el-message-bg-color: var(--el-color-info-light-9);
    --el-message-border-color: var(--el-border-color-lighter);
    --el-message-padding: 11px 15px;
    --el-message-close-size: 16px;
    --el-message-close-icon-color: var(--el-text-color-placeholder);
    --el-message-close-hover-color: var(--el-text-color-secondary);
    align-items: center;
    background-color: var(--el-message-bg-color);
    border-color: var(--el-message-border-color);
    border-radius: var(--el-border-radius-base);
    border-style: var(--el-border-style);
    border-width: var(--el-border-width);
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    left: 50%;
    max-width: calc(100% - 32px);
    padding: var(--el-message-padding);
    position: fixed;
    top: 20px;
    transform: translateX(-50%);
    transition: opacity var(--el-transition-duration), transform .4s, top .4s;
    width: -moz-fit-content;
    width: fit-content
}

.el-message.is-plain {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light)
}

.el-message p {
    margin: 0
}

.el-message--primary {
    --el-message-bg-color: var(--el-color-primary-light-9);
    --el-message-border-color: var(--el-color-primary-light-8);
    --el-message-text-color: var(--el-color-primary)
}

.el-message--primary .el-message__content {
    color: var(--el-message-text-color);
    overflow-wrap: break-word
}

.el-message .el-message-icon--primary {
    color: var(--el-message-text-color)
}

.el-message--success {
    --el-message-bg-color: var(--el-color-success-light-9);
    --el-message-border-color: var(--el-color-success-light-8);
    --el-message-text-color: var(--el-color-success)
}

.el-message--success .el-message__content {
    color: var(--el-message-text-color);
    overflow-wrap: break-word
}

.el-message .el-message-icon--success {
    color: var(--el-message-text-color)
}

.el-message--info {
    --el-message-bg-color: var(--el-color-info-light-9);
    --el-message-border-color: var(--el-color-info-light-8);
    --el-message-text-color: var(--el-color-info)
}

.el-message--info .el-message__content {
    color: var(--el-message-text-color);
    overflow-wrap: break-word
}

.el-message .el-message-icon--info {
    color: var(--el-message-text-color)
}

.el-message--warning {
    --el-message-bg-color: var(--el-color-warning-light-9);
    --el-message-border-color: var(--el-color-warning-light-8);
    --el-message-text-color: var(--el-color-warning)
}

.el-message--warning .el-message__content {
    color: var(--el-message-text-color);
    overflow-wrap: break-word
}

.el-message .el-message-icon--warning {
    color: var(--el-message-text-color)
}

.el-message--error {
    --el-message-bg-color: var(--el-color-error-light-9);
    --el-message-border-color: var(--el-color-error-light-8);
    --el-message-text-color: var(--el-color-error)
}

.el-message--error .el-message__content {
    color: var(--el-message-text-color);
    overflow-wrap: break-word
}

.el-message .el-message-icon--error {
    color: var(--el-message-text-color)
}

.el-message .el-message__badge {
    position: absolute;
    right: -8px;
    top: -8px
}

.el-message__content {
    font-size: 14px;
    line-height: 1;
    padding: 0
}

.el-message__content:focus {
    outline-width: 0
}

.el-message .el-message__closeBtn {
    color: var(--el-message-close-icon-color);
    cursor: pointer;
    font-size: var(--el-message-close-size)
}

.el-message .el-message__closeBtn:focus {
    outline-width: 0
}

.el-message .el-message__closeBtn:hover {
    color: var(--el-message-close-hover-color)
}

.el-message-fade-enter-from, .el-message-fade-leave-to {
    opacity: 0;
    transform: translate(-50%, -100%)
}

.el-notification {
    --el-notification-width: 330px;
    --el-notification-padding: 14px 26px 14px 13px;
    --el-notification-radius: 8px;
    --el-notification-shadow: var(--el-box-shadow-light);
    --el-notification-border-color: var(--el-border-color-lighter);
    --el-notification-icon-size: 24px;
    --el-notification-close-font-size: var(--el-message-close-size, 16px);
    --el-notification-group-margin-left: 13px;
    --el-notification-group-margin-right: 8px;
    --el-notification-content-font-size: var(--el-font-size-base);
    --el-notification-content-color: var(--el-text-color-regular);
    --el-notification-title-font-size: 16px;
    --el-notification-title-color: var(--el-text-color-primary);
    --el-notification-close-color: var(--el-text-color-secondary);
    --el-notification-close-hover-color: var(--el-text-color-regular);
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-notification-border-color);
    border-radius: var(--el-notification-radius);
    box-shadow: var(--el-notification-shadow);
    box-sizing: border-box;
    display: flex;
    overflow: hidden;
    overflow-wrap: break-word;
    padding: var(--el-notification-padding);
    position: fixed;
    transition: opacity var(--el-transition-duration), transform var(--el-transition-duration), left var(--el-transition-duration), right var(--el-transition-duration), top .4s, bottom var(--el-transition-duration);
    width: var(--el-notification-width);
    z-index: 9999
}

.el-notification.right {
    right: 16px
}

.el-notification.left {
    left: 16px
}

.el-notification__group {
    flex: 1;
    margin-left: var(--el-notification-group-margin-left);
    margin-right: var(--el-notification-group-margin-right);
    min-width: 0
}

.el-notification__title {
    color: var(--el-notification-title-color);
    font-size: var(--el-notification-title-font-size);
    font-weight: bold;
    line-height: var(--el-notification-icon-size);
    margin: 0
}

.el-notification__content {
    color: var(--el-notification-content-color);
    font-size: var(--el-notification-content-font-size);
    line-height: 24px;
    margin: 6px 0 0
}

.el-notification__content p {
    margin: 0
}

.el-notification .el-notification__icon {
    flex-shrink: 0;
    font-size: var(--el-notification-icon-size);
    height: var(--el-notification-icon-size);
    width: var(--el-notification-icon-size)
}

.el-notification .el-notification__closeBtn {
    color: var(--el-notification-close-color);
    cursor: pointer;
    font-size: var(--el-notification-close-font-size);
    position: absolute;
    right: 15px;
    top: 18px
}

.el-notification .el-notification__closeBtn:hover {
    color: var(--el-notification-close-hover-color)
}

.el-notification .el-notification--primary {
    --el-notification-icon-color: var(--el-color-primary);
    color: var(--el-notification-icon-color)
}

.el-notification .el-notification--success {
    --el-notification-icon-color: var(--el-color-success);
    color: var(--el-notification-icon-color)
}

.el-notification .el-notification--info {
    --el-notification-icon-color: var(--el-color-info);
    color: var(--el-notification-icon-color)
}

.el-notification .el-notification--warning {
    --el-notification-icon-color: var(--el-color-warning);
    color: var(--el-notification-icon-color)
}

.el-notification .el-notification--error {
    --el-notification-icon-color: var(--el-color-error);
    color: var(--el-notification-icon-color)
}

.el-notification-fade-enter-from.right {
    right: 0;
    transform: translateX(100%)
}

.el-notification-fade-enter-from.left {
    left: 0;
    transform: translateX(-100%)
}

.el-notification-fade-leave-to {
    opacity: 0
}

.el-overlay {
    background-color: var(--el-overlay-color-lighter);
    bottom: 0;
    height: 100%;
    left: 0;
    overflow: auto;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 2000
}

.el-overlay .el-overlay-root {
    height: 0
}

.el-page-header.is-contentful .el-page-header__main {
    border-top: 1px solid var(--el-border-color-light);
    margin-top: 16px
}

.el-page-header__header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    line-height: 24px
}

.el-page-header__left {
    align-items: center;
    display: flex;
    margin-right: 40px;
    position: relative
}

.el-page-header__back {
    align-items: center;
    cursor: pointer;
    display: flex
}

.el-page-header__left .el-divider--vertical {
    margin: 0 16px
}

.el-page-header__icon {
    align-items: center;
    display: flex;
    font-size: 16px;
    margin-right: 10px
}

.el-page-header__icon .el-icon {
    font-size: inherit
}

.el-page-header__title {
    font-size: 14px;
    font-weight: 500
}

.el-page-header__content {
    color: var(--el-text-color-primary);
    font-size: 18px
}

.el-page-header__breadcrumb {
    margin-bottom: 16px
}

.el-pagination {
    --el-pagination-font-size: 14px;
    --el-pagination-bg-color: var(--el-fill-color-blank);
    --el-pagination-text-color: var(--el-text-color-primary);
    --el-pagination-border-radius: 2px;
    --el-pagination-button-color: var(--el-text-color-primary);
    --el-pagination-button-width: 32px;
    --el-pagination-button-height: 32px;
    --el-pagination-button-disabled-color: var(--el-text-color-placeholder);
    --el-pagination-button-disabled-bg-color: var(--el-fill-color-blank);
    --el-pagination-button-bg-color: var(--el-fill-color);
    --el-pagination-hover-color: var(--el-color-primary);
    --el-pagination-font-size-small: 12px;
    --el-pagination-button-width-small: 24px;
    --el-pagination-button-height-small: 24px;
    --el-pagination-button-width-large: 40px;
    --el-pagination-button-height-large: 40px;
    --el-pagination-item-gap: 16px;
    align-items: center;
    color: var(--el-pagination-text-color);
    display: flex;
    font-size: var(--el-pagination-font-size);
    font-weight: normal;
    white-space: nowrap
}

.el-pagination .el-input__inner {
    -moz-appearance: textfield;
    text-align: center
}

.el-pagination .el-select {
    width: 128px
}

.el-pagination button {
    align-items: center;
    background: var(--el-pagination-bg-color);
    border: none;
    border-radius: var(--el-pagination-border-radius);
    box-sizing: border-box;
    color: var(--el-pagination-button-color);
    cursor: pointer;
    display: flex;
    font-size: var(--el-pagination-font-size);
    height: var(--el-pagination-button-height);
    justify-content: center;
    line-height: var(--el-pagination-button-height);
    min-width: var(--el-pagination-button-width);
    padding: 0 4px;
    text-align: center
}

.el-pagination button * {
    pointer-events: none
}

.el-pagination button:focus {
    outline: none
}

.el-pagination button.is-active, .el-pagination button:hover {
    color: var(--el-pagination-hover-color)
}

.el-pagination button.is-active {
    cursor: default;
    font-weight: bold
}

.el-pagination button.is-active.is-disabled {
    color: var(--el-text-color-secondary);
    font-weight: bold
}

.el-pagination button.is-disabled, .el-pagination button:disabled {
    background-color: var(--el-pagination-button-disabled-bg-color);
    color: var(--el-pagination-button-disabled-color);
    cursor: not-allowed
}

.el-pagination button:focus-visible {
    outline: 1px solid var(--el-pagination-hover-color);
    outline-offset: -1px
}

.el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
    display: block;
    font-size: 12px;
    font-weight: bold;
    width: inherit
}

.el-pagination > .is-first {
    margin-left: 0 !important
}

.el-pagination > .is-last {
    margin-right: 0 !important
}

.el-pagination .btn-prev {
    margin-left: var(--el-pagination-item-gap)
}

.el-pagination__sizes, .el-pagination__total {
    color: var(--el-text-color-regular);
    font-weight: normal;
    margin-left: var(--el-pagination-item-gap)
}

.el-pagination__total[disabled=true] {
    color: var(--el-text-color-placeholder)
}

.el-pagination__jump {
    align-items: center;
    color: var(--el-text-color-regular);
    display: flex;
    font-weight: normal;
    margin-left: var(--el-pagination-item-gap)
}

.el-pagination__jump[disabled=true] {
    color: var(--el-text-color-placeholder)
}

.el-pagination__goto {
    margin-right: 8px
}

.el-pagination__editor {
    box-sizing: border-box;
    text-align: center
}

.el-pagination__editor.el-input {
    width: 56px
}

.el-pagination__editor .el-input__inner::-webkit-inner-spin-button, .el-pagination__editor .el-input__inner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.el-pagination__classifier {
    margin-left: 8px
}

.el-pagination__rightwrapper {
    align-items: center;
    display: flex;
    flex: 1;
    justify-content: flex-end
}

.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li {
    background-color: var(--el-pagination-button-bg-color);
    margin: 0 4px
}

.el-pagination.is-background .btn-next.is-active, .el-pagination.is-background .btn-prev.is-active, .el-pagination.is-background .el-pager li.is-active {
    background-color: var(--el-color-primary);
    color: var(--el-color-white)
}

.el-pagination.is-background .btn-next.is-disabled, .el-pagination.is-background .btn-next:disabled, .el-pagination.is-background .btn-prev.is-disabled, .el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .el-pager li.is-disabled, .el-pagination.is-background .el-pager li:disabled {
    background-color: var(--el-disabled-bg-color);
    color: var(--el-text-color-placeholder)
}

.el-pagination.is-background .btn-next.is-disabled.is-active, .el-pagination.is-background .btn-next:disabled.is-active, .el-pagination.is-background .btn-prev.is-disabled.is-active, .el-pagination.is-background .btn-prev:disabled.is-active, .el-pagination.is-background .el-pager li.is-disabled.is-active, .el-pagination.is-background .el-pager li:disabled.is-active {
    background-color: var(--el-fill-color-dark);
    color: var(--el-text-color-secondary)
}

.el-pagination.is-background .btn-prev {
    margin-left: var(--el-pagination-item-gap)
}

.el-pagination--small .btn-next, .el-pagination--small .btn-prev, .el-pagination--small .el-pager li {
    font-size: var(--el-pagination-font-size-small);
    height: var(--el-pagination-button-height-small);
    line-height: var(--el-pagination-button-height-small);
    min-width: var(--el-pagination-button-width-small)
}

.el-pagination--small button, .el-pagination--small span:not([class*=suffix]) {
    font-size: var(--el-pagination-font-size-small)
}

.el-pagination--small .el-select {
    width: 100px
}

.el-pagination--large .btn-next, .el-pagination--large .btn-prev, .el-pagination--large .el-pager li {
    height: var(--el-pagination-button-height-large);
    line-height: var(--el-pagination-button-height-large);
    min-width: var(--el-pagination-button-width-large)
}

.el-pagination--large .el-select .el-input {
    width: 160px
}

.el-pager {
    font-size: 0;
    list-style: none;
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-pager, .el-pager li {
    align-items: center;
    display: flex
}

.el-pager li {
    background: var(--el-pagination-bg-color);
    border: none;
    border-radius: var(--el-pagination-border-radius);
    box-sizing: border-box;
    color: var(--el-pagination-button-color);
    cursor: pointer;
    font-size: var(--el-pagination-font-size);
    height: var(--el-pagination-button-height);
    justify-content: center;
    line-height: var(--el-pagination-button-height);
    min-width: var(--el-pagination-button-width);
    padding: 0 4px;
    text-align: center
}

.el-pager li * {
    pointer-events: none
}

.el-pager li:focus {
    outline: none
}

.el-pager li.is-active, .el-pager li:hover {
    color: var(--el-pagination-hover-color)
}

.el-pager li.is-active {
    cursor: default;
    font-weight: bold
}

.el-pager li.is-active.is-disabled {
    color: var(--el-text-color-secondary);
    font-weight: bold
}

.el-pager li.is-disabled, .el-pager li:disabled {
    background-color: var(--el-pagination-button-disabled-bg-color);
    color: var(--el-pagination-button-disabled-color);
    cursor: not-allowed
}

.el-pager li:focus-visible {
    outline: 1px solid var(--el-pagination-hover-color);
    outline-offset: -1px
}

.el-popconfirm__main {
    align-items: center;
    display: flex
}

.el-popconfirm__icon {
    margin-right: 5px
}

.el-popconfirm__action {
    margin-top: 8px;
    text-align: right
}

.el-popover {
    --el-popover-bg-color: var(--el-bg-color-overlay);
    --el-popover-font-size: var(--el-font-size-base);
    --el-popover-border-color: var(--el-border-color-lighter);
    --el-popover-padding: 12px;
    --el-popover-padding-large: 18px 20px;
    --el-popover-title-font-size: 16px;
    --el-popover-title-text-color: var(--el-text-color-primary);
    --el-popover-border-radius: 4px
}

.el-popover.el-popper {
    background: var(--el-popover-bg-color);
    border: 1px solid var(--el-popover-border-color);
    border-radius: var(--el-popover-border-radius);
    box-shadow: var(--el-box-shadow-light);
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    font-size: var(--el-popover-font-size);
    line-height: 1.4;
    min-width: 150px;
    overflow-wrap: break-word;
    padding: var(--el-popover-padding);
    z-index: var(--el-index-popper)
}

.el-popover.el-popper--plain {
    padding: var(--el-popover-padding-large)
}

.el-popover__title {
    color: var(--el-popover-title-text-color);
    font-size: var(--el-popover-title-font-size);
    line-height: 1;
    margin-bottom: 12px
}

.el-popover__reference:focus:hover, .el-popover__reference:focus:not(.focusing) {
    outline-width: 0
}

.el-popover.el-popper.is-dark {
    --el-popover-bg-color: var(--el-text-color-primary);
    --el-popover-border-color: var(--el-text-color-primary);
    --el-popover-title-text-color: var(--el-bg-color);
    color: var(--el-bg-color)
}

.el-popover.el-popper:focus, .el-popover.el-popper:focus:active {
    outline-width: 0
}

.el-progress {
    align-items: center;
    display: flex;
    line-height: 1;
    position: relative
}

.el-progress__text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    line-height: 1;
    margin-left: 5px;
    min-width: 50px
}

.el-progress__text i {
    display: block;
    vertical-align: middle
}

.el-progress--circle, .el-progress--dashboard {
    display: inline-block
}

.el-progress--circle .el-progress__text, .el-progress--dashboard .el-progress__text {
    left: 0;
    margin: 0;
    position: absolute;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
    width: 100%
}

.el-progress--circle .el-progress__text i, .el-progress--dashboard .el-progress__text i {
    display: inline-block;
    vertical-align: middle
}

.el-progress--without-text .el-progress__text {
    display: none
}

.el-progress--without-text .el-progress-bar {
    display: block;
    margin-right: 0;
    padding-right: 0
}

.el-progress--text-inside .el-progress-bar {
    margin-right: 0;
    padding-right: 0
}

.el-progress.is-success .el-progress-bar__inner {
    background-color: var(--el-color-success)
}

.el-progress.is-success .el-progress__text {
    color: var(--el-color-success)
}

.el-progress.is-warning .el-progress-bar__inner {
    background-color: var(--el-color-warning)
}

.el-progress.is-warning .el-progress__text {
    color: var(--el-color-warning)
}

.el-progress.is-exception .el-progress-bar__inner {
    background-color: var(--el-color-danger)
}

.el-progress.is-exception .el-progress__text {
    color: var(--el-color-danger)
}

.el-progress-bar {
    box-sizing: border-box;
    flex-grow: 1
}

.el-progress-bar__outer {
    background-color: var(--el-border-color-lighter);
    border-radius: 100px;
    height: 6px;
    overflow: hidden;
    position: relative;
    vertical-align: middle
}

.el-progress-bar__inner {
    background-color: var(--el-color-primary);
    border-radius: 100px;
    height: 100%;
    left: 0;
    line-height: 1;
    position: absolute;
    text-align: right;
    top: 0;
    transition: width .6s ease;
    white-space: nowrap
}

.el-progress-bar__inner:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.el-progress-bar__inner--indeterminate {
    animation: indeterminate 3s infinite;
    transform: translateZ(0)
}

.el-progress-bar__inner--striped {
    background-image: linear-gradient(45deg, rgba(0, 0, 0, .1) 25%, transparent 0, transparent 50%, rgba(0, 0, 0, .1) 0, rgba(0, 0, 0, .1) 75%, transparent 0, transparent);
    background-size: 1.25em 1.25em
}

.el-progress-bar__inner--striped.el-progress-bar__inner--striped-flow {
    animation: striped-flow 3s linear infinite
}

.el-progress-bar__innerText {
    color: #ffffff;
    display: inline-block;
    font-size: 12px;
    margin: 0 5px;
    vertical-align: middle
}

@keyframes progress {
    0% {
        background-position: 0 0
    }
    to {
        background-position: 32px 0
    }
}

@keyframes indeterminate {
    0% {
        left: -100%
    }
    to {
        left: 100%
    }
}

@keyframes striped-flow {
    0% {
        background-position: -100%
    }
    to {
        background-position: 100%
    }
}

.el-radio-button {
    --el-radio-button-checked-bg-color: var(--el-color-primary);
    --el-radio-button-checked-text-color: var(--el-color-white);
    --el-radio-button-checked-border-color: var(--el-color-primary);
    --el-radio-button-disabled-checked-fill: var(--el-border-color-extra-light)
}

.el-radio-button, .el-radio-button__inner {
    display: inline-block;
    outline: none;
    position: relative
}

.el-radio-button__inner {
    -webkit-appearance: none;
    background: var(--el-button-bg-color, var(--el-fill-color-blank));
    border: var(--el-border);
    border-left: 0;
    border-radius: 0;
    box-sizing: border-box;
    color: var(--el-button-text-color, var(--el-text-color-regular));
    cursor: pointer;
    font-size: var(--el-font-size-base);
    font-weight: var(--el-button-font-weight, var(--el-font-weight-primary));
    line-height: 1;
    margin: 0;
    padding: 8px 15px;
    text-align: center;
    transition: var(--el-transition-all);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap
}

.el-radio-button__inner.is-round {
    padding: 8px 15px
}

.el-radio-button__inner:hover {
    color: var(--el-color-primary)
}

.el-radio-button__inner [class*=el-icon-] {
    line-height: .9
}

.el-radio-button__inner [class*=el-icon-] + span {
    margin-left: 5px
}

.el-radio-button:first-child .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
    box-shadow: none !important
}

.el-radio-button.is-active .el-radio-button__original-radio:not(:disabled) + .el-radio-button__inner {
    background-color: var(--el-radio-button-checked-bg-color, var(--el-color-primary));
    border-color: var(--el-radio-button-checked-border-color, var(--el-color-primary));
    box-shadow: -1px 0 0 0 var(--el-radio-button-checked-border-color, var(--el-color-primary));
    color: var(--el-radio-button-checked-text-color, var(--el-color-white))
}

.el-radio-button__original-radio {
    opacity: 0;
    outline: none;
    position: absolute;
    z-index: -1
}

.el-radio-button__original-radio:focus-visible + .el-radio-button__inner {
    border-left: var(--el-border);
    border-left-color: var(--el-radio-button-checked-border-color, var(--el-color-primary));
    border-radius: var(--el-border-radius-base);
    box-shadow: none;
    outline: 2px solid var(--el-radio-button-checked-border-color);
    outline-offset: 1px;
    z-index: 2
}

.el-radio-button__original-radio:disabled + .el-radio-button__inner {
    background-color: var(--el-button-disabled-bg-color, var(--el-fill-color-blank));
    background-image: none;
    border-color: var(--el-button-disabled-border-color, var(--el-border-color-light));
    box-shadow: none;
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-radio-button__original-radio:disabled:checked + .el-radio-button__inner {
    background-color: var(--el-radio-button-disabled-checked-fill)
}

.el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0
}

.el-radio-button:first-child:last-child .el-radio-button__inner {
    border-radius: var(--el-border-radius-base)
}

.el-radio-button--large .el-radio-button__inner {
    border-radius: 0;
    font-size: var(--el-font-size-base);
    padding: 12px 19px
}

.el-radio-button--large .el-radio-button__inner.is-round {
    padding: 12px 19px
}

.el-radio-button--small .el-radio-button__inner {
    border-radius: 0;
    font-size: 12px;
    padding: 5px 11px
}

.el-radio-button--small .el-radio-button__inner.is-round {
    padding: 5px 11px
}

.el-radio-group {
    align-items: center;
    display: inline-flex;
    flex-wrap: wrap;
    font-size: 0
}

.el-radio {
    --el-radio-font-size: var(--el-font-size-base);
    --el-radio-text-color: var(--el-text-color-regular);
    --el-radio-font-weight: var(--el-font-weight-primary);
    --el-radio-input-height: 14px;
    --el-radio-input-width: 14px;
    --el-radio-input-border-radius: var(--el-border-radius-circle);
    --el-radio-input-bg-color: var(--el-fill-color-blank);
    --el-radio-input-border: var(--el-border);
    --el-radio-input-border-color: var(--el-border-color);
    --el-radio-input-border-color-hover: var(--el-color-primary);
    align-items: center;
    color: var(--el-radio-text-color);
    cursor: pointer;
    display: inline-flex;
    font-size: var(--el-font-size-base);
    font-weight: var(--el-radio-font-weight);
    height: 32px;
    margin-right: 30px;
    outline: none;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap
}

.el-radio.el-radio--large {
    height: 40px
}

.el-radio.el-radio--small {
    height: 24px
}

.el-radio.is-bordered {
    border: var(--el-border);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    padding: 0 15px 0 9px
}

.el-radio.is-bordered.is-checked {
    border-color: var(--el-color-primary)
}

.el-radio.is-bordered.is-disabled {
    border-color: var(--el-border-color-lighter);
    cursor: not-allowed
}

.el-radio.is-bordered.el-radio--large {
    border-radius: var(--el-border-radius-base);
    padding: 0 19px 0 11px
}

.el-radio.is-bordered.el-radio--large .el-radio__label {
    font-size: var(--el-font-size-base)
}

.el-radio.is-bordered.el-radio--large .el-radio__inner {
    height: 14px;
    width: 14px
}

.el-radio.is-bordered.el-radio--small {
    border-radius: var(--el-border-radius-base);
    padding: 0 11px 0 7px
}

.el-radio.is-bordered.el-radio--small .el-radio__label {
    font-size: 12px
}

.el-radio.is-bordered.el-radio--small .el-radio__inner {
    height: 12px;
    width: 12px
}

.el-radio:last-child {
    margin-right: 0
}

.el-radio__input {
    cursor: pointer;
    display: inline-flex;
    outline: none;
    position: relative;
    vertical-align: middle;
    white-space: nowrap
}

.el-radio__input.is-disabled .el-radio__inner {
    border-color: var(--el-disabled-border-color)
}

.el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled .el-radio__inner:after {
    background-color: var(--el-disabled-bg-color);
    cursor: not-allowed
}

.el-radio__input.is-disabled .el-radio__inner + .el-radio__label {
    cursor: not-allowed
}

.el-radio__input.is-disabled.is-checked .el-radio__inner {
    background-color: var(--el-disabled-bg-color);
    border-color: var(--el-disabled-border-color)
}

.el-radio__input.is-disabled.is-checked .el-radio__inner:after {
    background-color: var(--el-text-color-placeholder)
}

.el-radio__input.is-disabled + span.el-radio__label {
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-radio__input.is-checked .el-radio__inner {
    background: var(--el-color-primary);
    border-color: var(--el-color-primary)
}

.el-radio__input.is-checked .el-radio__inner:after {
    background-color: var(--el-color-white);
    transform: translate(-50%, -50%) scale(1)
}

.el-radio__input.is-checked + .el-radio__label {
    color: var(--el-color-primary)
}

.el-radio__input.is-focus .el-radio__inner {
    border-color: var(--el-radio-input-border-color-hover)
}

.el-radio__inner {
    background-color: var(--el-radio-input-bg-color);
    border: var(--el-radio-input-border);
    border-radius: var(--el-radio-input-border-radius);
    box-sizing: border-box;
    cursor: pointer;
    display: inline-block;
    height: var(--el-radio-input-height);
    position: relative;
    width: var(--el-radio-input-width)
}

.el-radio__inner:hover {
    border-color: var(--el-radio-input-border-color-hover)
}

.el-radio__inner:after {
    border-radius: var(--el-radio-input-border-radius);
    content: "";
    height: 4px;
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform .15s ease-in;
    width: 4px
}

.el-radio__original {
    bottom: 0;
    left: 0;
    margin: 0;
    opacity: 0;
    outline: none;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1
}

.el-radio__original:focus-visible + .el-radio__inner {
    border-radius: var(--el-radio-input-border-radius);
    outline: 2px solid var(--el-radio-input-border-color-hover);
    outline-offset: 1px
}

.el-radio:focus:not(:focus-visible):not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: 0 0 2px 2px var(--el-radio-input-border-color-hover)
}

.el-radio__label {
    font-size: var(--el-radio-font-size);
    padding-left: 8px
}

.el-radio.el-radio--large .el-radio__label {
    font-size: 14px
}

.el-radio.el-radio--large .el-radio__inner {
    height: 14px;
    width: 14px
}

.el-radio.el-radio--small .el-radio__label {
    font-size: 12px
}

.el-radio.el-radio--small .el-radio__inner {
    height: 12px;
    width: 12px
}

.el-rate {
    --el-rate-height: 20px;
    --el-rate-font-size: var(--el-font-size-base);
    --el-rate-icon-size: 18px;
    --el-rate-icon-margin: 6px;
    --el-rate-void-color: var(--el-border-color-darker);
    --el-rate-fill-color: #f7ba2a;
    --el-rate-disabled-void-color: var(--el-fill-color);
    --el-rate-text-color: var(--el-text-color-primary);
    align-items: center;
    display: inline-flex;
    height: 32px
}

.el-rate:active, .el-rate:focus {
    outline: none
}

.el-rate__item {
    color: var(--el-rate-void-color);
    cursor: pointer;
    display: inline-block;
    font-size: 0;
    line-height: normal;
    position: relative;
    vertical-align: middle
}

.el-rate .el-rate__icon {
    display: inline-block;
    font-size: var(--el-rate-icon-size);
    margin-right: var(--el-rate-icon-margin);
    position: relative;
    transition: var(--el-transition-duration)
}

.el-rate .el-rate__icon.hover {
    transform: scale(1.15)
}

.el-rate .el-rate__icon .path2 {
    left: 0;
    position: absolute;
    top: 0
}

.el-rate .el-rate__icon.is-active {
    color: var(--el-rate-fill-color)
}

.el-rate__decimal {
    color: var(--el-rate-fill-color);
    display: inline-block;
    overflow: hidden
}

.el-rate__decimal, .el-rate__decimal--box {
    left: 0;
    position: absolute;
    top: 0
}

.el-rate__text {
    color: var(--el-rate-text-color);
    font-size: var(--el-rate-font-size);
    vertical-align: middle
}

.el-rate--large {
    height: 40px
}

.el-rate--small {
    height: 24px
}

.el-rate--small .el-rate__icon {
    font-size: 14px
}

.el-rate.is-disabled .el-rate__item {
    color: var(--el-rate-disabled-void-color);
    cursor: auto
}

.el-result {
    --el-result-padding: 40px 30px;
    --el-result-icon-font-size: 64px;
    --el-result-title-font-size: 20px;
    --el-result-title-margin-top: 20px;
    --el-result-subtitle-margin-top: 10px;
    --el-result-extra-margin-top: 30px;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--el-result-padding);
    text-align: center
}

.el-result__icon svg {
    height: var(--el-result-icon-font-size);
    width: var(--el-result-icon-font-size)
}

.el-result__title {
    margin-top: var(--el-result-title-margin-top)
}

.el-result__title p {
    color: var(--el-text-color-primary);
    font-size: var(--el-result-title-font-size);
    line-height: 1.3;
    margin: 0
}

.el-result__subtitle {
    margin-top: var(--el-result-subtitle-margin-top)
}

.el-result__subtitle p {
    color: var(--el-text-color-regular);
    font-size: var(--el-font-size-base);
    line-height: 1.3;
    margin: 0
}

.el-result__extra {
    margin-top: var(--el-result-extra-margin-top)
}

.el-result .icon-primary {
    --el-result-color: var(--el-color-primary);
    color: var(--el-result-color)
}

.el-result .icon-success {
    --el-result-color: var(--el-color-success);
    color: var(--el-result-color)
}

.el-result .icon-warning {
    --el-result-color: var(--el-color-warning);
    color: var(--el-result-color)
}

.el-result .icon-danger {
    --el-result-color: var(--el-color-danger);
    color: var(--el-result-color)
}

.el-result .icon-error {
    --el-result-color: var(--el-color-error);
    color: var(--el-result-color)
}

.el-result .icon-info {
    --el-result-color: var(--el-color-info);
    color: var(--el-result-color)
}

.el-row {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    position: relative
}

.el-row.is-justify-center {
    justify-content: center
}

.el-row.is-justify-end {
    justify-content: flex-end
}

.el-row.is-justify-space-between {
    justify-content: space-between
}

.el-row.is-justify-space-around {
    justify-content: space-around
}

.el-row.is-justify-space-evenly {
    justify-content: space-evenly
}

.el-row.is-align-top {
    align-items: flex-start
}

.el-row.is-align-middle {
    align-items: center
}

.el-row.is-align-bottom {
    align-items: flex-end
}

.el-scrollbar {
    --el-scrollbar-opacity: 0.3;
    --el-scrollbar-bg-color: var(--el-text-color-secondary);
    --el-scrollbar-hover-opacity: 0.5;
    --el-scrollbar-hover-bg-color: var(--el-text-color-secondary);
    height: 100%;
    overflow: hidden;
    position: relative
}

.el-scrollbar__wrap {
    height: 100%;
    overflow: auto
}

.el-scrollbar__wrap--hidden-default {
    scrollbar-width: none
}

.el-scrollbar__wrap--hidden-default::-webkit-scrollbar {
    display: none
}

.el-scrollbar__thumb {
    background-color: var(--el-scrollbar-bg-color, var(--el-text-color-secondary));
    border-radius: inherit;
    cursor: pointer;
    display: block;
    height: 0;
    opacity: var(--el-scrollbar-opacity, .3);
    position: relative;
    transition: var(--el-transition-duration) background-color;
    width: 0
}

.el-scrollbar__thumb:hover {
    background-color: var(--el-scrollbar-hover-bg-color, var(--el-text-color-secondary));
    opacity: var(--el-scrollbar-hover-opacity, .5)
}

.el-scrollbar__bar {
    border-radius: 4px;
    bottom: 2px;
    position: absolute;
    right: 2px;
    z-index: 1
}

.el-scrollbar__bar.is-vertical {
    top: 2px;
    width: 6px
}

.el-scrollbar__bar.is-vertical > div {
    width: 100%
}

.el-scrollbar__bar.is-horizontal {
    height: 6px;
    left: 2px
}

.el-scrollbar__bar.is-horizontal > div {
    height: 100%
}

.el-scrollbar-fade-enter-active {
    transition: opacity .34s ease-out
}

.el-scrollbar-fade-leave-active {
    transition: opacity .12s ease-out
}

.el-scrollbar-fade-enter-from, .el-scrollbar-fade-leave-active {
    opacity: 0
}

.el-select-dropdown {
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    z-index: calc(var(--el-index-top) + 1)
}

.el-select-dropdown .el-scrollbar.is-empty .el-select-dropdown__list {
    padding: 0
}

.el-select-dropdown__empty, .el-select-dropdown__loading {
    color: var(--el-text-color-secondary);
    font-size: var(--el-select-font-size);
    margin: 0;
    padding: 10px 0;
    text-align: center
}

.el-select-dropdown__wrap {
    max-height: 274px
}

.el-select-dropdown__list {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 6px 0
}

.el-select-dropdown__list.el-vl__window {
    margin: 6px 0;
    padding: 0
}

.el-select-dropdown__header {
    border-bottom: 1px solid var(--el-border-color-light);
    padding: 10px
}

.el-select-dropdown__footer {
    border-top: 1px solid var(--el-border-color-light);
    padding: 10px
}

.el-select-dropdown__item {
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: var(--el-font-size-base);
    height: 34px;
    line-height: 34px;
    overflow: hidden;
    padding: 0 32px 0 20px;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-select-dropdown__item.is-hovering {
    background-color: var(--el-fill-color-light)
}

.el-select-dropdown__item.is-selected {
    color: var(--el-color-primary);
    font-weight: bold
}

.el-select-dropdown__item.is-disabled {
    background-color: unset;
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.is-selected:after {
    background-color: var(--el-color-primary);
    background-position: 50%;
    background-repeat: no-repeat;
    border-right: none;
    border-top: none;
    content: "";
    height: 12px;
    mask: url("data:image/svg+xml;utf8,%3Csvg class='icon' width='200' height='200' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='currentColor' d='M406.656 706.944L195.84 496.256a32 32 0 10-45.248 45.248l256 256 512-512a32 32 0 00-45.248-45.248L406.592 706.944z'%3E%3C/path%3E%3C/svg%3E") no-repeat;
    mask-size: 100% 100%;
    -webkit-mask: url("data:image/svg+xml;utf8,%3Csvg class='icon' width='200' height='200' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='currentColor' d='M406.656 706.944L195.84 496.256a32 32 0 10-45.248 45.248l256 256 512-512a32 32 0 00-45.248-45.248L406.592 706.944z'%3E%3C/path%3E%3C/svg%3E") no-repeat;
    -webkit-mask-size: 100% 100%;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.is-disabled:after {
    background-color: var(--el-text-color-placeholder)
}

.el-select-group {
    margin: 0;
    padding: 0
}

.el-select-group__wrap {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative
}

.el-select-group__title {
    box-sizing: border-box;
    color: var(--el-color-info);
    font-size: 12px;
    line-height: 34px;
    overflow: hidden;
    padding: 0 20px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-select-group .el-select-dropdown__item {
    padding-left: 20px
}

.el-select {
    --el-select-border-color-hover: var(--el-border-color-hover);
    --el-select-disabled-color: var(--el-disabled-text-color);
    --el-select-disabled-border: var(--el-disabled-border-color);
    --el-select-font-size: var(--el-font-size-base);
    --el-select-close-hover-color: var(--el-text-color-secondary);
    --el-select-input-color: var(--el-text-color-placeholder);
    --el-select-multiple-input-color: var(--el-text-color-regular);
    --el-select-input-focus-border-color: var(--el-color-primary);
    --el-select-input-font-size: 14px;
    --el-select-width: 100%;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    width: var(--el-select-width)
}

.el-select__wrapper {
    align-items: center;
    background-color: var(--el-fill-color-blank);
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 6px;
    line-height: 24px;
    min-height: 32px;
    padding: 4px 12px;
    position: relative;
    text-align: left;
    transform: translateZ(0);
    transition: var(--el-transition-duration)
}

.el-select__wrapper.is-filterable {
    cursor: text
}

.el-select__wrapper.is-focused {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset
}

.el-select__wrapper.is-hovering:not(.is-focused) {
    box-shadow: 0 0 0 1px var(--el-border-color-hover) inset
}

.el-select__wrapper.is-disabled {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-placeholder);
    cursor: not-allowed
}

.el-select__wrapper.is-disabled, .el-select__wrapper.is-disabled:hover {
    box-shadow: 0 0 0 1px var(--el-select-disabled-border) inset
}

.el-select__wrapper.is-disabled.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset
}

.el-select__wrapper.is-disabled .el-select__selected-item {
    color: var(--el-select-disabled-color)
}

.el-select__wrapper.is-disabled .el-select__caret, .el-select__wrapper.is-disabled .el-tag, .el-select__wrapper.is-disabled input {
    cursor: not-allowed
}

.el-select__prefix, .el-select__suffix {
    align-items: center;
    color: var(--el-input-icon-color, var(--el-text-color-placeholder));
    display: flex;
    flex-shrink: 0;
    gap: 6px
}

.el-select__caret {
    color: var(--el-select-input-color);
    cursor: pointer;
    font-size: var(--el-select-input-font-size);
    transform: rotate(0deg);
    transition: var(--el-transition-duration)
}

.el-select__caret.is-reverse {
    transform: rotate(180deg)
}

.el-select__clear {
    cursor: pointer
}

.el-select__clear:hover {
    color: var(--el-select-close-hover-color)
}

.el-select__selection {
    align-items: center;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 6px;
    min-width: 0;
    position: relative
}

.el-select__selection.is-near {
    margin-left: -8px
}

.el-select__selection .el-tag {
    border-color: transparent;
    cursor: pointer
}

.el-select__selection .el-tag.el-tag--plain {
    border-color: var(--el-tag-border-color)
}

.el-select__selection .el-tag .el-tag__content {
    min-width: 0
}

.el-select__selected-item {
    display: flex;
    flex-wrap: wrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-select__tags-text {
    line-height: normal
}

.el-select__placeholder, .el-select__tags-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-select__placeholder {
    color: var(--el-input-text-color, var(--el-text-color-regular));
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    z-index: -1
}

.el-select__placeholder.is-transparent {
    color: var(--el-text-color-placeholder);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-select__popper.el-popper {
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light)
}

.el-select__popper.el-popper, .el-select__popper.el-popper .el-popper__arrow:before {
    border: 1px solid var(--el-border-color-light)
}

.el-select__popper.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-select__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-select__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-select__popper.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-select__input-wrapper {
    flex: 1
}

.el-select__input-wrapper.is-hidden {
    opacity: 0;
    position: absolute;
    z-index: -1
}

.el-select__input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: transparent;
    border: none;
    color: var(--el-select-multiple-input-color);
    font-family: inherit;
    font-size: inherit;
    height: 24px;
    outline: none;
    padding: 0;
    width: 100%
}

.el-select__input.is-disabled {
    cursor: not-allowed
}

.el-select__input-calculator {
    left: 0;
    max-width: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    visibility: hidden;
    white-space: pre
}

.el-select--large .el-select__wrapper {
    font-size: 14px;
    gap: 6px;
    line-height: 24px;
    min-height: 40px;
    padding: 8px 16px
}

.el-select--large .el-select__selection {
    gap: 6px
}

.el-select--large .el-select__selection.is-near {
    margin-left: -8px
}

.el-select--large .el-select__prefix, .el-select--large .el-select__suffix {
    gap: 6px
}

.el-select--large .el-select__input {
    height: 24px
}

.el-select--small .el-select__wrapper {
    font-size: 12px;
    gap: 4px;
    line-height: 20px;
    min-height: 24px;
    padding: 2px 8px
}

.el-select--small .el-select__selection {
    gap: 4px
}

.el-select--small .el-select__selection.is-near {
    margin-left: -6px
}

.el-select--small .el-select__prefix, .el-select--small .el-select__suffix {
    gap: 4px
}

.el-select--small .el-select__input {
    height: 20px
}

.el-skeleton {
    --el-skeleton-circle-size: var(--el-avatar-size)
}

.el-skeleton__item {
    background: var(--el-skeleton-color);
    border-radius: var(--el-border-radius-base);
    display: inline-block;
    height: 16px;
    width: 100%
}

.el-skeleton__circle {
    border-radius: 50%;
    height: var(--el-skeleton-circle-size);
    line-height: var(--el-skeleton-circle-size);
    width: var(--el-skeleton-circle-size)
}

.el-skeleton__button {
    border-radius: 4px;
    height: 40px;
    width: 64px
}

.el-skeleton__p {
    width: 100%
}

.el-skeleton__p.is-last {
    width: 61%
}

.el-skeleton__p.is-first {
    width: 33%
}

.el-skeleton__text {
    height: var(--el-font-size-small);
    width: 100%
}

.el-skeleton__caption {
    height: var(--el-font-size-extra-small)
}

.el-skeleton__h1 {
    height: var(--el-font-size-extra-large)
}

.el-skeleton__h3 {
    height: var(--el-font-size-large)
}

.el-skeleton__h5 {
    height: var(--el-font-size-medium)
}

.el-skeleton__image {
    align-items: center;
    border-radius: 0;
    display: flex;
    justify-content: center;
    width: unset
}

.el-skeleton__image svg {
    color: var(--el-svg-monochrome-grey);
    fill: currentColor;
    height: 22%;
    width: 22%
}

.el-skeleton {
    --el-skeleton-color: var(--el-fill-color);
    --el-skeleton-to-color: var(--el-fill-color-darker)
}

@keyframes el-skeleton-loading {
    0% {
        background-position: 100% 50%
    }
    to {
        background-position: 0 50%
    }
}

.el-skeleton {
    width: 100%
}

.el-skeleton__first-line, .el-skeleton__paragraph {
    background: var(--el-skeleton-color);
    height: 16px;
    margin-top: 16px
}

.el-skeleton.is-animated .el-skeleton__item {
    animation: el-skeleton-loading 1.4s ease infinite;
    background: linear-gradient(90deg, var(--el-skeleton-color) 25%, var(--el-skeleton-to-color) 37%, var(--el-skeleton-color) 63%);
    background-size: 400% 100%
}

.el-slider {
    --el-slider-main-bg-color: var(--el-color-primary);
    --el-slider-runway-bg-color: var(--el-border-color-light);
    --el-slider-stop-bg-color: var(--el-color-white);
    --el-slider-disabled-color: var(--el-text-color-placeholder);
    --el-slider-border-radius: 3px;
    --el-slider-height: 6px;
    --el-slider-button-size: 20px;
    --el-slider-button-wrapper-size: 36px;
    --el-slider-button-wrapper-offset: -15px;
    align-items: center;
    display: flex;
    height: 32px;
    width: 100%
}

.el-slider__runway {
    background-color: var(--el-slider-runway-bg-color);
    border-radius: var(--el-slider-border-radius);
    cursor: pointer;
    flex: 1;
    height: var(--el-slider-height);
    position: relative
}

.el-slider__runway.show-input {
    margin-right: 30px;
    width: auto
}

.el-slider__runway.is-disabled {
    cursor: default
}

.el-slider__runway.is-disabled .el-slider__bar {
    background-color: var(--el-slider-disabled-color)
}

.el-slider__runway.is-disabled .el-slider__button {
    border-color: var(--el-slider-disabled-color)
}

.el-slider__runway.is-disabled .el-slider__button-wrapper.dragging, .el-slider__runway.is-disabled .el-slider__button-wrapper.hover, .el-slider__runway.is-disabled .el-slider__button-wrapper:hover {
    cursor: not-allowed
}

.el-slider__runway.is-disabled .el-slider__button.dragging, .el-slider__runway.is-disabled .el-slider__button.hover, .el-slider__runway.is-disabled .el-slider__button:hover {
    transform: scale(1)
}

.el-slider__runway.is-disabled .el-slider__button.dragging, .el-slider__runway.is-disabled .el-slider__button.hover, .el-slider__runway.is-disabled .el-slider__button:hover {
    cursor: not-allowed
}

.el-slider__input {
    flex-shrink: 0;
    width: 130px
}

.el-slider__bar {
    background-color: var(--el-slider-main-bg-color);
    border-bottom-left-radius: var(--el-slider-border-radius);
    border-top-left-radius: var(--el-slider-border-radius);
    height: var(--el-slider-height);
    position: absolute
}

.el-slider__button-wrapper {
    background-color: transparent;
    height: var(--el-slider-button-wrapper-size);
    line-height: normal;
    outline: none;
    position: absolute;
    text-align: center;
    top: var(--el-slider-button-wrapper-offset);
    transform: translateX(-50%);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: var(--el-slider-button-wrapper-size);
    z-index: 1
}

.el-slider__button-wrapper:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.el-slider__button-wrapper.hover, .el-slider__button-wrapper:hover {
    cursor: grab
}

.el-slider__button-wrapper.dragging {
    cursor: grabbing
}

.el-slider__button {
    background-color: var(--el-color-white);
    border: 2px solid var(--el-slider-main-bg-color);
    border-radius: 50%;
    box-sizing: border-box;
    display: inline-block;
    height: var(--el-slider-button-size);
    transition: var(--el-transition-duration-fast);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    width: var(--el-slider-button-size)
}

.el-slider__button.dragging, .el-slider__button.hover, .el-slider__button:hover {
    transform: scale(1.2)
}

.el-slider__button.hover, .el-slider__button:hover {
    cursor: grab
}

.el-slider__button.dragging {
    cursor: grabbing
}

.el-slider__stop {
    background-color: var(--el-slider-stop-bg-color);
    border-radius: var(--el-border-radius-circle);
    height: var(--el-slider-height);
    position: absolute;
    transform: translateX(-50%);
    width: var(--el-slider-height)
}

.el-slider__marks {
    height: 100%;
    left: 12px;
    top: 0;
    width: 18px
}

.el-slider__marks-text {
    color: var(--el-color-info);
    font-size: 14px;
    margin-top: 15px;
    position: absolute;
    transform: translateX(-50%);
    white-space: pre
}

.el-slider.is-vertical {
    display: inline-flex;
    flex: 0;
    height: 100%;
    position: relative;
    width: auto
}

.el-slider.is-vertical .el-slider__runway {
    height: 100%;
    margin: 0 16px;
    width: var(--el-slider-height)
}

.el-slider.is-vertical .el-slider__bar {
    border-radius: 0 0 3px 3px;
    height: auto;
    width: var(--el-slider-height)
}

.el-slider.is-vertical .el-slider__button-wrapper {
    left: var(--el-slider-button-wrapper-offset);
    top: auto;
    transform: translateY(50%)
}

.el-slider.is-vertical .el-slider__stop {
    transform: translateY(50%)
}

.el-slider.is-vertical .el-slider__marks-text {
    left: 15px;
    margin-top: 0;
    transform: translateY(50%)
}

.el-slider--large {
    height: 40px
}

.el-slider--small {
    height: 24px
}

.el-space {
    display: inline-flex;
    vertical-align: top
}

.el-space__item {
    display: flex;
    flex-wrap: wrap
}

.el-space__item > * {
    flex: 1
}

.el-space--vertical {
    flex-direction: column
}

.el-time-spinner {
    white-space: nowrap;
    width: 100%
}

.el-spinner {
    display: inline-block;
    vertical-align: middle
}

.el-spinner-inner {
    animation: rotate 2s linear infinite;
    height: 50px;
    width: 50px
}

.el-spinner-inner .path {
    stroke: var(--el-border-color-lighter);
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite
}

@keyframes rotate {
    to {
        transform: rotate(1turn)
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35
    }
    to {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124
    }
}

.el-step {
    flex-shrink: 1;
    position: relative
}

.el-step:last-of-type .el-step__line {
    display: none
}

.el-step:last-of-type.is-flex {
    flex-basis: auto !important;
    flex-grow: 0;
    flex-shrink: 0
}

.el-step:last-of-type .el-step__description, .el-step:last-of-type .el-step__main {
    padding-right: 0
}

.el-step__head {
    position: relative;
    width: 100%
}

.el-step__head.is-process {
    border-color: var(--el-text-color-primary);
    color: var(--el-text-color-primary)
}

.el-step__head.is-wait {
    border-color: var(--el-text-color-placeholder);
    color: var(--el-text-color-placeholder)
}

.el-step__head.is-success {
    border-color: var(--el-color-success);
    color: var(--el-color-success)
}

.el-step__head.is-error {
    border-color: var(--el-color-danger);
    color: var(--el-color-danger)
}

.el-step__head.is-finish {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary)
}

.el-step__icon {
    align-items: center;
    background: var(--el-bg-color);
    box-sizing: border-box;
    display: inline-flex;
    font-size: 14px;
    height: 24px;
    justify-content: center;
    position: relative;
    transition: .15s ease-out;
    width: 24px;
    z-index: 1
}

.el-step__icon.is-text {
    border: 2px solid;
    border-radius: 50%
}

.el-step__icon.is-icon {
    width: 40px
}

.el-step__icon-inner {
    color: inherit;
    display: inline-block;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-step__icon-inner[class*=el-icon]:not(.is-status) {
    font-size: 25px;
    font-weight: normal
}

.el-step__icon-inner.is-status {
    transform: translateY(1px)
}

.el-step__line {
    background-color: var(--el-text-color-placeholder);
    border-color: currentColor;
    position: absolute
}

.el-step__line-inner {
    border: 1px solid;
    box-sizing: border-box;
    display: block;
    height: 0;
    transition: .15s ease-out;
    width: 0
}

.el-step__main {
    text-align: left;
    white-space: normal
}

.el-step__title {
    font-size: 16px;
    line-height: 38px
}

.el-step__title.is-process {
    color: var(--el-text-color-primary);
    font-weight: bold
}

.el-step__title.is-wait {
    color: var(--el-text-color-placeholder)
}

.el-step__title.is-success {
    color: var(--el-color-success)
}

.el-step__title.is-error {
    color: var(--el-color-danger)
}

.el-step__title.is-finish {
    color: var(--el-color-primary)
}

.el-step__description {
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    margin-top: -5px;
    padding-right: 10%
}

.el-step__description.is-process {
    color: var(--el-text-color-primary)
}

.el-step__description.is-wait {
    color: var(--el-text-color-placeholder)
}

.el-step__description.is-success {
    color: var(--el-color-success)
}

.el-step__description.is-error {
    color: var(--el-color-danger)
}

.el-step__description.is-finish {
    color: var(--el-color-primary)
}

.el-step.is-horizontal {
    display: inline-block
}

.el-step.is-horizontal .el-step__line {
    height: 2px;
    left: 0;
    right: 0;
    top: 11px
}

.el-step.is-vertical {
    display: flex
}

.el-step.is-vertical .el-step__head {
    flex-grow: 0;
    width: 24px
}

.el-step.is-vertical .el-step__main {
    flex-grow: 1;
    padding-left: 10px
}

.el-step.is-vertical .el-step__title {
    line-height: 24px;
    padding-bottom: 8px
}

.el-step.is-vertical .el-step__line {
    bottom: 0;
    left: 11px;
    top: 0;
    width: 2px
}

.el-step.is-vertical .el-step__icon.is-icon {
    width: 24px
}

.el-step.is-center .el-step__head, .el-step.is-center .el-step__main {
    text-align: center
}

.el-step.is-center .el-step__description {
    padding-left: 20%;
    padding-right: 20%
}

.el-step.is-center .el-step__line {
    left: 50%;
    right: -50%
}

.el-step.is-simple {
    align-items: center;
    display: flex
}

.el-step.is-simple .el-step__head {
    font-size: 0;
    padding-right: 10px;
    width: auto
}

.el-step.is-simple .el-step__icon {
    background: transparent;
    font-size: 12px;
    height: 16px;
    width: 16px
}

.el-step.is-simple .el-step__icon-inner[class*=el-icon]:not(.is-status) {
    font-size: 18px
}

.el-step.is-simple .el-step__icon-inner.is-status {
    transform: scale(.8) translateY(1px)
}

.el-step.is-simple .el-step__main {
    align-items: stretch;
    display: flex;
    flex-grow: 1;
    position: relative
}

.el-step.is-simple .el-step__title {
    font-size: 16px;
    line-height: 20px
}

.el-step.is-simple:not(:last-of-type) .el-step__title {
    max-width: 50%;
    overflow-wrap: break-word
}

.el-step.is-simple .el-step__arrow {
    align-items: center;
    display: flex;
    flex-grow: 1;
    justify-content: center
}

.el-step.is-simple .el-step__arrow:after, .el-step.is-simple .el-step__arrow:before {
    background: var(--el-text-color-placeholder);
    content: "";
    display: inline-block;
    height: 15px;
    position: absolute;
    width: 1px
}

.el-step.is-simple .el-step__arrow:before {
    transform: rotate(-45deg) translateY(-4px);
    transform-origin: 0 0
}

.el-step.is-simple .el-step__arrow:after {
    transform: rotate(45deg) translateY(4px);
    transform-origin: 100% 100%
}

.el-step.is-simple:last-of-type .el-step__arrow {
    display: none
}

.el-steps {
    display: flex
}

.el-steps--simple {
    background: var(--el-fill-color-light);
    border-radius: 4px;
    padding: 13px 8%
}

.el-steps--horizontal {
    white-space: nowrap
}

.el-steps--vertical {
    flex-flow: column;
    height: 100%
}

.el-switch {
    --el-switch-on-color: var(--el-color-primary);
    --el-switch-off-color: var(--el-border-color);
    align-items: center;
    display: inline-flex;
    font-size: 14px;
    height: 32px;
    line-height: 20px;
    position: relative;
    vertical-align: middle
}

.el-switch.is-disabled .el-switch__core, .el-switch.is-disabled .el-switch__label {
    cursor: not-allowed
}

.el-switch__label {
    color: var(--el-text-color-primary);
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    height: 20px;
    transition: var(--el-transition-duration-fast);
    vertical-align: middle
}

.el-switch__label.is-active {
    color: var(--el-color-primary)
}

.el-switch__label--left {
    margin-right: 10px
}

.el-switch__label--right {
    margin-left: 10px
}

.el-switch__label * {
    display: inline-block;
    font-size: 14px;
    line-height: 1
}

.el-switch__label .el-icon {
    height: inherit
}

.el-switch__label .el-icon svg {
    vertical-align: middle
}

.el-switch__input {
    height: 0;
    margin: 0;
    opacity: 0;
    position: absolute;
    width: 0
}

.el-switch__input:focus-visible ~ .el-switch__core {
    outline: 2px solid var(--el-switch-on-color);
    outline-offset: 1px
}

.el-switch__core {
    align-items: center;
    background: var(--el-switch-off-color);
    border: 1px solid var(--el-switch-border-color, var(--el-switch-off-color));
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    height: 20px;
    min-width: 40px;
    outline: none;
    position: relative;
    transition: border-color var(--el-transition-duration), background-color var(--el-transition-duration)
}

.el-switch__core .el-switch__inner {
    align-items: center;
    display: flex;
    height: 16px;
    justify-content: center;
    overflow: hidden;
    padding: 0 4px 0 18px;
    transition: all var(--el-transition-duration);
    width: 100%
}

.el-switch__core .el-switch__inner .is-icon, .el-switch__core .el-switch__inner .is-text {
    color: var(--el-color-white);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap
}

.el-switch__core .el-switch__action {
    align-items: center;
    background-color: var(--el-color-white);
    border-radius: var(--el-border-radius-circle);
    color: var(--el-switch-off-color);
    display: flex;
    height: 16px;
    justify-content: center;
    left: 1px;
    position: absolute;
    transition: all var(--el-transition-duration);
    width: 16px
}

.el-switch.is-checked .el-switch__core {
    background-color: var(--el-switch-on-color);
    border-color: var(--el-switch-border-color, var(--el-switch-on-color))
}

.el-switch.is-checked .el-switch__core .el-switch__action {
    color: var(--el-switch-on-color);
    left: calc(100% - 17px)
}

.el-switch.is-checked .el-switch__core .el-switch__inner {
    padding: 0 18px 0 4px
}

.el-switch.is-disabled {
    opacity: .6
}

.el-switch--wide .el-switch__label.el-switch__label--left span {
    left: 10px
}

.el-switch--wide .el-switch__label.el-switch__label--right span {
    right: 10px
}

.el-switch .label-fade-enter-from, .el-switch .label-fade-leave-active {
    opacity: 0
}

.el-switch--large {
    font-size: 14px;
    height: 40px;
    line-height: 24px
}

.el-switch--large .el-switch__label {
    font-size: 14px;
    height: 24px
}

.el-switch--large .el-switch__label * {
    font-size: 14px
}

.el-switch--large .el-switch__core {
    border-radius: 12px;
    height: 24px;
    min-width: 50px
}

.el-switch--large .el-switch__core .el-switch__inner {
    height: 20px;
    padding: 0 6px 0 22px
}

.el-switch--large .el-switch__core .el-switch__action {
    height: 20px;
    width: 20px
}

.el-switch--large.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 21px)
}

.el-switch--large.is-checked .el-switch__core .el-switch__inner {
    padding: 0 22px 0 6px
}

.el-switch--small {
    font-size: 12px;
    height: 24px;
    line-height: 16px
}

.el-switch--small .el-switch__label {
    font-size: 12px;
    height: 16px
}

.el-switch--small .el-switch__label * {
    font-size: 12px
}

.el-switch--small .el-switch__core {
    border-radius: 8px;
    height: 16px;
    min-width: 30px
}

.el-switch--small .el-switch__core .el-switch__inner {
    height: 12px;
    padding: 0 2px 0 14px
}

.el-switch--small .el-switch__core .el-switch__action {
    height: 12px;
    width: 12px
}

.el-switch--small.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 13px)
}

.el-switch--small.is-checked .el-switch__core .el-switch__inner {
    padding: 0 14px 0 2px
}

.el-table-column--selection .cell {
    padding-left: 14px;
    padding-right: 14px
}

.el-table-filter {
    background-color: #ffffff;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 2px;
    box-shadow: var(--el-box-shadow-light);
    box-sizing: border-box
}

.el-table-filter__list {
    list-style: none;
    margin: 0;
    min-width: 100px;
    padding: 5px 0
}

.el-table-filter__list-item {
    cursor: pointer;
    font-size: var(--el-font-size-base);
    line-height: 36px;
    padding: 0 10px
}

.el-table-filter__list-item:hover {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary)
}

.el-table-filter__list-item.is-active {
    background-color: var(--el-color-primary);
    color: #ffffff
}

.el-table-filter__content {
    min-width: 100px
}

.el-table-filter__bottom {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 8px
}

.el-table-filter__bottom button {
    background: transparent;
    border: none;
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: var(--el-font-size-small);
    padding: 0 3px
}

.el-table-filter__bottom button:hover {
    color: var(--el-color-primary)
}

.el-table-filter__bottom button:focus {
    outline: none
}

.el-table-filter__bottom button.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-table-filter__wrap {
    max-height: 280px
}

.el-table-filter__checkbox-group {
    padding: 10px
}

.el-table-filter__checkbox-group label.el-checkbox {
    align-items: center;
    display: flex;
    height: unset;
    margin-bottom: 12px;
    margin-left: 5px;
    margin-right: 5px
}

.el-table-filter__checkbox-group .el-checkbox:last-child {
    margin-bottom: 0
}

.el-table {
    --el-table-border-color: var(--el-border-color-lighter);
    --el-table-border: 1px solid var(--el-table-border-color);
    --el-table-text-color: var(--el-text-color-regular);
    --el-table-header-text-color: var(--el-text-color-secondary);
    --el-table-row-hover-bg-color: var(--el-fill-color-light);
    --el-table-current-row-bg-color: var(--el-color-primary-light-9);
    --el-table-header-bg-color: var(--el-bg-color);
    --el-table-fixed-box-shadow: var(--el-box-shadow-light);
    --el-table-bg-color: var(--el-fill-color-blank);
    --el-table-tr-bg-color: var(--el-bg-color);
    --el-table-expanded-cell-bg-color: var(--el-fill-color-blank);
    --el-table-fixed-left-column: inset 10px 0 10px -10px rgba(0, 0, 0, 0.15);
    --el-table-fixed-right-column: inset -10px 0 10px -10px rgba(0, 0, 0, 0.15);
    --el-table-index: var(--el-index-normal);
    background-color: var(--el-table-bg-color);
    box-sizing: border-box;
    color: var(--el-table-text-color);
    font-size: var(--el-font-size-base);
    height: -moz-fit-content;
    height: fit-content;
    max-width: 100%;
    overflow: hidden;
    position: relative;
    width: 100%
}

.el-table__inner-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative
}

.el-table__inner-wrapper:before {
    bottom: 0;
    height: 1px;
    left: 0
}

.el-table tbody:focus-visible {
    outline: none
}

.el-table.has-footer.el-table--fluid-height tr:last-child td.el-table__cell, .el-table.has-footer.el-table--scrollable-y tr:last-child td.el-table__cell {
    border-bottom-color: transparent
}

.el-table__empty-block {
    align-items: center;
    display: flex;
    justify-content: center;
    left: 0;
    min-height: 60px;
    position: sticky;
    text-align: center;
    width: 100%
}

.el-table__empty-text {
    color: var(--el-text-color-secondary);
    line-height: 60px;
    width: 50%
}

.el-table__expand-column .cell {
    padding: 0;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-table__expand-icon {
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: 12px;
    height: 20px;
    position: relative;
    transition: transform var(--el-transition-duration-fast) ease-in-out
}

.el-table__expand-icon--expanded {
    transform: rotate(90deg)
}

.el-table__expand-icon > .el-icon {
    font-size: 12px
}

.el-table__expanded-cell {
    background-color: var(--el-table-expanded-cell-bg-color)
}

.el-table__expanded-cell[class*=cell] {
    padding: 20px 50px
}

.el-table__expanded-cell:hover {
    background-color: transparent !important
}

.el-table__placeholder {
    display: inline-block;
    width: 20px
}

.el-table__append-wrapper {
    overflow: hidden
}

.el-table--fit {
    border-bottom: 0;
    border-right: 0
}

.el-table--fit .el-table__cell.gutter {
    border-right-width: 1px
}

.el-table--fit .el-table__inner-wrapper:before {
    width: 100%
}

.el-table thead {
    color: var(--el-table-header-text-color)
}

.el-table thead th {
    font-weight: 600
}

.el-table thead.is-group th.el-table__cell {
    background: var(--el-fill-color-light)
}

.el-table .el-table__cell {
    box-sizing: border-box;
    min-width: 0;
    padding: 8px 0;
    position: relative;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    z-index: var(--el-table-index)
}

.el-table .el-table__cell.is-center {
    text-align: center
}

.el-table .el-table__cell.is-right {
    text-align: right
}

.el-table .el-table__cell.gutter {
    border-bottom-width: 0;
    border-right-width: 0;
    padding: 0;
    width: 15px
}

.el-table .el-table__cell.is-hidden > * {
    visibility: hidden
}

.el-table .cell {
    box-sizing: border-box;
    line-height: 23px;
    overflow: hidden;
    overflow-wrap: break-word;
    padding: 0 12px;
    text-overflow: ellipsis;
    white-space: normal
}

.el-table .cell.el-tooltip {
    min-width: 50px;
    white-space: nowrap
}

.el-table--large {
    font-size: var(--el-font-size-base)
}

.el-table--large .el-table__cell {
    padding: 12px 0
}

.el-table--large .cell {
    padding: 0 16px
}

.el-table--default {
    font-size: var(--el-font-size-base)
}

.el-table--default .el-table__cell {
    padding: 8px 0
}

.el-table--default .cell {
    padding: 0 12px
}

.el-table--small {
    font-size: var(--el-font-size-extra-small)
}

.el-table--small .el-table__cell {
    padding: 4px 0
}

.el-table--small .cell {
    padding: 0 8px
}

.el-table tr {
    background-color: var(--el-table-tr-bg-color)
}

.el-table tr input[type=checkbox] {
    margin: 0
}

.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
    border-bottom: var(--el-table-border)
}

.el-table th.el-table__cell.is-sortable {
    cursor: pointer
}

.el-table th.el-table__cell {
    background-color: var(--el-table-header-bg-color)
}

.el-table th.el-table__cell > .cell.highlight {
    color: var(--el-color-primary)
}

.el-table th.el-table__cell.required > div:before {
    background: #ff4d51;
    border-radius: 50%;
    content: "";
    display: inline-block;
    height: 8px;
    margin-right: 5px;
    vertical-align: middle;
    width: 8px
}

.el-table td.el-table__cell div {
    box-sizing: border-box
}

.el-table td.el-table__cell.gutter {
    width: 0
}

.el-table--border .el-table__inner-wrapper:after, .el-table--border:after, .el-table--border:before, .el-table__inner-wrapper:before {
    background-color: var(--el-table-border-color);
    content: "";
    position: absolute;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table--border .el-table__inner-wrapper:after {
    height: 1px;
    left: 0;
    top: 0;
    width: 100%;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table--border:before {
    height: 100%;
    left: 0;
    top: -1px;
    width: 1px
}

.el-table--border:after {
    height: 100%;
    right: 0;
    top: -1px;
    width: 1px
}

.el-table--border .el-table__inner-wrapper {
    border-bottom: none;
    border-right: none
}

.el-table--border .el-table__footer-wrapper {
    flex-shrink: 0;
    position: relative
}

.el-table--border .el-table__cell {
    border-right: var(--el-table-border)
}

.el-table--border th.el-table__cell.gutter:last-of-type {
    border-bottom: var(--el-table-border);
    border-bottom-width: 1px
}

.el-table--border th.el-table__cell {
    border-bottom: var(--el-table-border)
}

.el-table--hidden {
    visibility: hidden
}

.el-table__body-wrapper, .el-table__footer-wrapper, .el-table__header-wrapper {
    width: 100%
}

.el-table__body-wrapper tr td.el-table-fixed-column--left, .el-table__body-wrapper tr td.el-table-fixed-column--right, .el-table__body-wrapper tr th.el-table-fixed-column--left, .el-table__body-wrapper tr th.el-table-fixed-column--right, .el-table__footer-wrapper tr td.el-table-fixed-column--left, .el-table__footer-wrapper tr td.el-table-fixed-column--right, .el-table__footer-wrapper tr th.el-table-fixed-column--left, .el-table__footer-wrapper tr th.el-table-fixed-column--right, .el-table__header-wrapper tr td.el-table-fixed-column--left, .el-table__header-wrapper tr td.el-table-fixed-column--right, .el-table__header-wrapper tr th.el-table-fixed-column--left, .el-table__header-wrapper tr th.el-table-fixed-column--right {
    background: inherit;
    position: sticky !important;
    z-index: calc(var(--el-table-index) + 1)
}

.el-table__body-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__body-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__body-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__body-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--right.is-first-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--right.is-last-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--right.is-first-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--right.is-last-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--right.is-first-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--right.is-last-column:before {
    bottom: -1px;
    box-shadow: none;
    content: "";
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
    position: absolute;
    top: 0;
    touch-action: none;
    width: 10px
}

.el-table__body-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__body-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--right.is-first-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--right.is-first-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--left.is-first-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--right.is-first-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--left.is-first-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--right.is-first-column:before {
    left: -10px
}

.el-table__body-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__body-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__body-wrapper tr th.el-table-fixed-column--right.is-last-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__footer-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__footer-wrapper tr th.el-table-fixed-column--right.is-last-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--left.is-last-column:before, .el-table__header-wrapper tr td.el-table-fixed-column--right.is-last-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--left.is-last-column:before, .el-table__header-wrapper tr th.el-table-fixed-column--right.is-last-column:before {
    right: -10px
}

.el-table__body-wrapper tr td.el-table__fixed-right-patch, .el-table__body-wrapper tr th.el-table__fixed-right-patch, .el-table__footer-wrapper tr td.el-table__fixed-right-patch, .el-table__footer-wrapper tr th.el-table__fixed-right-patch, .el-table__header-wrapper tr td.el-table__fixed-right-patch, .el-table__header-wrapper tr th.el-table__fixed-right-patch {
    background: #fff;
    position: sticky !important;
    right: 0;
    z-index: calc(var(--el-table-index) + 1)
}

.el-table__header-wrapper {
    flex-shrink: 0
}

.el-table__header-wrapper tr th.el-table-fixed-column--left, .el-table__header-wrapper tr th.el-table-fixed-column--right {
    background-color: var(--el-table-header-bg-color)
}

.el-table__body, .el-table__footer, .el-table__header {
    border-collapse: separate;
    table-layout: fixed
}

.el-table__header-wrapper {
    overflow: hidden
}

.el-table__header-wrapper tbody td.el-table__cell {
    background-color: var(--el-table-row-hover-bg-color);
    color: var(--el-table-text-color)
}

.el-table__footer-wrapper {
    flex-shrink: 0;
    overflow: hidden
}

.el-table__footer-wrapper tfoot td.el-table__cell {
    background-color: var(--el-table-row-hover-bg-color);
    color: var(--el-table-text-color)
}

.el-table__body-wrapper .el-table-column--selection > .cell, .el-table__header-wrapper .el-table-column--selection > .cell {
    align-items: center;
    display: inline-flex;
    height: 23px
}

.el-table__body-wrapper .el-table-column--selection .el-checkbox, .el-table__header-wrapper .el-table-column--selection .el-checkbox {
    height: unset
}

.el-table.is-scrolling-left .el-table-fixed-column--right.is-first-column:before {
    box-shadow: var(--el-table-fixed-right-column)
}

.el-table.is-scrolling-left.el-table--border .el-table-fixed-column--left.is-last-column.el-table__cell {
    border-right: var(--el-table-border)
}

.el-table.is-scrolling-left th.el-table-fixed-column--left {
    background-color: var(--el-table-header-bg-color)
}

.el-table.is-scrolling-right .el-table-fixed-column--left.is-last-column:before {
    box-shadow: var(--el-table-fixed-left-column)
}

.el-table.is-scrolling-right .el-table-fixed-column--left.is-last-column.el-table__cell {
    border-right: none
}

.el-table.is-scrolling-right th.el-table-fixed-column--right {
    background-color: var(--el-table-header-bg-color)
}

.el-table.is-scrolling-middle .el-table-fixed-column--left.is-last-column.el-table__cell {
    border-right: none
}

.el-table.is-scrolling-middle .el-table-fixed-column--right.is-first-column:before {
    box-shadow: var(--el-table-fixed-right-column)
}

.el-table.is-scrolling-middle .el-table-fixed-column--left.is-last-column:before {
    box-shadow: var(--el-table-fixed-left-column)
}

.el-table.is-scrolling-none .el-table-fixed-column--left.is-first-column:before, .el-table.is-scrolling-none .el-table-fixed-column--left.is-last-column:before, .el-table.is-scrolling-none .el-table-fixed-column--right.is-first-column:before, .el-table.is-scrolling-none .el-table-fixed-column--right.is-last-column:before {
    box-shadow: none
}

.el-table.is-scrolling-none th.el-table-fixed-column--left, .el-table.is-scrolling-none th.el-table-fixed-column--right {
    background-color: var(--el-table-header-bg-color)
}

.el-table__body-wrapper {
    flex: 1;
    overflow: hidden;
    position: relative
}

.el-table__body-wrapper .el-scrollbar__bar {
    z-index: calc(var(--el-table-index) + 2)
}

.el-table .caret-wrapper {
    align-items: center;
    cursor: pointer;
    display: inline-flex;
    flex-direction: column;
    height: 14px;
    overflow: initial;
    position: relative;
    vertical-align: middle;
    width: 24px
}

.el-table .sort-caret {
    border: 5px solid transparent;
    height: 0;
    left: 7px;
    position: absolute;
    width: 0
}

.el-table .sort-caret.ascending {
    border-bottom-color: var(--el-text-color-placeholder);
    top: -5px
}

.el-table .sort-caret.descending {
    border-top-color: var(--el-text-color-placeholder);
    bottom: -3px
}

.el-table .ascending .sort-caret.ascending {
    border-bottom-color: var(--el-color-primary)
}

.el-table .descending .sort-caret.descending {
    border-top-color: var(--el-color-primary)
}

.el-table .hidden-columns {
    position: absolute;
    visibility: hidden;
    z-index: -1
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: var(--el-fill-color-lighter)
}

.el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
    background-color: var(--el-table-current-row-bg-color)
}

.el-table__body tr.hover-row.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell, .el-table__body tr.hover-row > td.el-table__cell, .el-table__body tr > td.hover-cell {
    background-color: var(--el-table-row-hover-bg-color)
}

.el-table__body tr.current-row > td.el-table__cell {
    background-color: var(--el-table-current-row-bg-color)
}

.el-table.el-table--scrollable-y .el-table__body-header {
    position: sticky;
    top: 0;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table.el-table--scrollable-y .el-table__body-footer {
    bottom: 0;
    position: sticky;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table__column-resize-proxy {
    border-left: var(--el-table-border);
    bottom: 0;
    left: 200px;
    position: absolute;
    top: 0;
    width: 0;
    z-index: calc(var(--el-table-index) + 9)
}

.el-table__column-filter-trigger {
    cursor: pointer;
    display: inline-block
}

.el-table__column-filter-trigger i {
    color: var(--el-color-info);
    font-size: 14px;
    vertical-align: middle
}

.el-table__border-left-patch {
    height: 100%;
    top: 0;
    width: 1px
}

.el-table__border-bottom-patch, .el-table__border-left-patch {
    background-color: var(--el-table-border-color);
    left: 0;
    position: absolute;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table__border-bottom-patch {
    height: 1px
}

.el-table__border-right-patch {
    background-color: var(--el-table-border-color);
    height: 100%;
    position: absolute;
    top: 0;
    width: 1px;
    z-index: calc(var(--el-table-index) + 2)
}

.el-table--enable-row-transition .el-table__body td.el-table__cell {
    transition: background-color .25s ease
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: var(--el-table-row-hover-bg-color)
}

.el-table [class*=el-table__row--level] .el-table__expand-icon {
    display: inline-block;
    height: 12px;
    line-height: 12px;
    margin-right: 8px;
    text-align: center;
    width: 12px
}

.el-table .el-table.el-table--border .el-table__cell {
    border-right: var(--el-table-border)
}

.el-table:not(.el-table--border) .el-table__cell {
    border-right: none
}

.el-table:not(.el-table--border) > .el-table__inner-wrapper:after {
    content: none
}

.el-table-v2 {
    --el-table-border-color: var(--el-border-color-lighter);
    --el-table-border: 1px solid var(--el-table-border-color);
    --el-table-text-color: var(--el-text-color-regular);
    --el-table-header-text-color: var(--el-text-color-secondary);
    --el-table-row-hover-bg-color: var(--el-fill-color-light);
    --el-table-current-row-bg-color: var(--el-color-primary-light-9);
    --el-table-header-bg-color: var(--el-bg-color);
    --el-table-fixed-box-shadow: var(--el-box-shadow-light);
    --el-table-bg-color: var(--el-fill-color-blank);
    --el-table-tr-bg-color: var(--el-bg-color);
    --el-table-expanded-cell-bg-color: var(--el-fill-color-blank);
    --el-table-fixed-left-column: inset 10px 0 10px -10px rgba(0, 0, 0, 0.15);
    --el-table-fixed-right-column: inset -10px 0 10px -10px rgba(0, 0, 0, 0.15);
    --el-table-index: var(--el-index-normal);
    font-size: var(--el-font-size-base)
}

.el-table-v2 * {
    box-sizing: border-box
}

.el-table-v2__root {
    position: relative
}

.el-table-v2__root:hover .el-table-v2__main .el-virtual-scrollbar {
    opacity: 1
}

.el-table-v2__main {
    background-color: var(--el-bg-color);
    display: flex;
    flex-direction: column-reverse;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0
}

.el-table-v2__main .el-vl__horizontal, .el-table-v2__main .el-vl__vertical {
    z-index: 2
}

.el-table-v2__left {
    background-color: var(--el-bg-color);
    box-shadow: 2px 0 4px 0 rgba(0, 0, 0, .06);
    display: flex;
    flex-direction: column-reverse;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0
}

.el-table-v2__left .el-virtual-scrollbar {
    opacity: 0
}

.el-table-v2__left .el-vl__horizontal, .el-table-v2__left .el-vl__vertical {
    z-index: -1
}

.el-table-v2__right {
    background-color: var(--el-bg-color);
    box-shadow: -2px 0 4px 0 rgba(0, 0, 0, .06);
    display: flex;
    flex-direction: column-reverse;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0
}

.el-table-v2__right .el-virtual-scrollbar {
    opacity: 0
}

.el-table-v2__right .el-vl__horizontal, .el-table-v2__right .el-vl__vertical {
    z-index: -1
}

.el-table-v2__header-row, .el-table-v2__row {
    padding-inline-end: var(--el-table-scrollbar-size)
}

.el-table-v2__header-wrapper {
    overflow: hidden
}

.el-table-v2__header {
    overflow: hidden;
    position: relative
}

.el-table-v2__header .el-checkbox {
    z-index: 0
}

.el-table-v2__footer {
    bottom: 0;
    overflow: hidden;
    right: 0
}

.el-table-v2__empty, .el-table-v2__footer, .el-table-v2__overlay {
    left: 0;
    position: absolute
}

.el-table-v2__overlay {
    bottom: 0;
    right: 0;
    top: 0;
    z-index: 9999
}

.el-table-v2__header-row {
    border-bottom: var(--el-table-border);
    display: flex
}

.el-table-v2__header-cell {
    align-items: center;
    background-color: var(--el-table-header-bg-color);
    color: var(--el-table-header-text-color);
    display: flex;
    font-weight: bold;
    height: 100%;
    overflow: hidden;
    padding: 0 8px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-table-v2__header-cell.is-align-center {
    justify-content: center;
    text-align: center
}

.el-table-v2__header-cell.is-align-right {
    justify-content: flex-end;
    text-align: right
}

.el-table-v2__header-cell.is-sortable {
    cursor: pointer
}

.el-table-v2__header-cell:hover .el-icon {
    display: block
}

.el-table-v2__sort-icon {
    display: none;
    opacity: .6;
    transition: opacity, display var(--el-transition-duration)
}

.el-table-v2__sort-icon.is-sorting {
    display: block;
    opacity: 1
}

.el-table-v2__row {
    align-items: center;
    border-bottom: var(--el-table-border);
    display: flex;
    transition: background-color var(--el-transition-duration)
}

.el-table-v2__row.is-hovered, .el-table-v2__row:hover {
    background-color: var(--el-table-row-hover-bg-color)
}

.el-table-v2__row-cell {
    align-items: center;
    display: flex;
    height: 100%;
    overflow: hidden;
    padding: 0 8px
}

.el-table-v2__row-cell.is-align-center {
    justify-content: center;
    text-align: center
}

.el-table-v2__row-cell.is-align-right {
    justify-content: flex-end;
    text-align: right
}

.el-table-v2__expand-icon {
    cursor: pointer;
    margin: 0 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-table-v2__expand-icon svg {
    transition: transform var(--el-transition-duration)
}

.el-table-v2__expand-icon.is-expanded svg {
    transform: rotate(90deg)
}

.el-table-v2:not(.is-dynamic) .el-table-v2__cell-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-table-v2.is-dynamic .el-table-v2__row {
    align-items: stretch;
    overflow: hidden
}

.el-table-v2.is-dynamic .el-table-v2__row .el-table-v2__row-cell {
    overflow-wrap: break-word
}

.el-tabs {
    --el-tabs-header-height: 40px;
    display: flex
}

.el-tabs__header {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin: 0 0 15px;
    padding: 0;
    position: relative
}

.el-tabs__header-vertical {
    flex-direction: column
}

.el-tabs__active-bar {
    background-color: var(--el-color-primary);
    bottom: 0;
    height: 2px;
    left: 0;
    list-style: none;
    position: absolute;
    transition: width var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier), transform var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier);
    z-index: 1
}

.el-tabs__new-tab {
    align-items: center;
    border: 1px solid var(--el-border-color);
    border-radius: 3px;
    color: var(--el-text-color-primary);
    cursor: pointer;
    display: flex;
    flex-shrink: 0;
    font-size: 12px;
    height: 20px;
    justify-content: center;
    line-height: 20px;
    margin: 10px 0 10px 10px;
    text-align: center;
    transition: all .15s;
    width: 20px
}

.el-tabs__new-tab .is-icon-plus {
    height: inherit;
    transform: scale(.8);
    width: inherit
}

.el-tabs__new-tab .is-icon-plus svg {
    vertical-align: middle
}

.el-tabs__new-tab:hover {
    color: var(--el-color-primary)
}

.el-tabs__new-tab-vertical {
    margin-left: 0
}

.el-tabs__nav-wrap {
    flex: 1 auto;
    margin-bottom: -1px;
    overflow: hidden;
    position: relative
}

.el-tabs__nav-wrap:after {
    background-color: var(--el-border-color-light);
    bottom: 0;
    content: "";
    height: 2px;
    left: 0;
    position: absolute;
    width: 100%;
    z-index: var(--el-index-normal)
}

.el-tabs__nav-wrap.is-scrollable {
    box-sizing: border-box;
    padding: 0 20px
}

.el-tabs__nav-scroll {
    overflow: hidden
}

.el-tabs__nav-next, .el-tabs__nav-prev {
    color: var(--el-text-color-secondary);
    cursor: pointer;
    font-size: 12px;
    line-height: 44px;
    position: absolute;
    text-align: center;
    width: 20px
}

.el-tabs__nav-next {
    right: 0
}

.el-tabs__nav-prev {
    left: 0
}

.el-tabs__nav {
    display: flex;
    float: left;
    position: relative;
    transition: transform var(--el-transition-duration);
    white-space: nowrap;
    z-index: calc(var(--el-index-normal) + 1)
}

.el-tabs__nav.is-stretch {
    display: flex;
    min-width: 100%
}

.el-tabs__nav.is-stretch > * {
    flex: 1;
    text-align: center
}

.el-tabs__item {
    align-items: center;
    box-sizing: border-box;
    color: var(--el-text-color-primary);
    display: flex;
    font-size: var(--el-font-size-base);
    font-weight: 500;
    height: var(--el-tabs-header-height);
    justify-content: center;
    list-style: none;
    padding: 0 20px;
    position: relative
}

.el-tabs__item:focus, .el-tabs__item:focus:active {
    outline: none
}

.el-tabs__item:focus-visible {
    border-radius: 3px;
    box-shadow: 0 0 2px 2px var(--el-color-primary) inset
}

.el-tabs__item .is-icon-close {
    border-radius: 50%;
    margin-left: 5px;
    text-align: center;
    transition: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier)
}

.el-tabs__item .is-icon-close:before {
    display: inline-block;
    transform: scale(.9)
}

.el-tabs__item .is-icon-close:hover {
    background-color: var(--el-text-color-placeholder);
    color: #ffffff
}

.el-tabs__item.is-active, .el-tabs__item:hover {
    color: var(--el-color-primary)
}

.el-tabs__item:hover {
    cursor: pointer
}

.el-tabs__item.is-disabled {
    color: var(--el-disabled-text-color);
    cursor: not-allowed
}

.el-tabs__content {
    flex-grow: 1;
    overflow: hidden;
    position: relative
}

.el-tabs--bottom > .el-tabs__header .el-tabs__item:nth-child(2), .el-tabs--top > .el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 0
}

.el-tabs--bottom > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top > .el-tabs__header .el-tabs__item:last-child {
    padding-right: 0
}

.el-tabs--bottom.el-tabs--border-card > .el-tabs__header .el-tabs__item:nth-child(2), .el-tabs--bottom.el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(2), .el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:nth-child(2), .el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:nth-child(2) {
    padding-left: 20px
}

.el-tabs--bottom.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--bottom.el-tabs--card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:last-child {
    padding-right: 20px
}

.el-tabs--card > .el-tabs__header {
    border-bottom: 1px solid var(--el-border-color-light);
    height: var(--el-tabs-header-height)
}

.el-tabs--card > .el-tabs__header .el-tabs__nav-wrap:after {
    content: none
}

.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 1px solid var(--el-border-color-light);
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    box-sizing: border-box
}

.el-tabs--card > .el-tabs__header .el-tabs__active-bar {
    display: none
}

.el-tabs--card > .el-tabs__header .el-tabs__item .is-icon-close {
    font-size: 12px;
    height: 14px;
    overflow: hidden;
    position: relative;
    right: -2px;
    transform-origin: 100% 50%;
    width: 0
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
    border-bottom: 1px solid transparent;
    border-left: 1px solid var(--el-border-color-light);
    transition: color var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier), padding var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier)
}

.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
    border-left: none
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable:hover {
    padding-left: 13px;
    padding-right: 13px
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable:hover .is-icon-close {
    width: 14px
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom-color: var(--el-bg-color)
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active.is-closable {
    padding-left: 20px;
    padding-right: 20px
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active.is-closable .is-icon-close {
    width: 14px
}

.el-tabs--border-card {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color)
}

.el-tabs--border-card > .el-tabs__content {
    padding: 15px
}

.el-tabs--border-card > .el-tabs__header {
    background-color: var(--el-fill-color-light);
    border-bottom: 1px solid var(--el-border-color-light);
    margin: 0
}

.el-tabs--border-card > .el-tabs__header .el-tabs__nav-wrap:after {
    content: none
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item {
    border: 1px solid transparent;
    color: var(--el-text-color-secondary);
    margin-top: -1px;
    transition: all var(--el-transition-duration) var(--el-transition-function-ease-in-out-bezier)
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item + .el-tabs__item, .el-tabs--border-card > .el-tabs__header .el-tabs__item:first-child {
    margin-left: -1px
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    background-color: var(--el-bg-color-overlay);
    border-left-color: var(--el-border-color);
    border-right-color: var(--el-border-color);
    color: var(--el-color-primary)
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
    color: var(--el-color-primary)
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-disabled {
    color: var(--el-disabled-text-color)
}

.el-tabs--border-card > .el-tabs__header .is-scrollable .el-tabs__item:first-child {
    margin-left: 0
}

.el-tabs--bottom {
    flex-direction: column
}

.el-tabs--bottom .el-tabs__header.is-bottom {
    margin-bottom: 0;
    margin-top: 10px
}

.el-tabs--bottom.el-tabs--border-card .el-tabs__header.is-bottom {
    border-bottom: 0;
    border-top: 1px solid var(--el-border-color)
}

.el-tabs--bottom.el-tabs--border-card .el-tabs__nav-wrap.is-bottom {
    margin-bottom: 0;
    margin-top: -1px
}

.el-tabs--bottom.el-tabs--border-card .el-tabs__item.is-bottom:not(.is-active) {
    border: 1px solid transparent
}

.el-tabs--bottom.el-tabs--border-card .el-tabs__item.is-bottom {
    margin: 0 -1px -1px
}

.el-tabs--left, .el-tabs--right {
    overflow: hidden
}

.el-tabs--left .el-tabs__header.is-left, .el-tabs--left .el-tabs__header.is-right, .el-tabs--left .el-tabs__nav-scroll, .el-tabs--left .el-tabs__nav-wrap.is-left, .el-tabs--left .el-tabs__nav-wrap.is-right, .el-tabs--right .el-tabs__header.is-left, .el-tabs--right .el-tabs__header.is-right, .el-tabs--right .el-tabs__nav-scroll, .el-tabs--right .el-tabs__nav-wrap.is-left, .el-tabs--right .el-tabs__nav-wrap.is-right {
    height: 100%
}

.el-tabs--left .el-tabs__active-bar.is-left, .el-tabs--left .el-tabs__active-bar.is-right, .el-tabs--right .el-tabs__active-bar.is-left, .el-tabs--right .el-tabs__active-bar.is-right {
    bottom: auto;
    height: auto;
    top: 0;
    width: 2px
}

.el-tabs--left .el-tabs__nav-wrap.is-left, .el-tabs--left .el-tabs__nav-wrap.is-right, .el-tabs--right .el-tabs__nav-wrap.is-left, .el-tabs--right .el-tabs__nav-wrap.is-right {
    margin-bottom: 0
}

.el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-next, .el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-next, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-next, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-next, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev {
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    text-align: center;
    width: 100%
}

.el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-next i, .el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev i, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-next i, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev i, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-next i, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev i, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-next i, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev i {
    transform: rotate(90deg)
}

.el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-prev, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-prev {
    left: auto;
    top: 0
}

.el-tabs--left .el-tabs__nav-wrap.is-left > .el-tabs__nav-next, .el-tabs--left .el-tabs__nav-wrap.is-right > .el-tabs__nav-next, .el-tabs--right .el-tabs__nav-wrap.is-left > .el-tabs__nav-next, .el-tabs--right .el-tabs__nav-wrap.is-right > .el-tabs__nav-next {
    bottom: 0;
    right: auto
}

.el-tabs--left .el-tabs__nav-wrap.is-left.is-scrollable, .el-tabs--left .el-tabs__nav-wrap.is-right.is-scrollable, .el-tabs--right .el-tabs__nav-wrap.is-left.is-scrollable, .el-tabs--right .el-tabs__nav-wrap.is-right.is-scrollable {
    padding: 30px 0
}

.el-tabs--left .el-tabs__nav-wrap.is-left:after, .el-tabs--left .el-tabs__nav-wrap.is-right:after, .el-tabs--right .el-tabs__nav-wrap.is-left:after, .el-tabs--right .el-tabs__nav-wrap.is-right:after {
    bottom: auto;
    height: 100%;
    top: 0;
    width: 2px
}

.el-tabs--left .el-tabs__nav.is-left, .el-tabs--left .el-tabs__nav.is-right, .el-tabs--right .el-tabs__nav.is-left, .el-tabs--right .el-tabs__nav.is-right {
    flex-direction: column
}

.el-tabs--left .el-tabs__item.is-left, .el-tabs--right .el-tabs__item.is-left {
    justify-content: flex-end
}

.el-tabs--left .el-tabs__item.is-right, .el-tabs--right .el-tabs__item.is-right {
    justify-content: flex-start
}

.el-tabs--left {
    flex-direction: row-reverse
}

.el-tabs--left .el-tabs__header.is-left {
    margin-bottom: 0;
    margin-right: 10px
}

.el-tabs--left .el-tabs__nav-wrap.is-left {
    margin-right: -1px
}

.el-tabs--left .el-tabs__active-bar.is-left, .el-tabs--left .el-tabs__nav-wrap.is-left:after {
    left: auto;
    right: 0
}

.el-tabs--left .el-tabs__item.is-left {
    text-align: right
}

.el-tabs--left.el-tabs--card .el-tabs__active-bar.is-left {
    display: none
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
    border-bottom: none;
    border-left: none;
    border-right: 1px solid var(--el-border-color-light);
    border-top: 1px solid var(--el-border-color-light);
    text-align: left
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left:first-child {
    border-right: 1px solid var(--el-border-color-light);
    border-top: none
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active {
    border: 1px solid var(--el-border-color-light);
    border-bottom: none;
    border-left: none;
    border-right: 1px solid #fff
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active:first-child {
    border-top: none
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active:last-child {
    border-bottom: none
}

.el-tabs--left.el-tabs--card .el-tabs__nav {
    border-bottom: 1px solid var(--el-border-color-light);
    border-radius: 4px 0 0 4px;
    border-right: none
}

.el-tabs--left.el-tabs--card .el-tabs__new-tab {
    float: none
}

.el-tabs--left.el-tabs--border-card .el-tabs__header.is-left {
    border-right: 1px solid var(--el-border-color)
}

.el-tabs--left.el-tabs--border-card .el-tabs__item.is-left {
    border: 1px solid transparent;
    margin: -1px 0 -1px -1px
}

.el-tabs--left.el-tabs--border-card .el-tabs__item.is-left.is-active {
    border-color: rgb(209, 219, 229) transparent
}

.el-tabs--right .el-tabs__header.is-right {
    margin-bottom: 0;
    margin-left: 10px
}

.el-tabs--right .el-tabs__nav-wrap.is-right {
    margin-left: -1px
}

.el-tabs--right .el-tabs__nav-wrap.is-right:after {
    left: 0;
    right: auto
}

.el-tabs--right .el-tabs__active-bar.is-right {
    left: 0
}

.el-tabs--right.el-tabs--card .el-tabs__active-bar.is-right {
    display: none
}

.el-tabs--right.el-tabs--card .el-tabs__item.is-right {
    border-bottom: none;
    border-top: 1px solid var(--el-border-color-light)
}

.el-tabs--right.el-tabs--card .el-tabs__item.is-right:first-child {
    border-left: 1px solid var(--el-border-color-light);
    border-top: none
}

.el-tabs--right.el-tabs--card .el-tabs__item.is-right.is-active {
    border: 1px solid var(--el-border-color-light);
    border-bottom: none;
    border-left: 1px solid #fff;
    border-right: none
}

.el-tabs--right.el-tabs--card .el-tabs__item.is-right.is-active:first-child {
    border-top: none
}

.el-tabs--right.el-tabs--card .el-tabs__item.is-right.is-active:last-child {
    border-bottom: none
}

.el-tabs--right.el-tabs--card .el-tabs__nav {
    border-bottom: 1px solid var(--el-border-color-light);
    border-left: none;
    border-radius: 0 4px 4px 0
}

.el-tabs--right.el-tabs--border-card .el-tabs__header.is-right {
    border-left: 1px solid var(--el-border-color)
}

.el-tabs--right.el-tabs--border-card .el-tabs__item.is-right {
    border: 1px solid transparent;
    margin: -1px -1px -1px 0
}

.el-tabs--right.el-tabs--border-card .el-tabs__item.is-right.is-active {
    border-color: rgb(209, 219, 229) transparent
}

.el-tabs--top {
    flex-direction: column-reverse
}

.slideInLeft-transition, .slideInRight-transition {
    display: inline-block
}

.slideInRight-enter {
    animation: slideInRight-enter var(--el-transition-duration)
}

.slideInRight-leave {
    animation: slideInRight-leave var(--el-transition-duration);
    left: 0;
    position: absolute;
    right: 0
}

.slideInLeft-enter {
    animation: slideInLeft-enter var(--el-transition-duration)
}

.slideInLeft-leave {
    animation: slideInLeft-leave var(--el-transition-duration);
    left: 0;
    position: absolute;
    right: 0
}

@keyframes slideInRight-enter {
    0% {
        opacity: 0;
        transform: translateX(100%);
        transform-origin: 0 0
    }
    to {
        opacity: 1;
        transform: translateX(0);
        transform-origin: 0 0
    }
}

@keyframes slideInRight-leave {
    0% {
        opacity: 1;
        transform: translateX(0);
        transform-origin: 0 0
    }
    to {
        opacity: 0;
        transform: translateX(100%);
        transform-origin: 0 0
    }
}

@keyframes slideInLeft-enter {
    0% {
        opacity: 0;
        transform: translateX(-100%);
        transform-origin: 0 0
    }
    to {
        opacity: 1;
        transform: translateX(0);
        transform-origin: 0 0
    }
}

@keyframes slideInLeft-leave {
    0% {
        opacity: 1;
        transform: translateX(0);
        transform-origin: 0 0
    }
    to {
        opacity: 0;
        transform: translateX(-100%);
        transform-origin: 0 0
    }
}

.el-tag {
    --el-tag-font-size: 12px;
    --el-tag-border-radius: 4px;
    --el-tag-border-radius-rounded: 9999px;
    align-items: center;
    background-color: var(--el-tag-bg-color);
    border-color: var(--el-tag-border-color);
    border-radius: var(--el-tag-border-radius);
    border-style: solid;
    border-width: 1px;
    box-sizing: border-box;
    color: var(--el-tag-text-color);
    display: inline-flex;
    font-size: var(--el-tag-font-size);
    height: 24px;
    justify-content: center;
    line-height: 1;
    padding: 0 9px;
    vertical-align: middle;
    white-space: nowrap;
    --el-icon-size: 14px
}

.el-tag, .el-tag.el-tag--primary {
    --el-tag-bg-color: var(--el-color-primary-light-9);
    --el-tag-border-color: var(--el-color-primary-light-8);
    --el-tag-hover-color: var(--el-color-primary)
}

.el-tag.el-tag--success {
    --el-tag-bg-color: var(--el-color-success-light-9);
    --el-tag-border-color: var(--el-color-success-light-8);
    --el-tag-hover-color: var(--el-color-success)
}

.el-tag.el-tag--warning {
    --el-tag-bg-color: var(--el-color-warning-light-9);
    --el-tag-border-color: var(--el-color-warning-light-8);
    --el-tag-hover-color: var(--el-color-warning)
}

.el-tag.el-tag--danger {
    --el-tag-bg-color: var(--el-color-danger-light-9);
    --el-tag-border-color: var(--el-color-danger-light-8);
    --el-tag-hover-color: var(--el-color-danger)
}

.el-tag.el-tag--error {
    --el-tag-bg-color: var(--el-color-error-light-9);
    --el-tag-border-color: var(--el-color-error-light-8);
    --el-tag-hover-color: var(--el-color-error)
}

.el-tag.el-tag--info {
    --el-tag-bg-color: var(--el-color-info-light-9);
    --el-tag-border-color: var(--el-color-info-light-8);
    --el-tag-hover-color: var(--el-color-info)
}

.el-tag.is-hit {
    border-color: var(--el-color-primary)
}

.el-tag.is-round {
    border-radius: var(--el-tag-border-radius-rounded)
}

.el-tag .el-tag__close {
    color: var(--el-tag-text-color);
    flex-shrink: 0
}

.el-tag .el-tag__close:hover {
    background-color: var(--el-tag-hover-color);
    color: var(--el-color-white)
}

.el-tag.el-tag--primary {
    --el-tag-text-color: var(--el-color-primary)
}

.el-tag.el-tag--success {
    --el-tag-text-color: var(--el-color-success)
}

.el-tag.el-tag--warning {
    --el-tag-text-color: var(--el-color-warning)
}

.el-tag.el-tag--danger {
    --el-tag-text-color: var(--el-color-danger)
}

.el-tag.el-tag--error {
    --el-tag-text-color: var(--el-color-error)
}

.el-tag.el-tag--info {
    --el-tag-text-color: var(--el-color-info)
}

.el-tag .el-icon {
    border-radius: 50%;
    cursor: pointer;
    font-size: calc(var(--el-icon-size) - 2px);
    height: var(--el-icon-size);
    width: var(--el-icon-size)
}

.el-tag .el-tag__close {
    margin-left: 6px
}

.el-tag--dark {
    --el-tag-text-color: var(--el-color-white)
}

.el-tag--dark, .el-tag--dark.el-tag--primary {
    --el-tag-bg-color: var(--el-color-primary);
    --el-tag-border-color: var(--el-color-primary);
    --el-tag-hover-color: var(--el-color-primary-light-3)
}

.el-tag--dark.el-tag--success {
    --el-tag-bg-color: var(--el-color-success);
    --el-tag-border-color: var(--el-color-success);
    --el-tag-hover-color: var(--el-color-success-light-3)
}

.el-tag--dark.el-tag--warning {
    --el-tag-bg-color: var(--el-color-warning);
    --el-tag-border-color: var(--el-color-warning);
    --el-tag-hover-color: var(--el-color-warning-light-3)
}

.el-tag--dark.el-tag--danger {
    --el-tag-bg-color: var(--el-color-danger);
    --el-tag-border-color: var(--el-color-danger);
    --el-tag-hover-color: var(--el-color-danger-light-3)
}

.el-tag--dark.el-tag--error {
    --el-tag-bg-color: var(--el-color-error);
    --el-tag-border-color: var(--el-color-error);
    --el-tag-hover-color: var(--el-color-error-light-3)
}

.el-tag--dark.el-tag--info {
    --el-tag-bg-color: var(--el-color-info);
    --el-tag-border-color: var(--el-color-info);
    --el-tag-hover-color: var(--el-color-info-light-3)
}

.el-tag--dark.el-tag--danger, .el-tag--dark.el-tag--error, .el-tag--dark.el-tag--info, .el-tag--dark.el-tag--primary, .el-tag--dark.el-tag--success, .el-tag--dark.el-tag--warning {
    --el-tag-text-color: var(--el-color-white)
}

.el-tag--plain, .el-tag--plain.el-tag--primary {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-primary-light-5);
    --el-tag-hover-color: var(--el-color-primary)
}

.el-tag--plain.el-tag--success {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-success-light-5);
    --el-tag-hover-color: var(--el-color-success)
}

.el-tag--plain.el-tag--warning {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-warning-light-5);
    --el-tag-hover-color: var(--el-color-warning)
}

.el-tag--plain.el-tag--danger {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-danger-light-5);
    --el-tag-hover-color: var(--el-color-danger)
}

.el-tag--plain.el-tag--error {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-error-light-5);
    --el-tag-hover-color: var(--el-color-error)
}

.el-tag--plain.el-tag--info {
    --el-tag-bg-color: var(--el-fill-color-blank);
    --el-tag-border-color: var(--el-color-info-light-5);
    --el-tag-hover-color: var(--el-color-info)
}

.el-tag.is-closable {
    padding-right: 5px
}

.el-tag--large {
    height: 32px;
    padding: 0 11px;
    --el-icon-size: 16px
}

.el-tag--large .el-tag__close {
    margin-left: 8px
}

.el-tag--large.is-closable {
    padding-right: 7px
}

.el-tag--small {
    height: 20px;
    padding: 0 7px;
    --el-icon-size: 12px
}

.el-tag--small .el-tag__close {
    margin-left: 4px
}

.el-tag--small.is-closable {
    padding-right: 3px
}

.el-tag--small .el-icon-close {
    transform: scale(.8)
}

.el-tag.el-tag--primary.is-hit {
    border-color: var(--el-color-primary)
}

.el-tag.el-tag--success.is-hit {
    border-color: var(--el-color-success)
}

.el-tag.el-tag--warning.is-hit {
    border-color: var(--el-color-warning)
}

.el-tag.el-tag--danger.is-hit {
    border-color: var(--el-color-danger)
}

.el-tag.el-tag--error.is-hit {
    border-color: var(--el-color-error)
}

.el-tag.el-tag--info.is-hit {
    border-color: var(--el-color-info)
}

.el-text {
    --el-text-font-size: var(--el-font-size-base);
    --el-text-color: var(--el-text-color-regular);
    align-self: center;
    color: var(--el-text-color);
    font-size: var(--el-text-font-size);
    margin: 0;
    overflow-wrap: break-word;
    padding: 0
}

.el-text.is-truncated {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-text.is-line-clamp {
    display: -webkit-inline-box;
    -webkit-box-orient: vertical;
    overflow: hidden
}

.el-text--large {
    --el-text-font-size: var(--el-font-size-medium)
}

.el-text--default {
    --el-text-font-size: var(--el-font-size-base)
}

.el-text--small {
    --el-text-font-size: var(--el-font-size-extra-small)
}

.el-text.el-text--primary {
    --el-text-color: var(--el-color-primary)
}

.el-text.el-text--success {
    --el-text-color: var(--el-color-success)
}

.el-text.el-text--warning {
    --el-text-color: var(--el-color-warning)
}

.el-text.el-text--danger {
    --el-text-color: var(--el-color-danger)
}

.el-text.el-text--error {
    --el-text-color: var(--el-color-error)
}

.el-text.el-text--info {
    --el-text-color: var(--el-color-info)
}

.el-text > .el-icon {
    vertical-align: -2px
}

.time-select {
    margin: 5px 0;
    min-width: 0
}

.time-select .el-picker-panel__content {
    margin: 0;
    max-height: 200px
}

.time-select-item {
    font-size: 14px;
    line-height: 20px;
    padding: 8px 10px
}

.time-select-item.disabled {
    color: var(--el-datepicker-border-color);
    cursor: not-allowed
}

.time-select-item:hover {
    background-color: var(--el-fill-color-light);
    cursor: pointer;
    font-weight: bold
}

.time-select .time-select-item.selected:not(.disabled) {
    color: var(--el-color-primary);
    font-weight: bold
}

.el-timeline-item {
    padding-bottom: 20px;
    position: relative
}

.el-timeline-item__wrapper {
    padding-left: 28px;
    position: relative;
    top: -3px
}

.el-timeline-item__tail {
    border-left: 2px solid var(--el-timeline-node-color);
    height: 100%;
    left: 4px;
    position: absolute
}

.el-timeline-item .el-timeline-item__icon {
    color: var(--el-color-white);
    font-size: var(--el-font-size-small)
}

.el-timeline-item__node {
    align-items: center;
    background-color: var(--el-timeline-node-color);
    border-color: var(--el-timeline-node-color);
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    position: absolute
}

.el-timeline-item__node--normal {
    height: var(--el-timeline-node-size-normal);
    left: -1px;
    width: var(--el-timeline-node-size-normal)
}

.el-timeline-item__node--large {
    height: var(--el-timeline-node-size-large);
    left: -2px;
    width: var(--el-timeline-node-size-large)
}

.el-timeline-item__node.is-hollow {
    background: var(--el-color-white);
    border-style: solid;
    border-width: 2px
}

.el-timeline-item__node--primary {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary)
}

.el-timeline-item__node--success {
    background-color: var(--el-color-success);
    border-color: var(--el-color-success)
}

.el-timeline-item__node--warning {
    background-color: var(--el-color-warning);
    border-color: var(--el-color-warning)
}

.el-timeline-item__node--danger {
    background-color: var(--el-color-danger);
    border-color: var(--el-color-danger)
}

.el-timeline-item__node--info {
    background-color: var(--el-color-info);
    border-color: var(--el-color-info)
}

.el-timeline-item__dot {
    align-items: center;
    display: flex;
    justify-content: center;
    position: absolute
}

.el-timeline-item__content {
    color: var(--el-text-color-primary)
}

.el-timeline-item__timestamp {
    color: var(--el-text-color-secondary);
    font-size: var(--el-font-size-small);
    line-height: 1
}

.el-timeline-item__timestamp.is-top {
    margin-bottom: 8px;
    padding-top: 4px
}

.el-timeline-item__timestamp.is-bottom {
    margin-top: 8px
}

.el-timeline {
    --el-timeline-node-size-normal: 12px;
    --el-timeline-node-size-large: 14px;
    --el-timeline-node-color: var(--el-border-color-light);
    font-size: var(--el-font-size-base);
    list-style: none;
    margin: 0
}

.el-timeline .el-timeline-item:last-child .el-timeline-item__tail {
    display: none
}

.el-timeline .el-timeline-item__center {
    align-items: center;
    display: flex
}

.el-timeline .el-timeline-item__center .el-timeline-item__wrapper {
    width: 100%
}

.el-timeline .el-timeline-item__center .el-timeline-item__tail {
    top: 0
}

.el-timeline .el-timeline-item__center:first-child .el-timeline-item__tail {
    height: calc(50% + 10px);
    top: calc(50% - 10px)
}

.el-timeline .el-timeline-item__center:last-child .el-timeline-item__tail {
    display: block;
    height: calc(50% - 10px)
}

.el-tooltip-v2__content {
    --el-tooltip-v2-padding: 5px 10px;
    --el-tooltip-v2-border-radius: 4px;
    --el-tooltip-v2-border-color: var(--el-border-color);
    background-color: var(--el-color-white);
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-tooltip-v2-border-radius);
    color: var(--el-color-black);
    padding: var(--el-tooltip-v2-padding)
}

.el-tooltip-v2__arrow {
    color: var(--el-color-white);
    height: var(--el-tooltip-v2-arrow-height);
    left: var(--el-tooltip-v2-arrow-x);
    pointer-events: none;
    position: absolute;
    top: var(--el-tooltip-v2-arrow-y);
    width: var(--el-tooltip-v2-arrow-width)
}

.el-tooltip-v2__arrow:after, .el-tooltip-v2__arrow:before {
    border: var(--el-tooltip-v2-arrow-border-width) solid transparent;
    content: "";
    height: 0;
    position: absolute;
    width: 0
}

.el-tooltip-v2__content[data-side^=top] .el-tooltip-v2__arrow {
    bottom: 0
}

.el-tooltip-v2__content[data-side^=top] .el-tooltip-v2__arrow:before {
    border-bottom: 0;
    border-top-color: var(--el-color-white);
    border-top-width: var(--el-tooltip-v2-arrow-border-width);
    top: calc(100% - 1px)
}

.el-tooltip-v2__content[data-side^=top] .el-tooltip-v2__arrow:after {
    border-bottom: 0;
    border-top-color: var(--el-border-color);
    border-top-width: var(--el-tooltip-v2-arrow-border-width);
    top: 100%;
    z-index: -1
}

.el-tooltip-v2__content[data-side^=bottom] .el-tooltip-v2__arrow {
    top: 0
}

.el-tooltip-v2__content[data-side^=bottom] .el-tooltip-v2__arrow:before {
    border-bottom-color: var(--el-color-white);
    border-bottom-width: var(--el-tooltip-v2-arrow-border-width);
    border-top: 0;
    bottom: calc(100% - 1px)
}

.el-tooltip-v2__content[data-side^=bottom] .el-tooltip-v2__arrow:after {
    border-bottom-color: var(--el-border-color);
    border-bottom-width: var(--el-tooltip-v2-arrow-border-width);
    border-top: 0;
    bottom: 100%;
    z-index: -1
}

.el-tooltip-v2__content[data-side^=left] .el-tooltip-v2__arrow {
    right: 0
}

.el-tooltip-v2__content[data-side^=left] .el-tooltip-v2__arrow:before {
    border-left-color: var(--el-color-white);
    border-left-width: var(--el-tooltip-v2-arrow-border-width);
    border-right: 0;
    left: calc(100% - 1px)
}

.el-tooltip-v2__content[data-side^=left] .el-tooltip-v2__arrow:after {
    border-left-color: var(--el-border-color);
    border-left-width: var(--el-tooltip-v2-arrow-border-width);
    border-right: 0;
    left: 100%;
    z-index: -1
}

.el-tooltip-v2__content[data-side^=right] .el-tooltip-v2__arrow {
    left: 0
}

.el-tooltip-v2__content[data-side^=right] .el-tooltip-v2__arrow:before {
    border-left: 0;
    border-right-color: var(--el-color-white);
    border-right-width: var(--el-tooltip-v2-arrow-border-width);
    right: calc(100% - 1px)
}

.el-tooltip-v2__content[data-side^=right] .el-tooltip-v2__arrow:after {
    border-left: 0;
    border-right-color: var(--el-border-color);
    border-right-width: var(--el-tooltip-v2-arrow-border-width);
    right: 100%;
    z-index: -1
}

.el-tooltip-v2__content.is-dark {
    --el-tooltip-v2-border-color: transparent;
    color: var(--el-color-white)
}

.el-tooltip-v2__content.is-dark, .el-tooltip-v2__content.is-dark .el-tooltip-v2__arrow {
    background-color: var(--el-color-black);
    border-color: transparent
}

.el-transfer {
    --el-transfer-border-color: var(--el-border-color-lighter);
    --el-transfer-border-radius: var(--el-border-radius-base);
    --el-transfer-panel-width: 200px;
    --el-transfer-panel-header-height: 40px;
    --el-transfer-panel-header-bg-color: var(--el-fill-color-light);
    --el-transfer-panel-footer-height: 40px;
    --el-transfer-panel-body-height: 278px;
    --el-transfer-item-height: 30px;
    --el-transfer-filter-height: 32px;
    font-size: var(--el-font-size-base)
}

.el-transfer__buttons {
    display: inline-block;
    padding: 0 30px;
    vertical-align: middle
}

.el-transfer__button {
    vertical-align: top
}

.el-transfer__button:nth-child(2) {
    margin: 0 0 0 10px
}

.el-transfer__button i, .el-transfer__button span {
    font-size: 14px
}

.el-transfer__button .el-icon + span {
    margin-left: 0
}

.el-transfer-panel {
    background: var(--el-bg-color-overlay);
    box-sizing: border-box;
    display: inline-block;
    max-height: 100%;
    overflow: hidden;
    position: relative;
    text-align: left;
    vertical-align: middle;
    width: var(--el-transfer-panel-width)
}

.el-transfer-panel__body {
    border-bottom: 1px solid var(--el-transfer-border-color);
    border-bottom-left-radius: var(--el-transfer-border-radius);
    border-bottom-right-radius: var(--el-transfer-border-radius);
    border-left: 1px solid var(--el-transfer-border-color);
    border-right: 1px solid var(--el-transfer-border-color);
    height: var(--el-transfer-panel-body-height);
    overflow: hidden
}

.el-transfer-panel__body.is-with-footer {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.el-transfer-panel__list {
    box-sizing: border-box;
    height: var(--el-transfer-panel-body-height);
    list-style: none;
    margin: 0;
    overflow: auto;
    padding: 6px 0
}

.el-transfer-panel__list.is-filterable {
    height: calc(100% - var(--el-transfer-filter-height) - 30px);
    padding-top: 0
}

.el-transfer-panel__item {
    display: block !important;
    height: var(--el-transfer-item-height);
    line-height: var(--el-transfer-item-height);
    padding-left: 15px
}

.el-transfer-panel__item + .el-transfer-panel__item {
    margin-left: 0
}

.el-transfer-panel__item.el-checkbox {
    color: var(--el-text-color-regular)
}

.el-transfer-panel__item:hover {
    color: var(--el-color-primary)
}

.el-transfer-panel__item.el-checkbox .el-checkbox__label {
    box-sizing: border-box;
    display: block;
    line-height: var(--el-transfer-item-height);
    overflow: hidden;
    padding-left: 22px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.el-transfer-panel__item .el-checkbox__input {
    position: absolute;
    top: 8px
}

.el-transfer-panel__filter {
    box-sizing: border-box;
    padding: 15px;
    text-align: center
}

.el-transfer-panel__filter .el-input__inner {
    box-sizing: border-box;
    display: inline-block;
    font-size: 12px;
    height: var(--el-transfer-filter-height);
    width: 100%
}

.el-transfer-panel__filter .el-icon-circle-close {
    cursor: pointer
}

.el-transfer-panel .el-transfer-panel__header {
    align-items: center;
    background: var(--el-transfer-panel-header-bg-color);
    border: 1px solid var(--el-transfer-border-color);
    border-top-left-radius: var(--el-transfer-border-radius);
    border-top-right-radius: var(--el-transfer-border-radius);
    box-sizing: border-box;
    color: var(--el-color-black);
    display: flex;
    height: var(--el-transfer-panel-header-height);
    margin: 0;
    padding-left: 15px
}

.el-transfer-panel .el-transfer-panel__header .el-checkbox {
    align-items: center;
    display: flex;
    position: relative;
    width: 100%
}

.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
    color: var(--el-text-color-primary);
    font-size: 16px;
    font-weight: normal
}

.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
    color: var(--el-text-color-secondary);
    font-size: 12px;
    font-weight: normal;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translate3d(0, -50%, 0)
}

.el-transfer-panel .el-transfer-panel__footer {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-transfer-border-color);
    border-bottom-left-radius: var(--el-transfer-border-radius);
    border-bottom-right-radius: var(--el-transfer-border-radius);
    height: var(--el-transfer-panel-footer-height);
    margin: 0;
    padding: 0
}

.el-transfer-panel .el-transfer-panel__footer:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.el-transfer-panel .el-transfer-panel__footer .el-checkbox {
    color: var(--el-text-color-regular);
    padding-left: 20px
}

.el-transfer-panel .el-transfer-panel__empty {
    color: var(--el-text-color-secondary);
    height: var(--el-transfer-item-height);
    line-height: var(--el-transfer-item-height);
    margin: 0;
    padding: 6px 15px 0;
    text-align: center
}

.el-transfer-panel .el-checkbox__label {
    padding-left: 8px
}

.el-transfer-panel .el-checkbox__inner {
    border-radius: 3px;
    height: 14px;
    width: 14px
}

.el-transfer-panel .el-checkbox__inner:after {
    height: 6px;
    left: 4px;
    width: 3px
}

.el-tree {
    --el-tree-node-content-height: 26px;
    --el-tree-node-hover-bg-color: var(--el-fill-color-light);
    --el-tree-text-color: var(--el-text-color-regular);
    --el-tree-expand-icon-color: var(--el-text-color-placeholder);
    background: var(--el-fill-color-blank);
    color: var(--el-tree-text-color);
    cursor: default;
    font-size: var(--el-font-size-base);
    position: relative
}

.el-tree__empty-block {
    height: 100%;
    min-height: 60px;
    position: relative;
    text-align: center;
    width: 100%
}

.el-tree__empty-text {
    color: var(--el-text-color-secondary);
    font-size: var(--el-font-size-base);
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.el-tree__drop-indicator {
    background-color: var(--el-color-primary);
    height: 1px;
    left: 0;
    position: absolute;
    right: 0
}

.el-tree-node {
    outline: none;
    white-space: nowrap
}

.el-tree-node:focus > .el-tree-node__content {
    background-color: var(--el-tree-node-hover-bg-color)
}

.el-tree-node.is-drop-inner > .el-tree-node__content .el-tree-node__label {
    background-color: var(--el-color-primary);
    color: #fff
}

.el-tree-node__content {
    --el-checkbox-height: var(--el-tree-node-content-height);
    align-items: center;
    cursor: pointer;
    display: flex;
    height: var(--el-tree-node-content-height)
}

.el-tree-node__content > .el-tree-node__expand-icon {
    box-sizing: content-box;
    padding: 6px
}

.el-tree-node__content > label.el-checkbox {
    margin-right: 8px
}

.el-tree-node__content:hover {
    background-color: var(--el-tree-node-hover-bg-color)
}

.el-tree.is-dragging .el-tree-node__content {
    cursor: move
}

.el-tree.is-dragging .el-tree-node__content * {
    pointer-events: none
}

.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
    cursor: not-allowed
}

.el-tree-node__expand-icon {
    color: var(--el-tree-expand-icon-color);
    cursor: pointer;
    font-size: 12px;
    transform: rotate(0deg);
    transition: transform var(--el-transition-duration) ease-in-out
}

.el-tree-node__expand-icon.expanded {
    transform: rotate(90deg)
}

.el-tree-node__expand-icon.is-leaf {
    color: transparent;
    cursor: default;
    visibility: hidden
}

.el-tree-node__expand-icon.is-hidden {
    visibility: hidden
}

.el-tree-node__loading-icon {
    color: var(--el-tree-expand-icon-color);
    font-size: var(--el-font-size-base);
    margin-right: 8px
}

.el-tree-node > .el-tree-node__children {
    background-color: transparent;
    overflow: hidden
}

.el-tree-node.is-expanded > .el-tree-node__children {
    display: block
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: var(--el-color-primary-light-9)
}

.el-tree-select {
    --el-tree-node-content-height: 26px;
    --el-tree-node-hover-bg-color: var(--el-fill-color-light);
    --el-tree-text-color: var(--el-text-color-regular);
    --el-tree-expand-icon-color: var(--el-text-color-placeholder)
}

.el-tree-select__popper .el-tree-node__expand-icon {
    margin-left: 8px
}

.el-tree-select__popper .el-tree-node.is-checked > .el-tree-node__content .el-select-dropdown__item.selected:after {
    content: none
}

.el-tree-select__popper .el-select-dropdown__list > .el-select-dropdown__item {
    padding-left: 32px
}

.el-tree-select__popper .el-select-dropdown__item {
    background: transparent !important;
    flex: 1;
    height: 20px;
    line-height: 20px;
    padding-left: 0
}

.el-upload {
    --el-upload-dragger-padding-horizontal: 40px;
    --el-upload-dragger-padding-vertical: 10px;
    align-items: center;
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    outline: none
}

.el-upload.is-disabled {
    cursor: not-allowed
}

.el-upload.is-disabled:focus {
    color: inherit
}

.el-upload.is-disabled:focus, .el-upload.is-disabled:focus .el-upload-dragger {
    border-color: var(--el-border-color-darker)
}

.el-upload.is-disabled .el-upload-dragger {
    background-color: var(--el-disabled-bg-color);
    cursor: not-allowed
}

.el-upload.is-disabled .el-upload-dragger .el-upload__text {
    color: var(--el-text-color-placeholder)
}

.el-upload.is-disabled .el-upload-dragger .el-upload__text em {
    color: var(--el-disabled-text-color)
}

.el-upload.is-disabled .el-upload-dragger:hover {
    border-color: var(--el-border-color-darker)
}

.el-upload__input {
    display: none
}

.el-upload__tip {
    color: var(--el-text-color-regular);
    font-size: 12px;
    margin-top: 7px
}

.el-upload iframe {
    filter: alpha(opacity=0);
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    z-index: -1
}

.el-upload--picture-card {
    --el-upload-picture-card-size: 148px;
    align-items: center;
    background-color: var(--el-fill-color-lighter);
    border: 1px dashed var(--el-border-color-darker);
    border-radius: 6px;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    height: var(--el-upload-picture-card-size);
    justify-content: center;
    vertical-align: top;
    width: var(--el-upload-picture-card-size)
}

.el-upload--picture-card > i {
    color: var(--el-text-color-secondary);
    font-size: 28px
}

.el-upload--picture-card:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary)
}

.el-upload.is-drag {
    display: block
}

.el-upload:focus {
    color: var(--el-color-primary)
}

.el-upload:focus, .el-upload:focus .el-upload-dragger {
    border-color: var(--el-color-primary)
}

.el-upload-dragger {
    background-color: var(--el-fill-color-blank);
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    box-sizing: border-box;
    cursor: pointer;
    overflow: hidden;
    padding: var(--el-upload-dragger-padding-horizontal) var(--el-upload-dragger-padding-vertical);
    position: relative;
    text-align: center
}

.el-upload-dragger .el-icon--upload {
    color: var(--el-text-color-placeholder);
    font-size: 67px;
    line-height: 50px;
    margin-bottom: 16px
}

.el-upload-dragger + .el-upload__tip {
    text-align: center
}

.el-upload-dragger ~ .el-upload__files {
    border-top: var(--el-border);
    margin-top: 7px;
    padding-top: 5px
}

.el-upload-dragger .el-upload__text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center
}

.el-upload-dragger .el-upload__text em {
    color: var(--el-color-primary);
    font-style: normal
}

.el-upload-dragger:hover {
    border-color: var(--el-color-primary)
}

.el-upload-dragger.is-dragover {
    background-color: var(--el-color-primary-light-9);
    border: 2px dashed var(--el-color-primary);
    padding: calc(var(--el-upload-dragger-padding-horizontal) - 1px) calc(var(--el-upload-dragger-padding-vertical) - 1px)
}

.el-upload-list {
    list-style: none;
    margin: 10px 0 0;
    padding: 0;
    position: relative
}

.el-upload-list__item {
    border-radius: 4px;
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 5px;
    position: relative;
    transition: all .5s cubic-bezier(.55, 0, .1, 1);
    width: 100%
}

.el-upload-list__item .el-progress {
    position: absolute;
    top: 20px;
    width: 100%
}

.el-upload-list__item .el-progress__text {
    position: absolute;
    right: 0;
    top: -13px
}

.el-upload-list__item .el-progress-bar {
    margin-right: 0;
    padding-right: 0
}

.el-upload-list__item .el-icon--upload-success {
    color: var(--el-color-success)
}

.el-upload-list__item .el-icon--close {
    color: var(--el-text-color-regular);
    cursor: pointer;
    display: none;
    opacity: .75;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity var(--el-transition-duration)
}

.el-upload-list__item .el-icon--close:hover {
    color: var(--el-color-primary);
    opacity: 1
}

.el-upload-list__item .el-icon--close-tip {
    color: var(--el-color-primary);
    cursor: pointer;
    display: none;
    font-size: 12px;
    font-style: normal;
    opacity: 1;
    position: absolute;
    right: 5px;
    top: 1px
}

.el-upload-list__item:hover {
    background-color: var(--el-fill-color-light)
}

.el-upload-list__item:hover .el-icon--close {
    display: inline-flex
}

.el-upload-list__item:hover .el-progress__text {
    display: none
}

.el-upload-list__item .el-upload-list__item-info {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 4px;
    width: calc(100% - 30px)
}

.el-upload-list__item.is-success .el-upload-list__item-status-label {
    display: inline-flex
}

.el-upload-list__item.is-success .el-upload-list__item-name:focus, .el-upload-list__item.is-success .el-upload-list__item-name:hover {
    color: var(--el-color-primary);
    cursor: pointer
}

.el-upload-list__item.is-success:focus:not(:hover) .el-icon--close-tip {
    display: inline-block
}

.el-upload-list__item.is-success:active, .el-upload-list__item.is-success:not(.focusing):focus {
    outline-width: 0
}

.el-upload-list__item.is-success:active .el-icon--close-tip, .el-upload-list__item.is-success:not(.focusing):focus .el-icon--close-tip {
    display: none
}

.el-upload-list__item.is-success:focus .el-upload-list__item-status-label, .el-upload-list__item.is-success:hover .el-upload-list__item-status-label {
    display: none;
    opacity: 0
}

.el-upload-list__item-name {
    align-items: center;
    color: var(--el-text-color-regular);
    display: inline-flex;
    font-size: var(--el-font-size-base);
    padding: 0 4px;
    text-align: center;
    transition: color var(--el-transition-duration)
}

.el-upload-list__item-name .el-icon {
    color: var(--el-text-color-secondary);
    margin-right: 6px
}

.el-upload-list__item-file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-upload-list__item-status-label {
    align-items: center;
    display: none;
    height: 100%;
    justify-content: center;
    line-height: inherit;
    position: absolute;
    right: 5px;
    top: 0;
    transition: opacity var(--el-transition-duration)
}

.el-upload-list__item-delete {
    color: var(--el-text-color-regular);
    display: none;
    font-size: 12px;
    position: absolute;
    right: 10px;
    top: 0
}

.el-upload-list__item-delete:hover {
    color: var(--el-color-primary)
}

.el-upload-list--picture-card {
    --el-upload-list-picture-card-size: 148px;
    display: inline-flex;
    flex-wrap: wrap;
    margin: 0
}

.el-upload-list--picture-card .el-upload-list__item {
    background-color: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    box-sizing: border-box;
    display: inline-flex;
    height: var(--el-upload-list-picture-card-size);
    margin: 0 8px 8px 0;
    overflow: hidden;
    padding: 0;
    width: var(--el-upload-list-picture-card-size)
}

.el-upload-list--picture-card .el-upload-list__item .el-icon--check, .el-upload-list--picture-card .el-upload-list__item .el-icon--circle-check {
    color: #ffffff
}

.el-upload-list--picture-card .el-upload-list__item .el-icon--close {
    display: none
}

.el-upload-list--picture-card .el-upload-list__item:hover .el-upload-list__item-status-label {
    display: block;
    opacity: 0
}

.el-upload-list--picture-card .el-upload-list__item:hover .el-progress__text {
    display: block
}

.el-upload-list--picture-card .el-upload-list__item .el-upload-list__item-name {
    display: none
}

.el-upload-list--picture-card .el-upload-list__item-thumbnail {
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

.el-upload-list--picture-card .el-upload-list__item-status-label {
    background: var(--el-color-success);
    height: 24px;
    right: -15px;
    text-align: center;
    top: -6px;
    transform: rotate(45deg);
    width: 40px
}

.el-upload-list--picture-card .el-upload-list__item-status-label i {
    font-size: 12px;
    margin-top: 11px;
    transform: rotate(-45deg)
}

.el-upload-list--picture-card .el-upload-list__item-actions {
    align-items: center;
    background-color: var(--el-overlay-color-lighter);
    color: #fff;
    cursor: default;
    display: inline-flex;
    font-size: 20px;
    height: 100%;
    justify-content: center;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    transition: opacity var(--el-transition-duration);
    width: 100%
}

.el-upload-list--picture-card .el-upload-list__item-actions span {
    cursor: pointer;
    display: none
}

.el-upload-list--picture-card .el-upload-list__item-actions span + span {
    margin-left: 16px
}

.el-upload-list--picture-card .el-upload-list__item-actions .el-upload-list__item-delete {
    color: inherit;
    font-size: inherit;
    position: static
}

.el-upload-list--picture-card .el-upload-list__item-actions:hover {
    opacity: 1
}

.el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: inline-flex
}

.el-upload-list--picture-card .el-progress {
    bottom: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 126px
}

.el-upload-list--picture-card .el-progress .el-progress__text {
    top: 50%
}

.el-upload-list--picture .el-upload-list__item {
    align-items: center;
    background-color: var(--el-fill-color-blank);
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    box-sizing: border-box;
    display: flex;
    margin-top: 10px;
    overflow: hidden;
    padding: 10px;
    z-index: 0
}

.el-upload-list--picture .el-upload-list__item .el-icon--check, .el-upload-list--picture .el-upload-list__item .el-icon--circle-check {
    color: #ffffff
}

.el-upload-list--picture .el-upload-list__item:hover .el-upload-list__item-status-label {
    display: inline-flex;
    opacity: 0
}

.el-upload-list--picture .el-upload-list__item:hover .el-progress__text {
    display: block
}

.el-upload-list--picture .el-upload-list__item.is-success .el-upload-list__item-name i {
    display: none
}

.el-upload-list--picture .el-upload-list__item .el-icon--close {
    top: 5px;
    transform: translateY(0)
}

.el-upload-list--picture .el-upload-list__item-thumbnail {
    align-items: center;
    background-color: var(--el-color-white);
    display: inline-flex;
    height: 70px;
    justify-content: center;
    -o-object-fit: contain;
    object-fit: contain;
    position: relative;
    width: 70px;
    z-index: 1
}

.el-upload-list--picture .el-upload-list__item-status-label {
    background: var(--el-color-success);
    height: 26px;
    position: absolute;
    right: -17px;
    text-align: center;
    top: -7px;
    transform: rotate(45deg);
    width: 46px
}

.el-upload-list--picture .el-upload-list__item-status-label i {
    font-size: 12px;
    margin-top: 12px;
    transform: rotate(-45deg)
}

.el-upload-list--picture .el-progress {
    position: relative;
    top: -7px
}

.el-upload-cover {
    cursor: default;
    height: 100%;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 10
}

.el-upload-cover:after {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

.el-upload-cover img {
    display: block;
    height: 100%;
    width: 100%
}

.el-upload-cover__label {
    background: var(--el-color-success);
    height: 24px;
    right: -15px;
    text-align: center;
    top: -6px;
    transform: rotate(45deg);
    width: 40px
}

.el-upload-cover__label i {
    color: #fff;
    font-size: 12px;
    margin-top: 11px;
    transform: rotate(-45deg)
}

.el-upload-cover__progress {
    display: inline-block;
    position: static;
    vertical-align: middle;
    width: 243px
}

.el-upload-cover__progress + .el-upload__inner {
    opacity: 0
}

.el-upload-cover__content {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.el-upload-cover__interact {
    background-color: var(--el-overlay-color-light);
    bottom: 0;
    height: 100%;
    left: 0;
    position: absolute;
    text-align: center;
    width: 100%
}

.el-upload-cover__interact .btn {
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    margin-top: 60px;
    transition: var(--el-transition-md-fade);
    vertical-align: middle
}

.el-upload-cover__interact .btn i {
    margin-top: 0
}

.el-upload-cover__interact .btn span {
    opacity: 0;
    transition: opacity .15s linear
}

.el-upload-cover__interact .btn:not(:first-child) {
    margin-left: 35px
}

.el-upload-cover__interact .btn:hover {
    transform: translateY(-13px)
}

.el-upload-cover__interact .btn:hover span {
    opacity: 1
}

.el-upload-cover__interact .btn i {
    color: #ffffff;
    display: block;
    font-size: 24px;
    line-height: inherit;
    margin: 0 auto 5px
}

.el-upload-cover__title {
    background-color: #ffffff;
    bottom: 0;
    color: var(--el-text-color-primary);
    font-size: 14px;
    font-weight: normal;
    height: 36px;
    left: 0;
    line-height: 36px;
    margin: 0;
    overflow: hidden;
    padding: 0 10px;
    position: absolute;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.el-upload-cover + .el-upload__inner {
    opacity: 0;
    position: relative;
    z-index: 1
}

.el-vl__wrapper {
    position: relative
}

.el-vl__wrapper.always-on .el-virtual-scrollbar, .el-vl__wrapper:hover .el-virtual-scrollbar {
    opacity: 1
}

.el-vl__window {
    scrollbar-width: none
}

.el-vl__window::-webkit-scrollbar {
    display: none
}

.el-virtual-scrollbar {
    opacity: 0;
    transition: opacity .34s ease-out
}

.el-virtual-scrollbar.always-on {
    opacity: 1
}

.el-vg__wrapper {
    position: relative
}

.el-popper {
    --el-popper-border-radius: var(--el-popover-border-radius, 4px);
    border-radius: var(--el-popper-border-radius);
    font-size: 12px;
    line-height: 20px;
    min-width: 10px;
    overflow-wrap: break-word;
    padding: 5px 11px;
    position: absolute;
    visibility: visible;
    z-index: 2000
}

.el-popper.is-dark {
    color: var(--el-bg-color)
}

.el-popper.is-dark, .el-popper.is-dark > .el-popper__arrow:before {
    background: var(--el-text-color-primary);
    border: 1px solid var(--el-text-color-primary)
}

.el-popper.is-dark > .el-popper__arrow:before {
    right: 0
}

.el-popper.is-light, .el-popper.is-light > .el-popper__arrow:before {
    background: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light)
}

.el-popper.is-light > .el-popper__arrow:before {
    right: 0
}

.el-popper.is-pure {
    padding: 0
}

.el-popper__arrow, .el-popper__arrow:before {
    height: 10px;
    position: absolute;
    width: 10px;
    z-index: -1
}

.el-popper__arrow:before {
    background: var(--el-text-color-primary);
    box-sizing: border-box;
    content: " ";
    transform: rotate(45deg)
}

.el-popper[data-popper-placement^=top] > .el-popper__arrow {
    bottom: -5px
}

.el-popper[data-popper-placement^=top] > .el-popper__arrow:before {
    border-bottom-right-radius: 2px
}

.el-popper[data-popper-placement^=bottom] > .el-popper__arrow {
    top: -5px
}

.el-popper[data-popper-placement^=bottom] > .el-popper__arrow:before {
    border-top-left-radius: 2px
}

.el-popper[data-popper-placement^=left] > .el-popper__arrow {
    right: -5px
}

.el-popper[data-popper-placement^=left] > .el-popper__arrow:before {
    border-top-right-radius: 2px
}

.el-popper[data-popper-placement^=right] > .el-popper__arrow {
    left: -5px
}

.el-popper[data-popper-placement^=right] > .el-popper__arrow:before {
    border-bottom-left-radius: 2px
}

.el-popper[data-popper-placement^=top] > .el-popper__arrow:before {
    border-left-color: transparent !important;
    border-top-color: transparent !important
}

.el-popper[data-popper-placement^=bottom] > .el-popper__arrow:before {
    border-bottom-color: transparent !important;
    border-right-color: transparent !important
}

.el-popper[data-popper-placement^=left] > .el-popper__arrow:before {
    border-bottom-color: transparent !important;
    border-left-color: transparent !important
}

.el-popper[data-popper-placement^=right] > .el-popper__arrow:before {
    border-right-color: transparent !important;
    border-top-color: transparent !important
}

.el-statistic {
    --el-statistic-title-font-weight: 400;
    --el-statistic-title-font-size: var(--el-font-size-extra-small);
    --el-statistic-title-color: var(--el-text-color-regular);
    --el-statistic-content-font-weight: 400;
    --el-statistic-content-font-size: var(--el-font-size-extra-large);
    --el-statistic-content-color: var(--el-text-color-primary)
}

.el-statistic__head {
    color: var(--el-statistic-title-color);
    font-size: var(--el-statistic-title-font-size);
    font-weight: var(--el-statistic-title-font-weight);
    line-height: 20px;
    margin-bottom: 4px
}

.el-statistic__content {
    color: var(--el-statistic-content-color);
    font-size: var(--el-statistic-content-font-size);
    font-weight: var(--el-statistic-content-font-weight)
}

.el-statistic__value {
    display: inline-block
}

.el-statistic__prefix {
    display: inline-block;
    margin-right: 4px
}

.el-statistic__suffix {
    display: inline-block;
    margin-left: 4px
}

.el-tour {
    --el-tour-width: 520px;
    --el-tour-padding-primary: 12px;
    --el-tour-font-line-height: var(--el-font-line-height-primary);
    --el-tour-title-font-size: 16px;
    --el-tour-title-text-color: var(--el-text-color-primary);
    --el-tour-title-font-weight: 400;
    --el-tour-close-color: var(--el-color-info);
    --el-tour-font-size: 14px;
    --el-tour-color: var(--el-text-color-primary);
    --el-tour-bg-color: var(--el-bg-color);
    --el-tour-border-radius: 4px
}

.el-tour__hollow {
    transition: all var(--el-transition-duration) ease
}

.el-tour__content {
    border-radius: var(--el-tour-border-radius);
    box-shadow: var(--el-box-shadow-light);
    outline: none;
    overflow-wrap: break-word;
    padding: var(--el-tour-padding-primary);
    width: var(--el-tour-width)
}

.el-tour__arrow, .el-tour__content {
    background: var(--el-tour-bg-color);
    box-sizing: border-box
}

.el-tour__arrow {
    height: 10px;
    pointer-events: none;
    position: absolute;
    transform: rotate(45deg);
    width: 10px
}

.el-tour__content[data-side^=top] .el-tour__arrow {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-tour__content[data-side^=bottom] .el-tour__arrow {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-tour__content[data-side^=left] .el-tour__arrow {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-tour__content[data-side^=right] .el-tour__arrow {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-tour__content[data-side^=top] .el-tour__arrow {
    bottom: -5px
}

.el-tour__content[data-side^=bottom] .el-tour__arrow {
    top: -5px
}

.el-tour__content[data-side^=left] .el-tour__arrow {
    right: -5px
}

.el-tour__content[data-side^=right] .el-tour__arrow {
    left: -5px
}

.el-tour__closebtn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: var(--el-message-close-size, 16px);
    height: 40px;
    outline: none;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 40px
}

.el-tour__closebtn .el-tour__close {
    color: var(--el-tour-close-color);
    font-size: inherit
}

.el-tour__closebtn:focus .el-tour__close, .el-tour__closebtn:hover .el-tour__close {
    color: var(--el-color-primary)
}

.el-tour__header {
    padding-bottom: var(--el-tour-padding-primary)
}

.el-tour__header.show-close {
    padding-right: calc(var(--el-tour-padding-primary) + var(--el-message-close-size, 16px))
}

.el-tour__title {
    color: var(--el-tour-title-text-color);
    font-size: var(--el-tour-title-font-size);
    font-weight: var(--el-tour-title-font-weight);
    line-height: var(--el-tour-font-line-height)
}

.el-tour__body {
    color: var(--el-tour-text-color);
    font-size: var(--el-tour-font-size)
}

.el-tour__body img, .el-tour__body video {
    max-width: 100%
}

.el-tour__footer {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    padding-top: var(--el-tour-padding-primary)
}

.el-tour__content .el-tour-indicators {
    display: inline-block;
    flex: 1
}

.el-tour__content .el-tour-indicator {
    background: var(--el-color-info-light-9);
    border-radius: 50%;
    display: inline-block;
    height: 6px;
    margin-right: 6px;
    width: 6px
}

.el-tour__content .el-tour-indicator.is-active {
    background: var(--el-color-primary)
}

.el-tour.el-tour--primary {
    --el-tour-title-text-color: #fff;
    --el-tour-text-color: #fff;
    --el-tour-bg-color: var(--el-color-primary);
    --el-tour-close-color: #fff
}

.el-tour.el-tour--primary .el-tour__closebtn:focus .el-tour__close, .el-tour.el-tour--primary .el-tour__closebtn:hover .el-tour__close {
    color: var(--el-tour-title-text-color)
}

.el-tour.el-tour--primary .el-button--default {
    background: #fff;
    border-color: var(--el-color-primary);
    color: var(--el-color-primary)
}

.el-tour.el-tour--primary .el-button--primary {
    border-color: #fff
}

.el-tour.el-tour--primary .el-tour-indicator {
    background: rgba(255, 255, 255, .15)
}

.el-tour.el-tour--primary .el-tour-indicator.is-active {
    background: #fff
}

.el-tour-parent--hidden {
    overflow: hidden
}

.el-anchor {
    --el-anchor-bg-color: var(--el-bg-color);
    --el-anchor-padding-indent: 14px;
    --el-anchor-line-height: 22px;
    --el-anchor-font-size: 12px;
    --el-anchor-color: var(--el-text-color-secondary);
    --el-anchor-active-color: var(--el-color-primary);
    --el-anchor-marker-bg-color: var(--el-color-primary);
    background-color: var(--el-anchor-bg-color);
    position: relative
}

.el-anchor__marker {
    background-color: var(--el-anchor-marker-bg-color);
    border-radius: 4px;
    opacity: 0;
    position: absolute;
    z-index: 0
}

.el-anchor.el-anchor--vertical .el-anchor__marker {
    height: 14px;
    left: 0;
    top: 8px;
    transition: top .25s ease-in-out, opacity .25s;
    width: 4px
}

.el-anchor.el-anchor--vertical .el-anchor__list {
    padding-left: var(--el-anchor-padding-indent)
}

.el-anchor.el-anchor--vertical.el-anchor--underline:before {
    background-color: rgba(5, 5, 5, .06);
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    width: 2px
}

.el-anchor.el-anchor--vertical.el-anchor--underline .el-anchor__marker {
    border-radius: unset;
    width: 2px
}

.el-anchor.el-anchor--horizontal .el-anchor__marker {
    bottom: 0;
    height: 2px;
    transition: left .25s ease-in-out, opacity .25s, width .25s;
    width: 20px
}

.el-anchor.el-anchor--horizontal .el-anchor__list {
    display: flex;
    padding-bottom: 4px
}

.el-anchor.el-anchor--horizontal .el-anchor__list .el-anchor__item {
    padding-left: 16px
}

.el-anchor.el-anchor--horizontal .el-anchor__list .el-anchor__item:first-child {
    padding-left: 0
}

.el-anchor.el-anchor--horizontal.el-anchor--underline:before {
    background-color: rgba(5, 5, 5, .06);
    bottom: 0;
    content: "";
    height: 2px;
    position: absolute;
    width: 100%
}

.el-anchor.el-anchor--horizontal.el-anchor--underline .el-anchor__marker {
    border-radius: unset;
    height: 2px
}

.el-anchor__item {
    display: flex;
    flex-direction: column;
    overflow: hidden
}

.el-anchor__link {
    cursor: pointer;
    font-size: var(--el-anchor-font-size);
    line-height: var(--el-anchor-line-height);
    max-width: 100%;
    outline: none;
    overflow: hidden;
    padding: 4px 0;
    text-decoration: none;
    text-overflow: ellipsis;
    transition: color var(--el-transition-duration);
    white-space: nowrap
}

.el-anchor__link, .el-anchor__link:focus, .el-anchor__link:hover {
    color: var(--el-anchor-color)
}

.el-anchor__link.is-active {
    color: var(--el-anchor-active-color)
}

.el-anchor .el-anchor__list .el-anchor__item a {
    display: inline-block
}

.el-segmented--vertical {
    flex-direction: column
}

.el-segmented--vertical .el-segmented__item {
    padding: 11px
}

.el-segmented {
    --el-segmented-color: var(--el-text-color-regular);
    --el-segmented-bg-color: var(--el-fill-color-light);
    --el-segmented-padding: 2px;
    --el-segmented-item-selected-color: var(--el-color-white);
    --el-segmented-item-selected-bg-color: var(--el-color-primary);
    --el-segmented-item-selected-disabled-bg-color: var(--el-color-primary-light-5);
    --el-segmented-item-hover-color: var(--el-text-color-primary);
    --el-segmented-item-hover-bg-color: var(--el-fill-color-dark);
    --el-segmented-item-active-bg-color: var(--el-fill-color-darker);
    --el-segmented-item-disabled-color: var(--el-text-color-placeholder);
    align-items: stretch;
    background: var(--el-segmented-bg-color);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    color: var(--el-segmented-color);
    display: inline-flex;
    font-size: 14px;
    min-height: 32px;
    padding: var(--el-segmented-padding)
}

.el-segmented__group {
    align-items: stretch;
    display: flex;
    position: relative;
    width: 100%
}

.el-segmented__item-selected {
    background: var(--el-segmented-item-selected-bg-color);
    border-radius: calc(var(--el-border-radius-base) - 2px);
    height: 100%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transition: all .3s;
    width: 10px
}

.el-segmented__item-selected.is-disabled {
    background: var(--el-segmented-item-selected-disabled-bg-color)
}

.el-segmented__item-selected.is-focus-visible:before {
    border-radius: inherit;
    content: "";
    inset: 0;
    outline: 2px solid var(--el-segmented-item-selected-bg-color);
    outline-offset: 1px;
    position: absolute
}

.el-segmented__item {
    align-items: center;
    border-radius: calc(var(--el-border-radius-base) - 2px);
    cursor: pointer;
    display: flex;
    flex: 1;
    padding: 0 11px
}

.el-segmented__item:not(.is-disabled):not(.is-selected):hover {
    background: var(--el-segmented-item-hover-bg-color);
    color: var(--el-segmented-item-hover-color)
}

.el-segmented__item:not(.is-disabled):not(.is-selected):active {
    background: var(--el-segmented-item-active-bg-color)
}

.el-segmented__item.is-selected, .el-segmented__item.is-selected.is-disabled {
    color: var(--el-segmented-item-selected-color)
}

.el-segmented__item.is-disabled {
    color: var(--el-segmented-item-disabled-color);
    cursor: not-allowed
}

.el-segmented__item-input {
    height: 0;
    margin: 0;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    width: 0
}

.el-segmented__item-label {
    flex: 1;
    line-height: normal;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    transition: color .3s;
    white-space: nowrap;
    z-index: 1
}

.el-segmented.is-block {
    display: flex
}

.el-segmented.is-block .el-segmented__item {
    min-width: 0
}

.el-segmented--large {
    border-radius: var(--el-border-radius-base);
    font-size: 16px;
    min-height: 40px
}

.el-segmented--large .el-segmented__item-selected {
    border-radius: calc(var(--el-border-radius-base) - 2px)
}

.el-segmented--large .el-segmented--vertical .el-segmented__item {
    padding: 11px
}

.el-segmented--large .el-segmented__item {
    border-radius: calc(var(--el-border-radius-base) - 2px);
    padding: 0 11px
}

.el-segmented--small {
    border-radius: calc(var(--el-border-radius-base) - 1px);
    font-size: 14px;
    min-height: 24px
}

.el-segmented--small .el-segmented__item-selected {
    border-radius: calc(var(--el-border-radius-base) - 3px)
}

.el-segmented--small .el-segmented--vertical .el-segmented__item {
    padding: 7px
}

.el-segmented--small .el-segmented__item {
    border-radius: calc(var(--el-border-radius-base) - 3px);
    padding: 0 7px
}

.el-mention {
    position: relative;
    width: 100%
}

.el-mention__popper.el-popper {
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light)
}

.el-mention__popper.el-popper, .el-mention__popper.el-popper .el-popper__arrow:before {
    border: 1px solid var(--el-border-color-light)
}

.el-mention__popper.el-popper[data-popper-placement^=top] .el-popper__arrow:before {
    border-left-color: transparent;
    border-top-color: transparent
}

.el-mention__popper.el-popper[data-popper-placement^=bottom] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-right-color: transparent
}

.el-mention__popper.el-popper[data-popper-placement^=left] .el-popper__arrow:before {
    border-bottom-color: transparent;
    border-left-color: transparent
}

.el-mention__popper.el-popper[data-popper-placement^=right] .el-popper__arrow:before {
    border-right-color: transparent;
    border-top-color: transparent
}

.el-mention-dropdown {
    --el-mention-font-size: var(--el-font-size-base);
    --el-mention-bg-color: var(--el-bg-color-overlay);
    --el-mention-shadow: var(--el-box-shadow-light);
    --el-mention-border: 1px solid var(--el-border-color-light);
    --el-mention-option-color: var(--el-text-color-regular);
    --el-mention-option-height: 34px;
    --el-mention-option-min-width: 100px;
    --el-mention-option-hover-background: var(--el-fill-color-light);
    --el-mention-option-selected-color: var(--el-color-primary);
    --el-mention-option-disabled-color: var(--el-text-color-placeholder);
    --el-mention-option-loading-color: var(--el-text-color-secondary);
    --el-mention-option-loading-padding: 10px 0;
    --el-mention-max-height: 174px;
    --el-mention-padding: 6px 0;
    --el-mention-header-padding: 10px;
    --el-mention-footer-padding: 10px
}

.el-mention-dropdown__item {
    box-sizing: border-box;
    color: var(--el-mention-option-color);
    cursor: pointer;
    font-size: var(--el-mention-font-size);
    height: var(--el-mention-option-height);
    line-height: var(--el-mention-option-height);
    min-width: var(--el-mention-option-min-width);
    overflow: hidden;
    padding: 0 20px;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap
}

.el-mention-dropdown__item.is-hovering {
    background-color: var(--el-mention-option-hover-background)
}

.el-mention-dropdown__item.is-selected {
    color: var(--el-mention-option-selected-color);
    font-weight: bold
}

.el-mention-dropdown__item.is-disabled {
    background-color: unset;
    color: var(--el-mention-option-disabled-color);
    cursor: not-allowed
}

.el-mention-dropdown {
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    z-index: calc(var(--el-index-top) + 1)
}

.el-mention-dropdown__loading {
    color: var(--el-mention-option-loading-color);
    font-size: 12px;
    margin: 0;
    min-width: var(--el-mention-option-min-width);
    padding: 10px 0;
    text-align: center
}

.el-mention-dropdown__wrap {
    max-height: var(--el-mention-max-height)
}

.el-mention-dropdown__list {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: var(--el-mention-padding)
}

.el-mention-dropdown__header {
    border-bottom: var(--el-mention-border);
    padding: var(--el-mention-header-padding)
}

.el-mention-dropdown__footer {
    border-top: var(--el-mention-border);
    padding: var(--el-mention-footer-padding)
}

.el-splitter {
    display: flex;
    height: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%
}

.el-splitter__mask {
    inset: 0;
    position: absolute;
    z-index: 999
}

.el-splitter__mask-horizontal {
    cursor: col-resize
}

.el-splitter__mask-vertical {
    cursor: row-resize
}

.el-splitter__horizontal {
    flex-direction: row
}

.el-splitter__vertical {
    flex-direction: column
}

.el-splitter-bar {
    flex: none;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.el-splitter-bar__disable {
    cursor: auto !important
}

.el-splitter-bar__disable:before {
    background-color: var(--el-border-color-light) !important
}

.el-splitter-bar__dragger {
    background: transparent;
    z-index: 1
}

.el-splitter-bar__dragger, .el-splitter-bar__dragger:before {
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%)
}

.el-splitter-bar__dragger:before {
    background-color: var(--el-border-color-light);
    content: ""
}

.el-splitter-bar__dragger:hover:before {
    background-color: var(--el-color-primary-light-5)
}

.el-splitter-bar__dragger-horizontal:before {
    height: 100%;
    width: 2px
}

.el-splitter-bar__dragger-vertical:before {
    height: 2px;
    width: 100%
}

.el-splitter-bar__dragger-active:before {
    background-color: var(--el-color-primary-light-3)
}

.el-splitter-bar:hover .el-splitter-bar__collapse-icon {
    opacity: 1
}

.el-splitter-bar__collapse-icon {
    align-items: center;
    background: var(--el-border-color-light);
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    opacity: 0;
    position: absolute;
    z-index: 9
}

.el-splitter-bar__collapse-icon:hover {
    background-color: var(--el-color-primary-light-5);
    opacity: 1
}

.el-splitter-bar__horizontal-collapse-icon-start {
    height: 24px;
    left: -12px;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px
}

.el-splitter-bar__horizontal-collapse-icon-end {
    height: 24px;
    left: 12px;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px
}

.el-splitter-bar__vertical-collapse-icon-start {
    height: 16px;
    right: 50%;
    top: -12px;
    transform: translate(50%, -50%);
    width: 24px
}

.el-splitter-bar__vertical-collapse-icon-end {
    height: 16px;
    right: 50%;
    top: 12px;
    transform: translate(50%, -50%);
    width: 24px
}

.el-splitter-panel {
    box-sizing: border-box;
    flex-grow: 0;
    overflow: auto;
    scrollbar-width: thin
}