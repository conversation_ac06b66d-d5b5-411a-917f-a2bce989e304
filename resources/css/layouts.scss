// ========================================
// 企业级框架布局样式系统 - Blade 模板前后台共用组件
// 中文注释用于中国合规
// ========================================

// 导入全局变量
@use 'variables' as *;
// Must be at the top
@use 'base';
// Must be at the top
@use 'global' as *; // Must be at the top
// ========================================
// 企业级布局滚动条修复系统 - 精确修复
// ========================================

/* 仅针对特定宽度问题的布局组件修复 */
.layout-overflow-fix {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.layout-overflow-fix::-webkit-scrollbar {
    display: none;
}

// ========================================
// 布局组件样式 - 使用 variables 中的 mixins 和变量
// ========================================

// ========================================
// 企业级页面布局修复 - 为固定顶部导航栏预留空间
// ========================================

// 页面主体布局修复 - 避免被固定导航栏遮挡
body {
    padding-top: var(--nav-height-top);
}

// 主要内容区域布局修复
.page-content,
.main-content,
#main-content,
main {
    margin-top: 0; // 重置可能的边距
}

// 页面容器布局修复
.page-container,
.container-1760,
.app-container,
#app-container {
    // 确保容器不被顶部导航栏遮挡
    position: relative;
}

// ========================================
// 企业级顶部导航栏样式 - 最大宽度1760px，无动画设计
// ========================================

// 企业级顶部导航栏 - 最大宽度1760px，无动画设计
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: var(--nav-height-top);
    background-color: $top-nav-bg;
    color: $top-nav-text;
    font-size: var(--font-size-xs); // 12px 基础字体大小
    z-index: var(--z-index-fixed);
    min-width: var(--container-min-width);
    @include no-animation;

    // 强制高度约束 - 确保任何屏幕下都是30px
    min-height: var(--nav-height-top);
    max-height: var(--nav-height-top);
    overflow: hidden;
    box-sizing: border-box;

    &.desktop-optimized {
        // 企业级桌面端优化标识
        max-width: 100%;
    }
}

.top-nav-container {
    @include enterprise-container;
    @include flex-center;
    justify-content: space-between;
    height: var(--nav-height-top);
    width: 100%;
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);

    // 强制高度约束
    min-height: var(--nav-height-top);
    max-height: var(--nav-height-top);
    box-sizing: border-box;

    @include enterprise-container-responsive;
}

// 导航栏区域布局
.top-nav-left-section {
    @include flex-center;
    gap: var(--spacing-3);
    flex-shrink: 0;
}

.top-nav-right-section {
    @include flex-center;
    gap: var(--spacing-2);
    flex-shrink: 1;
    min-width: 0;
}

.top-nav-user-section {
    @include flex-center;
    gap: var(--spacing-3);
    flex-shrink: 0;
}

.top-nav-link {
    @include flex-center;
    @include enterprise-link;
    padding: var(--spacing-1) var(--spacing-2);
    color: $top-nav-text;
    line-height: 1.4;
    white-space: nowrap;

    &:hover {
        color: $top-nav-hover-text;
        background-color: $top-nav-hover-bg;
    }

    &:focus,
    &:active,
    &:focus-visible {
        @include no-focus-outline;
        color: $top-nav-hover-text;
        background-color: $top-nav-hover-bg;
    }
}

// 扩展顶部导航链接样式
.top-nav-link {
    // 图标样式优化 - 与字体对齐，稍小尺寸
    i {
        @include enterprise-icon;
    }

    // 企业级按钮样式 - 无动画
    &.top-nav-button {
        background: transparent;
        border: none;
        font-family: inherit;
        padding: var(--spacing-1) var(--spacing-2);
        color: var(--color-gray-300);

        &:hover {
            color: $top-nav-hover-text;
            background-color: $top-nav-hover-bg;
        }

        &:focus,
        &:active {
            color: $top-nav-hover-text;
            background-color: $top-nav-hover-bg;
            @include no-focus-outline;
        }
    }
}

// 用户信息区域样式
.top-nav-user-info {
    color: #e5e7eb;
    @include flex-center;
    line-height: 1.4;
    flex-shrink: 0;

    i {
        @include enterprise-icon;
    }
}

.top-nav-badge {
    background-color: $badge-bg;
    color: $badge-text;
    font-size: 10px;
    line-height: 16px;
    height: 16px;
    min-width: 16px;
    padding: 0 4px;
    border-radius: 50%;
    text-align: center;
    position: absolute;
    top: -6px;
    right: -6px;
    z-index: 10;
    @include no-animation;
}

// 企业级文本显示系统 - 无动画
.top-nav-text {
    display: inline-block;
}

.top-nav-welcome {
    display: inline-block;
}

// ========================================
// 企业级顶部导航栏响应式系统
// ========================================

// 1760px+ 标准企业桌面
@media (min-width: 1760px) {
    .top-nav-container {
        max-width: 1760px;
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .top-nav-left-section,
    .top-nav-user-section {
        gap: 1rem;
    }

    .top-nav-right-section {
        gap: 0.75rem;
    }

    .top-nav-link {
        padding: var(--spacing-1) var(--spacing-2);
        font-size: var(--font-size-xs); // 12px 基础字体大小
    }

    .top-nav-username {
        max-width: 150px;
    }
}

// 1600px-1759px 中等企业桌面
@media (min-width: 1600px) and (max-width: 1759px) {
    .top-nav-container {
        max-width: 1600px;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .top-nav-username {
        max-width: 130px;
    }
}

/* 1440px-1599px 标准企业桌面 */
@media (min-width: 1440px) and (max-width: 1599px) {
    .top-nav-container {
        max-width: 1440px;
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .top-nav-username {
        max-width: 110px;
    }
}

/* 1200px-1439px 小桌面 */
@media (min-width: 1200px) and (max-width: 1439px) {
    .top-nav-container {
        max-width: 1200px;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .top-nav-left-section,
    .top-nav-user-section {
        gap: 0.5rem;
    }

    .top-nav-right-section {
        gap: 0.375rem;
    }

    .top-nav-link {
        padding: var(--spacing-1) 0.375rem;
        font-size: var(--font-size-xs); // 12px 基础字体大小
    }

    .top-nav-username {
        max-width: 100px;
    }

    .top-nav-welcome {
        display: none;
    }
}

/* 1200px 以下 - 水平滚动模式 */
@media (max-width: 1199px) {
    .top-nav {
        min-width: 1200px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .top-nav::-webkit-scrollbar {
        display: none;
    }

    .top-nav-container {
        min-width: 1200px;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .top-nav-link {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: var(--spacing-1) 0.375rem;
    }

    .top-nav-left-section,
    .top-nav-user-section,
    .top-nav-right-section {
        gap: 0.25rem;
    }

    .top-nav-username {
        max-width: 80px;
    }
}

/* 图标系统 - 全局统一标准 */
.top-nav-link i,
.top-nav-user-info i {
    font-size: 1em; // 保持标准图标大小
    margin-right: 0.375rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1em;
    vertical-align: middle;
    line-height: 1;
    flex-shrink: 0;
}

/* 特定图标的微调 */
.top-nav-link i.fa-envelope,
.top-nav-link i.fa-user,
.top-nav-link i.fa-cog {
    transform: translateY(0px); /* 这些图标不需要额外调整 */
}

.top-nav-link i.fas,
.top-nav-user-info i.fas {
    /* 确保 FontAwesome 图标完美对齐 */
    transform: translateY(0.5px); /* 微调垂直位置 */
}

/* 确保按钮内图标对齐 */
.top-nav-button i {
    margin-right: 0.375rem;
    vertical-align: baseline;
}

/* 企业级性能优化 - 无动画设计 */
.top-nav * {
    box-sizing: border-box;
}

.top-nav-link,
.top-nav-user-info,
.top-nav-separator {
    contain: layout style;
}

/* 企业级响应式文本系统 - 无动画 */
.top-nav-text[data-short] {
    position: relative;
}

.top-nav-text[data-short]::after {
    position: absolute;
    left: 0;
    top: 0;
    white-space: nowrap;
}

/* 企业级无障碍优化 - 确保无动画 */
.top-nav-link,
.top-nav-text,
.top-nav-badge,
.top-nav-separator {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

/* 响应式分隔符显示 */
.top-nav-separator-responsive {
    display: inline-block;
}

@media (max-width: 1439px) {
    .top-nav-separator-responsive {
        display: none;
    }
}

/* 隐藏部分功能保持核心功能 */
.top-nav-link-collapsible {
    display: inline-flex;
}

.top-nav-separator-collapsible {
    display: inline-block;
}

@media (max-width: 1199px) {
    .top-nav-link-collapsible {
        display: none;
    }

    .top-nav-separator-collapsible {
        display: none;
    }
}

/* 简化所有文本 */
@media (max-width: 1199px) {
    .top-nav-text[data-short]::after {
        content: attr(data-short);
    }

    .top-nav-text[data-short] {
        font-size: 0;
    }
}

/* 响应式图标 - 保持标准大小，只调整间距 */
@media (max-width: 1439px) {
    .top-nav-link i,
    .top-nav-user-info i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.3rem;
    }
}

@media (max-width: 1199px) {
    .top-nav-link i,
    .top-nav-user-info i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.25rem;
    }
}

/* 文字截断优化 */
.top-nav-text-truncate {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (min-width: 1440px) {
    .top-nav-text-truncate {
        max-width: 180px;
    }
}

// ========================================
// 小屏幕响应式支持 - 640px 到 1199px
// ========================================

/* 1024px-1199px 平板横屏 */
@media (min-width: 1024px) and (max-width: 1199px) {
    .top-nav {
        min-width: auto;
        overflow-x: visible;
        height: var(--nav-height-top); // 保持30px
    }

    .top-nav-container {
        max-width: 100%;
        min-width: auto;
        height: var(--nav-height-top); // 保持30px
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .top-nav-left-section,
    .top-nav-right-section {
        gap: 0.375rem;
        flex-shrink: 0;
    }

    .top-nav-link {
        padding: 0.125rem 0.375rem;
        font-size: var(--font-size-xs); // 12px 基础字体大小
        line-height: 1.4; // 保持标准行高
        min-height: 24px;
    }

    .top-nav-username {
        max-width: 80px;
        line-height: 1.4; // 保持标准行高
    }

    .top-nav-welcome {
        display: none;
    }
}

/* 768px-1023px 平板竖屏 */
@media (min-width: 768px) and (max-width: 1023px) {
    .top-nav {
        height: var(--nav-height-top); // 保持30px
    }

    .top-nav-container {
        height: var(--nav-height-top); // 保持30px
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .top-nav-left-section,
    .top-nav-right-section {
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .top-nav-link {
        padding: 0.25rem 0.375rem;
        font-size: var(--font-size-xs); // 12px 基础字体大小
        min-height: 24px;
        line-height: 1.4; // 保持标准行高
    }

    .top-nav-text {
        display: none;
    }

    .top-nav-welcome {
        display: none;
    }

    .top-nav-username {
        max-width: 60px;
        font-size: var(--font-size-xs); // 12px 基础字体大小
        line-height: 1.4; // 保持标准行高
    }

    .top-nav-link i,
    .top-nav-user-info i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.375rem;
    }
}

/* 640px-767px 大手机屏幕 - 智能内容隐藏 */
@media (min-width: 640px) and (max-width: 767px) {
    .top-nav {
        height: var(--nav-height-top); // 保持30px
    }

    .top-nav-container {
        height: var(--nav-height-top); // 保持30px
        padding-left: 0.375rem;
        padding-right: 0.375rem;
    }

    .top-nav-left-section,
    .top-nav-right-section {
        gap: 0.25rem; // 适当间距
        flex-shrink: 0;
    }

    .top-nav-link {
        padding: 0.25rem 0.375rem; // 保持标准内边距
        font-size: var(--font-size-xs); // 12px 基础字体大小
        min-height: 24px; // 适当最小高度
        line-height: 1.4; // 保持标准行高
        border-radius: 0.25rem;
    }

    /* 智能隐藏：隐藏分隔符，保留图标和关键文字 */
    .top-nav-separator {
        display: none;
    }

    /* 隐藏非关键文字，保留图标 */
    .top-nav-text {
        display: none;
    }

    .top-nav-welcome {
        display: none;
    }

    .top-nav-username {
        display: none;
    }

    /* 保持标准图标大小和间距 */
    .top-nav-link i,
    .top-nav-user-info i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.375rem; // 保持标准间距
    }

    /* 启用水平滚动来容纳更多内容 */
    .top-nav-right-section {
        max-width: 60%;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        flex-shrink: 1;
        min-width: 0;
    }

    .top-nav-right-section::-webkit-scrollbar {
        display: none;
    }
}

/* 640px以下 - 超小屏幕优化：最大化空间利用 */
@media (max-width: 639px) {
    .top-nav {
        height: var(--nav-height-top); // 保持30px
    }

    .top-nav-container {
        height: var(--nav-height-top); // 保持30px
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }

    .top-nav-left-section,
    .top-nav-right-section {
        gap: 0.25rem; // 保持适当间距
        flex-shrink: 0;
    }

    .top-nav-link {
        padding: 0.25rem 0.25rem; // 最小化内边距但保持可点击性
        font-size: var(--font-size-xs); // 12px 基础字体大小
        min-height: 24px; // 保持足够点击区域
        line-height: 1.4; // 保持标准行高
        border-radius: 0.25rem;
    }

    /* 隐藏所有非关键文字元素 */
    .top-nav-separator,
    .top-nav-text,
    .top-nav-welcome,
    .top-nav-username {
        display: none;
    }

    /* 保持标准图标大小 - 只显示图标提供最佳识别性 */
    .top-nav-link i,
    .top-nav-user-info i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0; // 超小屏幕下去掉图标间距
    }

    /* 水平滚动区域优化 */
    .top-nav-right-section {
        max-width: 70%;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        flex-shrink: 1;
        min-width: 0;
    }

    .top-nav-right-section::-webkit-scrollbar {
        display: none;
    }

    /* 隐藏部分非关键功能以节省空间 */
    .top-nav-link-collapsible {
        display: none;
    }

    .top-nav-separator-collapsible {
        display: none;
    }
}

/* 用户名显示优化 */
.top-nav-username {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.top-nav-separator {
    color: var(--color-gray-500);
    user-select: none;
    pointer-events: none;
    flex-shrink: 0;
    opacity: 0.7;
}


// ========================================
// 企业级底部版权栏样式
// ========================================

/* 底部版权栏基础样式 */
.bottom-copyright {
    position: fixed;
    width: 100%;
    bottom: 0;
    background-color: #000000;
    color: var(--color-gray-300);
    font-size: var(--font-size-xs); // 12px 基础字体大小
    height: var(--nav-height-bottom);
    z-index: 100;
    border-top: 1px solid var(--color-gray-800);

    // 强制高度约束 - 确保任何屏幕下都是30px
    min-height: var(--nav-height-bottom);
    max-height: var(--nav-height-bottom);
    overflow: hidden;
    box-sizing: border-box;
}

.bottom-copyright-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--nav-height-bottom);

    // 强制高度约束
    min-height: var(--nav-height-bottom);
    max-height: var(--nav-height-bottom);
    box-sizing: border-box;
}

/* 左侧版权信息区域 */
.bottom-copyright-left-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex-shrink: 0;
}

/* 右侧系统信息区域 */
.bottom-copyright-right-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    flex-shrink: 1;
    min-width: 0;
}

/* 版权栏链接样式 */
.bottom-copyright-link {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-2);
    text-decoration: none;
    color: var(--color-gray-300);
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    line-height: 1.4;
    white-space: nowrap;
    cursor: pointer;
    transition: var(--transition-none);
}

.bottom-copyright-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

/* 版权栏文本样式 */
.bottom-copyright-text {
    display: inline-flex;
    align-items: center;
    color: var(--color-gray-300);
    white-space: nowrap;
}

/* 版权栏分隔符 */
.bottom-copyright-separator {
    color: var(--color-gray-500);
    user-select: none;
    pointer-events: none;
    flex-shrink: 0;
    opacity: 0.7;
}

/* 版权栏图标样式 - 保持标准大小 */
.bottom-copyright-link i,
.bottom-copyright-text i {
    font-size: 1em; // 保持标准图标大小
    margin-right: 0.375rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1em;
    vertical-align: middle;
    line-height: 1;
    flex-shrink: 0;
}

// ========================================
// 企业级底部版权栏响应式系统
// ========================================

/* 1760px+ 标准企业桌面 */
@media (min-width: 1760px) {
    .bottom-copyright-container {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .bottom-copyright-left-section {
        gap: 1rem;
    }

    .bottom-copyright-right-section {
        gap: 0.75rem;
    }
}

/* 1600px-1759px 中等企业桌面 */
@media (min-width: 1600px) and (max-width: 1759px) {
    .bottom-copyright-container {
        max-width: 1600px;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

/* 1440px-1599px 标准企业桌面 */
@media (min-width: 1440px) and (max-width: 1599px) {
    .bottom-copyright-container {
        max-width: 1440px;
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }
}

/* 1200px-1439px 小企业桌面 */
@media (min-width: 1200px) and (max-width: 1439px) {
    .bottom-copyright-container {
        max-width: 1200px;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .bottom-copyright-left-section,
    .bottom-copyright-right-section {
        gap: 0.5rem;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        padding: var(--spacing-1) 0.375rem;
        font-size: var(--font-size-xs); // 12px 基础字体大小
    }
}

/* 1200px 以下 - 水平滚动模式 */
@media (max-width: 1199px) {
    .bottom-copyright {
        min-width: 1200px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .bottom-copyright::-webkit-scrollbar {
        display: none;
    }

    .bottom-copyright-container {
        min-width: 1200px;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: var(--spacing-1) 0.375rem;
    }

    .bottom-copyright-left-section,
    .bottom-copyright-right-section {
        gap: 0.25rem;
    }
}

/* 底部版权栏小屏幕响应式支持 */

/* 1024px-1199px 平板横屏 */
@media (min-width: 1024px) and (max-width: 1199px) {
    .bottom-copyright {
        min-width: auto;
        overflow-x: visible;
    }

    .bottom-copyright-container {
        min-width: auto;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .bottom-copyright-left-section,
    .bottom-copyright-right-section {
        gap: 0.5rem;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: var(--spacing-1) 0.375rem;
    }
}

/* 768px-1023px 平板竖屏 */
@media (min-width: 768px) and (max-width: 1023px) {
    .bottom-copyright {
        height: var(--nav-height-bottom); // 保持30px
    }

    .bottom-copyright-container {
        height: var(--nav-height-bottom); // 保持30px
        padding-left: 0.5rem;
        padding-right: 0.5rem;
        flex-direction: row;
        gap: 0.25rem;
        justify-content: space-between;
        align-items: center;
    }

    .bottom-copyright-left-section,
    .bottom-copyright-right-section {
        gap: 0.25rem;
        flex-shrink: 0;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: 0.125rem 0.25rem;
        line-height: 1.4; // 保持标准行高
    }

    .bottom-copyright-link i,
    .bottom-copyright-text i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.375rem;
    }

    /* 隐藏非关键信息 */
    .bottom-copyright-collapsible {
        display: none;
    }

    .bottom-copyright-separator-collapsible {
        display: none;
    }
}

/* 640px-767px 大手机屏幕 - 智能内容布局 */
@media (min-width: 640px) and (max-width: 767px) {
    .bottom-copyright {
        height: var(--nav-height-bottom); // 保持30px
    }

    .bottom-copyright-container {
        height: var(--nav-height-bottom); // 保持30px
        padding-left: 0.375rem;
        padding-right: 0.375rem;
        flex-direction: row;
        gap: 0.25rem; // 适当间距
        align-items: center;
        justify-content: space-between;
    }

    .bottom-copyright-left-section {
        gap: 0.25rem; // 适当间距
        flex-shrink: 0;
        order: 1;
    }

    .bottom-copyright-right-section {
        gap: 0.25rem; // 适当间距
        flex-shrink: 1;
        min-width: 0;
        order: 2;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .bottom-copyright-right-section::-webkit-scrollbar {
        display: none;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: 0.25rem 0.375rem; // 保持标准内边距
        min-height: 24px; // 适当最小高度
        line-height: 1.4; // 保持标准行高
        border-radius: 0.25rem;
    }

    /* 隐藏分隔符节省空间 */
    .bottom-copyright-separator {
        display: none;
    }

    /* 保持标准图标大小和间距 */
    .bottom-copyright-link i,
    .bottom-copyright-text i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.375rem; // 保持标准间距
    }

    .bottom-copyright-link:hover {
        background-color: rgba(255, 255, 255, 0.15);
    }
}

/* 640px以下 - 超小屏幕底部版权栏：极简布局 */
@media (max-width: 639px) {
    .bottom-copyright {
        height: var(--nav-height-bottom); // 保持30px
    }

    .bottom-copyright-container {
        height: var(--nav-height-bottom); // 保持30px
        padding-left: 0.25rem;
        padding-right: 0.25rem;
        flex-direction: row;
        gap: 0.25rem; // 保持适当间距
        align-items: center;
        justify-content: space-between;
    }

    .bottom-copyright-left-section {
        gap: 0.25rem; // 保持适当间距
        flex-shrink: 0;
        order: 1;
    }

    .bottom-copyright-right-section {
        gap: 0.25rem; // 保持适当间距
        flex-shrink: 1;
        min-width: 0;
        order: 2;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .bottom-copyright-right-section::-webkit-scrollbar {
        display: none;
    }

    .bottom-copyright-link,
    .bottom-copyright-text {
        font-size: var(--font-size-xs); // 12px 基础字体大小
        padding: 0.25rem 0.125rem; // 最小化水平内边距
        min-height: 24px; // 保持足够点击区域
        line-height: 1.4; // 保持标准行高
        border-radius: 0.25rem;
    }

    /* 隐藏所有分隔符 */
    .bottom-copyright-separator {
        display: none;
    }

    /* 保持标准图标大小，但去掉右边距 */
    .bottom-copyright-link i,
    .bottom-copyright-text i {
        font-size: 1em; // 保持标准图标大小
        margin-right: 0.125rem; // 最小间距
    }

    /* 隐藏非关键信息以节省空间 */
    .bottom-copyright-collapsible {
        display: none;
    }

    .bottom-copyright-separator-collapsible {
        display: none;
    }

    .bottom-copyright-separator-responsive {
        display: none;
    }

    .bottom-copyright-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* 响应式分隔符显示 */
.bottom-copyright-separator-responsive {
    display: inline-block;
}

@media (max-width: 1439px) {
    .bottom-copyright-separator-responsive {
        display: none;
    }
}

/* 隐藏部分功能 */
.bottom-copyright-collapsible {
    display: inline-flex;
}

.bottom-copyright-separator-collapsible {
    display: inline-block;
}

@media (max-width: 1199px) {
    .bottom-copyright-collapsible {
        display: none;
    }

    .bottom-copyright-separator-collapsible {
        display: none;
    }
}

/* 响应式文本简化系统 */
.bottom-copyright-version[data-short],
.bottom-copyright-time[data-short],
.bottom-copyright-env[data-short],
.bottom-copyright-laravel[data-short] {
    position: relative;
}

/* 1600px-1759px 开始简化版本信息文本 */
@media (min-width: 1600px) and (max-width: 1759px) {
    .bottom-copyright-version[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-version[data-short] {
        font-size: 0;
    }
}

/* 1440px-1599px 简化更多信息 */
@media (min-width: 1440px) and (max-width: 1599px) {
    .bottom-copyright-time[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-time[data-short] {
        font-size: 0;
    }

    .bottom-copyright-env[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-env[data-short] {
        font-size: 0;
    }

    .bottom-copyright-laravel[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-laravel[data-short] {
        font-size: 0;
    }
}

/* 1200px-1439px 所有文本简化 */
@media (min-width: 1200px) and (max-width: 1439px) {
    .bottom-copyright-text[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-text[data-short] {
        font-size: 0;
    }
}

/* 平板横屏简化版本信息 */
@media (min-width: 1024px) and (max-width: 1199px) {
    .bottom-copyright-version[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-version[data-short] {
        font-size: 0;
    }

    .bottom-copyright-time[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-time[data-short] {
        font-size: 0;
    }

    .bottom-copyright-env[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-env[data-short] {
        font-size: 0;
    }

    .bottom-copyright-laravel[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-laravel[data-short] {
        font-size: 0;
    }
}

/* 平板竖屏和手机简化所有文本 */
@media (max-width: 1023px) {
    .bottom-copyright-text[data-short]::after {
        content: attr(data-short);
    }

    .bottom-copyright-text[data-short] {
        font-size: 0;
    }
}

/* 企业级底部版权栏无障碍优化 - 确保无动画 */
.bottom-copyright-link,
.bottom-copyright-text,
.bottom-copyright-separator {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}
