<template>
    <AppLayout>
        <!-- 主应用内容 -->
        <router-view></router-view>
    </AppLayout>
</template>
<script>
import {useAppStoreSystem} from '@stores/app/useAppStoreSystem.js'
import {useBehaviorStore} from '@stores/global/useBehaviorStore.js'
import AppLayout from '@layouts/AppLayout.vue'

export default {
    name: 'Admin',
    components: {
        AppLayout,
    },
    data() {
        return {
            // 应用状态
            isAppReady: false,
            // 用户活动监测
            lastUserActivity: Date.now(),
            activityTimer: null,
        }
    },
    computed: {
        // Store实例
        AppStoreSystem() {
            return useAppStoreSystem()
        },
        BehaviorStore() {
            return useBehaviorStore()
        },
        // 从DOM元素获取路由激活状态
        routeActive() {
            return this.$el?.dataset?.routeActive || ''
        },
    },
    async created() {
        console.log('🚀 应用启动中...')
        // 初始化应用
        await this.initializeApplication()

        // 设置用户活动监听
        this.setupUserActivityListeners()

        console.log('✅ 应用初始化完成')
    },
    beforeUnmount() {
        this.cleanup()
    },
    methods: {
        /**
         * 初始化应用
         */
        async initializeApplication() {
            try {
                // 初始化核心系统（内部会管理加载状态）
                await this.AppStoreSystem.initializeApp()

                // 标记应用准备就绪
                this.isAppReady = true

                // 记录启动成功
                this.BehaviorStore.recordUserAction({
                    type: 'app_start',
                    target: 'application',
                    details: {
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        screenResolution: `${screen.width}x${screen.height}`
                    }
                })

            } catch (error) {
                console.error('❌ 应用初始化失败:', error)
                this.handleGlobalError(error, '应用初始化失败')
            }
        },

        /**
         * 全局错误处理 - 只上报不提示
         */
        handleGlobalError(error, context = '') {
            const errorMessage = error?.message || error?.toString() || '未知错误'

            console.error('🔥 全局错误:', error, context)

            // 只记录错误，不显示全局提示
            this.BehaviorStore.recordUserAction({
                type: 'error',
                target: 'global',
                details: {
                    error: errorMessage,
                    context,
                    stack: error?.stack || '无堆栈信息',
                    timestamp: Date.now(),
                    url: window.location.href,
                    userAgent: navigator.userAgent
                }
            })

            // 上报错误到store（用于统计）
            this.AppStoreSystem.reportError({
                message: errorMessage,
                context,
                timestamp: Date.now(),
                stack: error?.stack || '无堆栈信息'
            })
        },

        /**
         * 设置用户活动监听
         */
        setupUserActivityListeners() {
            const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

            activityEvents.forEach(event => {
                document.addEventListener(event, this.handleUserActivity, {passive: true})
            })

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', this.handleVisibilityChange)

            // 监听页面卸载
            window.addEventListener('beforeunload', this.handleBeforeUnload)

            // 全局未捕获错误处理
            window.addEventListener('error', this.handleWindowError)
            window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
        },

        /**
         * 处理用户活动
         */
        handleUserActivity() {
            this.lastUserActivity = Date.now()
            this.AppStoreSystem.updateLastActivity()
        },

        /**
         * 处理页面可见性变化
         */
        handleVisibilityChange() {
            if (document.hidden) {
                // 页面隐藏时记录
                this.BehaviorStore.recordUserAction({
                    type: 'page_hidden',
                    target: 'window',
                    details: {timestamp: Date.now()}
                })
            } else {
                // 页面显示时记录
                this.BehaviorStore.recordUserAction({
                    type: 'page_visible',
                    target: 'window',
                    details: {timestamp: Date.now()}
                })
                this.handleUserActivity()
            }
        },

        /**
         * 处理页面卸载前
         */
        handleBeforeUnload(event) {
            // 记录用户离开
            this.BehaviorStore.recordUserAction({
                type: 'page_unload',
                target: 'window',
                details: {
                    timestamp: Date.now(),
                    sessionDuration: Date.now() - this.AppStoreSystem.sessionStartTime
                }
            })

            // 执行优雅关闭
            this.AppStoreSystem.gracefulShutdown()
        },

        /**
         * 处理窗口错误
         */
        handleWindowError(event) {
            this.handleGlobalError(event.error, '窗口错误')
        },

        /**
         * 处理未捕获的Promise拒绝
         */
        handleUnhandledRejection(event) {
            this.handleGlobalError(event.reason, '未处理的Promise拒绝')
        },

        /**
         * 清理资源
         */
        cleanup() {
            // 清理定时器
            if (this.activityTimer) {
                clearTimeout(this.activityTimer)
            }

            // 移除事件监听
            const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
            activityEvents.forEach(event => {
                document.removeEventListener(event, this.handleUserActivity)
            })

            document.removeEventListener('visibilitychange', this.handleVisibilityChange)
            window.removeEventListener('beforeunload', this.handleBeforeUnload)
            window.removeEventListener('error', this.handleWindowError)
            window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
        },
    },
}
</script>
