import './bootstrap';
//
import {createApp} from 'vue';
import {createPinia} from 'pinia';
import axiosUtils from './utils/axiosUtils.js';
//
// ElementPlusIcon
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
//
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import router from './router/index'
//
import '@css/element-plus.css'
import '@css/base.scss'

// 导入主要的AdminFacade组件
import AdminFacade from './AdminFacade.vue';
//
// 创建Vue应用实例，使用空的根组件
const app = createApp({
    data() {
        return {
            message: 'Hello root Component'
        };
    },
    // 全局注册AdminFacade组件，这样可以在Laravel模板中直接使用<AdminFacade></AdminFacade>
    components: {
        //
        'adminfacade': AdminFacade,
    },
});
// 创建并配置Pinia
const pinia = createPinia();

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

// 使用Pinia状态管理
app.use(pinia);
// 使用Axios插件
app.use(axiosUtils);
// 使用 Vue Router
app.use(router);
// 设置全局$message方法
app.config.globalProperties.$message = ElementPlus.ElMessage
// 使用 Element Plus 插件
app.use(ElementPlus, {locale: zhCn, zIndex: 3000});

// 挂载应用到#app元素
app.mount('#app')
