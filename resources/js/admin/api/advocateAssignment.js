/**
 * 倡导者分配管理 API
 *
 * 功能特性：
 * - 倡导者角色分配数据管理
 * - 模拟API调用和数据生成
 * - 企业级数据结构
 * - 状态管理和统计
 * - 数据导出功能
 *
 * 版本：v2.0.0
 * 更新时间：2025-06-23
 * 作者：Augment Agent
 */

// 分配状态常量
export const ASSIGNMENT_STATUS = {
    PENDING: 'pending',
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    EXPIRED: 'expired'
}

// 分配状态标签
export const ASSIGNMENT_STATUS_LABELS = {
    [ASSIGNMENT_STATUS.PENDING]: '待分配',
    [ASSIGNMENT_STATUS.ACTIVE]: '已分配',
    [ASSIGNMENT_STATUS.INACTIVE]: '已停用',
    [ASSIGNMENT_STATUS.EXPIRED]: '已过期'
}

// 角色类型常量
export const ROLE_TYPES = {
    ADMIN: 'admin',
    SUPERVISOR: 'supervisor',
    SPECIALIST: 'specialist',
    ASSISTANT: 'assistant'
}

// 角色类型标签
export const ROLE_TYPE_LABELS = {
    [ROLE_TYPES.ADMIN]: '管理员',
    [ROLE_TYPES.SUPERVISOR]: '主管',
    [ROLE_TYPES.SPECIALIST]: '专员',
    [ROLE_TYPES.ASSISTANT]: '助理'
}

// 权限级别常量
export const PERMISSION_LEVELS = {
    HIGH: 'high',
    MEDIUM: 'medium',
    BASIC: 'basic',
    READONLY: 'readonly'
}

// 权限级别标签
export const PERMISSION_LEVEL_LABELS = {
    [PERMISSION_LEVELS.HIGH]: '高级权限',
    [PERMISSION_LEVELS.MEDIUM]: '中级权限',
    [PERMISSION_LEVELS.BASIC]: '基础权限',
    [PERMISSION_LEVELS.READONLY]: '只读权限'
}

/**
 * 生成模拟分配数据
 */
function generateMockAssignments(count = 50) {
    const assignments = []
    const statuses = Object.values(ASSIGNMENT_STATUS)
    const roleTypes = Object.values(ROLE_TYPES)
    const permissionLevels = Object.values(PERMISSION_LEVELS)

    const departments = ['管理部', '业务部', '运营部', '支持部', '技术部', '财务部', '人事部', '市场部']
    const roleNames = [
        '系统管理员', '业务主管', '客户专员', '技术助理', '财务管理员',
        '销售主管', '市场专员', '运营助理', '产品管理员', '质量主管',
        '采购专员', '仓库助理', '人事管理员', '培训主管', '客服专员',
        '数据助理', '安全管理员', '合规主管', '审计专员', '法务助理'
    ]

    const firstNames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴']
    const lastNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋']

    for (let i = 0; i < count; i++) {
        const status = statuses[i % statuses.length]
        const roleType = roleTypes[i % roleTypes.length]
        const permissionLevel = permissionLevels[i % permissionLevels.length]
        const department = departments[i % departments.length]
        const roleName = roleNames[i % roleNames.length]

        // 生成姓名
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
        const advocateName = firstName + lastName

        // 生成手机号
        const phonePrefix = ['138', '139', '150', '151', '188', '189'][Math.floor(Math.random() * 6)]
        const phoneNumber = phonePrefix + Math.random().toString().slice(2, 10)

        // 生成邮箱
        const emailDomains = ['company.com', 'enterprise.cn', 'business.net']
        const emailDomain = emailDomains[Math.floor(Math.random() * emailDomains.length)]
        const advocateEmail = `${advocateName.toLowerCase()}${i}@${emailDomain}`

        // 生成分配时间
        const assignedAt = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)

        // 生成过期时间（部分有过期时间）
        let expiresAt = null
        if (Math.random() > 0.3) { // 70%的分配有过期时间
            const daysToExpire = Math.floor(Math.random() * 365) + 30 // 30-395天后过期
            expiresAt = new Date(assignedAt.getTime() + daysToExpire * 24 * 60 * 60 * 1000)
        }

        assignments.push({
            id: i + 1,
            advocate_id: i + 1,
            advocate_name: advocateName,
            advocate_phone: phoneNumber,
            advocate_email: advocateEmail,
            role_id: (i % 20) + 1,
            role_name: roleName,
            role_type: roleType,
            role_type_label: ROLE_TYPE_LABELS[roleType],
            permission_level: permissionLevel,
            permission_level_label: PERMISSION_LEVEL_LABELS[permissionLevel],
            department: department,
            status: status,
            status_label: ASSIGNMENT_STATUS_LABELS[status],
            assigned_at: assignedAt.toISOString().split('T')[0],
            assigned_by: '系统管理员',
            expires_at: expiresAt ? expiresAt.toISOString().split('T')[0] : null,
            notes: `${advocateName}被分配为${department}的${roleName}`,
            created_at: assignedAt.toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
        })
    }

    return assignments
}

/**
 * 生成统计数据
 */
function generateCounts(assignments) {
    const counts = {
        total_count: assignments.length,
        pending_count: 0,
        active_count: 0,
        inactive_count: 0,
        expired_count: 0
    }

    assignments.forEach(assignment => {
        switch (assignment.status) {
            case ASSIGNMENT_STATUS.PENDING:
                counts.pending_count++
                break
            case ASSIGNMENT_STATUS.ACTIVE:
                counts.active_count++
                break
            case ASSIGNMENT_STATUS.INACTIVE:
                counts.inactive_count++
                break
            case ASSIGNMENT_STATUS.EXPIRED:
                counts.expired_count++
                break
        }
    })

    return counts
}

/**
 * 获取分配列表
 */
export async function getAssignmentList(params = {}) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    const {
        page = 1,
        per_page = 20,
        search = '',
        tab = 'all',
        status = '',
        role_type = '',
        start_date = '',
        end_date = ''
    } = params

    // 生成模拟数据
    let assignments = generateMockAssignments(100)

    // 应用筛选
    if (search) {
        assignments = assignments.filter(assignment =>
            assignment.advocate_name.includes(search) ||
            assignment.role_name.includes(search) ||
            assignment.department.includes(search)
        )
    }

    if (tab && tab !== 'all') {
        assignments = assignments.filter(assignment => assignment.status === tab)
    }

    if (status) {
        assignments = assignments.filter(assignment => assignment.status === status)
    }

    if (role_type) {
        assignments = assignments.filter(assignment => assignment.role_type === role_type)
    }

    if (start_date && end_date) {
        assignments = assignments.filter(assignment => {
            const assignedDate = new Date(assignment.assigned_at)
            return assignedDate >= new Date(start_date) && assignedDate <= new Date(end_date)
        })
    }

    // 分页
    const total = assignments.length
    const start = (page - 1) * per_page
    const end = start + per_page
    const paginatedAssignments = assignments.slice(start, end)

    // 生成统计数据
    const counts = generateCounts(generateMockAssignments(100)) // 使用完整数据生成统计

    return {
        assignments: paginatedAssignments,
        assignment_counts: counts,
        pagination: {
            current_page: page,
            per_page: per_page,
            total: total,
            last_page: Math.ceil(total / per_page)
        }
    }
}

/**
 * 更新分配状态
 */
export async function updateAssignmentStatus(assignmentId, status) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    console.log(`更新分配 ${assignmentId} 状态为: ${status}`)

    return {
        success: true,
        message: '分配状态更新成功'
    }
}

/**
 * 删除分配
 */
export async function deleteAssignment(assignmentId) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    console.log(`删除分配: ${assignmentId}`)

    return {
        success: true,
        message: '分配删除成功'
    }
}

/**
 * 批量删除分配
 */
export async function batchDeleteAssignments(assignmentIds) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log(`批量删除分配: ${assignmentIds.join(', ')}`)

    return {
        success: true,
        message: `成功删除 ${assignmentIds.length} 个分配`
    }
}

/**
 * 导出分配数据
 */
export async function exportAssignments(params = {}) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log('导出分配数据，参数:', params)

    // 模拟文件下载
    const blob = new Blob(['分配数据导出文件内容'], {type: 'text/csv'})
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `advocate_assignments_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    window.URL.revokeObjectURL(url)

    return {
        success: true,
        message: '分配数据导出成功'
    }
}
