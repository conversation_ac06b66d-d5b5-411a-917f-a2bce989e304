<!--
/**
 * 倡导者列表视图组件
 *
 * 功能特性：
 * - 倡导者数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 * - 敏感信息脱敏处理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="advocate-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 倡导者编号 -->
            <el-table-column
                label="倡导者编号"
                min-width="160"
                prop="advocate_no"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="advocate-cell">
                        <i class="fas fa-bullhorn advocate-icon"></i>
                        <span class="advocate-number">{{ row.advocate_no }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 倡导者信息 -->
            <el-table-column
                label="倡导者信息"
                min-width="180"
            >
                <template #default="{ row }">
                    <div class="advocate-info">
                        <div class="advocate-name">
                            <i class="fas fa-user advocate-icon"></i>
                            <span>{{ row.name }}</span>
                        </div>
                        <div class="advocate-phone">
                            <i class="fas fa-phone phone-icon"></i>
                            <span class="sensitive-data">{{ row.phone }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 倡导者等级 -->
            <el-table-column
                align="center"
                label="倡导者等级"
                prop="level"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getLevelType(row.level)"
                        size="small"
                    >
                        <i :class="getLevelIcon(row.level)" class="level-icon"></i>
                        {{ getLevelName(row.level) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 推广积分 -->
            <el-table-column
                align="right"
                label="推广积分"
                prop="points"
                sortable
                width="120"
            >
                <template #default="{ row }">
                    <div class="points-cell">
                        <span class="points">{{ formatPoints(row.points) }}</span>
                        <div class="referral-count">{{ row.referral_count }}人推荐</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 倡导者状态 -->
            <el-table-column
                align="center"
                label="倡导者状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        <i :class="getStatusIcon(row.status)" class="status-icon"></i>
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 注册时间 -->
            <el-table-column
                label="注册时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.created_at) }}</div>
                        <div class="time">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 最后活跃 -->
            <el-table-column
                label="最后活跃"
                prop="last_active_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.last_active_at) }}</div>
                        <div class="time">{{ formatTime(row.last_active_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="canEdit(row)"
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-if="row.status === 'pending'"
                                    @click="handleStatusChange(row, 'active')"
                                >
                                    <i class="fas fa-check"></i>
                                    激活
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'active'"
                                    @click="handleStatusChange(row, 'inactive')"
                                >
                                    <i class="fas fa-pause"></i>
                                    设为非活跃
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="['active', 'inactive'].includes(row.status)"
                                    @click="handleStatusChange(row, 'disabled')"
                                >
                                    <i class="fas fa-ban"></i>
                                    禁用
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'disabled'"
                                    @click="handleStatusChange(row, 'active')"
                                >
                                    <i class="fas fa-undo"></i>
                                    启用
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除倡导者
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'AdvocateListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'status-change',
        'delete',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'active': '活跃',
                'inactive': '非活跃',
                'pending': '待审核',
                'disabled': '已禁用',
                'suspended': '已暂停'
            },
            levelMap: {
                'junior': '初级倡导者',
                'intermediate': '中级倡导者',
                'senior': '高级倡导者',
                'expert': '专家倡导者'
            }
        }
    },
    methods: {
        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑倡导者
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 删除倡导者
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        },

        // 格式化积分
        formatPoints(points) {
            return Number(points || 0).toLocaleString('zh-CN')
        },

        // 格式化日期
        formatDate(datetime) {
            return datetime.split(' ')[0]
        },

        // 格式化时间
        formatTime(datetime) {
            return datetime.split(' ')[1]
        },

        // 获取等级名称
        getLevelName(level) {
            return this.levelMap[level] || level
        },

        // 获取等级图标
        getLevelIcon(level) {
            const iconMap = {
                'junior': 'fas fa-star',
                'intermediate': 'fas fa-star-half-alt',
                'senior': 'fas fa-crown',
                'expert': 'fas fa-gem'
            }
            return iconMap[level] || 'fas fa-star'
        },

        // 获取等级类型
        getLevelType(level) {
            const typeMap = {
                'junior': 'info',
                'intermediate': 'primary',
                'senior': 'warning',
                'expert': 'danger'
            }
            return typeMap[level] || 'info'
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                'active': 'fas fa-check-circle',
                'inactive': 'fas fa-pause-circle',
                'pending': 'fas fa-clock',
                'disabled': 'fas fa-ban',
                'suspended': 'fas fa-exclamation-triangle'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'inactive': 'warning',
                'pending': 'info',
                'disabled': 'danger',
                'suspended': 'warning'
            }
            return typeMap[status] || 'info'
        },

        // 判断是否可以编辑
        canEdit(row) {
            return ['active', 'inactive', 'pending'].includes(row.status)
        }
    }
}
</script>

<style lang="scss" scoped>
.advocate-list-view {
    .admin-table {
        // 倡导者编号单元格
        .advocate-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .advocate-icon {
                color: #409eff;
                font-size: 14px;
            }

            .advocate-number {
                font-weight: 500;
                color: #303133;
            }
        }

        // 倡导者信息单元格
        .advocate-info {
            .advocate-name {
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 4px;

                .advocate-icon {
                    color: #606266;
                    font-size: 12px;
                }

                span {
                    font-weight: 500;
                    color: #303133;
                }
            }

            .advocate-phone {
                display: flex;
                align-items: center;
                gap: 6px;

                .phone-icon {
                    color: #909399;
                    font-size: 12px;
                }

                .sensitive-data {
                    font-size: 12px;
                    color: #606266;
                    font-family: 'Courier New', monospace;
                }
            }
        }

        // 积分单元格
        .points-cell {
            text-align: right;

            .points {
                font-weight: 600;
                color: #e6a23c;
                font-size: 14px;
            }

            .referral-count {
                font-size: 12px;
                color: #909399;
                margin-top: 2px;
            }
        }

        // 时间单元格
        .datetime-cell {
            .date {
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
            }

            .time {
                font-size: 12px;
                color: #909399;
            }
        }

        // 图标样式
        .level-icon,
        .status-icon {
            margin-right: 4px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .advocate-list-view {
        .admin-table {
            font-size: 12px;

            .advocate-info {
                .advocate-name,
                .advocate-phone {
                    font-size: 11px;
                }
            }

            .points-cell {
                .points {
                    font-size: 13px;
                }

                .referral-count {
                    font-size: 11px;
                }
            }

            .datetime-cell {
                .date {
                    font-size: 12px;
                }

                .time {
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
