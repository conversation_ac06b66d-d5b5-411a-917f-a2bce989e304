<!--
/**
 * 倡导者分配管理页面
 *
 * 功能特性：
 * - 企业级分配管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 倡导者角色分配管理
 * - 数据导出和统计
 * - 响应式设计
 *
 * 路由路径：/admin/advocate/assignment
 * 页面标题：倡导者分配管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="advocate-assignment-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索倡导者姓名、角色名称或分配状态'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增分配
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出分配
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchRevoke"
                    type="warning"
                    @click="handleBatchRevoke"
                >
                    <i class="fas fa-times"></i>
                    批量撤销 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="分配状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in ASSIGNMENT_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="角色类型">
                            <el-select v-model="filters.role_type" clearable placeholder="选择角色类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="系统角色" value="system"></el-option>
                                <el-option label="业务角色" value="business"></el-option>
                                <el-option label="自定义角色" value="custom"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="倡导者等级">
                            <el-select v-model="filters.advocate_level" clearable placeholder="选择等级">
                                <el-option label="全部等级" value=""></el-option>
                                <el-option label="初级倡导者" value="junior"></el-option>
                                <el-option label="中级倡导者" value="intermediate"></el-option>
                                <el-option label="高级倡导者" value="senior"></el-option>
                                <el-option label="专家倡导者" value="expert"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分配日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 分配列表 -->
            <template #default>
                <AssignmentListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总分配: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已生效: <strong>{{ activeCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import AssignmentListView from './components/AssignmentListView.vue'

export default {
    name: 'AdminAdvocateAssignmentIndexPage',
    components: {
        BackendPageListLayout,
        AssignmentListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                role_type: '',
                advocate_level: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            activeCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部分配', icon: 'fas fa-list', badge: 0},
                {name: 'active', label: '已生效', icon: 'fas fa-check-circle', badge: 0},
                {name: 'pending', label: '待生效', icon: 'fas fa-clock', badge: 0},
                {name: 'expired', label: '已过期', icon: 'fas fa-times-circle', badge: 0},
                {name: 'revoked', label: '已撤销', icon: 'fas fa-ban', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 分配状态标签
            ASSIGNMENT_STATUS_LABELS: {
                'active': '已生效',
                'pending': '待生效',
                'expired': '已过期',
                'revoked': '已撤销'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchRevoke() {
            return this.selectedRows.some(row => ['active', 'pending'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟分配数据
                const mockData = [
                    {
                        id: 1,
                        advocate_name: '张倡导',
                        advocate_level: 'senior',
                        role_name: '高级倡导者',
                        role_type: 'business',
                        assigned_by: '管理员',
                        status: 'active',
                        assigned_at: '2024-01-15 10:30:00',
                        expires_at: '2024-12-31 23:59:59'
                    },
                    {
                        id: 2,
                        advocate_name: '李推广',
                        advocate_level: 'intermediate',
                        role_name: '中级倡导者',
                        role_type: 'business',
                        assigned_by: '管理员',
                        status: 'active',
                        assigned_at: '2024-01-15 11:20:00',
                        expires_at: '2024-12-31 23:59:59'
                    },
                    {
                        id: 3,
                        advocate_name: '王宣传',
                        advocate_level: 'junior',
                        role_name: '初级倡导者',
                        role_type: 'business',
                        assigned_by: '管理员',
                        status: 'pending',
                        assigned_at: '2024-01-15 12:45:00',
                        expires_at: '2024-12-31 23:59:59'
                    },
                    {
                        id: 4,
                        advocate_name: '赵营销',
                        advocate_level: 'expert',
                        role_name: '倡导者管理员',
                        role_type: 'system',
                        assigned_by: '超级管理员',
                        status: 'active',
                        assigned_at: '2024-01-14 16:20:00',
                        expires_at: '2024-12-31 23:59:59'
                    }
                ]

                this.dataList = mockData
                this.total = 234
                this.activeCount = 189
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.activeCount
            this.tabOptions[2].badge = 23
            this.tabOptions[3].badge = 12
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                role_type: '',
                advocate_level: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('新增分配功能开发中')
            // TODO: 跳转到新增页面
            // this.$router.push('/admin/advocate/assignment/create')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('分配数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchRevoke() {
            const revokableAssignments = this.selectedRows.filter(row =>
                ['active', 'pending'].includes(row.status)
            )

            if (revokableAssignments.length === 0) {
                this.$message.warning('没有可撤销的分配')
                return
            }

            this.$confirm(`确定要撤销选中的 ${revokableAssignments.length} 个分配吗？`, '确认批量撤销', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功撤销 ${revokableAssignments.length} 个分配`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个分配吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${this.selectedRows.length} 个分配`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看分配详情: ${row.advocate_name} - ${row.role_name}`)
            // TODO: 跳转到详情页面
            // this.$router.push(`/admin/advocate/assignment/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑分配: ${row.advocate_name} - ${row.role_name}`)
            // TODO: 跳转到编辑页面
            // this.$router.push(`/admin/advocate/assignment/edit/${row.id}`)
        },

        handleStatusChange(row, status) {
            this.$confirm(`确定要将分配 "${row.advocate_name} - ${row.role_name}" 状态更改为 "${this.ASSIGNMENT_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`分配状态已更新为: ${this.ASSIGNMENT_STATUS_LABELS[status]}`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消状态变更')
            })
        },

        handleDelete(row) {
            this.$confirm(`确定要删除分配 "${row.advocate_name} - ${row.role_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('分配删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.advocate-assignment-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .el-form-item {
            margin-bottom: 16px;
        }
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .advocate-assignment-index-page {
        .advanced-search-form {
            padding: 12px;

            .el-form-item {
                margin-bottom: 12px;
            }
        }

        .footer-stats {
            flex-direction: column;
            gap: 8px;
        }
    }
}
</style>
