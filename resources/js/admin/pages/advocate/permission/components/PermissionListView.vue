<!--
/**
 * 权限列表视图组件
 *
 * 功能特性：
 * - 权限数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="permission-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 权限名称 -->
            <el-table-column
                label="权限名称"
                min-width="160"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="permission-cell">
                        <i class="fas fa-key permission-icon"></i>
                        <span class="permission-name">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 权限标识 -->
            <el-table-column
                label="权限标识"
                min-width="160"
                prop="code"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="code-cell">
                        <code class="permission-code">{{ row.code }}</code>
                    </div>
                </template>
            </el-table-column>

            <!-- 权限描述 -->
            <el-table-column
                label="权限描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="description-cell">
                        <span class="description-text">{{ row.description }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 权限类型 -->
            <el-table-column
                align="center"
                label="权限类型"
                prop="type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        <i :class="getTypeIcon(row.type)" class="type-icon"></i>
                        {{ getTypeName(row.type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 所属模块 -->
            <el-table-column
                align="center"
                label="所属模块"
                prop="module"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getModuleTagType(row.module)"
                        size="small"
                    >
                        <i :class="getModuleIcon(row.module)" class="module-icon"></i>
                        {{ getModuleName(row.module) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 权限状态 -->
            <el-table-column
                align="center"
                label="权限状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        <i :class="getStatusIcon(row.status)" class="status-icon"></i>
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.created_at) }}</div>
                        <div class="time">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="canEdit(row)"
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-if="row.status === 'disabled'"
                                    @click="handleStatusChange(row, 'enabled')"
                                >
                                    <i class="fas fa-check"></i>
                                    启用权限
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'enabled'"
                                    @click="handleStatusChange(row, 'disabled')"
                                >
                                    <i class="fas fa-ban"></i>
                                    禁用权限
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'pending'"
                                    @click="handleStatusChange(row, 'enabled')"
                                >
                                    <i class="fas fa-check-circle"></i>
                                    审核通过
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除权限
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'PermissionListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'status-change',
        'delete',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'enabled': '已启用',
                'disabled': '已禁用',
                'pending': '待审核'
            },
            typeMap: {
                'system': '系统权限',
                'business': '业务权限',
                'data': '数据权限',
                'operation': '操作权限'
            },
            moduleMap: {
                'advocate': '倡导者管理',
                'user': '用户管理',
                'order': '订单管理',
                'system': '系统设置'
            }
        }
    },
    methods: {
        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑权限
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 删除权限
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        },

        // 格式化日期
        formatDate(datetime) {
            return datetime.split(' ')[0]
        },

        // 格式化时间
        formatTime(datetime) {
            return datetime.split(' ')[1]
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                'enabled': 'fas fa-check-circle',
                'disabled': 'fas fa-ban',
                'pending': 'fas fa-clock'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'enabled': 'success',
                'disabled': 'danger',
                'pending': 'warning'
            }
            return typeMap[status] || 'info'
        },

        // 获取类型名称
        getTypeName(type) {
            return this.typeMap[type] || type
        },

        // 获取类型图标
        getTypeIcon(type) {
            const iconMap = {
                'system': 'fas fa-cog',
                'business': 'fas fa-briefcase',
                'data': 'fas fa-database',
                'operation': 'fas fa-tools'
            }
            return iconMap[type] || 'fas fa-key'
        },

        // 获取类型标签类型
        getTypeTagType(type) {
            const typeMap = {
                'system': 'danger',
                'business': 'primary',
                'data': 'warning',
                'operation': 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取模块名称
        getModuleName(module) {
            return this.moduleMap[module] || module
        },

        // 获取模块图标
        getModuleIcon(module) {
            const iconMap = {
                'advocate': 'fas fa-bullhorn',
                'user': 'fas fa-users',
                'order': 'fas fa-receipt',
                'system': 'fas fa-cog'
            }
            return iconMap[module] || 'fas fa-cube'
        },

        // 获取模块标签类型
        getModuleTagType(module) {
            const typeMap = {
                'advocate': 'success',
                'user': 'primary',
                'order': 'warning',
                'system': 'danger'
            }
            return typeMap[module] || 'info'
        },

        // 判断是否可以编辑
        canEdit(row) {
            return ['enabled', 'disabled', 'pending'].includes(row.status)
        }
    }
}
</script>

<style lang="scss" scoped>
.permission-list-view {
    .admin-table {
        // 权限名称单元格
        .permission-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .permission-icon {
                color: #409eff;
                font-size: 14px;
            }

            .permission-name {
                font-weight: 500;
                color: #303133;
            }
        }

        // 权限标识单元格
        .code-cell {
            .permission-code {
                background-color: #f5f7fa;
                color: #606266;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                border: 1px solid #e4e7ed;
            }
        }

        // 描述单元格
        .description-cell {
            .description-text {
                color: #606266;
                font-size: 13px;
                line-height: 1.4;
            }
        }

        // 时间单元格
        .datetime-cell {
            .date {
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
            }

            .time {
                font-size: 12px;
                color: #909399;
            }
        }

        // 图标样式
        .type-icon,
        .module-icon,
        .status-icon {
            margin-right: 4px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .permission-list-view {
        .admin-table {
            font-size: 12px;

            .permission-cell {
                .permission-name {
                    font-size: 12px;
                }
            }

            .code-cell {
                .permission-code {
                    font-size: 11px;
                    padding: 1px 4px;
                }
            }

            .description-cell {
                .description-text {
                    font-size: 12px;
                }
            }

            .datetime-cell {
                .date {
                    font-size: 12px;
                }

                .time {
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
