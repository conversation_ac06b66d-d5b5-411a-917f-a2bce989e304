<!--
/**
 * 账单管理页面
 *
 * 功能特性：
 * - 企业级账单管理系统
 * - 账单状态跟踪和处理
 * - 高级搜索和批量操作
 * - 财务数据统计分析
 * - 数据导出和报表
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/bill/bill
 * 页面标题：账单管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
  <div class="bill-index-page">
    <BackendPageListLayout
        :active-tab-value="activeTab"
        :current-page="currentPage"
        :loading="loading"
        :page-size="pageSize"
        :search-placeholder="'搜索账单号、客户姓名、手机号或备注信息'"
        :search-query="searchQuery"
        :show-advanced-search="showAdvancedSearch"
        :show-bottom-pagination="true"
        :show-footer="true"
        :show-header="true"
        :show-tabs="true"
        :show-top-pagination="true"
        :tab-options="tabOptions"
        :total="total"
        :v_loading="v_loading"
        @tab-change="handleTabChange"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @search-input="handleSearchInput"
        @search-clear="handleSearchClear"
    >
      <!-- 左侧操作按钮 -->
      <template #header-left>
        <el-button type="primary" @click="handleCreate">
          <i class="fas fa-plus"></i>
          新增账单
        </el-button>
        <el-button :loading="loading" type="primary" @click="handleRefresh">
          <i class="fas fa-sync-alt"></i>
          刷新
        </el-button>
        <el-button :loading="exportLoading" type="success" @click="handleExport">
          <i class="fas fa-download"></i>
          导出账单
        </el-button>
        <el-button
            v-if="hasSelectedRows"
            :disabled="!canBatchConfirm"
            type="warning"
            @click="handleBatchConfirm"
        >
          <i class="fas fa-check"></i>
          批量确认 ({{ selectedRows.length }})
        </el-button>
        <el-button
            v-if="hasSelectedRows"
            :disabled="!canBatchSend"
            type="info"
            @click="handleBatchSend"
        >
          <i class="fas fa-paper-plane"></i>
          批量发送 ({{ selectedRows.length }})
        </el-button>
      </template>

      <!-- 高级搜索面板 -->
      <template #advanced-search>
        <div class="advanced-search-form">
          <el-form :model="filters" inline label-width="100px">
            <el-form-item label="账单状态">
              <el-select v-model="filters.status" clearable placeholder="选择状态">
                <el-option label="全部状态" value=""></el-option>
                <el-option
                    v-for="(label, status) in BILL_STATUS_LABELS"
                    :key="status"
                    :label="label"
                    :value="status"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="账单类型">
              <el-select v-model="filters.type" clearable placeholder="选择类型">
                <el-option label="全部类型" value=""></el-option>
                <el-option label="收入账单" value="income"></el-option>
                <el-option label="支出账单" value="expense"></el-option>
                <el-option label="退款账单" value="refund"></el-option>
                <el-option label="调整账单" value="adjustment"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="账单日期">
              <el-date-picker
                  v-model="dateRange"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                  @change="handleDateRangeChange"
              />
            </el-form-item>
            <el-form-item label="账单金额">
              <el-input-number
                  v-model="filters.min_amount"
                  :min="0"
                  :precision="2"
                  placeholder="最小金额"
                  style="width: 120px; margin-right: 8px;"
              />
              <span style="margin: 0 8px;">-</span>
              <el-input-number
                  v-model="filters.max_amount"
                  :min="0"
                  :precision="2"
                  placeholder="最大金额"
                  style="width: 120px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleAdvancedSearch">
                <i class="fas fa-search"></i>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <i class="fas fa-undo"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>

      <!-- 账单列表 -->
      <template #default>
        <BillListView
            :data-list="dataList"
            :loading="loading"
            :search-query="searchQuery"
            @delete="handleDelete"
            @edit="handleEdit"
            @selection-change="handleSelectionChange"
            @data-change="fetchData"
            @view-detail="handleViewDetail"
            @status-change="handleStatusChange"
        />
      </template>

      <!-- 底部信息 -->
      <template #footer-left-section>
        <div class="footer-stats">
					<span class="stat-item">
						总账单: <strong>{{ total }}</strong>
					</span>
          <span class="stat-item">
						总金额: <strong>¥{{ formatAmount(totalAmount) }}</strong>
					</span>
          <span class="stat-item">
						收入: <strong class="income">¥{{ formatAmount(incomeAmount) }}</strong>
					</span>
          <span class="stat-item">
						支出: <strong class="expense">¥{{ formatAmount(expenseAmount) }}</strong>
					</span>
          <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
        </div>
      </template>
    </BackendPageListLayout>
  </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import BillListView from './components/BillListView.vue'

export default {
  name: 'AdminBillBillIndexPage',
  components: {
    BackendPageListLayout,
    BillListView
  },
  data() {
    return {
      // 基础状态
      loading: false,
      exportLoading: false,
      v_loading: false,

      // 搜索和筛选
      searchQuery: '',
      showAdvancedSearch: false,
      filters: {
        status: '',
        type: '',
        start_date: '',
        end_date: '',
        min_amount: null,
        max_amount: null
      },
      dateRange: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      totalAmount: 0,
      incomeAmount: 0,
      expenseAmount: 0,

      // Tab选项
      activeTab: 'all',
      tabOptions: [
        {name: 'all', label: '全部账单', icon: 'fas fa-list', badge: 0},
        {name: 'pending', label: '待确认', icon: 'fas fa-clock', badge: 0},
        {name: 'confirmed', label: '已确认', icon: 'fas fa-check-circle', badge: 0},
        {name: 'paid', label: '已支付', icon: 'fas fa-credit-card', badge: 0},
        {name: 'income', label: '收入账单', icon: 'fas fa-arrow-up', badge: 0},
        {name: 'expense', label: '支出账单', icon: 'fas fa-arrow-down', badge: 0}
      ],

      // 数据
      dataList: [],
      selectedRows: [],

      // 账单状态标签
      BILL_STATUS_LABELS: {
        'draft': '草稿',
        'pending': '待确认',
        'confirmed': '已确认',
        'paid': '已支付',
        'cancelled': '已取消',
        'overdue': '已逾期'
      }
    }
  },
  computed: {
    hasSelectedRows() {
      return this.selectedRows.length > 0
    },
    canBatchConfirm() {
      return this.selectedRows.some(row => row.status === 'pending')
    },
    canBatchSend() {
      return this.selectedRows.some(row => ['confirmed', 'overdue'].includes(row.status))
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取数据 (兼容模板命名)
    async fetchData() {
      return this.loadData()
    },

    // 加载数据
    async loadData() {
      try {
        this.v_loading = true
        this.loading = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟账单数据（敏感信息脱敏）
        const mockData = [
          {
            id: 1,
            bill_no: 'BILL202401150001',
            customer_name: '张三',
            customer_phone: '138****5678', // 手机号脱敏
            customer_email: 'zha***@example.com', // 邮箱脱敏
            amount: 2999.00,
            type: 'income',
            status: 'confirmed',
            due_date: '2024-02-15',
            description: '产品销售收入',
            created_at: '2024-01-15 10:30:00',
            updated_at: '2024-01-15 10:35:00'
          },
          {
            id: 2,
            bill_no: 'BILL202401150002',
            customer_name: '李四',
            customer_phone: '139****1234',
            customer_email: 'li***@example.com',
            amount: 1580.50,
            type: 'expense',
            status: 'paid',
            due_date: '2024-02-20',
            description: '办公用品采购',
            created_at: '2024-01-15 11:20:00',
            updated_at: '2024-01-15 14:30:00'
          },
          {
            id: 3,
            bill_no: 'BILL202401150003',
            customer_name: '王五',
            customer_phone: '136****9876',
            customer_email: 'wan***@example.com',
            amount: 899.90,
            type: 'income',
            status: 'pending',
            due_date: '2024-02-10',
            description: '服务费收入',
            created_at: '2024-01-15 12:45:00',
            updated_at: '2024-01-15 12:45:00'
          },
          {
            id: 4,
            bill_no: 'BILL202401150004',
            customer_name: '赵六',
            customer_phone: '135****4567',
            customer_email: 'zha***@example.com',
            amount: 4568.80,
            type: 'refund',
            status: 'confirmed',
            due_date: '2024-02-25',
            description: '产品退款',
            created_at: '2024-01-14 16:20:00',
            updated_at: '2024-01-15 09:15:00'
          }
        ]

        this.dataList = mockData
        this.total = 856
        this.totalAmount = 125680.50
        this.incomeAmount = 89456.30
        this.expenseAmount = 36224.20
        this.updateTabBadges()

      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败，请重试')
      } finally {
        this.v_loading = false
        this.loading = false
      }
    },

    // 更新Tab徽章
    updateTabBadges() {
      this.tabOptions[0].badge = this.total
      this.tabOptions[1].badge = 126
      this.tabOptions[2].badge = 423
      this.tabOptions[3].badge = 298
      this.tabOptions[4].badge = 456
      this.tabOptions[5].badge = 234
    },

    // 格式化金额
    formatAmount(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // Tab切换
    handleTabChange(tab) {
      this.activeTab = tab.name
      this.currentPage = 1
      this.loadData()
    },

    // 分页处理
    handlePageChange(page) {
      this.currentPage = page
      this.loadData()
    },

    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadData()
    },

    // 搜索处理
    handleSearchInput(value) {
      this.searchQuery = value
      this.currentPage = 1
      this.loadData()
    },

    handleSearchClear() {
      this.searchQuery = ''
      this.currentPage = 1
      this.loadData()
    },

    // 高级搜索
    handleAdvancedSearch() {
      this.currentPage = 1
      this.loadData()
    },

    handleDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.filters.start_date = dates[0]
        this.filters.end_date = dates[1]
      } else {
        this.filters.start_date = ''
        this.filters.end_date = ''
      }
    },

    resetFilters() {
      this.filters = {
        status: '',
        type: '',
        start_date: '',
        end_date: '',
        min_amount: null,
        max_amount: null
      }
      this.dateRange = []
      this.handleAdvancedSearch()
    },

    // 操作处理
    handleCreate() {
      this.$message.info('跳转到新增账单页面')
      // TODO: 跳转到账单创建页面
      // this.$router.push('/admin/bill/bill/create')
    },

    handleRefresh() {
      this.loadData()
    },

    async handleExport() {
      try {
        this.exportLoading = true

        // 模拟导出
        await new Promise(resolve => setTimeout(resolve, 2000))

        this.$message.success('账单数据导出成功')

      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
      } finally {
        this.exportLoading = false
      }
    },

    // 批量操作
    handleBatchConfirm() {
      const pendingBills = this.selectedRows.filter(row => row.status === 'pending')

      if (pendingBills.length === 0) {
        this.$message.warning('没有可确认的账单')
        return
      }

      this.$confirm(`确定要确认选中的 ${pendingBills.length} 个账单吗？`, '确认批量确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`成功确认 ${pendingBills.length} 个账单`)
        this.selectedRows = []
        this.loadData()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    handleBatchSend() {
      const sendableBills = this.selectedRows.filter(row =>
          ['confirmed', 'overdue'].includes(row.status)
      )

      if (sendableBills.length === 0) {
        this.$message.warning('没有可发送的账单')
        return
      }

      this.$confirm(`确定要发送选中的 ${sendableBills.length} 个账单吗？`, '确认批量发送', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`成功发送 ${sendableBills.length} 个账单`)
        this.selectedRows = []
        this.loadData()
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 列表操作
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    handleViewDetail(row) {
      this.$message.info(`查看账单详情: ${row.bill_no}`)
      // TODO: 跳转到账单详情页面
      // this.$router.push(`/admin/bill/bill/detail/${row.id}`)
    },

    handleEdit(row) {
      this.$message.info(`编辑账单: ${row.bill_no}`)
      // TODO: 跳转到账单编辑页面
      // this.$router.push(`/admin/bill/bill/edit/${row.id}`)
    },

    handleStatusChange(row, status) {
      this.$confirm(`确定要将账单 "${row.bill_no}" 状态更改为 "${this.BILL_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success(`账单状态已更新为: ${this.BILL_STATUS_LABELS[status]}`)
        this.loadData()
      }).catch(() => {
        this.$message.info('已取消状态变更')
      })
    },

    handleDelete(row) {
      this.$confirm(`确定要删除账单 "${row.bill_no}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('账单删除成功')
        this.loadData()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bill-index-page {
  .advanced-search-form {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;

    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .footer-stats {
    display: flex;
    gap: 16px;

    .stat-item {
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
        font-weight: 600;

        &.income {
          color: #67c23a;
        }

        &.expense {
          color: #f56c6c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bill-index-page {
    .advanced-search-form {
      padding: 12px;

      .el-form-item {
        margin-bottom: 12px;
      }
    }

    .footer-stats {
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
