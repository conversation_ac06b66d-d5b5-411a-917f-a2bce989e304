<!--
/**
 * 分类管理页面
 *
 * 功能特性：
 * - 企业级分类管理系统
 * - 分类层级管理和排序
 * - 高级搜索和批量操作
 * - 分类状态管理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/category/category
 * 页面标题：分类管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="category-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索分类名称、描述或关键词'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增分类
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出分类
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDisable"
                    type="warning"
                    @click="handleBatchDisable"
                >
                    <i class="fas fa-ban"></i>
                    批量禁用 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="分类状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in CATEGORY_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分类类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="商品分类" value="product"></el-option>
                                <el-option label="内容分类" value="content"></el-option>
                                <el-option label="服务分类" value="service"></el-option>
                                <el-option label="其他分类" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="父级分类">
                            <el-select v-model="filters.parent_id" clearable placeholder="选择父级分类">
                                <el-option label="顶级分类" value="0"></el-option>
                                <el-option label="有父级分类" value="has_parent"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 分类列表 -->
            <template #default>
                <CategoryListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总分类: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						启用分类: <strong>{{ enabledCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import CategoryListView from './components/CategoryListView.vue'

export default {
    name: 'AdminCategoryCategoryIndexPage',
    components: {
        BackendPageListLayout,
        CategoryListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                parent_id: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            enabledCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部分类', icon: 'fas fa-list', badge: 0},
                {name: 'enabled', label: '启用中', icon: 'fas fa-check-circle', badge: 0},
                {name: 'disabled', label: '已禁用', icon: 'fas fa-ban', badge: 0},
                {name: 'product', label: '商品分类', icon: 'fas fa-cube', badge: 0},
                {name: 'content', label: '内容分类', icon: 'fas fa-file-alt', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 分类状态标签
            CATEGORY_STATUS_LABELS: {
                'enabled': '启用',
                'disabled': '禁用'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchDisable() {
            return this.selectedRows.some(row => row.status === 'enabled')
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟分类数据
                const mockData = [
                    {
                        id: 1,
                        name: '电子产品',
                        slug: 'electronics',
                        description: '各类电子产品分类',
                        type: 'product',
                        parent_id: 0,
                        level: 1,
                        sort_order: 1,
                        status: 'enabled',
                        children_count: 5,
                        product_count: 128,
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        name: '服装鞋帽',
                        slug: 'clothing',
                        description: '服装、鞋子、帽子等分类',
                        type: 'product',
                        parent_id: 0,
                        level: 1,
                        sort_order: 2,
                        status: 'enabled',
                        children_count: 8,
                        product_count: 256,
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        name: '新闻资讯',
                        slug: 'news',
                        description: '新闻和资讯内容分类',
                        type: 'content',
                        parent_id: 0,
                        level: 1,
                        sort_order: 3,
                        status: 'enabled',
                        children_count: 3,
                        product_count: 89,
                        created_at: '2024-01-15 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    },
                    {
                        id: 4,
                        name: '技术服务',
                        slug: 'tech-service',
                        description: '技术支持和服务分类',
                        type: 'service',
                        parent_id: 0,
                        level: 1,
                        sort_order: 4,
                        status: 'disabled',
                        children_count: 2,
                        product_count: 45,
                        created_at: '2024-01-14 16:20:00',
                        updated_at: '2024-01-15 09:15:00'
                    }
                ]

                this.dataList = mockData
                this.total = 156
                this.enabledCount = 134
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.enabledCount
            this.tabOptions[2].badge = this.total - this.enabledCount
            this.tabOptions[3].badge = 89
            this.tabOptions[4].badge = 67
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                parent_id: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('跳转到新增分类页面')
            // TODO: 跳转到分类创建页面
            // this.$router.push('/admin/category/category/create')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('分类数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchDisable() {
            const enabledCategories = this.selectedRows.filter(row => row.status === 'enabled')

            if (enabledCategories.length === 0) {
                this.$message.warning('没有可禁用的分类')
                return
            }

            this.$confirm(`确定要禁用选中的 ${enabledCategories.length} 个分类吗？`, '确认批量禁用', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功禁用 ${enabledCategories.length} 个分类`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个分类吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${this.selectedRows.length} 个分类`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看分类详情: ${row.name}`)
            // TODO: 跳转到分类详情页面
            // this.$router.push(`/admin/category/category/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑分类: ${row.name}`)
            // TODO: 跳转到分类编辑页面
            // this.$router.push(`/admin/category/category/edit/${row.id}`)
        },

        handleStatusChange(row, status) {
            this.$confirm(`确定要将分类 "${row.name}" 状态更改为 "${this.CATEGORY_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`分类状态已更新为: ${this.CATEGORY_STATUS_LABELS[status]}`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消状态变更')
            })
        },

        handleDelete(row) {
            this.$confirm(`确定要删除分类 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('分类删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.category-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .el-form-item {
            margin-bottom: 16px;
        }
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .category-index-page {
        .advanced-search-form {
            padding: 12px;

            .el-form-item {
                margin-bottom: 12px;
            }
        }

        .footer-stats {
            flex-direction: column;
            gap: 8px;
        }
    }
}
</style>
