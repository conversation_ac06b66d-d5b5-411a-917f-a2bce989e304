<!--
/**
 * 版权代理管理页面
 *
 * 功能特性：
 * - 企业级版权代理管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 代理状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/copyright/agent
 * 页面标题：版权代理管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-agent-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索代理名称、联系人或联系方式'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增代理
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="代理状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="活跃" value="active"></el-option>
                                <el-option label="暂停" value="suspended"></el-option>
                                <el-option label="终止" value="terminated"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="代理类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="独家代理" value="exclusive"></el-option>
                                <el-option label="非独家代理" value="non_exclusive"></el-option>
                                <el-option label="区域代理" value="regional"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="签约日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 代理列表 -->
            <template #default>
                <CopyrightAgentListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总代理: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						活跃代理: <strong>{{ activeAgentCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import CopyrightAgentListView from './components/CopyrightAgentListView.vue'

export default {
    name: 'AdminCopyrightAgentIndexPage',
    components: {
        BackendPageListLayout,
        CopyrightAgentListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            activeAgentCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部代理', icon: 'fas fa-list', badge: 0},
                {name: 'active', label: '活跃代理', icon: 'fas fa-check-circle', badge: 0},
                {name: 'suspended', label: '暂停代理', icon: 'fas fa-pause-circle', badge: 0},
                {name: 'terminated', label: '终止代理', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟版权代理数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        agent_name: '华夏版权代理有限公司',
                        contact_person: '张经理',
                        contact_phone: '138****5678',
                        contact_email: 'zha***@huaxia.com',
                        agent_type: 'exclusive',
                        status: 'active',
                        contract_start: '2024-01-01',
                        contract_end: '2025-12-31',
                        commission_rate: 15.0,
                        created_at: '2024-01-01 09:00:00',
                        updated_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        agent_name: '东方知识产权代理所',
                        contact_person: '李主任',
                        contact_phone: '139****1234',
                        contact_email: 'li***@dongfang.com',
                        agent_type: 'non_exclusive',
                        status: 'active',
                        contract_start: '2023-06-01',
                        contract_end: '2024-05-31',
                        commission_rate: 12.0,
                        created_at: '2023-06-01 14:20:00',
                        updated_at: '2024-01-10 16:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 156
                this.activeAgentCount = 89
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 89
            this.tabOptions[2].badge = 23
            this.tabOptions[3].badge = 44
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('新增版权代理功能开发中')
            // TODO: 跳转到新增页面
            // this.$router.push('/admin/copyright/agent/create')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('版权代理数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个代理吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${this.selectedRows.length} 个代理`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看代理详情: ${row.agent_name}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑代理: ${row.agent_name}`)
        },

        handleStatusChange(row, status) {
            this.$message.success(`代理状态已更新为: ${status}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除代理 "${row.agent_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style scoped>
.copyright-agent-index-page {
    height: 100%;
}

.advanced-search-form {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.footer-stats {
    display: flex;
    gap: 20px;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.stat-item strong {
    color: #333;
    margin-left: 4px;
}
</style>
