<!--
/**
 * 版权列表视图组件
 *
 * 功能特性：
 * - 版权数据表格展示
 * - 多选和批量操作
 * - 状态标签和操作按钮
 * - 响应式设计
 * - 敏感信息脱敏
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            empty-text="暂无版权数据"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <!-- 多选列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 版权号 -->
            <el-table-column label="版权号" prop="copyright_no" show-overflow-tooltip width="160">
                <template #default="{ row }">
                    <el-link type="primary" @click="$emit('view-detail', row)">
                        {{ row.copyright_no }}
                    </el-link>
                </template>
            </el-table-column>

            <!-- 作品名称 -->
            <el-table-column label="作品名称" min-width="200" prop="work_name" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="work-info">
                        <div class="work-name">{{ row.work_name }}</div>
                        <div class="work-type">{{ getCopyrightTypeLabel(row.copyright_type) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 版权持有人 -->
            <el-table-column label="版权持有人" prop="holder_name" show-overflow-tooltip width="150">
                <template #default="{ row }">
                    <div class="holder-info">
                        <div class="holder-name">{{ row.holder_name }}</div>
                        <div class="holder-contact">{{ row.holder_phone }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 代理机构 -->
            <el-table-column label="代理机构" prop="agent_name" show-overflow-tooltip width="180"/>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" prop="status" width="100">
                <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 申请日期 -->
            <el-table-column align="center" label="申请日期" prop="apply_date" width="120"/>

            <!-- 到期日期 -->
            <el-table-column align="center" label="到期日期" prop="expire_date" width="120">
                <template #default="{ row }">
                    <span :class="{ 'text-danger': isExpiringSoon(row.expire_date) }">
                        {{ row.expire_date }}
                    </span>
                </template>
            </el-table-column>

            <!-- 操作 -->
            <el-table-column align="center" fixed="right" label="操作" width="200">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            title="查看详情"
                            type="primary"
                            @click="$emit('view-detail', row)"
                        >
                            <i class="fas fa-eye"></i>
                        </el-button>
                        <el-button
                            size="small"
                            title="编辑"
                            type="success"
                            @click="$emit('edit', row)"
                        >
                            <i class="fas fa-edit"></i>
                        </el-button>
                        <el-dropdown @command="(command) => handleStatusCommand(command, row)">
                            <el-button size="small" title="状态操作" type="warning">
                                <i class="fas fa-cog"></i>
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="active"
                                    >
                                        <i class="fas fa-check"></i> 批准
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="rejected"
                                    >
                                        <i class="fas fa-times"></i> 拒绝
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="suspended"
                                    >
                                        <i class="fas fa-pause"></i> 暂停
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="expired"
                                    >
                                        <i class="fas fa-clock"></i> 设为过期
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-button
                            :disabled="!canDelete(row)"
                            size="small"
                            title="删除"
                            type="danger"
                            @click="$emit('delete', row)"
                        >
                            <i class="fas fa-trash"></i>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'CopyrightListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'delete'
    ],
    data() {
        return {
            // 版权类型标签
            COPYRIGHT_TYPE_LABELS: {
                'text': '文字作品',
                'music': '音乐作品',
                'art': '美术作品',
                'photo': '摄影作品',
                'video': '影视作品',
                'software': '软件作品'
            },
            // 状态标签
            STATUS_LABELS: {
                'active': '有效',
                'pending': '审核中',
                'expired': '已过期',
                'rejected': '已拒绝',
                'suspended': '已暂停'
            }
        }
    },
    methods: {
        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 获取版权类型标签
        getCopyrightTypeLabel(type) {
            return this.COPYRIGHT_TYPE_LABELS[type] || type
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.STATUS_LABELS[status] || status
        },

        // 获取状态标签类型
        getStatusTagType(status) {
            const typeMap = {
                'active': 'success',
                'pending': 'warning',
                'expired': 'danger',
                'rejected': 'danger',
                'suspended': 'info'
            }
            return typeMap[status] || 'info'
        },

        // 检查是否即将到期
        isExpiringSoon(expireDate) {
            if (!expireDate) return false
            const expire = new Date(expireDate)
            const now = new Date()
            const diffTime = expire.getTime() - now.getTime()
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
            return diffDays <= 30 && diffDays > 0 // 30天内到期
        },

        // 检查是否可以删除
        canDelete(row) {
            return ['pending', 'rejected', 'expired'].includes(row.status)
        },

        // 处理状态操作命令
        handleStatusCommand(command, row) {
            this.$emit('status-change', row, command)
        }
    }
}
</script>

<style lang="scss" scoped>
.copyright-list-view {
    .work-info {
        .work-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
        }

        .work-type {
            font-size: 12px;
            color: #909399;
        }
    }

    .holder-info {
        .holder-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
        }

        .holder-contact {
            font-size: 12px;
            color: #909399;
        }
    }

    .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        flex-wrap: wrap;

        .el-button {
            padding: 5px 8px;
        }
    }

    .text-danger {
        color: #f56c6c;
        font-weight: 500;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .copyright-list-view {
        .action-buttons {
            flex-direction: column;
            gap: 2px;

            .el-button {
                width: 100%;
                margin: 0;
            }
        }
    }
}
</style>
