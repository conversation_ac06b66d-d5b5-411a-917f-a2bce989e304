<!--
/**
 * 版权持有人列表视图组件
 *
 * 功能特性：
 * - 持有人数据表格展示
 * - 多选和批量操作
 * - 状态标签和操作按钮
 * - 响应式设计
 * - 敏感信息脱敏
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-holder-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            empty-text="暂无持有人数据"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <!-- 多选列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 持有人姓名/名称 -->
            <el-table-column label="持有人姓名/名称" min-width="180" prop="holder_name" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="holder-info">
                        <el-link type="primary" @click="$emit('view-detail', row)">
                            {{ row.holder_name }}
                        </el-link>
                        <div class="holder-type">{{ getHolderTypeLabel(row.holder_type) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 证件信息 -->
            <el-table-column label="证件信息" prop="id_card" show-overflow-tooltip width="160">
                <template #default="{ row }">
                    <div class="id-info">
                        <div class="id-type">{{ getIdTypeLabel(row.holder_type) }}</div>
                        <div class="id-number">{{ row.id_card }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 联系方式 -->
            <el-table-column label="联系方式" show-overflow-tooltip width="150">
                <template #default="{ row }">
                    <div class="contact-info">
                        <div class="phone">{{ row.phone }}</div>
                        <div class="email">{{ row.email }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 所在地区 -->
            <el-table-column label="所在地区" prop="region" show-overflow-tooltip width="100"/>

            <!-- 版权统计 -->
            <el-table-column align="center" label="版权统计" width="120">
                <template #default="{ row }">
                    <div class="copyright-stats">
                        <div class="copyright-count">数量: {{ row.copyright_count }}</div>
                        <div class="total-value">价值: ¥{{ formatAmount(row.total_value) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 认证状态 -->
            <el-table-column align="center" label="认证状态" prop="status" width="100">
                <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 注册时间 -->
            <el-table-column align="center" label="注册时间" prop="created_at" width="160">
                <template #default="{ row }">
                    {{ formatDateTime(row.created_at) }}
                </template>
            </el-table-column>

            <!-- 操作 -->
            <el-table-column align="center" fixed="right" label="操作" width="200">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            title="查看详情"
                            type="primary"
                            @click="$emit('view-detail', row)"
                        >
                            <i class="fas fa-eye"></i>
                        </el-button>
                        <el-button
                            size="small"
                            title="编辑"
                            type="success"
                            @click="$emit('edit', row)"
                        >
                            <i class="fas fa-edit"></i>
                        </el-button>
                        <el-dropdown @command="(command) => handleStatusCommand(command, row)">
                            <el-button size="small" title="状态操作" type="warning">
                                <i class="fas fa-cog"></i>
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="verified"
                                    >
                                        <i class="fas fa-check"></i> 认证通过
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="rejected"
                                    >
                                        <i class="fas fa-times"></i> 拒绝认证
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'verified'"
                                        command="suspended"
                                    >
                                        <i class="fas fa-pause"></i> 暂停
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'suspended'"
                                        command="verified"
                                    >
                                        <i class="fas fa-play"></i> 恢复
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-button
                            :disabled="!canDelete(row)"
                            size="small"
                            title="删除"
                            type="danger"
                            @click="$emit('delete', row)"
                        >
                            <i class="fas fa-trash"></i>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'CopyrightHolderListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'delete'
    ],
    data() {
        return {
            // 持有人类型标签
            HOLDER_TYPE_LABELS: {
                'individual': '个人',
                'company': '企业',
                'organization': '机构'
            },
            // 证件类型标签
            ID_TYPE_LABELS: {
                'individual': '身份证',
                'company': '统一社会信用代码',
                'organization': '组织机构代码'
            },
            // 状态标签
            STATUS_LABELS: {
                'verified': '已认证',
                'pending': '待认证',
                'rejected': '已拒绝',
                'suspended': '已暂停'
            }
        }
    },
    methods: {
        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 获取持有人类型标签
        getHolderTypeLabel(type) {
            return this.HOLDER_TYPE_LABELS[type] || type
        },

        // 获取证件类型标签
        getIdTypeLabel(type) {
            return this.ID_TYPE_LABELS[type] || '证件'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.STATUS_LABELS[status] || status
        },

        // 获取状态标签类型
        getStatusTagType(status) {
            const typeMap = {
                'verified': 'success',
                'pending': 'warning',
                'rejected': 'danger',
                'suspended': 'info'
            }
            return typeMap[status] || 'info'
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            })
        },

        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return ''
            return dateTime.replace(/:\d{2}$/, '') // 移除秒数
        },

        // 检查是否可以删除
        canDelete(row) {
            return ['pending', 'rejected'].includes(row.status)
        },

        // 处理状态操作命令
        handleStatusCommand(command, row) {
            this.$emit('status-change', row, command)
        }
    }
}
</script>

<style lang="scss" scoped>
.copyright-holder-list-view {
    .holder-info {
        .holder-type {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
        }
    }

    .id-info {
        .id-type {
            font-size: 12px;
            color: #909399;
            margin-bottom: 2px;
        }

        .id-number {
            font-size: 13px;
            color: #303133;
            font-family: monospace;
        }
    }

    .contact-info {
        .phone {
            font-size: 13px;
            color: #303133;
            margin-bottom: 2px;
        }

        .email {
            font-size: 12px;
            color: #909399;
        }
    }

    .copyright-stats {
        .copyright-count {
            font-size: 12px;
            color: #303133;
            margin-bottom: 2px;
        }

        .total-value {
            font-size: 12px;
            color: #67c23a;
            font-weight: 500;
        }
    }

    .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        flex-wrap: wrap;

        .el-button {
            padding: 5px 8px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .copyright-holder-list-view {
        .action-buttons {
            flex-direction: column;
            gap: 2px;

            .el-button {
                width: 100%;
                margin: 0;
            }
        }
    }
}
</style>
