<!--
/**
 * 版权持有人管理页面
 *
 * 功能特性：
 * - 企业级版权持有人管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 持有人状态跟踪和处理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/copyright/copyright_holder
 * 页面标题：版权持有人
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-holder-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索持有人姓名、身份证号或联系方式'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增持有人
                </el-button>
                <el-button :loading="exportLoading" type="info" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchVerify"
                    type="warning"
                    @click="handleBatchVerify"
                >
                    <i class="fas fa-check-circle"></i>
                    批量认证 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDelete"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="认证状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in HOLDER_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="持有人类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="个人" value="individual"></el-option>
                                <el-option label="企业" value="company"></el-option>
                                <el-option label="机构" value="organization"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="注册日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="所在地区">
                            <el-input
                                v-model="filters.region"
                                clearable
                                placeholder="输入地区名称"
                                style="width: 200px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 持有人列表 -->
            <template #default>
                <CopyrightHolderListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总持有人: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已认证: <strong>{{ verifiedHolderCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import CopyrightHolderListView from './components/CopyrightHolderListView.vue'

export default {
    name: 'AdminCopyrightHolderIndexPage',
    components: {
        BackendPageListLayout,
        CopyrightHolderListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                region: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            verifiedHolderCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部持有人', icon: 'fas fa-user-shield', badge: 0},
                {name: 'verified', label: '已认证', icon: 'fas fa-check-circle', badge: 0},
                {name: 'pending', label: '待认证', icon: 'fas fa-clock', badge: 0},
                {name: 'rejected', label: '已拒绝', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 持有人状态标签
            HOLDER_STATUS_LABELS: {
                'verified': '已认证',
                'pending': '待认证',
                'rejected': '已拒绝',
                'suspended': '已暂停'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchVerify() {
            return this.selectedRows.some(row => row.status === 'pending')
        },
        canBatchDelete() {
            return this.selectedRows.some(row => ['pending', 'rejected'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟持有人数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        holder_name: '张三',
                        holder_type: 'individual',
                        id_card: '110101****1234', // 身份证脱敏
                        phone: '138****5678', // 手机号脱敏
                        email: 'zha***@example.com', // 邮箱脱敏
                        region: '北京市',
                        address: '北京市朝阳区***小区',
                        status: 'verified',
                        copyright_count: 12,
                        total_value: 156000.00,
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        holder_name: '上海科技有限公司',
                        holder_type: 'company',
                        id_card: '91310000****0001', // 统一社会信用代码脱敏
                        phone: '139****1234',
                        email: 'inf***@example.com',
                        region: '上海市',
                        address: '上海市浦东新区***大厦',
                        status: 'pending',
                        copyright_count: 45,
                        total_value: 890000.00,
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 1234
                this.verifiedHolderCount = 987
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 987
            this.tabOptions[2].badge = 156
            this.tabOptions[3].badge = 91
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                start_date: '',
                end_date: '',
                region: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleCreate() {
            this.$message.info('跳转到新增持有人页面')
            // TODO: 跳转到新增页面
            // this.$router.push('/admin/copyright/copyright_holder/create')
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('持有人数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchVerify() {
            const verifiableItems = this.selectedRows.filter(row => row.status === 'pending')
            if (verifiableItems.length === 0) {
                this.$message.warning('没有可认证的持有人记录')
                return
            }
            this.$confirm(`确定要认证选中的 ${verifiableItems.length} 个持有人吗？`, '确认批量认证', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功认证 ${verifiableItems.length} 个持有人`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        handleBatchDelete() {
            const deletableItems = this.selectedRows.filter(row => ['pending', 'rejected'].includes(row.status))
            if (deletableItems.length === 0) {
                this.$message.warning('没有可删除的持有人记录')
                return
            }
            this.$confirm(`确定要删除选中的 ${deletableItems.length} 个持有人吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableItems.length} 个持有人`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看持有人详情: ${row.holder_name}`)
            // TODO: 跳转到持有人详情页面
        },

        handleEdit(row) {
            this.$message.info(`编辑持有人: ${row.holder_name}`)
            // TODO: 跳转到持有人编辑页面
        },

        handleStatusChange(row, status) {
            this.$confirm(`确定要将持有人 "${row.holder_name}" 状态更改为 "${this.HOLDER_STATUS_LABELS[status]}" 吗？`, '确认状态变更', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`持有人状态已更新为: ${this.HOLDER_STATUS_LABELS[status]}`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消状态变更')
            })
        },

        handleDelete(row) {
            this.$confirm(`确定要删除持有人 "${row.holder_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('持有人删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.copyright-holder-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .el-form-item {
            margin-bottom: 16px;
        }
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .copyright-holder-index-page {
        .advanced-search-form {
            padding: 12px;

            .el-form-item {
                margin-bottom: 12px;
            }
        }

        .footer-stats {
            flex-direction: column;
            gap: 8px;
        }
    }
}
</style>
