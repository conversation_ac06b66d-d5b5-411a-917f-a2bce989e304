<!--
/**
 * 版权关联列表视图组件
 *
 * 功能特性：
 * - 关联数据表格展示
 * - 多选和批量操作
 * - 状态标签和操作按钮
 * - 响应式设计
 * - 敏感信息脱敏
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="copyright-relate-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            empty-text="暂无关联数据"
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <!-- 多选列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 关联编号 -->
            <el-table-column label="关联编号" prop="relate_no" show-overflow-tooltip width="140">
                <template #default="{ row }">
                    <el-link type="primary" @click="$emit('view-detail', row)">
                        {{ row.relate_no }}
                    </el-link>
                </template>
            </el-table-column>

            <!-- 版权信息 -->
            <el-table-column label="版权信息" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="copyright-info">
                        <div class="copyright-no">{{ row.copyright_no }}</div>
                        <div class="copyright-name">{{ row.copyright_name }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 关联类型 -->
            <el-table-column align="center" label="关联类型" prop="relate_type" width="100">
                <template #default="{ row }">
                    <el-tag :type="getRelateTypeTagType(row.relate_type)" size="small">
                        {{ getRelateTypeLabel(row.relate_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 关联方信息 -->
            <el-table-column label="关联方信息" show-overflow-tooltip width="160">
                <template #default="{ row }">
                    <div class="related-party-info">
                        <div class="party-name">{{ row.related_party }}</div>
                        <div class="party-contact">{{ row.related_contact }}</div>
                        <div class="party-phone">{{ row.related_phone }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 关联期限 -->
            <el-table-column align="center" label="关联期限" width="140">
                <template #default="{ row }">
                    <div class="relate-period">
                        <div class="start-date">{{ row.start_date }}</div>
                        <div class="separator">至</div>
                        <div :class="{ 'text-danger': isExpiringSoon(row.end_date) }" class="end-date">
                            {{ row.end_date }}
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 关联金额 -->
            <el-table-column align="right" label="关联金额" prop="amount" width="120">
                <template #default="{ row }">
                    <span class="amount">¥{{ formatAmount(row.amount) }}</span>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" prop="status" width="100">
                <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" prop="created_at" width="160">
                <template #default="{ row }">
                    {{ formatDateTime(row.created_at) }}
                </template>
            </el-table-column>

            <!-- 操作 -->
            <el-table-column align="center" fixed="right" label="操作" width="200">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            title="查看详情"
                            type="primary"
                            @click="$emit('view-detail', row)"
                        >
                            <i class="fas fa-eye"></i>
                        </el-button>
                        <el-button
                            size="small"
                            title="编辑"
                            type="success"
                            @click="$emit('edit', row)"
                        >
                            <i class="fas fa-edit"></i>
                        </el-button>
                        <el-dropdown @command="(command) => handleStatusCommand(command, row)">
                            <el-button size="small" title="状态操作" type="warning">
                                <i class="fas fa-cog"></i>
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="active"
                                    >
                                        <i class="fas fa-check"></i> 激活
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="suspended"
                                    >
                                        <i class="fas fa-pause"></i> 暂停
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="['active', 'suspended'].includes(row.status)"
                                        command="terminated"
                                    >
                                        <i class="fas fa-stop"></i> 终止
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'suspended'"
                                        command="active"
                                    >
                                        <i class="fas fa-play"></i> 恢复
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <el-button
                            :disabled="!canDelete(row)"
                            size="small"
                            title="删除"
                            type="danger"
                            @click="$emit('delete', row)"
                        >
                            <i class="fas fa-trash"></i>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'CopyrightRelateListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'delete'
    ],
    data() {
        return {
            // 关联类型标签
            RELATE_TYPE_LABELS: {
                'license': '授权许可',
                'transfer': '转让',
                'pledge': '质押',
                'inherit': '继承',
                'cooperation': '合作'
            },
            // 状态标签
            STATUS_LABELS: {
                'active': '活跃',
                'pending': '待审核',
                'expired': '已过期',
                'suspended': '已暂停',
                'terminated': '已终止'
            }
        }
    },
    methods: {
        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 获取关联类型标签
        getRelateTypeLabel(type) {
            return this.RELATE_TYPE_LABELS[type] || type
        },

        // 获取关联类型标签颜色
        getRelateTypeTagType(type) {
            const typeMap = {
                'license': 'success',
                'transfer': 'warning',
                'pledge': 'danger',
                'inherit': 'info',
                'cooperation': 'primary'
            }
            return typeMap[type] || 'info'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.STATUS_LABELS[status] || status
        },

        // 获取状态标签类型
        getStatusTagType(status) {
            const typeMap = {
                'active': 'success',
                'pending': 'warning',
                'expired': 'danger',
                'suspended': 'info',
                'terminated': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })
        },

        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return ''
            return dateTime.replace(/:\d{2}$/, '') // 移除秒数
        },

        // 检查是否即将到期
        isExpiringSoon(endDate) {
            if (!endDate) return false
            const expire = new Date(endDate)
            const now = new Date()
            const diffTime = expire.getTime() - now.getTime()
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
            return diffDays <= 30 && diffDays > 0 // 30天内到期
        },

        // 检查是否可以删除
        canDelete(row) {
            return ['pending', 'expired', 'terminated'].includes(row.status)
        },

        // 处理状态操作命令
        handleStatusCommand(command, row) {
            this.$emit('status-change', row, command)
        }
    }
}
</script>

<style lang="scss" scoped>
.copyright-relate-list-view {
    .copyright-info {
        .copyright-no {
            font-size: 12px;
            color: #909399;
            margin-bottom: 2px;
        }

        .copyright-name {
            font-weight: 500;
            color: #303133;
        }
    }

    .related-party-info {
        .party-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
        }

        .party-contact {
            font-size: 12px;
            color: #606266;
            margin-bottom: 1px;
        }

        .party-phone {
            font-size: 12px;
            color: #909399;
        }
    }

    .relate-period {
        .start-date, .end-date {
            font-size: 12px;
            color: #303133;
        }

        .separator {
            font-size: 12px;
            color: #909399;
            margin: 2px 0;
        }
    }

    .amount {
        font-weight: 500;
        color: #67c23a;
    }

    .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
        flex-wrap: wrap;

        .el-button {
            padding: 5px 8px;
        }
    }

    .text-danger {
        color: #f56c6c;
        font-weight: 500;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .copyright-relate-list-view {
        .action-buttons {
            flex-direction: column;
            gap: 2px;

            .el-button {
                width: 100%;
                margin: 0;
            }
        }
    }
}
</style>
