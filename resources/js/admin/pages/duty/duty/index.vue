<!--
/**
 * 职责管理页面
 *
 * 功能特性：
 * - 企业级职责管理系统
 * - 职责分类和权限管理
 * - 高级搜索和批量操作
 * - 职责分配和审核
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/duty/duty
 * 页面标题：职责管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="duty-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索职责名称、职责代码或负责人'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增职责
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchAssign"
                    type="warning"
                    @click="handleBatchAssign"
                >
                    <i class="fas fa-user-plus"></i>
                    批量分配 ({{ selectedRows.length }})
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDelete"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="职责状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in DUTY_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="职责类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="管理职责" value="management"></el-option>
                                <el-option label="技术职责" value="technical"></el-option>
                                <el-option label="业务职责" value="business"></el-option>
                                <el-option label="支持职责" value="support"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="优先级">
                            <el-select v-model="filters.priority" clearable placeholder="选择优先级">
                                <el-option label="全部优先级" value=""></el-option>
                                <el-option label="高" value="high"></el-option>
                                <el-option label="中" value="medium"></el-option>
                                <el-option label="低" value="low"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 职责列表 -->
            <template #default>
                <DutyListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @assign="handleAssign"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总职责: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已分配: <strong>{{ assignedDutyCount }}</strong>
					</span>
                    <span class="stat-item">
						待分配: <strong>{{ unassignedDutyCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import DutyListView from './components/DutyListView.vue'

export default {
    name: 'AdminDutyDutyIndexPage',
    components: {
        BackendPageListLayout,
        DutyListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                priority: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            assignedDutyCount: 0,
            unassignedDutyCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部职责', icon: 'fas fa-list', badge: 0},
                {name: 'assigned', label: '已分配', icon: 'fas fa-user-check', badge: 0},
                {name: 'unassigned', label: '待分配', icon: 'fas fa-user-clock', badge: 0},
                {name: 'high_priority', label: '高优先级', icon: 'fas fa-exclamation-triangle', badge: 0},
                {name: 'disabled', label: '已禁用', icon: 'fas fa-ban', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 职责状态标签
            DUTY_STATUS_LABELS: {
                'active': '活跃',
                'assigned': '已分配',
                'unassigned': '待分配',
                'disabled': '已禁用',
                'archived': '已归档'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchAssign() {
            return this.selectedRows.some(row => row.status === 'unassigned')
        },
        canBatchDelete() {
            return this.selectedRows.some(row => ['disabled', 'archived'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟职责数据
                const mockData = [
                    {
                        id: 1,
                        duty_name: '系统管理员',
                        duty_code: 'SYS_ADMIN',
                        duty_type: 'management',
                        priority: 'high',
                        assignee: '张三',
                        assignee_phone: '138****5678',
                        description: '负责系统整体管理和维护',
                        status: 'assigned',
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        duty_name: '技术开发',
                        duty_code: 'TECH_DEV',
                        duty_type: 'technical',
                        priority: 'high',
                        assignee: '李四',
                        assignee_phone: '139****1234',
                        description: '负责系统技术开发和优化',
                        status: 'assigned',
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 3,
                        duty_name: '客户服务',
                        duty_code: 'CUSTOMER_SERVICE',
                        duty_type: 'support',
                        priority: 'medium',
                        assignee: null,
                        assignee_phone: null,
                        description: '负责客户咨询和问题处理',
                        status: 'unassigned',
                        created_at: '2024-01-15 12:45:00',
                        updated_at: '2024-01-15 12:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 89
                this.assignedDutyCount = 56
                this.unassignedDutyCount = 23
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.assignedDutyCount
            this.tabOptions[2].badge = this.unassignedDutyCount
            this.tabOptions[3].badge = 15
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                priority: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('跳转到新增职责页面')
            // TODO: 跳转到新增页面
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('职责数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchAssign() {
            const assignableItems = this.selectedRows.filter(row => row.status === 'unassigned')

            if (assignableItems.length === 0) {
                this.$message.warning('没有可分配的职责')
                return
            }

            this.$message.info(`批量分配 ${assignableItems.length} 个职责`)
            // TODO: 实现批量分配逻辑
        },

        handleBatchDelete() {
            const deletableItems = this.selectedRows.filter(row =>
                ['disabled', 'archived'].includes(row.status)
            )

            if (deletableItems.length === 0) {
                this.$message.warning('没有可删除的职责')
                return
            }

            this.$confirm(`确定要删除选中的 ${deletableItems.length} 个职责吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableItems.length} 个职责`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看职责详情: ${row.duty_name}`)
            // TODO: 跳转到详情页面
        },

        handleEdit(row) {
            this.$message.info(`编辑职责: ${row.duty_name}`)
            // TODO: 跳转到编辑页面
        },

        handleAssign(row) {
            this.$message.info(`分配职责: ${row.duty_name}`)
            // TODO: 实现职责分配逻辑
        },

        handleStatusChange(row, newStatus) {
            this.$message.success(`职责 ${row.duty_name} 状态已更新为: ${this.DUTY_STATUS_LABELS[newStatus]}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除职责 "${row.duty_name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`职责 ${row.duty_name} 删除成功`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.duty-index-page {
    .advanced-search-form {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;
        align-items: center;

        .stat-item {
            font-size: 12px;
            color: #666;

            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
