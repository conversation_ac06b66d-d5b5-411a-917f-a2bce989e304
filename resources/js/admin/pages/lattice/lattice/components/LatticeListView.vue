<!--
/**
 * 格子列表视图组件
 *
 * 功能特性：
 * - 格子数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="lattice-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 格子名称 -->
            <el-table-column
                label="格子名称"
                min-width="150"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="lattice-name-cell">
                        <i class="fas fa-th lattice-icon"></i>
                        <span class="lattice-name">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 格子类型 -->
            <el-table-column
                align="center"
                label="类型"
                prop="type_name"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ row.type_name }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 位置区域 -->
            <el-table-column
                label="位置区域"
                prop="area_name"
                width="120"
            >
                <template #default="{ row }">
                    <div class="area-cell">
                        <i class="fas fa-map-marker-alt area-icon"></i>
                        <span>{{ row.area_name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 排序位置 -->
            <el-table-column
                align="center"
                label="排序"
                prop="position"
                width="80"
            >
                <template #default="{ row }">
                    <div class="position-cell">
                        <i class="fas fa-sort-numeric-up position-icon"></i>
                        <span>{{ row.position }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 尺寸 -->
            <el-table-column
                align="center"
                label="尺寸(W×H)"
                width="120"
            >
                <template #default="{ row }">
                    <div class="size-cell">
                        <i class="fas fa-expand-arrows-alt size-icon"></i>
                        <span>{{ row.width }}×{{ row.height }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 内容类型 -->
            <el-table-column
                align="center"
                label="内容类型"
                prop="content_type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getContentTypeTagType(row.content_type)"
                        size="small"
                    >
                        {{ getContentTypeLabel(row.content_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 点击量 -->
            <el-table-column
                align="right"
                label="点击量"
                prop="click_count"
                width="100"
            >
                <template #default="{ row }">
                    <div class="count-cell">
                        <i class="fas fa-mouse-pointer count-icon"></i>
                        <span>{{ formatNumber(row.click_count) }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 浏览量 -->
            <el-table-column
                align="right"
                label="浏览量"
                prop="view_count"
                width="100"
            >
                <template #default="{ row }">
                    <div class="count-cell">
                        <i class="fas fa-eye count-icon"></i>
                        <span>{{ formatNumber(row.view_count) }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 更新时间 -->
            <el-table-column
                label="更新时间"
                prop="updated_at"
                sortable
                width="180"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <i class="fas fa-clock time-icon"></i>
                        <span>{{ row.updated_at }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="300"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>

                        <el-button
                            size="small"
                            type="info"
                            @click="handlePreview(row)"
                        >
                            <i class="fas fa-search"></i>
                            预览
                        </el-button>

                        <el-dropdown
                            trigger="click"
                            @command="(command) => handleDropdownCommand(command, row)"
                        >
                            <el-button size="small" type="success">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        command="edit"
                                    >
                                        <i class="fas fa-edit"></i>
                                        编辑
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status !== 'active'"
                                        command="activate"
                                    >
                                        <i class="fas fa-play"></i>
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="deactivate"
                                    >
                                        <i class="fas fa-pause"></i>
                                        停用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="copy"
                                        divided
                                    >
                                        <i class="fas fa-copy"></i>
                                        复制
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="statistics"
                                        divided
                                    >
                                        <i class="fas fa-chart-bar"></i>
                                        统计数据
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        divided
                                    >
                                        <i class="fas fa-trash text-danger"></i>
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'LatticeListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'status-change',
        'preview'
    ],
    data() {
        return {
            // 状态标签映射
            statusLabels: {
                'active': '启用',
                'inactive': '停用',
                'draft': '草稿',
                'pending': '待审核'
            },
            // 内容类型标签映射
            contentTypeLabels: {
                'image': '图片',
                'product_list': '商品列表',
                'menu': '菜单',
                'text': '文本',
                'video': '视频'
            }
        }
    },
    methods: {
        // 获取类型标签样式
        getTypeTagType(type) {
            const typeMap = {
                'product': 'primary',
                'advertisement': 'success',
                'function': 'warning',
                'navigation': 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.statusLabels[status] || status
        },

        // 获取状态标签样式
        getStatusTagType(status) {
            const statusMap = {
                'active': 'success',
                'inactive': 'danger',
                'draft': 'warning',
                'pending': 'info'
            }
            return statusMap[status] || 'info'
        },

        // 获取内容类型标签
        getContentTypeLabel(type) {
            return this.contentTypeLabels[type] || type
        },

        // 获取内容类型标签样式
        getContentTypeTagType(type) {
            const typeMap = {
                'image': 'primary',
                'product_list': 'success',
                'menu': 'info',
                'text': 'warning',
                'video': 'danger'
            }
            return typeMap[type] || 'info'
        },

        // 格式化数字
        formatNumber(num) {
            if (num === null || num === undefined) return '0'
            return num.toLocaleString()
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 预览
        handlePreview(row) {
            this.$emit('preview', row)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'edit':
                    this.handleEdit(row)
                    break
                case 'activate':
                    this.handleStatusChange(row, 'active')
                    break
                case 'deactivate':
                    this.handleStatusChange(row, 'inactive')
                    break
                case 'copy':
                    this.$message.info(`复制格子: ${row.name}`)
                    break
                case 'statistics':
                    this.$message.info(`查看统计数据: ${row.name}`)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.lattice-list-view {
    .admin-table {
        .lattice-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .lattice-icon {
                color: #409eff;
                font-size: 14px;
            }

            .lattice-name {
                font-weight: 500;
            }
        }

        .area-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .area-icon {
                color: #e6a23c;
                font-size: 12px;
            }
        }

        .position-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            .position-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .size-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            .size-icon {
                color: #67c23a;
                font-size: 12px;
            }
        }

        .count-cell {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 6px;

            .count-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .time-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .time-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        :deep(.text-danger) {
            color: #f56c6c !important;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .lattice-list-view {
        .admin-table {
            .action-buttons {
                flex-direction: column;
                gap: 4px;

                .el-button {
                    width: 100%;
                    margin: 0;
                }
            }
        }
    }
}
</style>
