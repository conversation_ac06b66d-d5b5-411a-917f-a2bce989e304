<!--
/**
 * 订单列表视图组件
 *
 * 功能特性：
 * - 订单数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 * - 敏感信息脱敏处理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="order-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 订单号 -->
            <el-table-column
                label="订单号"
                min-width="160"
                prop="order_no"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="order-cell">
                        <i class="fas fa-receipt order-icon"></i>
                        <span class="order-number">{{ row.order_no }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 客户信息 -->
            <el-table-column
                label="客户信息"
                min-width="180"
            >
                <template #default="{ row }">
                    <div class="customer-info">
                        <div class="customer-name">
                            <i class="fas fa-user customer-icon"></i>
                            <span>{{ row.customer_name }}</span>
                        </div>
                        <div class="customer-phone">
                            <i class="fas fa-phone phone-icon"></i>
                            <span class="sensitive-data">{{ row.customer_phone }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 订单金额 -->
            <el-table-column
                align="right"
                label="订单金额"
                prop="total_amount"
                sortable
                width="140"
            >
                <template #default="{ row }">
                    <div class="amount-cell">
                        <span class="amount">¥{{ formatAmount(row.total_amount) }}</span>
                        <div class="product-count">{{ row.product_count }}件商品</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 支付方式 -->
            <el-table-column
                align="center"
                label="支付方式"
                prop="payment_method"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getPaymentMethodType(row.payment_method)"
                        size="small"
                    >
                        <i :class="getPaymentMethodIcon(row.payment_method)" class="payment-icon"></i>
                        {{ getPaymentMethodName(row.payment_method) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 订单状态 -->
            <el-table-column
                align="center"
                label="订单状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        <i :class="getStatusIcon(row.status)" class="status-icon"></i>
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 下单时间 -->
            <el-table-column
                label="下单时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="datetime-cell">
                        <div class="date">{{ formatDate(row.created_at) }}</div>
                        <div class="time">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="canEdit(row)"
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-if="row.status === 'paid'"
                                    @click="handleStatusChange(row, 'shipped')"
                                >
                                    <i class="fas fa-truck"></i>
                                    发货
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'shipped'"
                                    @click="handleStatusChange(row, 'completed')"
                                >
                                    <i class="fas fa-check-double"></i>
                                    完成
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="['pending', 'paid'].includes(row.status)"
                                    @click="handleStatusChange(row, 'cancelled')"
                                >
                                    <i class="fas fa-times"></i>
                                    取消订单
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="row.status === 'completed'"
                                    @click="handleStatusChange(row, 'refunded')"
                                >
                                    <i class="fas fa-undo"></i>
                                    申请退款
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除订单
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'OrderListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'status-change',
        'delete',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'pending': '待付款',
                'paid': '已付款',
                'shipped': '已发货',
                'completed': '已完成',
                'cancelled': '已取消',
                'refunded': '已退款'
            },
            paymentMethodMap: {
                'wechat': '微信支付',
                'alipay': '支付宝',
                'bank_card': '银行卡',
                'balance': '余额支付'
            }
        }
    },
    methods: {
        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑订单
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 删除订单
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount).toFixed(2)
        },

        // 格式化日期
        formatDate(datetime) {
            return datetime.split(' ')[0]
        },

        // 格式化时间
        formatTime(datetime) {
            return datetime.split(' ')[1]
        },

        // 获取支付方式名称
        getPaymentMethodName(method) {
            return this.paymentMethodMap[method] || method
        },

        // 获取支付方式图标
        getPaymentMethodIcon(method) {
            const iconMap = {
                'wechat': 'fab fa-weixin',
                'alipay': 'fas fa-mobile-alt',
                'bank_card': 'fas fa-credit-card',
                'balance': 'fas fa-wallet'
            }
            return iconMap[method] || 'fas fa-money-bill'
        },

        // 获取支付方式类型
        getPaymentMethodType(method) {
            const typeMap = {
                'wechat': 'success',
                'alipay': 'primary',
                'bank_card': 'warning',
                'balance': 'info'
            }
            return typeMap[method] || 'info'
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                'pending': 'fas fa-clock',
                'paid': 'fas fa-check-circle',
                'shipped': 'fas fa-truck',
                'completed': 'fas fa-check-double',
                'cancelled': 'fas fa-times-circle',
                'refunded': 'fas fa-undo'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'pending': 'warning',
                'paid': 'primary',
                'shipped': 'info',
                'completed': 'success',
                'cancelled': 'danger',
                'refunded': 'info'
            }
            return typeMap[status] || 'info'
        },

        // 判断是否可以编辑
        canEdit(row) {
            return ['pending', 'paid'].includes(row.status)
        }
    }
}
</script>

<style lang="scss" scoped>
.order-list-view {
    .admin-table {
        // 订单号单元格
        .order-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .order-icon {
                color: #409eff;
                font-size: 14px;
            }

            .order-number {
                font-weight: 500;
                color: #303133;
            }
        }

        // 客户信息单元格
        .customer-info {
            .customer-name {
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 4px;

                .customer-icon {
                    color: #606266;
                    font-size: 12px;
                }

                span {
                    font-weight: 500;
                    color: #303133;
                }
            }

            .customer-phone {
                display: flex;
                align-items: center;
                gap: 6px;

                .phone-icon {
                    color: #909399;
                    font-size: 12px;
                }

                .sensitive-data {
                    font-size: 12px;
                    color: #606266;
                    font-family: 'Courier New', monospace;
                }
            }
        }

        // 金额单元格
        .amount-cell {
            text-align: right;

            .amount {
                font-weight: 600;
                color: #e6a23c;
                font-size: 14px;
            }

            .product-count {
                font-size: 12px;
                color: #909399;
                margin-top: 2px;
            }
        }

        // 时间单元格
        .datetime-cell {
            .date {
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
            }

            .time {
                font-size: 12px;
                color: #909399;
            }
        }

        // 图标样式
        .payment-icon,
        .status-icon {
            margin-right: 4px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .order-list-view {
        .admin-table {
            font-size: 12px;

            .customer-info {
                .customer-name,
                .customer-phone {
                    font-size: 11px;
                }
            }

            .amount-cell {
                .amount {
                    font-size: 13px;
                }

                .product-count {
                    font-size: 11px;
                }
            }

            .datetime-cell {
                .date {
                    font-size: 12px;
                }

                .time {
                    font-size: 11px;
                }
            }
        }
    }
}
</style>
