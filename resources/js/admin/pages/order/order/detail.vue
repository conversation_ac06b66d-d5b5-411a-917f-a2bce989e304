<!--
/**
 * 订单详情页面
 *
 * 功能特性：
 * - 订单详细信息展示
 * - 订单状态跟踪
 * - 订单操作功能
 * - 企业级设计
 * - 响应式布局
 *
 * 路由路径：/admin/order/order/detail/:id
 * 页面标题：订单详情
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="order-detail-page">
        <BackendViewActionLayout
            :loading="loading"
            :page-description="`订单号：${orderData.order_no || '加载中...'}`"
            :refreshing="refreshing"
            :show-empty-state="showEmptyState"
            page-title="订单详情"
        >
            <div class="content-area">
                <!-- 订单基本信息 -->
                <el-card class="info-card" shadow="never">
                    <template #header>
                        <div class="card-header">
                            <span>订单信息</span>
                            <el-tag :type="getStatusType(orderData.status)" size="large">
                                <i :class="getStatusIcon(orderData.status)"></i>
                                {{ getStatusName(orderData.status) }}
                            </el-tag>
                        </div>
                    </template>
                    <div class="order-info">
                        <el-row :gutter="24">
                            <el-col :span="12">
                                <div class="info-item">
                                    <label>订单号：</label>
                                    <span class="order-no">{{ orderData.order_no }}</span>
                                </div>
                                <div class="info-item">
                                    <label>下单时间：</label>
                                    <span>{{ orderData.created_at }}</span>
                                </div>
                                <div class="info-item">
                                    <label>支付方式：</label>
                                    <el-tag :type="getPaymentMethodType(orderData.payment_method)" size="small">
                                        <i :class="getPaymentMethodIcon(orderData.payment_method)"></i>
                                        {{ getPaymentMethodName(orderData.payment_method) }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <label>订单金额：</label>
                                    <span class="amount">¥{{ formatAmount(orderData.total_amount) }}</span>
                                </div>
                                <div class="info-item">
                                    <label>商品数量：</label>
                                    <span>{{ orderData.product_count }}件</span>
                                </div>
                                <div class="info-item">
                                    <label>更新时间：</label>
                                    <span>{{ orderData.updated_at }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>

                <!-- 客户信息 -->
                <el-card class="info-card" shadow="never">
                    <template #header>
                        <span>客户信息</span>
                    </template>
                    <div class="customer-info">
                        <el-row :gutter="24">
                            <el-col :span="12">
                                <div class="info-item">
                                    <label>客户姓名：</label>
                                    <span>{{ orderData.customer_name }}</span>
                                </div>
                                <div class="info-item">
                                    <label>联系电话：</label>
                                    <span class="sensitive-data">{{ orderData.customer_phone }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <label>邮箱地址：</label>
                                    <span class="sensitive-data">{{ orderData.customer_email }}</span>
                                </div>
                                <div class="info-item">
                                    <label>收货地址：</label>
                                    <span>{{ orderData.shipping_address || '暂无' }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>

                <!-- 商品信息 -->
                <el-card class="info-card" shadow="never">
                    <template #header>
                        <span>商品信息</span>
                    </template>
                    <div class="product-info">
                        <el-table :data="orderData.products || []" border>
                            <el-table-column label="商品名称" min-width="200" prop="name"/>
                            <el-table-column label="规格" prop="spec" width="120"/>
                            <el-table-column align="right" label="单价" prop="price" width="100">
                                <template #default="{ row }">
                                    ¥{{ formatAmount(row.price) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="数量" prop="quantity" width="80"/>
                            <el-table-column align="right" label="小计" prop="subtotal" width="120">
                                <template #default="{ row }">
                                    <span class="subtotal">¥{{ formatAmount(row.subtotal) }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-card>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <el-button @click="handleBack">
                        <i class="fas fa-arrow-left"></i>
                        返回列表
                    </el-button>
                    <el-button
                        v-if="canEdit(orderData)"
                        type="primary"
                        @click="handleEdit"
                    >
                        <i class="fas fa-edit"></i>
                        编辑订单
                    </el-button>
                    <el-button
                        v-if="orderData.status === 'paid'"
                        type="success"
                        @click="handleShip"
                    >
                        <i class="fas fa-truck"></i>
                        发货
                    </el-button>
                    <el-button
                        v-if="['pending', 'paid'].includes(orderData.status)"
                        type="warning"
                        @click="handleCancel"
                    >
                        <i class="fas fa-times"></i>
                        取消订单
                    </el-button>
                </div>
            </div>
        </BackendViewActionLayout>
    </div>
</template>

<script>
import BackendViewActionLayout from '@layouts/BackendLayout/BackendPageLayout/BackendViewActionLayout.vue'

export default {
    name: 'AdminOrderDetailPage',
    components: {
        BackendViewActionLayout
    },
    data() {
        return {
            loading: false,
            refreshing: false,
            showEmptyState: false,
            orderData: {}
        }
    },
    computed: {
        orderId() {
            return this.$route.params.id
        }
    },
    mounted() {
        this.initPage()
    },
    methods: {
        // 初始化页面
        async initPage() {
            await this.loadOrderData()
            // 设置页面标题
            document.title = `订单详情 - ${this.orderData.order_no || '订单管理'}`
        },

        // 加载订单数据
        async loadOrderData() {
            try {
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟订单数据
                this.orderData = {
                    id: this.orderId,
                    order_no: 'ORD202401150001',
                    customer_name: '张三',
                    customer_phone: '138****5678',
                    customer_email: 'zha***@example.com',
                    shipping_address: '北京市朝阳区某某街道123号',
                    total_amount: 299.00,
                    product_count: 2,
                    payment_method: 'wechat',
                    status: 'paid',
                    created_at: '2024-01-15 10:30:00',
                    updated_at: '2024-01-15 10:35:00',
                    products: [
                        {
                            name: '商品A',
                            spec: '红色/L',
                            price: 199.00,
                            quantity: 1,
                            subtotal: 199.00
                        },
                        {
                            name: '商品B',
                            spec: '蓝色/M',
                            price: 100.00,
                            quantity: 1,
                            subtotal: 100.00
                        }
                    ]
                }

                this.showEmptyState = false

            } catch (error) {
                console.error('加载订单数据失败:', error)
                this.$message.error('加载订单数据失败，请重试')
                this.showEmptyState = true
            } finally {
                this.loading = false
            }
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount).toFixed(2)
        },

        // 获取状态相关方法（复用OrderListView的逻辑）
        getStatusName(status) {
            const statusMap = {
                'pending': '待付款',
                'paid': '已付款',
                'shipped': '已发货',
                'completed': '已完成',
                'cancelled': '已取消',
                'refunded': '已退款'
            }
            return statusMap[status] || status
        },

        getStatusIcon(status) {
            const iconMap = {
                'pending': 'fas fa-clock',
                'paid': 'fas fa-check-circle',
                'shipped': 'fas fa-truck',
                'completed': 'fas fa-check-double',
                'cancelled': 'fas fa-times-circle',
                'refunded': 'fas fa-undo'
            }
            return iconMap[status] || 'fas fa-question-circle'
        },

        getStatusType(status) {
            const typeMap = {
                'pending': 'warning',
                'paid': 'primary',
                'shipped': 'info',
                'completed': 'success',
                'cancelled': 'danger',
                'refunded': 'info'
            }
            return typeMap[status] || 'info'
        },

        getPaymentMethodName(method) {
            const methodMap = {
                'wechat': '微信支付',
                'alipay': '支付宝',
                'bank_card': '银行卡',
                'balance': '余额支付'
            }
            return methodMap[method] || method
        },

        getPaymentMethodIcon(method) {
            const iconMap = {
                'wechat': 'fab fa-weixin',
                'alipay': 'fas fa-mobile-alt',
                'bank_card': 'fas fa-credit-card',
                'balance': 'fas fa-wallet'
            }
            return iconMap[method] || 'fas fa-money-bill'
        },

        getPaymentMethodType(method) {
            const typeMap = {
                'wechat': 'success',
                'alipay': 'primary',
                'bank_card': 'warning',
                'balance': 'info'
            }
            return typeMap[method] || 'info'
        },

        // 判断是否可以编辑
        canEdit(order) {
            return ['pending', 'paid'].includes(order.status)
        },

        // 操作处理
        handleBack() {
            this.$router.push('/admin/order/order')
        },

        handleEdit() {
            this.$router.push(`/admin/order/order/edit/${this.orderId}`)
        },

        handleShip() {
            this.$confirm('确定要发货此订单吗？', '确认发货', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('订单已发货')
                this.loadOrderData()
            }).catch(() => {
                this.$message.info('已取消发货')
            })
        },

        handleCancel() {
            this.$confirm('确定要取消此订单吗？', '确认取消', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('订单已取消')
                this.loadOrderData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
    .content-area {
        .info-card {
            margin-bottom: 24px;
            border-radius: 8px;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 600;
                color: #303133;
            }

            .order-info,
            .customer-info {
                .info-item {
                    display: flex;
                    margin-bottom: 16px;

                    label {
                        width: 100px;
                        color: #606266;
                        font-weight: 500;
                    }

                    .order-no {
                        font-weight: 600;
                        color: #409eff;
                    }

                    .amount {
                        font-weight: 600;
                        color: #e6a23c;
                        font-size: 16px;
                    }

                    .sensitive-data {
                        color: #606266;
                        font-family: 'Courier New', monospace;
                    }
                }
            }

            .product-info {
                .subtotal {
                    font-weight: 600;
                    color: #e6a23c;
                }
            }
        }

        .action-buttons {
            text-align: center;
            padding: 24px 0;

            .el-button {
                margin: 0 8px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .order-detail-page {
        .content-area {
            .info-card {
                margin-bottom: 16px;

                .order-info,
                .customer-info {
                    .info-item {
                        flex-direction: column;
                        margin-bottom: 12px;

                        label {
                            width: auto;
                            margin-bottom: 4px;
                        }
                    }
                }
            }

            .action-buttons {
                .el-button {
                    margin: 4px;
                    width: calc(50% - 8px);
                }
            }
        }
    }
}
</style>
