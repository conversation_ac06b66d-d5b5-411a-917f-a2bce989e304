<!--
/**
 * 支付记录列表视图组件
 *
 * 功能特性：
 * - 支付数据表格展示
 * - 敏感信息脱敏显示
 * - 状态管理和操作
 * - 响应式设计
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="payment-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>
            <el-table-column
                label="交易号"
                min-width="180"
                prop="transaction_no"
                show-overflow-tooltip
            />
            <el-table-column
                label="订单号"
                min-width="150"
                prop="order_no"
                show-overflow-tooltip
            />
            <el-table-column
                label="用户"
                prop="user_name"
                width="100"
            >
                <template #default="{ row }">
                    <span class="sensitive-data">{{ row.user_name }}</span>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="支付方式"
                prop="payment_method"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag :type="getPaymentMethodType(row.payment_method)" size="small">
                        {{ getPaymentMethodName(row.payment_method) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="支付金额"
                prop="amount"
                width="120"
            >
                <template #default="{ row }">
                    <span class="amount-text">¥{{ row.amount }}</span>
                </template>
            </el-table-column>
            <el-table-column
                align="right"
                label="手续费"
                prop="fee"
                width="100"
            >
                <template #default="{ row }">
                    <span class="fee-text">¥{{ row.fee }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="账户信息"
                prop="account_info"
                width="120"
            >
                <template #default="{ row }">
                    <span class="sensitive-data">{{ row.account_info }}</span>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="支付状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                label="支付时间"
                prop="paid_at"
                sortable
                width="160"
            />
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="240"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        v-if="row.status === 'success'"
                        size="small"
                        type="warning"
                        @click="handleRefund(row)"
                    >
                        退款
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="handleEdit(row)">
                                    <i class="fas fa-edit"></i>
                                    编辑记录
                                </el-dropdown-item>
                                <el-dropdown-item @click="handleStatusChange(row, 'cancelled')">
                                    <i class="fas fa-ban"></i>
                                    取消支付
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除记录
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'PaymentListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'status-change',
        'refund'
    ],
    data() {
        return {
            statusMap: {
                'success': '支付成功',
                'failed': '支付失败',
                'processing': '处理中',
                'refunded': '已退款',
                'cancelled': '已取消'
            },
            paymentMethodMap: {
                'wechat': '微信支付',
                'alipay': '支付宝',
                'bank': '银行卡',
                'balance': '余额支付'
            }
        }
    },
    methods: {
        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'success': 'success',
                'failed': 'danger',
                'processing': 'warning',
                'refunded': 'info',
                'cancelled': 'info'
            }
            return typeMap[status] || 'info'
        },

        // 获取支付方式名称
        getPaymentMethodName(method) {
            return this.paymentMethodMap[method] || method
        },

        // 获取支付方式类型
        getPaymentMethodType(method) {
            const typeMap = {
                'wechat': 'success',
                'alipay': 'primary',
                'bank': 'warning',
                'balance': 'info'
            }
            return typeMap[method] || 'info'
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑记录
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除记录
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变化
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 退款
        handleRefund(row) {
            this.$emit('refund', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.payment-list-view {
    .admin-table {
        .sensitive-data {
            color: #606266;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .amount-text {
            color: #67c23a;
            font-weight: 600;
        }

        .fee-text {
            color: #909399;
            font-size: 12px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .payment-list-view {
        .admin-table {
            font-size: 12px;
        }
    }
}
</style>
