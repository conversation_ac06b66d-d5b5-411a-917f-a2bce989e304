<!--
/**
 * 配件列表视图组件
 *
 * 功能特性：
 * - 配件数据表格展示
 * - 库存状态监控
 * - 供应商信息展示
 * - 行选择和批量操作
 * - 状态切换和库存更新
 * - 操作按钮组
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="accessory-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            row-key="id"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 配件信息 -->
            <el-table-column label="配件信息" min-width="200">
                <template #default="{ row }">
                    <div class="accessory-info">
                        <div class="name">{{ row.name }}</div>
                        <div class="details">
                            <span class="code">{{ row.code }}</span>
                            <span class="model">型号: {{ row.model }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 类型 -->
            <el-table-column align="center" label="类型" width="100">
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ row.type_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 材质 -->
            <el-table-column align="center" label="材质" width="100">
                <template #default="{ row }">
                    <el-tag
                        :type="getMaterialTagType(row.material)"
                        effect="plain"
                        size="small"
                    >
                        {{ row.material_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 规格 -->
            <el-table-column align="center" label="规格" width="120">
                <template #default="{ row }">
                    {{ row.specifications }}
                </template>
            </el-table-column>

            <!-- 供应商 -->
            <el-table-column label="供应商" min-width="150">
                <template #default="{ row }">
                    <div class="supplier-info">
                        <div class="name">{{ row.supplier }}</div>
                        <div class="code">{{ row.supplier_code }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 单价 -->
            <el-table-column align="center" label="单价" width="100">
                <template #default="{ row }">
                    <div class="price-info">
                        <span class="price">¥{{ row.unit_price }}</span>
                        <span class="unit">/{{ row.unit }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 库存信息 -->
            <el-table-column align="center" label="库存信息" min-width="150">
                <template #default="{ row }">
                    <div class="stock-info">
                        <div class="stock-quantity">
							<span
                                :class="getStockQuantityClass(row.stock_status)"
                                class="quantity"
                            >
								{{ row.stock_quantity }}
							</span>
                            <span class="unit">{{ row.unit }}</span>
                        </div>
                        <div class="stock-status">
                            <el-tag
                                :type="getStockStatusTagType(row.stock_status)"
                                size="small"
                            >
                                {{ getStockStatusLabel(row.stock_status) }}
                            </el-tag>
                        </div>
                        <div class="min-stock">
                            最低: {{ row.min_stock }}{{ row.unit }}
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column label="描述" min-width="180" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.description }}
                </template>
            </el-table-column>

            <!-- 排序 -->
            <el-table-column align="center" label="排序" width="80">
                <template #default="{ row }">
                    {{ row.sort_order }}
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" width="100">
                <template #default="{ row }">
                    <el-switch
                        :model-value="row.status === 'active'"
                        @change="handleStatusChange(row)"
                    />
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" width="120">
                <template #default="{ row }">
                    {{ row.created_at }}
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column align="center" fixed="right" label="操作" width="200">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            link
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="success"
                            @click="handleStockUpdate(row)"
                        >
                            <i class="fas fa-boxes"></i>
                            库存
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="danger"
                            @click="handleDelete(row)"
                        >
                            <i class="fas fa-trash"></i>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'AccessoryListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'status-change',
        'view-detail',
        'edit',
        'delete',
        'stock-update'
    ],
    methods: {
        // 获取类型标签类型
        getTypeTagType(type) {
            const typeMap = {
                button: 'primary',
                zipper: 'success',
                label: 'warning',
                thread: 'info',
                other: 'danger'
            }
            return typeMap[type] || 'info'
        },

        // 获取材质标签类型
        getMaterialTagType(material) {
            const materialMap = {
                metal: 'warning',
                plastic: 'primary',
                wood: 'success',
                fabric: 'info'
            }
            return materialMap[material] || 'info'
        },

        // 获取库存状态标签类型
        getStockStatusTagType(status) {
            const statusMap = {
                sufficient: 'success',
                insufficient: 'warning',
                out_of_stock: 'danger'
            }
            return statusMap[status] || 'info'
        },

        // 获取库存状态标签
        getStockStatusLabel(status) {
            const labelMap = {
                sufficient: '充足',
                insufficient: '不足',
                out_of_stock: '缺货'
            }
            return labelMap[status] || '未知'
        },

        // 获取库存数量样式类
        getStockQuantityClass(status) {
            return {
                'stock-sufficient': status === 'sufficient',
                'stock-insufficient': status === 'insufficient',
                'stock-out': status === 'out_of_stock'
            }
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 处理状态变化
        handleStatusChange(row) {
            row.status = row.status === 'active' ? 'inactive' : 'active'
            this.$emit('status-change', row)
        },

        // 处理查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 处理编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 处理库存更新
        handleStockUpdate(row) {
            this.$emit('stock-update', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.accessory-list-view {
    .accessory-info {
        .name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
        }

        .details {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .code {
                font-size: 12px;
                color: #909399;
            }

            .model {
                font-size: 12px;
                color: #606266;
            }
        }
    }

    .supplier-info {
        .name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
        }

        .code {
            font-size: 12px;
            color: #909399;
        }
    }

    .price-info {
        .price {
            font-weight: 600;
            color: #e6a23c;
        }

        .unit {
            font-size: 12px;
            color: #909399;
        }
    }

    .stock-info {
        .stock-quantity {
            margin-bottom: 4px;

            .quantity {
                font-weight: 600;

                &.stock-sufficient {
                    color: #67c23a;
                }

                &.stock-insufficient {
                    color: #e6a23c;
                }

                &.stock-out {
                    color: #f56c6c;
                }
            }

            .unit {
                font-size: 12px;
                color: #909399;
                margin-left: 2px;
            }
        }

        .stock-status {
            margin-bottom: 4px;
        }

        .min-stock {
            font-size: 12px;
            color: #909399;
        }
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 4px;
        flex-wrap: wrap;

        .el-button {
            padding: 4px 6px;

            i {
                margin-right: 2px;
            }
        }
    }

    :deep(.el-table) {
        .el-table__row {
            &:hover {
                background-color: #f5f7fa;
            }
        }

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
