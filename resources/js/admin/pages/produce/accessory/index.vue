<!--
/**
 * 配件管理页面
 *
 * 功能特性：
 * - 管理产品配件和附件
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 配件分类和库存管理
 *
 * 路由路径：/admin/produce/accessory
 * 页面标题：配件管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="accessory-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索配件名称、型号或供应商'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="配件类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="纽扣" value="button"></el-option>
                                <el-option label="拉链" value="zipper"></el-option>
                                <el-option label="标签" value="label"></el-option>
                                <el-option label="线材" value="thread"></el-option>
                                <el-option label="其他" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="材质">
                            <el-select v-model="filters.material" clearable placeholder="选择材质">
                                <el-option label="全部材质" value=""></el-option>
                                <el-option label="金属" value="metal"></el-option>
                                <el-option label="塑料" value="plastic"></el-option>
                                <el-option label="木质" value="wood"></el-option>
                                <el-option label="布料" value="fabric"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="库存状态">
                            <el-select v-model="filters.stock_status" clearable placeholder="选择库存状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="充足" value="sufficient"></el-option>
                                <el-option label="不足" value="insufficient"></el-option>
                                <el-option label="缺货" value="out_of_stock"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 配件列表 -->
            <template #default>
                <AccessoryListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                    @stock-update="handleStockUpdate"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总配件: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						库存不足: <strong>{{ getInsufficientStockCount() }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import AccessoryListView from './components/AccessoryListView.vue'

export default {
    name: 'AdminProduceAccessoryIndexPage',
    components: {
        BackendPageListLayout,
        AccessoryListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                material: '',
                stock_status: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部配件', value: 'all'},
                {label: '纽扣', value: 'button'},
                {label: '拉链', value: 'zipper'},
                {label: '标签', value: 'label'},
                {label: '线材', value: 'thread'},
                {label: '其他', value: 'other'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const types = ['button', 'zipper', 'label', 'thread', 'other']
            const materials = ['metal', 'plastic', 'wood', 'fabric']
            const typeLabels = {
                button: '纽扣',
                zipper: '拉链',
                label: '标签',
                thread: '线材',
                other: '其他'
            }
            const materialLabels = {
                metal: '金属',
                plastic: '塑料',
                wood: '木质',
                fabric: '布料'
            }

            const accessories = [
                '圆形纽扣', '方形纽扣', '金属拉链', '塑料拉链', '品牌标签',
                '尺码标签', '棉线', '涤纶线', '装饰扣', '暗扣',
                '魔术贴', '松紧带', '织带', '绳带', '铆钉',
                '眼扣', '四合扣', '按扣', '拉头', '拉片'
            ]

            this.dataList = Array.from({length: 50}, (_, index) => {
                const type = types[index % types.length]
                const material = materials[index % materials.length]
                const accessory = accessories[index % accessories.length]
                const stock = Math.floor(Math.random() * 1000)
                const minStock = Math.floor(Math.random() * 100) + 50

                return {
                    id: index + 1,
                    name: accessory,
                    code: `ACC${String(index + 1).padStart(4, '0')}`,
                    model: `M${String(index + 1).padStart(3, '0')}`,
                    type: type,
                    type_label: typeLabels[type],
                    material: material,
                    material_label: materialLabels[material],
                    description: `高品质${materialLabels[material]}材质的${accessory}`,
                    supplier: `供应商${Math.floor(index / 5) + 1}`,
                    supplier_code: `SUP${String(Math.floor(index / 5) + 1).padStart(3, '0')}`,
                    unit_price: (Math.random() * 10 + 0.5).toFixed(2),
                    stock_quantity: stock,
                    min_stock: minStock,
                    stock_status: this.getStockStatus(stock, minStock),
                    unit: this.getUnit(type),
                    specifications: this.getSpecifications(type, material),
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 获取库存状态
        getStockStatus(stock, minStock) {
            if (stock === 0) return 'out_of_stock'
            if (stock <= minStock) return 'insufficient'
            return 'sufficient'
        },

        // 获取单位
        getUnit(type) {
            const unitMap = {
                button: '个',
                zipper: '条',
                label: '张',
                thread: '卷',
                other: '个'
            }
            return unitMap[type] || '个'
        },

        // 获取规格
        getSpecifications(type, material) {
            const specs = {
                button: '直径12mm',
                zipper: '长度20cm',
                label: '5x2cm',
                thread: '1000m/卷',
                other: '标准规格'
            }
            return specs[type] || '标准规格'
        },

        // 获取库存不足数量
        getInsufficientStockCount() {
            return this.dataList.filter(item =>
                item.stock_status === 'insufficient' || item.stock_status === 'out_of_stock'
            ).length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                type: '',
                material: '',
                stock_status: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的配件吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑配件: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除配件 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看配件详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleStockUpdate(row) {
            this.$message.success(`${row.name} 库存已更新`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.accessory-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
