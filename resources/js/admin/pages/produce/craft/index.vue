<!--
/**
 * 工艺管理页面
 *
 * 功能特性：
 * - 管理生产工艺和技术
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/produce/craft
 * 页面标题：工艺管理
 *
 * 版本：v1.0.0
 * 更新时间：2025年1月
 * 作者：Augment Agent
 */
-->

<template>
    <div class="craft-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索工艺名称、类型或技术要求'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="工艺类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option
                                    v-for="(label, type) in CRAFT_TYPE_LABELS"
                                    :key="type"
                                    :label="label"
                                    :value="type"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="难度等级">
                            <el-select v-model="filters.difficulty" clearable placeholder="选择难度">
                                <el-option label="简单" value="easy"></el-option>
                                <el-option label="中等" value="medium"></el-option>
                                <el-option label="困难" value="hard"></el-option>
                                <el-option label="专家级" value="expert"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="适用材料">
                            <el-select v-model="filters.material" clearable placeholder="选择材料">
                                <el-option label="棉质" value="cotton"></el-option>
                                <el-option label="丝绸" value="silk"></el-option>
                                <el-option label="羊毛" value="wool"></el-option>
                                <el-option label="合成纤维" value="synthetic"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                                <el-option label="测试中" value="testing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 工艺列表 -->
            <template #default>
                <CraftListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @test="handleTest"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总工艺: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import CraftListView from './components/CraftListView.vue'

export default {
    name: 'AdminProduceCraftIndexPage',
    components: {
        BackendPageListLayout,
        CraftListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                difficulty: '',
                material: '',
                status: ''
            },

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部工艺', value: 'all'},
                {label: '缝制工艺', value: 'sewing'},
                {label: '印染工艺', value: 'dyeing'},
                {label: '整理工艺', value: 'finishing'},
                {label: '装饰工艺', value: 'decoration'}
            ],

            // 数据相关
            dataList: [],
            selectedRows: [],

            // 常量定义
            CRAFT_TYPE_LABELS: {
                'sewing': '缝制工艺',
                'dyeing': '印染工艺',
                'finishing': '整理工艺',
                'decoration': '装饰工艺',
                'cutting': '裁剪工艺',
                'pressing': '熨烫工艺'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.loading = true
                this.v_loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟工艺数据
                const mockData = [
                    {
                        id: 1,
                        name: '精密平缝工艺',
                        code: 'CRA001',
                        type: 'sewing',
                        difficulty: 'medium',
                        description: '适用于高档服装的精密平缝技术',
                        materials: ['cotton', 'silk'],
                        time_required: '2-3小时',
                        equipment: ['平缝机', '专用压脚'],
                        quality_score: 95,
                        cost_level: 'medium',
                        status: 'active',
                        created_at: '2025-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '天然植物染色',
                        code: 'CRA002',
                        type: 'dyeing',
                        difficulty: 'hard',
                        description: '使用天然植物提取物进行环保染色',
                        materials: ['cotton', 'wool'],
                        time_required: '6-8小时',
                        equipment: ['染缸', '温控设备'],
                        quality_score: 88,
                        cost_level: 'high',
                        status: 'active',
                        created_at: '2025-01-14 15:20:00'
                    }
                ]

                this.dataList = mockData
                this.total = 45 // 模拟总数

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(query) {
            this.searchQuery = query
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                type: '',
                difficulty: '',
                material: '',
                status: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的工艺吗？', '批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑工艺: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除工艺 "${row.name}" 吗？`, '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看工艺详情: ${row.name}`)
        },

        handleTest(row) {
            this.$message.info(`测试工艺: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`工艺状态已更新: ${row.name}`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.craft-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            color: #666;
            font-size: 14px;

            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
