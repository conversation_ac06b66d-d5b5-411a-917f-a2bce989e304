<!--
/**
 * 生产定制管理页面
 * 路由路径：/admin/produce/custom
 * 页面标题：生产定制
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="produce-custom-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索定制名称、类型或客户'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <template #left-section>
                <el-button type="primary" @click="handleCreate"><i class="fas fa-plus"></i> 新增定制</el-button>
                <el-button :class="{ 'is-active': showAdvancedSearch }" type="primary" @click="toggleAdvancedSearch"><i
                    class="fas fa-filter"></i> 高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport"><i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh"><i class="fas fa-sync-alt"></i> 刷新
                </el-button>
                <el-button v-if="hasSelectedRows" :disabled="!hasSelectedRows" type="danger" @click="handleBatchDelete">
                    <i class="fas fa-trash"></i> 批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="定制类型">
                            <el-select v-model="filters.custom_type" clearable placeholder="选择类型">
                                <el-option label="个人定制" value="personal"></el-option>
                                <el-option label="企业定制" value="enterprise"></el-option>
                                <el-option label="批量定制" value="batch"></el-option>
                                <el-option label="高端定制" value="luxury"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="定制状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="进行中" value="processing"></el-option>
                                <el-option label="已完成" value="completed"></el-option>
                                <el-option label="已取消" value="cancelled"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch"><i class="fas fa-search"></i> 搜索
                            </el-button>
                            <el-button @click="resetFilters"><i class="fas fa-undo"></i> 重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>
            <template #default>
                <el-table :data="dataList" :loading="loading" border class="admin-table" stripe
                          @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55"/>
                    <el-table-column label="ID" prop="id" sortable width="80"/>
                    <el-table-column label="定制名称" min-width="180" prop="name"/>
                    <el-table-column label="定制类型" prop="custom_type" width="120">
                        <template #default="{ row }">
                            <el-tag size="small" type="primary">{{ getCustomTypeName(row.custom_type) }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="客户名称" prop="customer_name" width="120"/>
                    <el-table-column label="状态" prop="status" width="100">
                        <template #default="{ row }">
                            <el-tag :type="getStatusType(row.status)" size="small">{{
                                    getStatusName(row.status)
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="created_at" width="180"/>
                    <el-table-column align="center" fixed="right" label="操作" width="200">
                        <template #default="{ row }">
                            <el-button size="small" type="primary" @click="handleViewDetail(row)">查看</el-button>
                            <el-button size="small" type="success" @click="handleEdit(row)">编辑</el-button>
                            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <template #footer-left-section>
                <div class="footer-stats">
                    <span class="stat-item">总定制: <strong>{{ total }}</strong></span>
                    <span v-if="hasSelectedRows" class="stat-item">已选择: <strong>{{ selectedRows.length }}</strong> 项</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'

export default {
    name: 'AdminProduceCustomIndexPage',
    components: {BackendPageListLayout},
    data() {
        return {
            v_loading: false, loading: false, exportLoading: false, searchQuery: '', showAdvancedSearch: false,
            filters: {custom_type: '', status: ''}, currentPage: 1, pageSize: 20, total: 0, activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部', badge: 0},
                {name: 'processing', label: '进行中', badge: 0},
                {name: 'completed', label: '已完成', badge: 0},
                {name: 'cancelled', label: '已取消', badge: 0}
            ],
            dataList: [], selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        async loadData() {
            try {
                this.v_loading = true
                await new Promise(resolve => setTimeout(resolve, 1000))
                this.dataList = [
                    {
                        id: 1,
                        name: '高端西装定制',
                        custom_type: 'luxury',
                        customer_name: '张先生',
                        status: 'processing',
                        created_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '企业工装定制',
                        custom_type: 'enterprise',
                        customer_name: '某某公司',
                        status: 'completed',
                        created_at: '2024-01-14 09:45:00'
                    }
                ]
                this.total = 67
                this.updateTabBadges()
            } catch (error) {
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
            }
        },
        updateTabBadges() {
            this.tabOptions[0].badge = this.total;
            this.tabOptions[1].badge = 34;
            this.tabOptions[2].badge = 28;
            this.tabOptions[3].badge = 5
        },
        getCustomTypeName(type) {
            const map = {'personal': '个人定制', 'enterprise': '企业定制', 'batch': '批量定制', 'luxury': '高端定制'}
            return map[type] || type
        },
        getStatusName(status) {
            const map = {'processing': '进行中', 'completed': '已完成', 'cancelled': '已取消'};
            return map[status] || status
        },
        getStatusType(status) {
            const map = {'processing': 'warning', 'completed': 'success', 'cancelled': 'danger'};
            return map[status] || 'info'
        },
        handleTabChange(tab) {
            this.activeTab = tab.name;
            this.currentPage = 1;
            this.loadData()
        },
        handlePageChange(page) {
            this.currentPage = page;
            this.loadData()
        },
        handlePageSizeChange(size) {
            this.pageSize = size;
            this.currentPage = 1;
            this.loadData()
        },
        handleSearchInput(value) {
            this.searchQuery = value;
            this.currentPage = 1;
            this.loadData()
        },
        handleSearchClear() {
            this.searchQuery = '';
            this.currentPage = 1;
            this.loadData()
        },
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },
        handleAdvancedSearch() {
            this.currentPage = 1;
            this.loadData()
        },
        resetFilters() {
            this.filters = {custom_type: '', status: ''};
            this.handleAdvancedSearch()
        },
        handleCreate() {
            this.$message.info('新增生产定制功能待实现')
        },
        handleEdit(row) {
            this.$message.info(`编辑生产定制: ${row.name}`)
        },
        handleDelete(row) {
            this.$confirm(`确定要删除生产定制 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(() => {
                this.$message.success('删除成功');
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },
        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个生产定制吗？`, '确认批量删除', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(() => {
                this.$message.success('批量删除成功');
                this.selectedRows = [];
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },
        handleExport() {
            this.exportLoading = true;
            setTimeout(() => {
                this.exportLoading = false;
                this.$message.success('导出成功')
            }, 2000)
        },
        handleRefresh() {
            this.loadData()
        },
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },
        handleViewDetail(row) {
            this.$message.info(`查看生产定制详情: ${row.name}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.produce-custom-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
