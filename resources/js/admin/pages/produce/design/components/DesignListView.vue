<!--
/**
 * 设计列表视图组件
 *
 * 功能特性：
 * - 设计数据表格展示
 * - 支持排序和筛选
 * - 行选择和批量操作
 * - 状态切换和操作按钮
 * - 设计预览功能
 *
 * 版本：v1.0.0
 * 更新时间：2025年1月
 * 作者：Augment Agent
 */
-->

<template>
    <div class="design-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>

            <el-table-column
                label="设计信息"
                min-width="250"
            >
                <template #default="{ row }">
                    <div class="design-info">
                        <div class="design-header">
                            <div class="design-thumbnail">
                                <img
                                    :alt="row.name"
                                    :src="row.thumbnail || '/images/placeholder.jpg'"
                                    @error="handleImageError"
                                />
                            </div>
                            <div class="design-details">
                                <div class="design-name">{{ row.name }}</div>
                                <div class="design-code">编号: {{ row.code }}</div>
                                <div class="design-designer">设计师: {{ row.designer }}</div>
                            </div>
                        </div>
                        <div class="design-description">{{ row.description }}</div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="风格"
                prop="style"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag size="small" type="primary">{{ getStyleName(row.style) }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="配色方案"
                width="120"
            >
                <template #default="{ row }">
                    <div class="color-palette">
                        <div
                            v-for="(color, index) in row.colors.slice(0, 3)"
                            :key="index"
                            :style="{ backgroundColor: color }"
                            :title="color"
                            class="color-dot"
                        ></div>
                        <span v-if="row.colors.length > 3" class="color-more">+{{ row.colors.length - 3 }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                label="标签"
                width="150"
            >
                <template #default="{ row }">
                    <div class="design-tags">
                        <el-tag
                            v-for="tag in row.tags.slice(0, 2)"
                            :key="tag"
                            class="tag-item"
                            size="small"
                        >
                            {{ tag }}
                        </el-tag>
                        <span v-if="row.tags.length > 2" class="tags-more">+{{ row.tags.length - 2 }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="数据统计"
                width="120"
            >
                <template #default="{ row }">
                    <div class="design-stats">
                        <div class="stat-item">
                            <i class="fas fa-eye"></i>
                            {{ row.views }}
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-heart"></i>
                            {{ row.likes }}
                        </div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            />

            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="250"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="info"
                        @click="handlePreview(row)"
                    >
                        <i class="fas fa-search"></i>
                        预览
                    </el-button>
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleViewDetail(row)"
                    >
                        <i class="fas fa-eye"></i>
                        查看
                    </el-button>
                    <el-button
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        <i class="fas fa-edit"></i>
                        编辑
                    </el-button>
                    <el-button
                        size="small"
                        type="danger"
                        @click="handleDelete(row)"
                    >
                        <i class="fas fa-trash"></i>
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'DesignListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'delete',
        'status-change',
        'preview'
    ],
    data() {
        return {
            // 风格映射
            styleMap: {
                'modern': '现代风格',
                'classic': '经典风格',
                'minimal': '简约风格',
                'vintage': '复古风格',
                'bohemian': '波西米亚',
                'industrial': '工业风格'
            },
            // 状态映射
            statusMap: {
                'draft': '草稿',
                'reviewing': '审核中',
                'approved': '已通过',
                'published': '已发布',
                'archived': '已下架'
            }
        }
    },
    methods: {
        // 获取风格名称
        getStyleName(style) {
            return this.styleMap[style] || style
        },

        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'draft': 'info',
                'reviewing': 'warning',
                'approved': 'success',
                'published': 'primary',
                'archived': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 处理图片加载错误
        handleImageError(event) {
            event.target.src = '/images/placeholder.jpg'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 预览设计
        handlePreview(row) {
            this.$emit('preview', row)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态切换
        handleStatusChange(row) {
            this.$emit('status-change', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.design-list-view {
    .admin-table {
        .design-info {
            .design-header {
                display: flex;
                gap: 12px;
                margin-bottom: 8px;

                .design-thumbnail {
                    width: 60px;
                    height: 60px;
                    border-radius: 6px;
                    overflow: hidden;
                    border: 1px solid #ddd;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .design-details {
                    flex: 1;

                    .design-name {
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 4px;
                    }

                    .design-code,
                    .design-designer {
                        font-size: 12px;
                        color: #666;
                        margin-bottom: 2px;
                    }
                }
            }

            .design-description {
                font-size: 12px;
                color: #888;
                line-height: 1.4;
                max-height: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
        }

        .color-palette {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;

            .color-dot {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 1px solid #ddd;
                cursor: pointer;
            }

            .color-more {
                font-size: 12px;
                color: #666;
                margin-left: 4px;
            }
        }

        .design-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;

            .tag-item {
                margin: 0;
            }

            .tags-more {
                font-size: 12px;
                color: #666;
                margin-left: 4px;
            }
        }

        .design-stats {
            .stat-item {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;

                i {
                    font-size: 10px;
                }
            }
        }
    }
}
</style>
