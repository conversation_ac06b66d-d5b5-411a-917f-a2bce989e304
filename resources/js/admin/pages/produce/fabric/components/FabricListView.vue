<!--
/**
 * 面料列表视图组件
 *
 * 功能特性：
 * - 面料数据表格展示
 * - 支持排序和筛选
 * - 行选择和批量操作
 * - 状态切换和操作按钮
 *
 * 版本：v1.0.0
 * 更新时间：2025年1月
 * 作者：Augment Agent
 */
-->

<template>
    <div class="fabric-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>

            <el-table-column
                label="面料信息"
                min-width="200"
            >
                <template #default="{ row }">
                    <div class="fabric-info">
                        <div class="fabric-name">{{ row.name }}</div>
                        <div class="fabric-code">编号: {{ row.code }}</div>
                        <div class="fabric-material">材质: {{ getMaterialName(row.material) }}</div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="类型"
                prop="type"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag size="small" type="info">{{ getTypeName(row.type) }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="颜色"
                prop="color"
                width="80"
            >
                <template #default="{ row }">
                    <div class="color-display">
                        <div
                            :style="{ backgroundColor: row.color }"
                            :title="row.color"
                            class="color-block"
                        ></div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="规格"
                width="120"
            >
                <template #default="{ row }">
                    <div class="fabric-specs">
                        <div>宽度: {{ row.width }}</div>
                        <div>重量: {{ row.weight }}</div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="right"
                label="价格"
                prop="price"
                sortable
                width="100"
            >
                <template #default="{ row }">
                    <span class="price-text">¥{{ row.price }}/m</span>
                </template>
            </el-table-column>

            <el-table-column
                align="right"
                label="库存"
                prop="stock"
                sortable
                width="100"
            >
                <template #default="{ row }">
                    <span :class="getStockClass(row.stock)">{{ row.stock }}m</span>
                </template>
            </el-table-column>

            <el-table-column
                label="供应商"
                prop="supplier"
                show-overflow-tooltip
                width="120"
            />

            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-switch
                        :value="row.status === 'active'"
                        active-text="启用"
                        inactive-text="禁用"
                        @change="handleStatusChange(row)"
                    />
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            />

            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="200"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleViewDetail(row)"
                    >
                        <i class="fas fa-eye"></i>
                        查看
                    </el-button>
                    <el-button
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        <i class="fas fa-edit"></i>
                        编辑
                    </el-button>
                    <el-button
                        size="small"
                        type="danger"
                        @click="handleDelete(row)"
                    >
                        <i class="fas fa-trash"></i>
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'FabricListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'delete',
        'status-change'
    ],
    data() {
        return {
            // 类型映射
            typeMap: {
                'plain': '平纹',
                'twill': '斜纹',
                'satin': '缎纹',
                'knit': '针织',
                'woven': '机织'
            },
            // 材质映射
            materialMap: {
                'cotton': '棉质',
                'silk': '丝绸',
                'wool': '羊毛',
                'polyester': '聚酯纤维',
                'blend': '混纺'
            }
        }
    },
    methods: {
        // 获取类型名称
        getTypeName(type) {
            return this.typeMap[type] || type
        },

        // 获取材质名称
        getMaterialName(material) {
            return this.materialMap[material] || material
        },

        // 获取库存样式类
        getStockClass(stock) {
            if (stock <= 100) {
                return 'stock-low'
            } else if (stock <= 500) {
                return 'stock-medium'
            }
            return 'stock-high'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态切换
        handleStatusChange(row) {
            const newStatus = row.status === 'active' ? 'inactive' : 'active'
            row.status = newStatus
            this.$emit('status-change', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.fabric-list-view {
    .admin-table {
        .fabric-info {
            .fabric-name {
                font-weight: 600;
                color: #333;
                margin-bottom: 4px;
            }

            .fabric-code,
            .fabric-material {
                font-size: 12px;
                color: #666;
                margin-bottom: 2px;
            }
        }

        .color-display {
            display: flex;
            justify-content: center;

            .color-block {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                border: 1px solid #ddd;
                cursor: pointer;
            }
        }

        .fabric-specs {
            font-size: 12px;
            color: #666;

            div {
                margin-bottom: 2px;
            }
        }

        .price-text {
            font-weight: 600;
            color: #e74c3c;
        }

        .stock-low {
            color: #e74c3c;
            font-weight: 600;
        }

        .stock-medium {
            color: #f39c12;
            font-weight: 600;
        }

        .stock-high {
            color: #27ae60;
            font-weight: 600;
        }
    }
}
</style>
