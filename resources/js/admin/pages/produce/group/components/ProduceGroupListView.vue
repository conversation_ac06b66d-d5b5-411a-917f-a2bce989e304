<!--
/**
 * 生产分组列表视图组件
 *
 * 功能特性：
 * - 生产分组数据表格展示
 * - 成员管理功能
 * - 状态管理和操作
 * - 响应式设计
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="produce-group-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>

            <el-table-column
                label="分组名称"
                min-width="180"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="group-name">
                        <i class="fas fa-layer-group group-icon"></i>
                        <span class="name-text">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="分组类型"
                prop="group_type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag :type="getGroupTypeColor(row.group_type)" size="small">
                        {{ getGroupTypeName(row.group_type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            />

            <el-table-column
                align="center"
                label="成员数量"
                prop="member_count"
                width="100"
            >
                <template #default="{ row }">
					<span class="member-count">
						<i class="fas fa-users"></i>
						{{ row.member_count }}
					</span>
                </template>
            </el-table-column>

            <el-table-column
                label="组长"
                prop="leader"
                width="120"
            />

            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            />

            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="320"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleViewDetail(row)"
                    >
                        <i class="fas fa-eye"></i>
                        查看
                    </el-button>
                    <el-button
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        <i class="fas fa-edit"></i>
                        编辑
                    </el-button>
                    <el-button
                        size="small"
                        type="warning"
                        @click="handleManageMembers(row)"
                    >
                        <i class="fas fa-users-cog"></i>
                        成员
                    </el-button>
                    <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="toggle-status">
                                    <i class="fas fa-toggle-on"></i>
                                    {{ row.status === 'active' ? '停用' : '启用' }}
                                </el-dropdown-item>
                                <el-dropdown-item command="copy">
                                    <i class="fas fa-copy"></i>
                                    复制分组
                                </el-dropdown-item>
                                <el-dropdown-item command="export-members">
                                    <i class="fas fa-download"></i>
                                    导出成员
                                </el-dropdown-item>
                                <el-dropdown-item command="delete" divided>
                                    <i class="fas fa-trash"></i>
                                    删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ProduceGroupListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'view-detail',
        'edit',
        'delete',
        'status-change',
        'manage-members'
    ],
    data() {
        return {
            // 状态映射
            statusMap: {
                'active': '启用中',
                'inactive': '已停用',
                'archived': '已归档',
                'pending': '待审核'
            },

            // 分组类型映射
            groupTypeMap: {
                'product': '产品分组',
                'process': '工艺分组',
                'department': '部门分组',
                'project': '项目分组'
            }
        }
    },
    methods: {
        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'inactive': 'info',
                'archived': 'warning',
                'pending': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 获取分组类型名称
        getGroupTypeName(type) {
            return this.groupTypeMap[type] || type
        },

        // 获取分组类型颜色
        getGroupTypeColor(type) {
            const colorMap = {
                'product': 'primary',
                'process': 'success',
                'department': 'warning',
                'project': 'info'
            }
            return colorMap[type] || 'info'
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 管理成员
        handleManageMembers(row) {
            this.$emit('manage-members', row)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'toggle-status':
                    const newStatus = row.status === 'active' ? 'inactive' : 'active'
                    this.handleStatusChange(row, newStatus)
                    break
                case 'copy':
                    this.$message.info(`复制生产分组 "${row.name}"`)
                    break
                case 'export-members':
                    this.$message.info(`导出分组 "${row.name}" 的成员列表`)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.produce-group-list-view {
    .admin-table {
        .group-name {
            display: flex;
            align-items: center;
            gap: 8px;

            .group-icon {
                color: #409eff;
                font-size: 16px;
            }

            .name-text {
                font-weight: 500;
            }
        }

        .member-count {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #606266;

            .fas {
                font-size: 12px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .produce-group-list-view {
        :deep(.el-table) {
            .el-table__body-wrapper {
                overflow-x: auto;
            }
        }
    }
}
</style>
