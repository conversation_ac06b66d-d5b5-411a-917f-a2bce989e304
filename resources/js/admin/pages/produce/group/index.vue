<!--
/**
 * 生产分组管理页面
 *
 * 功能特性：
 * - 管理生产分组和组织结构
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/produce/group
 * 页面标题：生产分组
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="produce-group-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索分组名称、类型或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增分组
                </el-button>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="分组类型">
                            <el-select v-model="filters.group_type" clearable placeholder="选择类型">
                                <el-option label="产品分组" value="product"></el-option>
                                <el-option label="工艺分组" value="process"></el-option>
                                <el-option label="部门分组" value="department"></el-option>
                                <el-option label="项目分组" value="project"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分组状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in GROUP_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 生产分组列表 -->
            <template #default>
                <ProduceGroupListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                    @manage-members="handleManageMembers"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总分组: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ProduceGroupListView from './components/ProduceGroupListView.vue'

export default {
    name: 'AdminProduceGroupIndexPage',
    components: {
        BackendPageListLayout,
        ProduceGroupListView
    },
    data() {
        return {
            // 基础状态
            v_loading: false,
            loading: false,
            exportLoading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                group_type: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部', badge: 0},
                {name: 'active', label: '启用中', badge: 0},
                {name: 'inactive', label: '已停用', badge: 0},
                {name: 'archived', label: '已归档', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 状态标签
            GROUP_STATUS_LABELS: {
                'active': '启用中',
                'inactive': '已停用',
                'archived': '已归档',
                'pending': '待审核'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 数据加载
        async loadData() {
            try {
                this.v_loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟生产分组数据
                const mockData = [
                    {
                        id: 1,
                        name: '服装生产组',
                        group_type: 'product',
                        description: '负责服装类产品的生产制造',
                        member_count: 25,
                        leader: '张组长',
                        status: 'active',
                        created_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '质量检测组',
                        group_type: 'process',
                        description: '负责产品质量检测和控制',
                        member_count: 12,
                        leader: '李主管',
                        status: 'active',
                        created_at: '2024-01-14 09:45:00'
                    }
                ]

                this.dataList = mockData
                this.total = 45
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 28
            this.tabOptions[2].badge = 12
            this.tabOptions[3].badge = 5
        },

        // 事件处理
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                group_type: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        // 操作方法
        handleCreate() {
            this.$message.info('新增生产分组功能待实现')
        },

        handleEdit(row) {
            this.$message.info(`编辑生产分组: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除生产分组 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个生产分组吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('批量删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleRefresh() {
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleStatusChange(row, status) {
            this.$message.success(`生产分组状态已更新为: ${this.GROUP_STATUS_LABELS[status]}`)
            this.loadData()
        },

        handleViewDetail(row) {
            this.$message.info(`查看生产分组详情: ${row.name}`)
        },

        handleManageMembers(row) {
            this.$message.info(`管理分组成员: ${row.name}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.produce-group-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
