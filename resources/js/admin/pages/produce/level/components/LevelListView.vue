<!--
/**
 * 等级列表视图组件
 *
 * 功能特性：
 * - 等级数据表格展示
 * - 评分和权重显示
 * - 等级图标和颜色展示
 * - 行选择和批量操作
 * - 状态切换
 * - 操作按钮组
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="level-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            row-key="id"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 等级信息 -->
            <el-table-column label="等级信息" min-width="180">
                <template #default="{ row }">
                    <div class="level-info">
                        <div class="level-header">
                            <i :class="row.icon" :style="{ color: row.color }"></i>
                            <span :style="{ color: row.color }" class="name">{{ row.name }}</span>
                        </div>
                        <div class="details">
                            <span class="code">{{ row.code }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 类型 -->
            <el-table-column align="center" label="类型" width="120">
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ row.type_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 评分 -->
            <el-table-column align="center" label="评分" width="120">
                <template #default="{ row }">
                    <div class="score-info">
                        <div :style="{ color: row.color }" class="score-value">
                            {{ row.score }}分
                        </div>
                        <el-progress
                            :color="row.color"
                            :percentage="row.score"
                            :show-text="false"
                            :stroke-width="6"
                        />
                    </div>
                </template>
            </el-table-column>

            <!-- 权重 -->
            <el-table-column align="center" label="权重" width="100">
                <template #default="{ row }">
                    <span class="weight-value">{{ row.weight }}</span>
                </template>
            </el-table-column>

            <!-- 评判标准 -->
            <el-table-column label="评判标准" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    <div class="criteria-info">
                        <div class="criteria-text">{{ row.criteria }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 要求描述 -->
            <el-table-column label="要求描述" min-width="180" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.requirements }}
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column label="描述" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.description }}
                </template>
            </el-table-column>

            <!-- 使用次数 -->
            <el-table-column align="center" label="使用次数" width="100">
                <template #default="{ row }">
                    <span class="usage-count">{{ row.usage_count }}</span>
                </template>
            </el-table-column>

            <!-- 排序 -->
            <el-table-column align="center" label="排序" width="80">
                <template #default="{ row }">
                    {{ row.sort_order }}
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" width="100">
                <template #default="{ row }">
                    <el-switch
                        :model-value="row.status === 'active'"
                        @change="handleStatusChange(row)"
                    />
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" width="120">
                <template #default="{ row }">
                    {{ row.created_at }}
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column align="center" fixed="right" label="操作" width="180">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            link
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="danger"
                            @click="handleDelete(row)"
                        >
                            <i class="fas fa-trash"></i>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'LevelListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'status-change',
        'view-detail',
        'edit',
        'delete'
    ],
    methods: {
        // 获取类型标签类型
        getTypeTagType(type) {
            const typeMap = {
                quality: 'success',
                price: 'primary',
                craft: 'warning',
                material: 'info'
            }
            return typeMap[type] || 'info'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 处理状态变化
        handleStatusChange(row) {
            row.status = row.status === 'active' ? 'inactive' : 'active'
            this.$emit('status-change', row)
        },

        // 处理查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 处理编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理删除
        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.level-list-view {
    .level-info {
        .level-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            i {
                font-size: 16px;
            }

            .name {
                font-weight: 600;
                font-size: 14px;
            }
        }

        .details {
            .code {
                font-size: 12px;
                color: #909399;
            }
        }
    }

    .score-info {
        .score-value {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }

        :deep(.el-progress) {
            .el-progress-bar__outer {
                background-color: #f0f2f5;
            }
        }
    }

    .weight-value {
        font-weight: 600;
        color: #606266;
    }

    .criteria-info {
        .criteria-text {
            color: #606266;
            font-size: 13px;
        }
    }

    .usage-count {
        font-weight: 600;
        color: #409eff;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;

        .el-button {
            padding: 4px 8px;

            i {
                margin-right: 4px;
            }
        }
    }

    :deep(.el-table) {
        .el-table__row {
            &:hover {
                background-color: #f5f7fa;
            }
        }

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
