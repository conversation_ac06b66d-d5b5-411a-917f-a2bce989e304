<!--
/**
 * 等级管理页面
 *
 * 功能特性：
 * - 管理产品等级和品质分级
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 等级权重和评分管理
 *
 * 路由路径：/admin/produce/level
 * 页面标题：等级管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="level-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索等级名称、代码或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="等级类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="品质等级" value="quality"></el-option>
                                <el-option label="价格等级" value="price"></el-option>
                                <el-option label="工艺等级" value="craft"></el-option>
                                <el-option label="材料等级" value="material"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="评分范围">
                            <el-select v-model="filters.score_range" clearable placeholder="选择评分范围">
                                <el-option label="全部范围" value=""></el-option>
                                <el-option label="90-100分" value="90-100"></el-option>
                                <el-option label="80-89分" value="80-89"></el-option>
                                <el-option label="70-79分" value="70-79"></el-option>
                                <el-option label="60-69分" value="60-69"></el-option>
                                <el-option label="60分以下" value="0-59"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 等级列表 -->
            <template #default>
                <LevelListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总等级: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						平均评分: <strong>{{ getAverageScore() }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import LevelListView from './components/LevelListView.vue'

export default {
    name: 'AdminProduceLevelIndexPage',
    components: {
        BackendPageListLayout,
        LevelListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                score_range: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部等级', value: 'all'},
                {label: '品质等级', value: 'quality'},
                {label: '价格等级', value: 'price'},
                {label: '工艺等级', value: 'craft'},
                {label: '材料等级', value: 'material'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const types = ['quality', 'price', 'craft', 'material']
            const typeLabels = {
                quality: '品质等级',
                price: '价格等级',
                craft: '工艺等级',
                material: '材料等级'
            }

            const levels = [
                'S级', 'A级', 'B级', 'C级', 'D级',
                '特级', '一级', '二级', '三级', '四级',
                '优秀', '良好', '合格', '待改进', '不合格',
                '顶级', '高级', '中级', '初级', '入门'
            ]

            this.dataList = Array.from({length: 50}, (_, index) => {
                const type = types[index % types.length]
                const level = levels[index % levels.length]
                const score = Math.floor(Math.random() * 40) + 60 // 60-100分

                return {
                    id: index + 1,
                    name: level,
                    code: `LV${String(index + 1).padStart(3, '0')}`,
                    type: type,
                    type_label: typeLabels[type],
                    description: `${typeLabels[type]}中的${level}标准`,
                    score: score,
                    weight: (Math.random() * 0.5 + 0.5).toFixed(2), // 0.5-1.0权重
                    criteria: this.generateCriteria(level, type),
                    requirements: this.generateRequirements(level),
                    color: this.generateLevelColor(score),
                    icon: this.generateLevelIcon(level),
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    usage_count: Math.floor(Math.random() * 500),
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 生成评判标准
        generateCriteria(level, type) {
            const criteriaMap = {
                quality: ['外观质量', '功能性能', '耐用性'],
                price: ['成本控制', '市场定位', '性价比'],
                craft: ['工艺复杂度', '技术要求', '制作精度'],
                material: ['材料品质', '环保标准', '安全性能']
            }
            return criteriaMap[type] ? criteriaMap[type].join(', ') : '综合评判标准'
        },

        // 生成要求描述
        generateRequirements(level) {
            const requirements = [
                '符合行业标准',
                '通过质量检测',
                '满足客户需求',
                '达到预期效果'
            ]
            return requirements.slice(0, 2).join('; ')
        },

        // 生成等级颜色
        generateLevelColor(score) {
            if (score >= 90) return '#67c23a'
            if (score >= 80) return '#409eff'
            if (score >= 70) return '#e6a23c'
            if (score >= 60) return '#f56c6c'
            return '#909399'
        },

        // 生成等级图标
        generateLevelIcon(level) {
            const iconMap = {
                'S级': 'fas fa-crown',
                'A级': 'fas fa-star',
                'B级': 'fas fa-medal',
                'C级': 'fas fa-award',
                'D级': 'fas fa-certificate'
            }
            return iconMap[level] || 'fas fa-star'
        },

        // 计算平均评分
        getAverageScore() {
            if (this.dataList.length === 0) return '0.0'
            const total = this.dataList.reduce((sum, item) => sum + item.score, 0)
            return (total / this.dataList.length).toFixed(1)
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                type: '',
                score_range: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的等级吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑等级: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除等级 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看等级详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.level-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
