<!--
/**
 * 挂钩管理页面
 *
 * 功能特性：
 * - 管理生产流程中的挂钩和连接点
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 挂钩类型和状态管理
 *
 * 路由路径：/admin/produce/peg
 * 页面标题：挂钩管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="peg-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索挂钩名称、编码或连接点'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="挂钩类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="流程挂钩" value="process"></el-option>
                                <el-option label="数据挂钩" value="data"></el-option>
                                <el-option label="事件挂钩" value="event"></el-option>
                                <el-option label="接口挂钩" value="api"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="连接状态">
                            <el-select v-model="filters.connection_status" clearable placeholder="选择连接状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="已连接" value="connected"></el-option>
                                <el-option label="未连接" value="disconnected"></el-option>
                                <el-option label="连接中" value="connecting"></el-option>
                                <el-option label="连接失败" value="failed"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="优先级">
                            <el-select v-model="filters.priority" clearable placeholder="选择优先级">
                                <el-option label="全部优先级" value=""></el-option>
                                <el-option label="高优先级" value="high"></el-option>
                                <el-option label="中优先级" value="medium"></el-option>
                                <el-option label="低优先级" value="low"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 挂钩列表 -->
            <template #default>
                <PegListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                    @test-connection="handleTestConnection"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总挂钩: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已连接: <strong>{{ getConnectedCount() }}</strong>
					</span>
                    <span class="stat-item">
						连接失败: <strong>{{ getFailedCount() }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import PegListView from './components/PegListView.vue'

export default {
    name: 'AdminProducePegIndexPage',
    components: {
        BackendPageListLayout,
        PegListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                connection_status: '',
                priority: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部挂钩', value: 'all'},
                {label: '流程挂钩', value: 'process'},
                {label: '数据挂钩', value: 'data'},
                {label: '事件挂钩', value: 'event'},
                {label: '接口挂钩', value: 'api'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const types = ['process', 'data', 'event', 'api']
            const connectionStatuses = ['connected', 'disconnected', 'connecting', 'failed']
            const priorities = ['high', 'medium', 'low']

            const typeLabels = {
                process: '流程挂钩',
                data: '数据挂钩',
                event: '事件挂钩',
                api: '接口挂钩'
            }

            const connectionStatusLabels = {
                connected: '已连接',
                disconnected: '未连接',
                connecting: '连接中',
                failed: '连接失败'
            }

            const priorityLabels = {
                high: '高优先级',
                medium: '中优先级',
                low: '低优先级'
            }

            const pegs = [
                '生产开始挂钩', '质检完成挂钩', '包装流程挂钩', '发货通知挂钩', '库存更新挂钩',
                '订单状态挂钩', '支付完成挂钩', '用户注册挂钩', '数据同步挂钩', '日志记录挂钩',
                '异常处理挂钩', '性能监控挂钩', '安全检查挂钩', '备份完成挂钩', '清理任务挂钩',
                '邮件发送挂钩', '短信通知挂钩', '推送消息挂钩', '文件上传挂钩', '缓存更新挂钩'
            ]

            this.dataList = Array.from({length: 50}, (_, index) => {
                const type = types[index % types.length]
                const connectionStatus = connectionStatuses[index % connectionStatuses.length]
                const priority = priorities[index % priorities.length]
                const peg = pegs[index % pegs.length]

                return {
                    id: index + 1,
                    name: peg,
                    code: `PEG${String(index + 1).padStart(3, '0')}`,
                    type: type,
                    type_label: typeLabels[type],
                    connection_status: connectionStatus,
                    connection_status_label: connectionStatusLabels[connectionStatus],
                    priority: priority,
                    priority_label: priorityLabels[priority],
                    description: `${typeLabels[type]}的${peg}配置`,
                    endpoint: this.generateEndpoint(type, index),
                    trigger_condition: this.generateTriggerCondition(type),
                    execution_count: Math.floor(Math.random() * 10000),
                    last_execution: this.generateLastExecution(),
                    response_time: (Math.random() * 1000).toFixed(0) + 'ms',
                    success_rate: (Math.random() * 30 + 70).toFixed(1) + '%',
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 生成端点地址
        generateEndpoint(type, index) {
            const endpoints = {
                process: `/api/process/hook/${index + 1}`,
                data: `/api/data/sync/${index + 1}`,
                event: `/api/events/trigger/${index + 1}`,
                api: `/api/webhook/${index + 1}`
            }
            return endpoints[type] || `/api/hook/${index + 1}`
        },

        // 生成触发条件
        generateTriggerCondition(type) {
            const conditions = {
                process: '流程状态变更时',
                data: '数据更新时',
                event: '特定事件发生时',
                api: 'API调用时'
            }
            return conditions[type] || '条件满足时'
        },

        // 生成最后执行时间
        generateLastExecution() {
            const now = new Date()
            const randomHours = Math.floor(Math.random() * 24)
            const lastExecution = new Date(now.getTime() - randomHours * 60 * 60 * 1000)
            return lastExecution.toISOString().slice(0, 16).replace('T', ' ')
        },

        // 获取已连接数量
        getConnectedCount() {
            return this.dataList.filter(item => item.connection_status === 'connected').length
        },

        // 获取连接失败数量
        getFailedCount() {
            return this.dataList.filter(item => item.connection_status === 'failed').length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                type: '',
                connection_status: '',
                priority: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的挂钩吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑挂钩: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除挂钩 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看挂钩详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleTestConnection(row) {
            this.$message.info(`正在测试挂钩连接: ${row.name}`)
            // 模拟测试连接
            setTimeout(() => {
                this.$message.success('连接测试成功')
            }, 1500)
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.peg-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
