<!--
/**
 * 生产属性列表视图组件
 *
 * 功能特性：
 * - 属性数据表格展示
 * - 数据类型和验证规则展示
 * - 默认值和取值范围展示
 * - 行选择和批量操作
 * - 状态切换
 * - 操作按钮组
 *
 * 版本：v1.0.0
 * 作者：Admin Team
 */
-->

<template>
    <div class="property-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            row-key="id"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 属性信息 -->
            <el-table-column label="属性信息" min-width="180">
                <template #default="{ row }">
                    <div class="property-info">
                        <div class="property-header">
                            <i :class="getPropertyIcon(row.type)" :style="{ color: getPropertyColor(row.type) }"></i>
                            <span class="name">{{ row.name }}</span>
                            <el-tag v-if="row.required" effect="plain" size="small" type="danger">必填</el-tag>
                        </div>
                        <div class="details">
                            <span class="code">{{ row.code }}</span>
                            <span class="group">{{ row.group_name }}</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 属性类型 -->
            <el-table-column align="center" label="属性类型" width="120">
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ row.type_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 数据类型 -->
            <el-table-column align="center" label="数据类型" width="100">
                <template #default="{ row }">
                    <el-tag
                        :type="getDataTypeTagType(row.data_type)"
                        effect="plain"
                        size="small"
                    >
                        {{ row.data_type_label }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 默认值 -->
            <el-table-column align="center" label="默认值" width="120">
                <template #default="{ row }">
                    <div class="default-value">
                        <span class="value">{{ row.default_value || '-' }}</span>
                        <span v-if="row.unit" class="unit">{{ row.unit }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 取值范围 -->
            <el-table-column align="center" label="取值范围" min-width="150">
                <template #default="{ row }">
                    <div class="value-range">
                        <div v-if="row.data_type === 'number' && (row.min_value || row.max_value)" class="number-range">
							<span class="range-text">
								{{ row.min_value || '∞' }} ~ {{ row.max_value || '∞' }}
							</span>
                            <span v-if="row.unit" class="unit">{{ row.unit }}</span>
                        </div>
                        <div v-else-if="row.data_type === 'select' && row.options" class="select-options">
                            <el-tooltip :content="row.options" placement="top">
                                <span class="options-preview">{{ getOptionsPreview(row.options) }}</span>
                            </el-tooltip>
                        </div>
                        <div v-else class="no-range">
                            <span>-</span>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 验证规则 -->
            <el-table-column label="验证规则" min-width="150" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.validation_rules }}
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column label="描述" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.description }}
                </template>
            </el-table-column>

            <!-- 使用次数 -->
            <el-table-column align="center" label="使用次数" width="100">
                <template #default="{ row }">
                    <span class="usage-count">{{ row.usage_count }}</span>
                </template>
            </el-table-column>

            <!-- 显示顺序 -->
            <el-table-column align="center" label="显示顺序" width="100">
                <template #default="{ row }">
                    {{ row.display_order }}
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" width="100">
                <template #default="{ row }">
                    <el-switch
                        :model-value="row.status === 'active'"
                        @change="handleStatusChange(row)"
                    />
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" width="120">
                <template #default="{ row }">
                    {{ row.created_at }}
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column align="center" fixed="right" label="操作" width="180">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            link
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-button
                            link
                            size="small"
                            type="danger"
                            @click="handleDelete(row)"
                        >
                            <i class="fas fa-trash"></i>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'PropertyListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'status-change',
        'view-detail',
        'edit',
        'delete'
    ],
    methods: {
        // 获取属性图标
        getPropertyIcon(type) {
            const iconMap = {
                physical: 'fas fa-ruler',
                chemical: 'fas fa-flask',
                mechanical: 'fas fa-cog',
                process: 'fas fa-industry',
                quality: 'fas fa-star'
            }
            return iconMap[type] || 'fas fa-tag'
        },

        // 获取属性颜色
        getPropertyColor(type) {
            const colorMap = {
                physical: '#409eff',
                chemical: '#67c23a',
                mechanical: '#e6a23c',
                process: '#f56c6c',
                quality: '#9c27b0'
            }
            return colorMap[type] || '#909399'
        },

        // 获取类型标签类型
        getTypeTagType(type) {
            const typeMap = {
                physical: 'primary',
                chemical: 'success',
                mechanical: 'warning',
                process: 'danger',
                quality: 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取数据类型标签类型
        getDataTypeTagType(dataType) {
            const dataTypeMap = {
                number: 'primary',
                text: 'success',
                boolean: 'warning',
                date: 'info',
                select: 'danger'
            }
            return dataTypeMap[dataType] || 'info'
        },

        // 获取选项预览
        getOptionsPreview(options) {
            if (!options) return '-'
            const optionList = options.split(',')
            if (optionList.length <= 2) return options
            return `${optionList.slice(0, 2).join(', ')}...`
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 处理状态变化
        handleStatusChange(row) {
            row.status = row.status === 'active' ? 'inactive' : 'active'
            this.$emit('status-change', row)
        },

        // 处理查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 处理编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理删除
        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style lang="scss" scoped>
.property-list-view {
    .property-info {
        .property-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            i {
                font-size: 14px;
            }

            .name {
                font-weight: 600;
                color: #303133;
            }
        }

        .details {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .code {
                font-size: 12px;
                color: #909399;
            }

            .group {
                font-size: 11px;
                color: #606266;
            }
        }
    }

    .default-value {
        display: flex;
        align-items: center;
        gap: 4px;

        .value {
            font-weight: 500;
            color: #606266;
        }

        .unit {
            font-size: 12px;
            color: #909399;
        }
    }

    .value-range {
        .number-range {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;

            .range-text {
                font-size: 12px;
                color: #606266;
                font-family: 'Courier New', monospace;
            }

            .unit {
                font-size: 11px;
                color: #909399;
            }
        }

        .select-options {
            .options-preview {
                font-size: 12px;
                color: #606266;
                cursor: pointer;
            }
        }

        .no-range {
            color: #c0c4cc;
        }
    }

    .usage-count {
        font-weight: 600;
        color: #409eff;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;

        .el-button {
            padding: 4px 8px;

            i {
                margin-right: 4px;
            }
        }
    }

    :deep(.el-table) {
        .el-table__row {
            &:hover {
                background-color: #f5f7fa;
            }
        }

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
