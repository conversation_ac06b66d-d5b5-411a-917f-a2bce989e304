<!--
/**
 * 色调管理页面
 *
 * 功能特性：
 * - 管理产品色调和颜色配置
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 色彩预览功能
 *
 * 路由路径：/admin/produce/shade
 * 页面标题：色调管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="shade-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索色调名称、色值或分类'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="色调分类">
                            <el-select v-model="filters.category" clearable placeholder="选择分类">
                                <el-option label="全部分类" value=""></el-option>
                                <el-option label="暖色调" value="warm"></el-option>
                                <el-option label="冷色调" value="cool"></el-option>
                                <el-option label="中性色调" value="neutral"></el-option>
                                <el-option label="特殊色调" value="special"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 色调列表 -->
            <template #default>
                <ShadeListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总色调: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ShadeListView from './components/ShadeListView.vue'

export default {
    name: 'AdminProduceShadeIndexPage',
    components: {
        BackendPageListLayout,
        ShadeListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                category: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部色调', value: 'all'},
                {label: '暖色调', value: 'warm'},
                {label: '冷色调', value: 'cool'},
                {label: '中性色调', value: 'neutral'},
                {label: '特殊色调', value: 'special'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const categories = ['warm', 'cool', 'neutral', 'special']
            const categoryLabels = {
                warm: '暖色调',
                cool: '冷色调',
                neutral: '中性色调',
                special: '特殊色调'
            }

            this.dataList = Array.from({length: 50}, (_, index) => {
                const category = categories[index % categories.length]
                return {
                    id: index + 1,
                    name: `色调${String(index + 1).padStart(3, '0')}`,
                    code: `SHADE${String(index + 1).padStart(3, '0')}`,
                    hex_value: this.generateRandomColor(),
                    rgb_value: this.generateRandomRGB(),
                    category: category,
                    category_label: categoryLabels[category],
                    description: `这是一个${categoryLabels[category]}的色调配置`,
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 生成随机颜色
        generateRandomColor() {
            return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
        },

        // 生成随机RGB值
        generateRandomRGB() {
            const r = Math.floor(Math.random() * 256)
            const g = Math.floor(Math.random() * 256)
            const b = Math.floor(Math.random() * 256)
            return `rgb(${r}, ${g}, ${b})`
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                category: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的色调吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑色调: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除色调 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看色调详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.shade-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
