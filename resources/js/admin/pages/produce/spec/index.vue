<!--
/**
 * 生产规格管理页面
 *
 * 功能特性：
 * - 管理生产规格和标准
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/produce/spec
 * 页面标题：生产规格
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="produce-spec-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索规格名称、类型或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增规格
                </el-button>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="规格类型">
                            <el-select v-model="filters.spec_type" clearable placeholder="选择类型">
                                <el-option label="尺寸规格" value="size"></el-option>
                                <el-option label="材质规格" value="material"></el-option>
                                <el-option label="工艺规格" value="process"></el-option>
                                <el-option label="质量规格" value="quality"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="规格状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用中" value="active"></el-option>
                                <el-option label="已停用" value="inactive"></el-option>
                                <el-option label="草稿" value="draft"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 生产规格列表 -->
            <template #default>
                <el-table
                    :data="dataList"
                    :loading="loading"
                    border
                    class="admin-table"
                    stripe
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55"/>
                    <el-table-column label="ID" prop="id" sortable width="80"/>
                    <el-table-column label="规格名称" min-width="180" prop="name"/>
                    <el-table-column label="规格类型" prop="spec_type" width="120">
                        <template #default="{ row }">
                            <el-tag size="small" type="primary">{{ getSpecTypeName(row.spec_type) }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="描述" min-width="200" prop="description"/>
                    <el-table-column label="状态" prop="status" width="100">
                        <template #default="{ row }">
                            <el-tag :type="getStatusType(row.status)" size="small">{{
                                    getStatusName(row.status)
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="created_at" width="180"/>
                    <el-table-column align="center" fixed="right" label="操作" width="200">
                        <template #default="{ row }">
                            <el-button size="small" type="primary" @click="handleViewDetail(row)">查看</el-button>
                            <el-button size="small" type="success" @click="handleEdit(row)">编辑</el-button>
                            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
                    <span class="stat-item">总规格: <strong>{{ total }}</strong></span>
                    <span v-if="hasSelectedRows" class="stat-item">已选择: <strong>{{ selectedRows.length }}</strong> 项</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'

export default {
    name: 'AdminProduceSpecIndexPage',
    components: {
        BackendPageListLayout
    },
    data() {
        return {
            v_loading: false,
            loading: false,
            exportLoading: false,
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {spec_type: '', status: '', start_date: '', end_date: ''},
            dateRange: [],
            currentPage: 1,
            pageSize: 20,
            total: 0,
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部', badge: 0},
                {name: 'active', label: '启用中', badge: 0},
                {name: 'inactive', label: '已停用', badge: 0},
                {name: 'draft', label: '草稿', badge: 0}
            ],
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        async loadData() {
            try {
                this.v_loading = true
                await new Promise(resolve => setTimeout(resolve, 1000))
                this.dataList = [
                    {
                        id: 1,
                        name: 'S码尺寸规格',
                        spec_type: 'size',
                        description: '小号服装尺寸标准',
                        status: 'active',
                        created_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        name: '纯棉材质规格',
                        spec_type: 'material',
                        description: '100%纯棉材质标准',
                        status: 'active',
                        created_at: '2024-01-14 09:45:00'
                    }
                ]
                this.total = 56
                this.updateTabBadges()
            } catch (error) {
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
            }
        },
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 42
            this.tabOptions[2].badge = 10
            this.tabOptions[3].badge = 4
        },
        getSpecTypeName(type) {
            const map = {'size': '尺寸规格', 'material': '材质规格', 'process': '工艺规格', 'quality': '质量规格'}
            return map[type] || type
        },
        getStatusName(status) {
            const map = {'active': '启用中', 'inactive': '已停用', 'draft': '草稿'}
            return map[status] || status
        },
        getStatusType(status) {
            const map = {'active': 'success', 'inactive': 'info', 'draft': 'warning'}
            return map[status] || 'info'
        },
        handleTabChange(tab) {
            this.activeTab = tab.name;
            this.currentPage = 1;
            this.loadData()
        },
        handlePageChange(page) {
            this.currentPage = page;
            this.loadData()
        },
        handlePageSizeChange(size) {
            this.pageSize = size;
            this.currentPage = 1;
            this.loadData()
        },
        handleSearchInput(value) {
            this.searchQuery = value;
            this.currentPage = 1;
            this.loadData()
        },
        handleSearchClear() {
            this.searchQuery = '';
            this.currentPage = 1;
            this.loadData()
        },
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },
        handleAdvancedSearch() {
            this.currentPage = 1;
            this.loadData()
        },
        resetFilters() {
            this.filters = {spec_type: '', status: '', start_date: '', end_date: ''};
            this.dateRange = [];
            this.handleAdvancedSearch()
        },
        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0];
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = '';
                this.filters.end_date = ''
            }
        },
        handleCreate() {
            this.$message.info('新增生产规格功能待实现')
        },
        handleEdit(row) {
            this.$message.info(`编辑生产规格: ${row.name}`)
        },
        handleDelete(row) {
            this.$confirm(`确定要删除生产规格 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(() => {
                this.$message.success('删除成功');
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },
        handleBatchDelete() {
            this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个生产规格吗？`, '确认批量删除', {
                confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
            }).then(() => {
                this.$message.success('批量删除成功');
                this.selectedRows = [];
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },
        handleExport() {
            this.exportLoading = true;
            setTimeout(() => {
                this.exportLoading = false;
                this.$message.success('导出成功')
            }, 2000)
        },
        handleRefresh() {
            this.loadData()
        },
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },
        handleViewDetail(row) {
            this.$message.info(`查看生产规格详情: ${row.name}`)
        }
    }
}
</script>

<style lang="scss" scoped>
.produce-spec-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
