<!--
/**
 * 生产标签管理页面
 *
 * 功能特性：
 * - 管理生产相关标签和分类标记
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 * - 标签颜色和图标管理
 *
 * 路由路径：/admin/produce/tag
 * 页面标题：生产标签
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="tag-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索标签名称、关键词或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="标签类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="工艺标签" value="craft"></el-option>
                                <el-option label="材料标签" value="material"></el-option>
                                <el-option label="质量标签" value="quality"></el-option>
                                <el-option label="功能标签" value="function"></el-option>
                                <el-option label="风格标签" value="style"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="标签颜色">
                            <el-select v-model="filters.color" clearable placeholder="选择颜色">
                                <el-option label="全部颜色" value=""></el-option>
                                <el-option label="红色" value="red"></el-option>
                                <el-option label="蓝色" value="blue"></el-option>
                                <el-option label="绿色" value="green"></el-option>
                                <el-option label="黄色" value="yellow"></el-option>
                                <el-option label="紫色" value="purple"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="使用频率">
                            <el-select v-model="filters.usage_level" clearable placeholder="选择频率">
                                <el-option label="全部频率" value=""></el-option>
                                <el-option label="高频使用" value="high"></el-option>
                                <el-option label="中频使用" value="medium"></el-option>
                                <el-option label="低频使用" value="low"></el-option>
                                <el-option label="很少使用" value="rare"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 标签列表 -->
            <template #default>
                <TagListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总标签: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						高频使用: <strong>{{ getHighUsageCount() }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import TagListView from './components/TagListView.vue'

export default {
    name: 'AdminProduceTagIndexPage',
    components: {
        BackendPageListLayout,
        TagListView
    },
    data() {
        return {
            // 加载状态
            loading: false,
            v_loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                type: '',
                color: '',
                usage_level: '',
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {label: '全部标签', value: 'all'},
                {label: '工艺标签', value: 'craft'},
                {label: '材料标签', value: 'material'},
                {label: '质量标签', value: 'quality'},
                {label: '功能标签', value: 'function'},
                {label: '风格标签', value: 'style'}
            ],

            // 数据列表
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true
            this.v_loading = true
            try {
                // 模拟API调用
                await this.simulateApiCall()
                this.generateMockData()
            } catch (error) {
                console.error('加载数据失败:', error)
            } finally {
                this.loading = false
                this.v_loading = false
            }
        },

        // 模拟API调用
        simulateApiCall() {
            return new Promise(resolve => {
                setTimeout(resolve, 800)
            })
        },

        // 生成模拟数据
        generateMockData() {
            const types = ['craft', 'material', 'quality', 'function', 'style']
            const colors = ['red', 'blue', 'green', 'yellow', 'purple']
            const usageLevels = ['high', 'medium', 'low', 'rare']

            const typeLabels = {
                craft: '工艺标签',
                material: '材料标签',
                quality: '质量标签',
                function: '功能标签',
                style: '风格标签'
            }

            const colorLabels = {
                red: '红色',
                blue: '蓝色',
                green: '绿色',
                yellow: '黄色',
                purple: '紫色'
            }

            const usageLevelLabels = {
                high: '高频使用',
                medium: '中频使用',
                low: '低频使用',
                rare: '很少使用'
            }

            const tags = [
                '环保', '防水', '透气', '抗菌', '防紫外线',
                '手工制作', '机器生产', '精工细作', '快速制造', '定制生产',
                '高端品质', '标准品质', '经济实用', '奢华级别', '入门级别',
                '多功能', '单一功能', '智能化', '传统工艺', '现代设计',
                '简约风格', '复古风格', '时尚潮流', '经典款式', '创新设计'
            ]

            this.dataList = Array.from({length: 50}, (_, index) => {
                const type = types[index % types.length]
                const color = colors[index % colors.length]
                const usageLevel = usageLevels[index % usageLevels.length]
                const tag = tags[index % tags.length]
                const usageCount = Math.floor(Math.random() * 1000)

                return {
                    id: index + 1,
                    name: tag,
                    code: `TAG${String(index + 1).padStart(3, '0')}`,
                    type: type,
                    type_label: typeLabels[type],
                    color: color,
                    color_label: colorLabels[color],
                    color_hex: this.getColorHex(color),
                    usage_level: usageLevel,
                    usage_level_label: usageLevelLabels[usageLevel],
                    description: `${typeLabels[type]}中的${tag}标识`,
                    keywords: this.generateKeywords(tag, type),
                    icon: this.generateIcon(type),
                    usage_count: usageCount,
                    related_products: Math.floor(Math.random() * 100),
                    status: Math.random() > 0.2 ? 'active' : 'inactive',
                    sort_order: index + 1,
                    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    updated_at: new Date().toISOString().split('T')[0]
                }
            })
            this.total = this.dataList.length
        },

        // 获取颜色十六进制值
        getColorHex(color) {
            const colorMap = {
                red: '#f56c6c',
                blue: '#409eff',
                green: '#67c23a',
                yellow: '#e6a23c',
                purple: '#9c27b0'
            }
            return colorMap[color] || '#909399'
        },

        // 生成关键词
        generateKeywords(tag, type) {
            const keywords = [tag, type, '生产', '制造']
            return keywords.slice(0, 3).join(', ')
        },

        // 生成图标
        generateIcon(type) {
            const iconMap = {
                craft: 'fas fa-hammer',
                material: 'fas fa-cube',
                quality: 'fas fa-star',
                function: 'fas fa-cog',
                style: 'fas fa-paint-brush'
            }
            return iconMap[type] || 'fas fa-tag'
        },

        // 获取高频使用数量
        getHighUsageCount() {
            return this.dataList.filter(item => item.usage_level === 'high').length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                type: '',
                color: '',
                usage_level: '',
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.currentPage = 1
            this.loadData()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的标签吗？', '批量删除', {
                type: 'warning'
            }).then(() => {
                this.selectedRows = []
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        // 列表操作
        handleEdit(row) {
            this.$message.info(`编辑标签: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除标签 "${row.name}" 吗？`, '删除确认', {
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            })
        },

        handleViewDetail(row) {
            this.$message.info(`查看标签详情: ${row.name}`)
        },

        handleStatusChange(row) {
            this.$message.success(`${row.name} 状态已更新`)
            this.loadData()
        },

        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.tag-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        align-items: center;
        gap: 24px;
        color: #606266;
        font-size: 14px;

        .stat-item {
            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
