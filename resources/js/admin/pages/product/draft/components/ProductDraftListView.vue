<!--
/**
 * 产品草稿列表视图组件
 *
 * 功能特性：
 * - 企业级表格展示
 * - 多选和批量操作
 * - 状态管理和操作
 * - 产品信息展示
 * - 响应式设计
 * - 数据高亮搜索
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="product-draft-list-view">
        <el-table
            ref="dataTable"
            v-loading="loading"
            :data="dataList"
            :height="tableHeight"
            border
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 多选列 -->
            <el-table-column
                align="center"
                fixed="left"
                type="selection"
                width="50"
            />

            <!-- 序号列 -->
            <el-table-column
                align="center"
                fixed="left"
                label="序号"
                type="index"
                width="60"
            />

            <!-- 产品信息 -->
            <el-table-column
                fixed="left"
                label="产品信息"
                min-width="250"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="product-info-cell">
                        <div class="product-image">
                            <img v-if="row.cover_image" :src="row.cover_image" alt="产品图片"/>
                            <div v-else class="no-image">
                                <i class="fas fa-image"></i>
                            </div>
                        </div>
                        <div class="product-details">
                            <div class="product-name" v-html="highlightSearchText(row.name)"></div>
                            <div class="product-sku">SKU: <span v-html="highlightSearchText(row.sku)"></span></div>
                            <div class="product-category">
                                <el-tag size="small" type="info">{{ row.category_name }}</el-tag>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column
                label="描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <span v-html="highlightSearchText(row.description)"></span>
                </template>
            </el-table-column>

            <!-- 价格 -->
            <el-table-column
                align="center"
                label="价格"
                prop="price"
                sortable
                width="120"
            >
                <template #default="{ row }">
                    <div class="price-cell">
                        ¥{{ formatAmount(row.price) }}
                    </div>
                </template>
            </el-table-column>

            <!-- 库存 -->
            <el-table-column
                align="center"
                label="库存"
                prop="stock"
                sortable
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStockTag(row.stock)"
                        size="small"
                    >
                        {{ row.stock }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-select
                        v-model="row.status"
                        size="small"
                        @change="handleStatusChange(row)"
                    >
                        <el-option label="编辑中" value="editing"></el-option>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="审核通过" value="approved"></el-option>
                        <el-option label="审核拒绝" value="rejected"></el-option>
                    </el-select>
                </template>
            </el-table-column>

            <!-- 审核信息 -->
            <el-table-column
                align="center"
                label="审核信息"
                width="150"
            >
                <template #default="{ row }">
                    <div v-if="row.reviewer" class="review-info">
                        <div class="reviewer">{{ row.reviewer }}</div>
                        <div v-if="row.review_note" class="review-note">
                            <el-tooltip :content="row.review_note" placement="top">
                                <i class="fas fa-comment-alt"></i>
                            </el-tooltip>
                        </div>
                    </div>
                    <span v-else class="text-muted">未审核</span>
                </template>
            </el-table-column>

            <!-- 创建人 -->
            <el-table-column
                align="center"
                label="创建人"
                prop="created_by"
                show-overflow-tooltip
                width="100"
            >
                <template #default="{ row }">
                    <span v-html="highlightSearchText(row.created_by)"></span>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column
                align="center"
                label="创建时间"
                prop="created_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <div>{{ formatDate(row.created_at) }}</div>
                        <small class="text-muted">{{ formatTime(row.created_at) }}</small>
                    </div>
                </template>
            </el-table-column>

            <!-- 更新时间 -->
            <el-table-column
                align="center"
                label="更新时间"
                prop="updated_at"
                sortable
                width="160"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <div>{{ formatDate(row.updated_at) }}</div>
                        <small class="text-muted">{{ formatTime(row.updated_at) }}</small>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="240"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            详情
                        </el-button>
                        <el-button
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-button
                            v-if="row.status === 'approved'"
                            size="small"
                            type="success"
                            @click="handlePublish(row)"
                        >
                            <i class="fas fa-paper-plane"></i>
                            发布
                        </el-button>
                        <el-button
                            v-if="['editing', 'rejected'].includes(row.status)"
                            size="small"
                            type="danger"
                            @click="handleDelete(row)"
                        >
                            <i class="fas fa-trash"></i>
                            删除
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ProductDraftListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'publish',
        'delete'
    ],
    data() {
        return {
            tableHeight: 'calc(100vh - 320px)'
        }
    },
    methods: {
        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 高亮搜索文本
        highlightSearchText(text) {
            if (!this.searchQuery || !text) return text

            const regex = new RegExp(`(${this.searchQuery})`, 'gi')
            return text.replace(regex, '<mark class="search-highlight">$1</mark>')
        },

        // 获取库存标签
        getStockTag(stock) {
            if (stock <= 0) return 'danger'
            if (stock <= 10) return 'warning'
            if (stock <= 50) return 'info'
            return 'success'
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })
        },

        // 格式化日期
        formatDate(dateTime) {
            if (!dateTime) return '-'
            return dateTime.split(' ')[0]
        },

        // 格式化时间
        formatTime(dateTime) {
            if (!dateTime) return '-'
            return dateTime.split(' ')[1]
        },

        // 操作处理
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        handleEdit(row) {
            this.$emit('edit', row)
        },

        handleStatusChange(row) {
            this.$emit('status-change', row)
        },

        handlePublish(row) {
            this.$emit('publish', row)
        },

        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style scoped>
.product-draft-list-view {
    height: 100%;
}

.product-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #ddd;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #999;
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-weight: 500;
    margin-bottom: 4px;
    word-break: break-word;
}

.product-sku {
    color: #666;
    font-size: 11px;
    margin-bottom: 4px;
}

.product-category {
    font-size: 10px;
}

.price-cell {
    font-weight: 600;
    color: #e6a23c;
}

.review-info {
    line-height: 1.3;
}

.review-info .reviewer {
    font-size: 11px;
    color: #666;
    margin-bottom: 2px;
}

.review-info .review-note {
    color: #409eff;
    cursor: pointer;
}

.time-cell {
    line-height: 1.2;
}

.text-muted {
    color: #999;
    font-size: 11px;
}

.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .el-button {
    margin: 0;
    padding: 4px 8px;
    font-size: 11px;
}

.action-buttons .el-button i {
    margin-right: 2px;
}

:deep(.search-highlight) {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

:deep(.el-table) {
    font-size: 12px;
}

:deep(.el-table th) {
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
}

:deep(.el-table td) {
    padding: 8px 0;
}

:deep(.el-table .el-table__cell) {
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table--striped .el-table__row--striped td) {
    background-color: #fafafa;
}

:deep(.el-table__row:hover td) {
    background-color: #f0f9ff !important;
}

:deep(.el-select) {
    width: 100px;
}

:deep(.el-select .el-input__inner) {
    font-size: 11px;
    padding: 0 8px;
}

:deep(.el-tag) {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 2px;
}
</style>
