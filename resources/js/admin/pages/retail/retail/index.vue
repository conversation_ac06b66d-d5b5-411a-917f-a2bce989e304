<!--
/**
 * 零售管理页面
 *
 * 功能特性：
 * - 企业级零售管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 零售数据统计和分析
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/retail/retail
 * 页面标题：零售管理
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="retail-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索零售单号、客户姓名、手机号或商品名称'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增零售
                </el-button>
                <el-button type="info" @click="handleExport" :loading="exportLoading">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                    :disabled="!canBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="零售状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in RETAIL_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="支付方式">
                            <el-select v-model="filters.payment_method" clearable placeholder="选择支付方式">
                                <el-option label="全部方式" value=""></el-option>
                                <el-option label="现金" value="cash"></el-option>
                                <el-option label="微信支付" value="wechat"></el-option>
                                <el-option label="支付宝" value="alipay"></el-option>
                                <el-option label="银行卡" value="bank_card"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="销售日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item label="销售金额">
                            <el-input-number
                                v-model="filters.min_amount"
                                :min="0"
                                :precision="2"
                                placeholder="最小金额"
                                style="width: 120px; margin-right: 8px;"
                            />
                            <span style="margin: 0 8px;">-</span>
                            <el-input-number
                                v-model="filters.max_amount"
                                :min="0"
                                :precision="2"
                                placeholder="最大金额"
                                style="width: 120px;"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 零售列表 -->
            <template #default>
                <RetailListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @edit="handleEdit"
                    @status-change="handleStatusChange"
                    @delete="handleDelete"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总零售单: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						总销售额: <strong>¥{{ formatAmount(totalAmount) }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import RetailListView from './components/RetailListView.vue'

export default {
    name: 'AdminRetailRetailIndexPage',
    components: {
        BackendPageListLayout,
        RetailListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                payment_method: '',
                start_date: '',
                end_date: '',
                min_amount: null,
                max_amount: null
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            totalAmount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部零售', icon: 'fas fa-list', badge: 0},
                {name: 'completed', label: '已完成', icon: 'fas fa-check-circle', badge: 0},
                {name: 'pending', label: '待付款', icon: 'fas fa-clock', badge: 0},
                {name: 'cancelled', label: '已取消', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: [],

            // 零售状态标签
            RETAIL_STATUS_LABELS: {
                'pending': '待付款',
                'completed': '已完成',
                'cancelled': '已取消',
                'refunded': '已退款'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchDelete() {
            return this.selectedRows.some(row => ['pending', 'cancelled'].includes(row.status))
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟零售数据（敏感信息脱敏）
                const mockData = [
                    {
                        id: 1,
                        retail_no: 'RET202401150001',
                        customer_name: '张三',
                        customer_phone: '138****5678', // 手机号脱敏
                        total_amount: 199.00,
                        product_count: 2,
                        payment_method: 'cash',
                        status: 'completed',
                        created_at: '2024-01-15 10:30:00',
                        updated_at: '2024-01-15 10:35:00'
                    },
                    {
                        id: 2,
                        retail_no: 'RET202401150002',
                        customer_name: '李四',
                        customer_phone: '139****1234',
                        total_amount: 89.50,
                        product_count: 1,
                        payment_method: 'wechat',
                        status: 'completed',
                        created_at: '2024-01-15 11:20:00',
                        updated_at: '2024-01-15 11:25:00'
                    }
                ]

                this.dataList = mockData
                this.total = 856
                this.totalAmount = 45680.50
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 623
            this.tabOptions[2].badge = 156
            this.tabOptions[3].badge = 77
        },

        // 格式化金额
        formatAmount(amount) {
            return Number(amount || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                payment_method: '',
                start_date: '',
                end_date: '',
                min_amount: null,
                max_amount: null
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleCreate() {
            this.$message.info('跳转到新增零售页面')
            // TODO: 跳转到新增页面
            // this.$router.push('/admin/retail/retail/create')
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('零售数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchDelete() {
            const deletableItems = this.selectedRows.filter(row =>
                ['pending', 'cancelled'].includes(row.status)
            )

            if (deletableItems.length === 0) {
                this.$message.warning('没有可删除的零售记录')
                return
            }

            this.$confirm(`确定要删除选中的 ${deletableItems.length} 条零售记录吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableItems.length} 条零售记录`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看零售详情: ${row.retail_no}`)
            // TODO: 跳转到详情页面
            // this.$router.push(`/admin/retail/retail/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑零售: ${row.retail_no}`)
            // TODO: 跳转到编辑页面
            // this.$router.push(`/admin/retail/retail/edit/${row.id}`)
        },

        handleStatusChange(row, newStatus) {
            this.$message.success(`零售单 ${row.retail_no} 状态已更新为: ${this.RETAIL_STATUS_LABELS[newStatus]}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除零售单 ${row.retail_no} 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`零售单 ${row.retail_no} 已删除`)
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>
