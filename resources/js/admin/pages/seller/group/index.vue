<!--
/**
 * 商家分组管理页面
 *
 * 功能特性：
 * - 企业级商家分组管理系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 分组层级管理
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/seller/group
 * 页面标题：商家分组
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="seller-group-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索分组名称、描述或创建人'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新建分组
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!canBatchDelete"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="分组状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分组层级">
                            <el-select v-model="filters.level" clearable placeholder="选择层级">
                                <el-option label="全部层级" value=""></el-option>
                                <el-option label="一级分组" value="1"></el-option>
                                <el-option label="二级分组" value="2"></el-option>
                                <el-option label="三级分组" value="3"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 分组列表 -->
            <template #default>
                <SellerGroupListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总分组: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						启用分组: <strong>{{ activeGroupCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import SellerGroupListView from './components/SellerGroupListView.vue'

export default {
    name: 'AdminSellerGroupIndexPage',
    components: {
        BackendPageListLayout,
        SellerGroupListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                level: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            activeGroupCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部分组', icon: 'fas fa-list', badge: 0},
                {name: 'active', label: '启用分组', icon: 'fas fa-check-circle', badge: 0},
                {name: 'inactive', label: '禁用分组', icon: 'fas fa-times-circle', badge: 0},
                {name: 'level1', label: '一级分组', icon: 'fas fa-layer-group', badge: 0},
                {name: 'level2', label: '二级分组', icon: 'fas fa-sitemap', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        },
        canBatchDelete() {
            return this.selectedRows.some(row => row.status !== 'system')
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟商家分组数据
                const mockData = [
                    {
                        id: 1,
                        name: '服装类商家',
                        description: '专营服装、鞋帽等时尚商品的商家',
                        level: 1,
                        parent_id: null,
                        seller_count: 156,
                        status: 'active',
                        sort_order: 1,
                        created_by: '管理员',
                        created_at: '2024-01-10 09:00:00',
                        updated_at: '2024-01-15 14:30:00'
                    },
                    {
                        id: 2,
                        name: '数码电子',
                        description: '销售手机、电脑、数码配件等电子产品',
                        level: 1,
                        parent_id: null,
                        seller_count: 89,
                        status: 'active',
                        sort_order: 2,
                        created_by: '管理员',
                        created_at: '2024-01-10 09:15:00',
                        updated_at: '2024-01-12 16:20:00'
                    },
                    {
                        id: 3,
                        name: '家居用品',
                        description: '家具、家电、装饰用品等家居商品',
                        level: 1,
                        parent_id: null,
                        seller_count: 234,
                        status: 'active',
                        sort_order: 3,
                        created_by: '管理员',
                        created_at: '2024-01-10 10:30:00',
                        updated_at: '2024-01-14 11:45:00'
                    },
                    {
                        id: 4,
                        name: '食品饮料',
                        description: '食品、饮料、保健品等消费品',
                        level: 1,
                        parent_id: null,
                        seller_count: 67,
                        status: 'inactive',
                        sort_order: 4,
                        created_by: '管理员',
                        created_at: '2024-01-11 14:20:00',
                        updated_at: '2024-01-13 09:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 456
                this.activeGroupCount = 389
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.activeGroupCount
            this.tabOptions[2].badge = this.total - this.activeGroupCount
            this.tabOptions[3].badge = 234
            this.tabOptions[4].badge = 156
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                level: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('跳转到新建商家分组页面')
            // TODO: 跳转到新建页面
            // this.$router.push('/admin/seller/group/create')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true

                // 模拟导出
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('商家分组数据导出成功')

            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchDelete() {
            const deletableGroups = this.selectedRows.filter(row => row.status !== 'system')

            if (deletableGroups.length === 0) {
                this.$message.warning('没有可删除的分组')
                return
            }

            this.$confirm(`确定要删除选中的 ${deletableGroups.length} 个分组吗？`, '确认批量删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功删除 ${deletableGroups.length} 个分组`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看分组详情: ${row.name}`)
            // TODO: 跳转到详情页面
            // this.$router.push(`/admin/seller/group/detail/${row.id}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑分组: ${row.name}`)
            // TODO: 跳转到编辑页面
            // this.$router.push(`/admin/seller/group/edit/${row.id}`)
        },

        handleStatusChange(row) {
            const newStatus = row.status === 'active' ? 'inactive' : 'active'
            this.$message.success(`分组状态已更新为: ${newStatus === 'active' ? '启用' : '禁用'}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除分组 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('分组删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style scoped>
.seller-group-index-page {
    height: 100%;
}

.advanced-search-form {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;
}

.footer-stats {
    display: flex;
    gap: 24px;
    align-items: center;
    font-size: 12px;
    color: #666;
}

.stat-item strong {
    color: #333;
    font-weight: 600;
}
</style>
