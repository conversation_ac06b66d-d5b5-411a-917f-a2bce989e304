<!--
/**
 * 发货地址列表视图组件
 *
 * 功能特性：
 * - 发货地址数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="send-address-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 地址名称 -->
            <el-table-column
                label="地址名称"
                min-width="150"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="address-name-cell">
                        <i class="fas fa-map-marker-alt address-icon"></i>
                        <span class="address-name">{{ row.name }}</span>
                        <el-tag v-if="row.is_default" class="default-tag" size="small" type="warning">
                            默认
                        </el-tag>
                    </div>
                </template>
            </el-table-column>

            <!-- 地址类型 -->
            <el-table-column
                align="center"
                label="类型"
                prop="type"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ getTypeLabel(row.type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 联系人 -->
            <el-table-column
                label="联系人"
                prop="contact_name"
                width="100"
            >
                <template #default="{ row }">
                    <div class="contact-cell">
                        <i class="fas fa-user contact-icon"></i>
                        <span>{{ row.contact_name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 联系电话 -->
            <el-table-column
                label="联系电话"
                prop="contact_phone"
                width="130"
            >
                <template #default="{ row }">
                    <div class="phone-cell">
                        <i class="fas fa-phone phone-icon"></i>
                        <span class="phone-number">{{ row.contact_phone }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 省市区 -->
            <el-table-column
                label="省市区"
                width="180"
            >
                <template #default="{ row }">
                    <div class="location-cell">
                        <i class="fas fa-location-arrow location-icon"></i>
                        <span>{{ row.province }} {{ row.city }} {{ row.district }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 详细地址 -->
            <el-table-column
                label="详细地址"
                min-width="200"
                prop="address"
                show-overflow-tooltip
            />

            <!-- 邮政编码 -->
            <el-table-column
                align="center"
                label="邮编"
                prop="postal_code"
                width="80"
            />

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <i class="fas fa-clock time-icon"></i>
                        <span>{{ row.created_at }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="300"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>

                        <el-button
                            size="small"
                            type="success"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>

                        <el-dropdown
                            trigger="click"
                            @command="(command) => handleDropdownCommand(command, row)"
                        >
                            <el-button size="small" type="info">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="!row.is_default"
                                        command="set-default"
                                    >
                                        <i class="fas fa-star"></i>
                                        设为默认
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status !== 'active'"
                                        command="activate"
                                    >
                                        <i class="fas fa-play"></i>
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="deactivate"
                                    >
                                        <i class="fas fa-pause"></i>
                                        停用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="copy"
                                        divided
                                    >
                                        <i class="fas fa-copy"></i>
                                        复制地址
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        divided
                                    >
                                        <i class="fas fa-trash text-danger"></i>
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'SendAddressListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'status-change',
        'set-default'
    ],
    data() {
        return {
            // 类型标签映射
            typeLabels: {
                'warehouse': '仓库',
                'store': '门店',
                'office': '办公',
                'temporary': '临时'
            },
            // 状态标签映射
            statusLabels: {
                'active': '启用',
                'inactive': '停用',
                'default': '默认地址'
            }
        }
    },
    methods: {
        // 获取类型标签
        getTypeLabel(type) {
            return this.typeLabels[type] || type
        },

        // 获取类型标签样式
        getTypeTagType(type) {
            const typeMap = {
                'warehouse': 'primary',
                'store': 'success',
                'office': 'info',
                'temporary': 'warning'
            }
            return typeMap[type] || 'info'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.statusLabels[status] || status
        },

        // 获取状态标签样式
        getStatusTagType(status) {
            const statusMap = {
                'active': 'success',
                'inactive': 'danger',
                'default': 'warning'
            }
            return statusMap[status] || 'info'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 设置默认地址
        handleSetDefault(row) {
            this.$emit('set-default', row)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'set-default':
                    this.handleSetDefault(row)
                    break
                case 'activate':
                    this.handleStatusChange(row, 'active')
                    break
                case 'deactivate':
                    this.handleStatusChange(row, 'inactive')
                    break
                case 'copy':
                    this.$message.info(`复制地址: ${row.name}`)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.send-address-list-view {
    .admin-table {
        .address-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .address-icon {
                color: #409eff;
                font-size: 14px;
            }

            .address-name {
                font-weight: 500;
            }

            .default-tag {
                margin-left: auto;
            }
        }

        .contact-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .contact-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .phone-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .phone-icon {
                color: #67c23a;
                font-size: 12px;
            }

            .phone-number {
                font-family: monospace;
            }
        }

        .location-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .location-icon {
                color: #e6a23c;
                font-size: 12px;
            }
        }

        .time-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .time-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        :deep(.text-danger) {
            color: #f56c6c !important;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .send-address-list-view {
        .admin-table {
            .action-buttons {
                flex-direction: column;
                gap: 4px;

                .el-button {
                    width: 100%;
                    margin: 0;
                }
            }
        }
    }
}
</style>
