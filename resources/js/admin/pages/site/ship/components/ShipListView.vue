<!--
/**
 * 物流列表视图组件
 *
 * 功能特性：
 * - 物流数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="ship-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 运单号 -->
            <el-table-column
                label="运单号"
                min-width="150"
                prop="tracking_number"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="tracking-cell">
                        <i class="fas fa-barcode tracking-icon"></i>
                        <span class="tracking-number">{{ row.tracking_number }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 物流公司 -->
            <el-table-column
                label="物流公司"
                prop="company_name"
                width="120"
            >
                <template #default="{ row }">
                    <div class="company-cell">
                        <i class="fas fa-truck company-icon"></i>
                        <span>{{ row.company_name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 发件人 -->
            <el-table-column
                label="发件人"
                width="150"
            >
                <template #default="{ row }">
                    <div class="sender-cell">
                        <div class="sender-name">{{ row.sender_name }}</div>
                        <div class="sender-phone">{{ row.sender_phone }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 收件人 -->
            <el-table-column
                label="收件人"
                width="150"
            >
                <template #default="{ row }">
                    <div class="receiver-cell">
                        <div class="receiver-name">{{ row.receiver_name }}</div>
                        <div class="receiver-phone">{{ row.receiver_phone }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 收件地址 -->
            <el-table-column
                label="收件地址"
                min-width="200"
                prop="receiver_address"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="address-cell">
                        <i class="fas fa-map-marker-alt address-icon"></i>
                        <span>{{ row.receiver_address }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 重量 -->
            <el-table-column
                align="right"
                label="重量(kg)"
                prop="weight"
                width="100"
            >
                <template #default="{ row }">
                    <div class="weight-cell">
                        <i class="fas fa-weight weight-icon"></i>
                        <span>{{ row.weight }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 运费 -->
            <el-table-column
                align="right"
                label="运费"
                prop="fee"
                width="100"
            >
                <template #default="{ row }">
                    <div class="fee-cell">
                        <i class="fas fa-yen-sign fee-icon"></i>
                        <span class="fee-amount">{{ row.fee }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 发货时间 -->
            <el-table-column
                label="发货时间"
                prop="shipped_at"
                sortable
                width="180"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <i class="fas fa-clock time-icon"></i>
                        <span>{{ row.shipped_at || '未发货' }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>

                        <el-button
                            size="small"
                            type="info"
                            @click="handleTrack(row)"
                        >
                            <i class="fas fa-search-location"></i>
                            跟踪
                        </el-button>

                        <el-dropdown
                            trigger="click"
                            @command="(command) => handleDropdownCommand(command, row)"
                        >
                            <el-button size="small" type="success">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="ship"
                                    >
                                        <i class="fas fa-shipping-fast"></i>
                                        发货
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'shipped' || row.status === 'in_transit'"
                                        command="deliver"
                                    >
                                        <i class="fas fa-check-circle"></i>
                                        确认送达
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="edit"
                                    >
                                        <i class="fas fa-edit"></i>
                                        编辑
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="print"
                                        divided
                                    >
                                        <i class="fas fa-print"></i>
                                        打印面单
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        divided
                                    >
                                        <i class="fas fa-trash text-danger"></i>
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ShipListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'status-change',
        'track'
    ],
    data() {
        return {
            // 状态标签映射
            statusLabels: {
                'pending': '待发货',
                'shipped': '已发货',
                'in_transit': '运输中',
                'delivered': '已送达',
                'returned': '已退回'
            }
        }
    },
    methods: {
        // 获取状态标签
        getStatusLabel(status) {
            return this.statusLabels[status] || status
        },

        // 获取状态标签样式
        getStatusTagType(status) {
            const statusMap = {
                'pending': 'warning',
                'shipped': 'primary',
                'in_transit': 'info',
                'delivered': 'success',
                'returned': 'danger'
            }
            return statusMap[status] || 'info'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 跟踪物流
        handleTrack(row) {
            this.$emit('track', row)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'ship':
                    this.handleStatusChange(row, 'shipped')
                    break
                case 'deliver':
                    this.handleStatusChange(row, 'delivered')
                    break
                case 'edit':
                    this.handleEdit(row)
                    break
                case 'print':
                    this.$message.info(`打印面单: ${row.tracking_number}`)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.ship-list-view {
    .admin-table {
        .tracking-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .tracking-icon {
                color: #409eff;
                font-size: 14px;
            }

            .tracking-number {
                font-weight: 500;
                font-family: monospace;
            }
        }

        .company-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .company-icon {
                color: #67c23a;
                font-size: 12px;
            }
        }

        .sender-cell,
        .receiver-cell {
            .sender-name,
            .receiver-name {
                font-weight: 500;
                margin-bottom: 2px;
            }

            .sender-phone,
            .receiver-phone {
                font-size: 12px;
                color: #909399;
                font-family: monospace;
            }
        }

        .address-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .address-icon {
                color: #e6a23c;
                font-size: 12px;
                flex-shrink: 0;
            }
        }

        .weight-cell {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 6px;

            .weight-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .fee-cell {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 6px;

            .fee-icon {
                color: #67c23a;
                font-size: 12px;
            }

            .fee-amount {
                font-weight: 600;
                color: #67c23a;
            }
        }

        .time-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .time-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        :deep(.text-danger) {
            color: #f56c6c !important;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .ship-list-view {
        .admin-table {
            .action-buttons {
                flex-direction: column;
                gap: 4px;

                .el-button {
                    width: 100%;
                    margin: 0;
                }
            }
        }
    }
}
</style>
