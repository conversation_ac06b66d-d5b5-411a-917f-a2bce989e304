<!--
/**
 * 物流部件管理页面
 *
 * 功能特性：
 * - 管理物流配送相关部件和设备
 * - 企业级管理界面
 * - 数据表格展示
 * - 搜索和筛选功能
 * - 批量操作支持
 *
 * 路由路径：/admin/site/ship_part
 * 页面标题：物流部件
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="ship-part-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="v_loading"
            :page-size="pageSize"
            :search-placeholder="'搜索部件名称、型号或供应商'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #left-section>
                <el-button
                    :class="{ 'is-active': showAdvancedSearch }"
                    type="primary"
                    @click="toggleAdvancedSearch"
                >
                    <i class="fas fa-filter"></i>
                    高级筛选
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button :loading="loading" type="info" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    :disabled="!hasSelectedRows"
                    type="danger"
                    @click="handleBatchDelete"
                >
                    <i class="fas fa-trash"></i>
                    批量删除 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="部件状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option
                                    v-for="(label, status) in PART_STATUS_LABELS"
                                    :key="status"
                                    :label="label"
                                    :value="status"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="部件类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="运输设备" value="transport"></el-option>
                                <el-option label="包装材料" value="packaging"></el-option>
                                <el-option label="安全设备" value="safety"></el-option>
                                <el-option label="监控设备" value="monitoring"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商">
                            <el-select v-model="filters.supplier" clearable placeholder="选择供应商">
                                <el-option label="顺丰科技" value="sf_tech"></el-option>
                                <el-option label="京东物流" value="jd_logistics"></el-option>
                                <el-option label="菜鸟网络" value="cainiao"></el-option>
                                <el-option label="德邦快递" value="deppon"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 物流部件列表 -->
            <template #default>
                <ShipPartListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @maintenance="handleMaintenance"
                    @selection-change="handleSelectionChange"
                    @status-change="handleStatusChange"
                    @view-detail="handleViewDetail"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总部件: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import ShipPartListView from './components/ShipPartListView.vue'

export default {
    name: 'AdminSiteShipPartIndexPage',
    components: {
        BackendPageListLayout,
        ShipPartListView
    },
    data() {
        return {
            // 加载状态
            v_loading: false,
            loading: false,
            exportLoading: false,

            // 搜索相关
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                supplier: ''
            },

            // 分页相关
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab相关
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部部件', icon: 'fas fa-boxes', badge: 0},
                {name: 'active', label: '使用中', icon: 'fas fa-check-circle', badge: 0},
                {name: 'maintenance', label: '维护中', icon: 'fas fa-wrench', badge: 0},
                {name: 'inactive', label: '停用', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据相关
            dataList: [],
            selectedRows: [],

            // 状态标签映射
            PART_STATUS_LABELS: {
                'active': '使用中',
                'maintenance': '维护中',
                'inactive': '停用',
                'damaged': '损坏'
            }
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.loadData()
    },
    methods: {
        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟物流部件数据
                const mockData = [
                    {
                        id: 1,
                        name: '智能分拣机',
                        model: 'SF-Sort-2024',
                        type: 'transport',
                        type_name: '运输设备',
                        status: 'active',
                        supplier: 'sf_tech',
                        supplier_name: '顺丰科技',
                        location: '北京总仓库-A区',
                        purchase_price: 158000.00,
                        purchase_date: '2024-01-01',
                        warranty_period: '3年',
                        last_maintenance: '2024-01-10',
                        next_maintenance: '2024-04-10',
                        description: '高效智能包裹分拣设备，支持多种包裹规格',
                        created_at: '2024-01-01 10:00:00'
                    },
                    {
                        id: 2,
                        name: '防震包装材料',
                        model: 'JD-Pack-Pro',
                        type: 'packaging',
                        type_name: '包装材料',
                        status: 'active',
                        supplier: 'jd_logistics',
                        supplier_name: '京东物流',
                        location: '上海分仓-B区',
                        purchase_price: 25000.00,
                        purchase_date: '2024-01-05',
                        warranty_period: '1年',
                        last_maintenance: '2024-01-15',
                        next_maintenance: '2024-07-15',
                        description: '环保防震包装材料，适用于易碎商品运输',
                        created_at: '2024-01-05 09:15:00'
                    },
                    {
                        id: 3,
                        name: 'GPS跟踪设备',
                        model: 'CN-Track-X1',
                        type: 'monitoring',
                        type_name: '监控设备',
                        status: 'maintenance',
                        supplier: 'cainiao',
                        supplier_name: '菜鸟网络',
                        location: '广州门店-维修区',
                        purchase_price: 8500.00,
                        purchase_date: '2024-01-10',
                        warranty_period: '2年',
                        last_maintenance: '2024-01-14',
                        next_maintenance: '2024-02-14',
                        description: '高精度GPS定位跟踪设备，实时监控货物位置',
                        created_at: '2024-01-10 11:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 15 // 模拟总数

                // 更新Tab徽章
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = this.dataList.filter(item => item.status === 'active').length
            this.tabOptions[2].badge = this.dataList.filter(item => item.status === 'maintenance').length
            this.tabOptions[3].badge = this.dataList.filter(item => item.status === 'inactive').length
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        toggleAdvancedSearch() {
            this.showAdvancedSearch = !this.showAdvancedSearch
        },

        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                supplier: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleExport() {
            this.exportLoading = true
            setTimeout(() => {
                this.exportLoading = false
                this.$message.success('导出成功')
            }, 2000)
        },

        handleBatchDelete() {
            this.$confirm('确定要删除选中的物流部件吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleEdit(row) {
            this.$message.info(`编辑物流部件: ${row.name}`)
        },

        handleDelete(row) {
            this.$confirm(`确定要删除物流部件 "${row.name}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        },

        handleStatusChange(row, status) {
            this.$message.success(`部件状态已更新为: ${this.PART_STATUS_LABELS[status]}`)
            this.loadData()
        },

        handleViewDetail(row) {
            this.$message.info(`查看部件详情: ${row.name}`)
        },

        handleMaintenance(row) {
            this.$confirm(`确定要对 "${row.name}" 进行维护吗？`, '确认维护', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                this.$message.success('维护任务已创建')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消维护')
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.ship-part-index-page {
    .advanced-search-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 20px;
        align-items: center;
        color: #666;
        font-size: 14px;

        .stat-item {
            strong {
                color: #333;
                font-weight: 600;
            }
        }
    }
}
</style>
