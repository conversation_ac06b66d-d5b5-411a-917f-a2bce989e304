<!--
/**
 * 站点列表视图组件
 *
 * 功能特性：
 * - 站点数据表格展示
 * - 状态管理和操作
 * - 响应式设计
 * - 企业级UI风格
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="site-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <!-- 选择列 -->
            <el-table-column type="selection" width="55"/>

            <!-- ID列 -->
            <el-table-column
                label="ID"
                prop="id"
                sortable
                width="80"
            />

            <!-- 站点名称 -->
            <el-table-column
                label="站点名称"
                min-width="150"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="site-name-cell">
                        <i class="fas fa-globe site-icon"></i>
                        <span class="site-name">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 域名 -->
            <el-table-column
                label="域名"
                min-width="180"
                prop="domain"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-link
                        :href="`https://${row.domain}`"
                        target="_blank"
                        type="primary"
                    >
                        {{ row.domain }}
                        <i class="fas fa-external-link-alt"></i>
                    </el-link>
                </template>
            </el-table-column>

            <!-- 站点类型 -->
            <el-table-column
                align="center"
                label="站点类型"
                prop="type"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getTypeTagType(row.type)"
                        size="small"
                    >
                        {{ getTypeLabel(row.type) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="120"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusTagType(row.status)"
                        size="small"
                    >
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 描述 -->
            <el-table-column
                label="描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            />

            <!-- 创建时间 -->
            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="180"
            >
                <template #default="{ row }">
                    <div class="time-cell">
                        <i class="fas fa-clock time-icon"></i>
                        <span>{{ row.created_at }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            查看
                        </el-button>

                        <el-button
                            size="small"
                            type="success"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>

                        <el-dropdown
                            trigger="click"
                            @command="(command) => handleDropdownCommand(command, row)"
                        >
                            <el-button size="small" type="info">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status !== 'active'"
                                        command="activate"
                                    >
                                        <i class="fas fa-play"></i>
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'active'"
                                        command="deactivate"
                                    >
                                        <i class="fas fa-pause"></i>
                                        停用
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="maintenance"
                                        divided
                                    >
                                        <i class="fas fa-wrench"></i>
                                        维护模式
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        divided
                                    >
                                        <i class="fas fa-trash text-danger"></i>
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'SiteListView',
    props: {
        // 数据列表
        dataList: {
            type: Array,
            default: () => []
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'status-change'
    ],
    data() {
        return {
            // 类型标签映射
            typeLabels: {
                'main': '主站',
                'sub': '子站',
                'test': '测试站',
                'dev': '开发站'
            },
            // 状态标签映射
            statusLabels: {
                'active': '活跃',
                'inactive': '停用',
                'maintenance': '维护中',
                'pending': '待审核'
            }
        }
    },
    methods: {
        // 获取类型标签
        getTypeLabel(type) {
            return this.typeLabels[type] || type
        },

        // 获取类型标签样式
        getTypeTagType(type) {
            const typeMap = {
                'main': 'primary',
                'sub': 'success',
                'test': 'warning',
                'dev': 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取状态标签
        getStatusLabel(status) {
            return this.statusLabels[status] || status
        },

        // 获取状态标签样式
        getStatusTagType(status) {
            const statusMap = {
                'active': 'success',
                'inactive': 'danger',
                'maintenance': 'warning',
                'pending': 'info'
            }
            return statusMap[status] || 'info'
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 下拉菜单命令处理
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'activate':
                    this.handleStatusChange(row, 'active')
                    break
                case 'deactivate':
                    this.handleStatusChange(row, 'inactive')
                    break
                case 'maintenance':
                    this.handleStatusChange(row, 'maintenance')
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.site-list-view {
    .admin-table {
        .site-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .site-icon {
                color: #409eff;
                font-size: 14px;
            }

            .site-name {
                font-weight: 500;
            }
        }

        .time-cell {
            display: flex;
            align-items: center;
            gap: 6px;

            .time-icon {
                color: #909399;
                font-size: 12px;
            }
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        :deep(.el-link) {
            .fas {
                margin-left: 4px;
                font-size: 12px;
            }
        }

        :deep(.text-danger) {
            color: #f56c6c !important;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .site-list-view {
        .admin-table {
            .action-buttons {
                flex-direction: column;
                gap: 4px;

                .el-button {
                    width: 100%;
                    margin: 0;
                }
            }
        }
    }
}
</style>
