<!--
/**
 * 创建商店页面
 *
 * 功能特性：
 * - 商店信息录入表单
 * - 表单验证和提交
 * - 企业级UI设计
 * - 响应式布局
 *
 * 路由路径：/admin/store/store/create
 * 页面标题：新建商店
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="store-create-page">
        <el-card class="form-card">
            <template #header>
                <div class="card-header">
                    <h3>
                        <i class="fas fa-plus-circle"></i>
                        新建商店
                    </h3>
                    <el-button @click="handleBack">
                        <i class="fas fa-arrow-left"></i>
                        返回列表
                    </el-button>
                </div>
            </template>

            <el-form
                ref="storeForm"
                :model="formData"
                :rules="formRules"
                class="store-form"
                label-width="120px"
            >
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="商店名称" prop="store_name">
                            <el-input
                                v-model="formData.store_name"
                                maxlength="50"
                                placeholder="请输入商店名称"
                                show-word-limit
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商店分类" prop="category">
                            <el-select v-model="formData.category" placeholder="请选择商店分类">
                                <el-option label="服装类" value="clothing"></el-option>
                                <el-option label="数码类" value="digital"></el-option>
                                <el-option label="食品类" value="food"></el-option>
                                <el-option label="家居类" value="home"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="店主姓名" prop="owner_name">
                            <el-input
                                v-model="formData.owner_name"
                                maxlength="20"
                                placeholder="请输入店主姓名"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话" prop="owner_phone">
                            <el-input
                                v-model="formData.owner_phone"
                                maxlength="11"
                                placeholder="请输入联系电话"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="邮箱地址" prop="owner_email">
                            <el-input
                                v-model="formData.owner_email"
                                placeholder="请输入邮箱地址"
                                type="email"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商店状态" prop="status">
                            <el-select v-model="formData.status" placeholder="请选择商店状态">
                                <el-option label="待审核" value="pending"></el-option>
                                <el-option label="活跃" value="active"></el-option>
                                <el-option label="已暂停" value="suspended"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="商店地址" prop="address">
                    <el-input
                        v-model="formData.address"
                        maxlength="200"
                        placeholder="请输入商店地址"
                        show-word-limit
                    />
                </el-form-item>

                <el-form-item label="商店描述" prop="description">
                    <el-input
                        v-model="formData.description"
                        :rows="4"
                        maxlength="500"
                        placeholder="请输入商店描述"
                        show-word-limit
                        type="textarea"
                    />
                </el-form-item>

                <el-form-item>
                    <el-button :loading="submitLoading" type="primary" @click="handleSubmit">
                        <i class="fas fa-save"></i>
                        保存商店
                    </el-button>
                    <el-button @click="handleReset">
                        <i class="fas fa-undo"></i>
                        重置表单
                    </el-button>
                    <el-button @click="handleBack">
                        <i class="fas fa-times"></i>
                        取消
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script>
export default {
    name: 'AdminStoreStoreCreatePage',
    data() {
        return {
            submitLoading: false,
            formData: {
                store_name: '',
                category: '',
                owner_name: '',
                owner_phone: '',
                owner_email: '',
                address: '',
                description: '',
                status: 'pending'
            },
            formRules: {
                store_name: [
                    {required: true, message: '请输入商店名称', trigger: 'blur'},
                    {min: 2, max: 50, message: '商店名称长度在 2 到 50 个字符', trigger: 'blur'}
                ],
                category: [
                    {required: true, message: '请选择商店分类', trigger: 'change'}
                ],
                owner_name: [
                    {required: true, message: '请输入店主姓名', trigger: 'blur'},
                    {min: 2, max: 20, message: '店主姓名长度在 2 到 20 个字符', trigger: 'blur'}
                ],
                owner_phone: [
                    {required: true, message: '请输入联系电话', trigger: 'blur'},
                    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
                ],
                owner_email: [
                    {required: true, message: '请输入邮箱地址', trigger: 'blur'},
                    {type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'}
                ],
                status: [
                    {required: true, message: '请选择商店状态', trigger: 'change'}
                ],
                address: [
                    {required: true, message: '请输入商店地址', trigger: 'blur'},
                    {max: 200, message: '地址长度不能超过 200 个字符', trigger: 'blur'}
                ]
            }
        }
    },
    methods: {
        // 提交表单
        async handleSubmit() {
            try {
                await this.$refs.storeForm.validate()

                this.submitLoading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 2000))

                this.$message.success('商店创建成功')

                // 跳转回列表页
                this.$router.push('/admin/store/store')

            } catch (error) {
                if (error !== false) { // 不是表单验证错误
                    console.error('创建商店失败:', error)
                    this.$message.error('创建商店失败，请重试')
                }
            } finally {
                this.submitLoading = false
            }
        },

        // 重置表单
        handleReset() {
            this.$refs.storeForm.resetFields()
        },

        // 返回列表
        handleBack() {
            this.$router.push('/admin/store/store')
        }
    }
}
</script>

<style lang="scss" scoped>
.store-create-page {
    padding: 20px;

    .form-card {
        max-width: 800px;
        margin: 0 auto;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;

                i {
                    margin-right: 8px;
                    color: #409eff;
                }
            }
        }

        .store-form {
            margin-top: 20px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .store-create-page {
        padding: 10px;

        .form-card {
            .card-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    }
}
</style>
