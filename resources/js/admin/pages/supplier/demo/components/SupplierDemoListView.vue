<!--
/**
 * 供货商演示列表视图组件
 *
 * 功能特性：
 * - 企业级表格展示
 * - 多选和批量操作
 * - 状态标签和操作按钮
 * - 敏感信息脱敏显示
 * - 响应式设计
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="supplier-demo-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <!-- 多选列 -->
            <el-table-column align="center" type="selection" width="55"/>

            <!-- 序号列 -->
            <el-table-column align="center" label="序号" type="index" width="80"/>

            <!-- 演示信息 -->
            <el-table-column label="演示信息" min-width="250">
                <template #default="{ row }">
                    <div class="demo-info">
                        <div class="demo-title">{{ row.demo_title }}</div>
                        <div class="demo-description">{{ row.demo_description }}</div>
                        <div class="demo-type">
                            <el-tag :type="getDemoTypeTagType(row.demo_type)" size="small">
                                {{ getDemoTypeLabel(row.demo_type) }}
                            </el-tag>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 供货商信息 -->
            <el-table-column label="供货商信息" min-width="180">
                <template #default="{ row }">
                    <div class="supplier-info">
                        <div class="supplier-name">{{ row.supplier_name }}</div>
                        <div class="product-name">产品: {{ row.product_name }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 演示链接 -->
            <el-table-column label="演示链接" min-width="200">
                <template #default="{ row }">
                    <div class="demo-url">
                        <el-link :href="row.demo_url" target="_blank" type="primary">
                            <i class="fas fa-external-link-alt"></i>
                            查看演示
                        </el-link>
                    </div>
                </template>
            </el-table-column>

            <!-- 统计数据 -->
            <el-table-column align="center" label="统计数据" width="120">
                <template #default="{ row }">
                    <div class="stats-info">
                        <div class="view-count">
                            <i class="fas fa-eye"></i>
                            {{ row.view_count }}
                        </div>
                        <div class="like-count">
                            <i class="fas fa-heart"></i>
                            {{ row.like_count }}
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column align="center" label="状态" prop="status" width="100">
                <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                        {{ getStatusLabel(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 创建时间 -->
            <el-table-column align="center" label="创建时间" prop="created_at" width="160">
                <template #default="{ row }">
                    <div class="time-info">
                        <div>{{ formatDate(row.created_at) }}</div>
                        <div class="time-detail">{{ formatTime(row.created_at) }}</div>
                    </div>
                </template>
            </el-table-column>

            <!-- 操作列 -->
            <el-table-column align="center" fixed="right" label="操作" width="200">
                <template #default="{ row }">
                    <div class="action-buttons">
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleViewDetail(row)"
                        >
                            <i class="fas fa-eye"></i>
                            详情
                        </el-button>
                        <el-button
                            size="small"
                            type="warning"
                            @click="handleEdit(row)"
                        >
                            <i class="fas fa-edit"></i>
                            编辑
                        </el-button>
                        <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                            <el-button size="small" type="info">
                                更多
                                <i class="fas fa-chevron-down"></i>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="approve"
                                        icon="fas fa-check"
                                    >
                                        审核通过
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'pending'"
                                        command="reject"
                                        icon="fas fa-times"
                                    >
                                        审核拒绝
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'approved'"
                                        command="publish"
                                        icon="fas fa-globe"
                                    >
                                        发布演示
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-if="row.status === 'published'"
                                        command="unpublish"
                                        icon="fas fa-eye-slash"
                                    >
                                        取消发布
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="preview"
                                        divided
                                        icon="fas fa-play"
                                    >
                                        预览演示
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="delete"
                                        divided
                                        icon="fas fa-trash"
                                    >
                                        删除
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'SupplierDemoListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'data-change',
        'view-detail',
        'edit',
        'status-change',
        'delete'
    ],
    methods: {
        // 获取演示类型标签类型
        getDemoTypeTagType(type) {
            const typeMap = {
                'product': 'primary',
                'service': 'success',
                'technology': 'warning',
                'solution': 'info'
            }
            return typeMap[type] || 'info'
        },

        // 获取演示类型标签文本
        getDemoTypeLabel(type) {
            const typeMap = {
                'product': '产品演示',
                'service': '服务演示',
                'technology': '技术演示',
                'solution': '方案演示'
            }
            return typeMap[type] || '未知类型'
        },

        // 获取状态标签类型
        getStatusTagType(status) {
            const statusMap = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger',
                'published': 'primary'
            }
            return statusMap[status] || 'info'
        },

        // 获取状态标签文本
        getStatusLabel(status) {
            const statusMap = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'published': '已发布'
            }
            return statusMap[status] || '未知状态'
        },

        // 格式化日期
        formatDate(dateTime) {
            if (!dateTime) return '-'
            return dateTime.split(' ')[0]
        },

        // 格式化时间
        formatTime(dateTime) {
            if (!dateTime) return '-'
            return dateTime.split(' ')[1] || ''
        },

        // 处理选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleViewDetail(row) {
            this.$emit('view-detail', row)
        },

        // 编辑
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 处理下拉菜单命令
        handleDropdownCommand(command, row) {
            switch (command) {
                case 'approve':
                    this.handleStatusChange(row, 'approved')
                    break
                case 'reject':
                    this.handleStatusChange(row, 'rejected')
                    break
                case 'publish':
                    this.handleStatusChange(row, 'published')
                    break
                case 'unpublish':
                    this.handleStatusChange(row, 'approved')
                    break
                case 'preview':
                    this.handlePreview(row)
                    break
                case 'delete':
                    this.handleDelete(row)
                    break
            }
        },

        // 状态变更
        handleStatusChange(row, status) {
            this.$emit('status-change', row, status)
        },

        // 预览演示
        handlePreview(row) {
            window.open(row.demo_url, '_blank')
        },

        // 删除
        handleDelete(row) {
            this.$emit('delete', row)
        }
    }
}
</script>

<style scoped>
.supplier-demo-list-view {
    width: 100%;
}

.demo-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.demo-title {
    font-weight: 500;
    color: #333;
}

.demo-description {
    font-size: 12px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.demo-type {
    display: flex;
    align-items: center;
}

.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.supplier-name {
    font-weight: 500;
    color: #333;
}

.product-name {
    font-size: 12px;
    color: #666;
}

.demo-url {
    display: flex;
    align-items: center;
}

.stats-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
}

.view-count,
.like-count {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
}

.view-count i {
    color: #409eff;
}

.like-count i {
    color: #f56c6c;
}

.time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 12px;
}

.time-detail {
    color: #999;
    font-size: 11px;
}

.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.action-buttons .el-button {
    padding: 4px 8px;
    font-size: 12px;
}

.action-buttons .el-button i {
    margin-right: 2px;
}
</style>
