<!--
/**
 * 供货商演示页面
 *
 * 功能特性：
 * - 企业级供货商演示系统
 * - Tab状态切换和筛选
 * - 高级搜索和批量操作
 * - 演示数据管理和统计
 * - 数据导出和统计
 * - 响应式设计
 * - 敏感信息脱敏显示
 *
 * 路由路径：/admin/supplier/demo
 * 页面标题：供货商演示
 *
 * 版本：v2.1.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="supplier-demo-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索演示名称、供货商或产品名称'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button type="primary" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增演示
                </el-button>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button :loading="exportLoading" type="success" @click="handleExport">
                    <i class="fas fa-download"></i>
                    导出数据
                </el-button>
                <el-button
                    v-if="hasSelectedRows"
                    type="warning"
                    @click="handleBatchApprove"
                >
                    <i class="fas fa-check"></i>
                    批量审核 ({{ selectedRows.length }})
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="100px">
                        <el-form-item label="演示状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="待审核" value="pending"></el-option>
                                <el-option label="已通过" value="approved"></el-option>
                                <el-option label="已拒绝" value="rejected"></el-option>
                                <el-option label="已发布" value="published"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="演示类型">
                            <el-select v-model="filters.type" clearable placeholder="选择类型">
                                <el-option label="全部类型" value=""></el-option>
                                <el-option label="产品演示" value="product"></el-option>
                                <el-option label="服务演示" value="service"></el-option>
                                <el-option label="技术演示" value="technology"></el-option>
                                <el-option label="方案演示" value="solution"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供货商">
                            <el-select v-model="filters.supplier_id" clearable placeholder="选择供货商">
                                <el-option label="全部供货商" value=""></el-option>
                                <el-option label="华东制造有限公司" value="1"></el-option>
                                <el-option label="北方贸易集团" value="2"></el-option>
                                <el-option label="南方科技公司" value="3"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 演示列表 -->
            <template #default>
                <SupplierDemoListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @delete="handleDelete"
                    @edit="handleEdit"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                    @view-detail="handleViewDetail"
                    @status-change="handleStatusChange"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总演示: <strong>{{ total }}</strong>
					</span>
                    <span class="stat-item">
						已发布: <strong>{{ publishedDemoCount }}</strong>
					</span>
                    <span class="stat-item">
						待审核: <strong>{{ pendingDemoCount }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import SupplierDemoListView from './components/SupplierDemoListView.vue'

export default {
    name: 'AdminSupplierDemoIndexPage',
    components: {
        BackendPageListLayout,
        SupplierDemoListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            exportLoading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                type: '',
                supplier_id: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,
            publishedDemoCount: 0,
            pendingDemoCount: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部演示', icon: 'fas fa-list', badge: 0},
                {name: 'pending', label: '待审核', icon: 'fas fa-clock', badge: 0},
                {name: 'approved', label: '已通过', icon: 'fas fa-check-circle', badge: 0},
                {name: 'published', label: '已发布', icon: 'fas fa-globe', badge: 0},
                {name: 'rejected', label: '已拒绝', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟供货商演示数据
                const mockData = [
                    {
                        id: 1,
                        demo_title: '智能制造设备演示',
                        demo_type: 'product',
                        supplier_name: '华东制造有限公司',
                        product_name: '智能生产线设备',
                        demo_description: '展示最新的智能制造设备功能和优势',
                        demo_url: 'https://demo.example.com/smart-manufacturing',
                        status: 'published',
                        view_count: 1256,
                        like_count: 89,
                        created_at: '2024-01-10 09:00:00',
                        updated_at: '2024-01-15 10:30:00'
                    },
                    {
                        id: 2,
                        demo_title: '新材料技术方案',
                        demo_type: 'solution',
                        supplier_name: '北方贸易集团',
                        product_name: '高强度复合材料',
                        demo_description: '介绍新型复合材料的应用方案和技术优势',
                        demo_url: 'https://demo.example.com/new-materials',
                        status: 'pending',
                        view_count: 234,
                        like_count: 12,
                        created_at: '2024-01-12 14:20:00',
                        updated_at: '2024-01-12 14:20:00'
                    }
                ]

                this.dataList = mockData
                this.total = 156
                this.publishedDemoCount = 89
                this.pendingDemoCount = 23
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 23
            this.tabOptions[2].badge = 34
            this.tabOptions[3].badge = 89
            this.tabOptions[4].badge = 10
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                type: '',
                supplier_id: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleCreate() {
            this.$message.info('新增演示功能开发中')
        },

        handleRefresh() {
            this.loadData()
        },

        async handleExport() {
            try {
                this.exportLoading = true
                await new Promise(resolve => setTimeout(resolve, 2000))
                this.$message.success('演示数据导出成功')
            } catch (error) {
                console.error('导出失败:', error)
                this.$message.error('导出失败，请重试')
            } finally {
                this.exportLoading = false
            }
        },

        // 批量操作
        handleBatchApprove() {
            const pendingDemos = this.selectedRows.filter(row => row.status === 'pending')

            if (pendingDemos.length === 0) {
                this.$message.warning('没有可审核的演示')
                return
            }

            this.$confirm(`确定要审核通过选中的 ${pendingDemos.length} 个演示吗？`, '确认批量审核', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success(`成功审核通过 ${pendingDemos.length} 个演示`)
                this.selectedRows = []
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消操作')
            })
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        },

        handleViewDetail(row) {
            this.$message.info(`查看演示详情: ${row.demo_title}`)
        },

        handleEdit(row) {
            this.$message.info(`编辑演示: ${row.demo_title}`)
        },

        handleStatusChange(row, status) {
            this.$message.success(`演示状态已更新为: ${status}`)
            this.loadData()
        },

        handleDelete(row) {
            this.$confirm(`确定要删除演示 "${row.demo_title}" 吗？`, '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                this.loadData()
            }).catch(() => {
                this.$message.info('已取消删除')
            })
        }
    }
}
</script>

<style scoped>
.supplier-demo-index-page {
    height: 100%;
}

.advanced-search-form {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.footer-stats {
    display: flex;
    gap: 20px;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.stat-item strong {
    color: #333;
    margin-left: 4px;
}
</style>
