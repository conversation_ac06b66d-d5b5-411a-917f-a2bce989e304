<!--
/**
 * 用户角色分配管理页面
 *
 * 功能特性：
 * - 用户角色分配管理
 * - 权限分配管理
 * - 企业级管理界面
 * - 搜索和筛选功能
 *
 * 路由路径：/admin/user/assignment
 * 页面标题：角色分配管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="user-assignment-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索用户名或角色名称'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleAssign">
                    <i class="fas fa-user-plus"></i>
                    分配角色
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="用户角色">
                            <el-select v-model="filters.role" clearable placeholder="选择角色">
                                <el-option label="全部角色" value=""></el-option>
                                <el-option label="超级管理员" value="super_admin"></el-option>
                                <el-option label="管理员" value="admin"></el-option>
                                <el-option label="普通用户" value="user"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分配状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="已分配" value="assigned"></el-option>
                                <el-option label="未分配" value="unassigned"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 分配列表 -->
            <template #default>
                <AssignmentListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总分配: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import AssignmentListView from './components/AssignmentListView.vue'

export default {
    name: 'AdminUserAssignmentIndexPage',
    components: {
        BackendPageListLayout,
        AssignmentListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                role: '',
                status: ''
            },

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部分配', icon: 'fas fa-users', badge: 0},
                {name: 'assigned', label: '已分配', icon: 'fas fa-user-check', badge: 0},
                {name: 'unassigned', label: '未分配', icon: 'fas fa-user-times', badge: 0},
                {name: 'pending', label: '待分配', icon: 'fas fa-user-clock', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟分配数据
                const mockData = [
                    {
                        id: 1,
                        user: {
                            id: 1,
                            name: '张三',
                            email: '<EMAIL>'
                        },
                        roles: [
                            {id: 1, name: '超级管理员', code: 'super_admin'}
                        ],
                        permissions_count: 50,
                        status: 'assigned',
                        assigned_at: '2024-01-01 09:00:00',
                        assigned_by: '系统管理员'
                    },
                    {
                        id: 2,
                        user: {
                            id: 2,
                            name: '李四',
                            email: '<EMAIL>'
                        },
                        roles: [
                            {id: 2, name: '管理员', code: 'admin'},
                            {id: 3, name: '普通用户', code: 'user'}
                        ],
                        permissions_count: 35,
                        status: 'assigned',
                        assigned_at: '2024-01-02 10:15:00',
                        assigned_by: '超级管理员'
                    },
                    {
                        id: 3,
                        user: {
                            id: 3,
                            name: '王五',
                            email: '<EMAIL>'
                        },
                        roles: [],
                        permissions_count: 0,
                        status: 'unassigned',
                        assigned_at: null,
                        assigned_by: null
                    }
                ]

                this.dataList = mockData
                this.total = 25
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 18
            this.tabOptions[2].badge = 5
            this.tabOptions[3].badge = 2
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        resetFilters() {
            this.filters = {
                role: '',
                status: ''
            }
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleAssign() {
            this.$message.info('角色分配功能开发中...')
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.user-assignment-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
