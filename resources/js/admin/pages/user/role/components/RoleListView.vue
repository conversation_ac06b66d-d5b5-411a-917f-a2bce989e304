<!--
/**
 * 角色列表视图组件
 *
 * 功能特性：
 * - 角色数据表格展示
 * - 权限管理功能
 * - 状态管理和操作
 * - 响应式设计
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="role-list-view">
        <el-table
            :data="dataList"
            :loading="loading"
            border
            class="admin-table"
            stripe
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"/>
            <el-table-column label="ID" prop="id" sortable width="80"/>

            <el-table-column
                label="角色名称"
                min-width="150"
                prop="name"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <div class="role-name-cell">
                        <i class="fas fa-user-tag role-icon"></i>
                        <span class="role-name">{{ row.name }}</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                label="角色代码"
                prop="code"
                show-overflow-tooltip
                width="120"
            >
                <template #default="{ row }">
                    <el-tag size="small" type="info">{{ row.code }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="描述"
                min-width="200"
                prop="description"
                show-overflow-tooltip
            />

            <el-table-column
                align="center"
                label="状态"
                prop="status"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getStatusType(row.status)"
                        size="small"
                    >
                        {{ getStatusName(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="权限数量"
                prop="permissions_count"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag size="small" type="primary">
                        {{ row.permissions_count }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                align="center"
                label="用户数量"
                prop="users_count"
                width="100"
            >
                <template #default="{ row }">
                    <el-tag size="small" type="success">
                        {{ row.users_count }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="创建时间"
                prop="created_at"
                sortable
                width="160"
            />

            <el-table-column
                align="center"
                fixed="right"
                label="操作"
                width="280"
            >
                <template #default="{ row }">
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleView(row)"
                    >
                        查看
                    </el-button>
                    <el-button
                        size="small"
                        type="success"
                        @click="handleEdit(row)"
                    >
                        编辑
                    </el-button>
                    <el-button
                        size="small"
                        type="warning"
                        @click="handlePermissions(row)"
                    >
                        权限
                    </el-button>
                    <el-dropdown trigger="click">
                        <el-button size="small" type="info">
                            更多
                            <i class="fas fa-chevron-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="handleCopy(row)">
                                    <i class="fas fa-copy"></i>
                                    复制角色
                                </el-dropdown-item>
                                <el-dropdown-item divided @click="handleDelete(row)">
                                    <i class="fas fa-trash"></i>
                                    删除角色
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'RoleListView',
    props: {
        dataList: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        searchQuery: {
            type: String,
            default: ''
        }
    },
    emits: [
        'selection-change',
        'edit',
        'delete',
        'view-detail',
        'permissions',
        'copy',
        'data-change'
    ],
    data() {
        return {
            statusMap: {
                'active': '启用',
                'inactive': '禁用'
            }
        }
    },
    methods: {
        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || status
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'inactive': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 选择变化
        handleSelectionChange(selection) {
            this.$emit('selection-change', selection)
        },

        // 查看详情
        handleView(row) {
            this.$emit('view-detail', row)
        },

        // 编辑角色
        handleEdit(row) {
            this.$emit('edit', row)
        },

        // 权限管理
        handlePermissions(row) {
            this.$emit('permissions', row)
        },

        // 复制角色
        handleCopy(row) {
            this.$emit('copy', row)
        },

        // 删除角色
        handleDelete(row) {
            this.$emit('delete', row)
            // 触发数据变化事件
            this.$emit('data-change')
        }
    }
}
</script>

<style lang="scss" scoped>
.role-list-view {
    .admin-table {
        .role-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .role-icon {
                color: #409eff;
                font-size: 14px;
            }

            .role-name {
                font-weight: 500;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .role-list-view {
        .admin-table {
            font-size: 12px;
        }
    }
}
</style>
