<!--
/**
 * 用户角色管理页面
 *
 * 功能特性：
 * - 角色列表展示
 * - 角色权限管理
 * - 企业级管理界面
 * - 搜索和筛选功能
 *
 * 路由路径：/admin/user/role
 * 页面标题：角色管理
 *
 * 版本：v1.0.0
 * 更新时间：2024年
 * 作者：Admin Team
 */
-->

<template>
    <div class="user-role-index-page">
        <BackendPageListLayout
            :active-tab-value="activeTab"
            :current-page="currentPage"
            :loading="loading"
            :page-size="pageSize"
            :search-placeholder="'搜索角色名称或描述'"
            :search-query="searchQuery"
            :show-advanced-search="showAdvancedSearch"
            :show-bottom-pagination="true"
            :show-footer="true"
            :show-header="true"
            :show-tabs="true"
            :show-top-pagination="true"
            :tab-options="tabOptions"
            :total="total"
            :v_loading="v_loading"
            @tab-change="handleTabChange"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            @search-input="handleSearchInput"
            @search-clear="handleSearchClear"
        >
            <!-- 左侧操作按钮 -->
            <template #header-left>
                <el-button :loading="loading" type="primary" @click="handleRefresh">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </el-button>
                <el-button type="success" @click="handleCreate">
                    <i class="fas fa-plus"></i>
                    新增角色
                </el-button>
            </template>

            <!-- 高级搜索面板 -->
            <template #advanced-search>
                <div class="advanced-search-form">
                    <el-form :model="filters" inline label-width="80px">
                        <el-form-item label="角色状态">
                            <el-select v-model="filters.status" clearable placeholder="选择状态">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建日期">
                            <el-date-picker
                                v-model="dateRange"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                @change="handleDateRangeChange"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleAdvancedSearch">
                                <i class="fas fa-search"></i>
                                搜索
                            </el-button>
                            <el-button @click="resetFilters">
                                <i class="fas fa-undo"></i>
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </template>

            <!-- 角色列表 -->
            <template #default>
                <RoleListView
                    :data-list="dataList"
                    :loading="loading"
                    :search-query="searchQuery"
                    @selection-change="handleSelectionChange"
                    @data-change="fetchData"
                />
            </template>

            <!-- 底部信息 -->
            <template #footer-left-section>
                <div class="footer-stats">
					<span class="stat-item">
						总角色: <strong>{{ total }}</strong>
					</span>
                    <span v-if="hasSelectedRows" class="stat-item">
						已选择: <strong>{{ selectedRows.length }}</strong> 项
					</span>
                </div>
            </template>
        </BackendPageListLayout>
    </div>
</template>

<script>
import BackendPageListLayout from '@layouts/BackendLayout/BackendPageLayout/BackendPageListLayout.vue'
import RoleListView from './components/RoleListView.vue'

export default {
    name: 'AdminUserRoleIndexPage',
    components: {
        BackendPageListLayout,
        RoleListView
    },
    data() {
        return {
            // 基础状态
            loading: false,
            v_loading: false,

            // 搜索和筛选
            searchQuery: '',
            showAdvancedSearch: false,
            filters: {
                status: '',
                start_date: '',
                end_date: ''
            },
            dateRange: [],

            // 分页
            currentPage: 1,
            pageSize: 20,
            total: 0,

            // Tab选项
            activeTab: 'all',
            tabOptions: [
                {name: 'all', label: '全部角色', icon: 'fas fa-users-cog', badge: 0},
                {name: 'active', label: '启用角色', icon: 'fas fa-check-circle', badge: 0},
                {name: 'inactive', label: '禁用角色', icon: 'fas fa-times-circle', badge: 0}
            ],

            // 数据
            dataList: [],
            selectedRows: []
        }
    },
    computed: {
        hasSelectedRows() {
            return this.selectedRows.length > 0
        }
    },
    mounted() {
        this.fetchData()
    },
    methods: {
        // 获取数据 (兼容模板命名)
        async fetchData() {
            return this.loadData()
        },

        // 加载数据
        async loadData() {
            try {
                this.v_loading = true
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟角色数据
                const mockData = [
                    {
                        id: 1,
                        name: '超级管理员',
                        code: 'super_admin',
                        description: '系统超级管理员，拥有所有权限',
                        status: 'active',
                        permissions_count: 50,
                        users_count: 2,
                        created_at: '2024-01-01 09:00:00'
                    },
                    {
                        id: 2,
                        name: '管理员',
                        code: 'admin',
                        description: '系统管理员，拥有大部分管理权限',
                        status: 'active',
                        permissions_count: 35,
                        users_count: 5,
                        created_at: '2024-01-02 10:15:00'
                    },
                    {
                        id: 3,
                        name: '普通用户',
                        code: 'user',
                        description: '普通用户角色，基础权限',
                        status: 'active',
                        permissions_count: 10,
                        users_count: 128,
                        created_at: '2024-01-03 11:30:00'
                    }
                ]

                this.dataList = mockData
                this.total = 15
                this.updateTabBadges()

            } catch (error) {
                console.error('加载数据失败:', error)
                this.$message.error('加载数据失败，请重试')
            } finally {
                this.v_loading = false
                this.loading = false
            }
        },

        // 更新Tab徽章
        updateTabBadges() {
            this.tabOptions[0].badge = this.total
            this.tabOptions[1].badge = 12
            this.tabOptions[2].badge = 3
        },

        // Tab切换
        handleTabChange(tab) {
            this.activeTab = tab.name
            this.currentPage = 1
            this.loadData()
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page
            this.loadData()
        },

        handlePageSizeChange(size) {
            this.pageSize = size
            this.currentPage = 1
            this.loadData()
        },

        // 搜索处理
        handleSearchInput(value) {
            this.searchQuery = value
            this.currentPage = 1
            this.loadData()
        },

        handleSearchClear() {
            this.searchQuery = ''
            this.currentPage = 1
            this.loadData()
        },

        // 高级搜索
        handleAdvancedSearch() {
            this.currentPage = 1
            this.loadData()
        },

        handleDateRangeChange(dates) {
            if (dates && dates.length === 2) {
                this.filters.start_date = dates[0]
                this.filters.end_date = dates[1]
            } else {
                this.filters.start_date = ''
                this.filters.end_date = ''
            }
        },

        resetFilters() {
            this.filters = {
                status: '',
                start_date: '',
                end_date: ''
            }
            this.dateRange = []
            this.handleAdvancedSearch()
        },

        // 操作处理
        handleRefresh() {
            this.loadData()
        },

        handleCreate() {
            this.$message.info('创建角色功能开发中...')
        },

        // 列表操作
        handleSelectionChange(selection) {
            this.selectedRows = selection
        }
    }
}
</script>

<style lang="scss" scoped>
.user-role-index-page {
    .advanced-search-form {
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;
    }

    .footer-stats {
        display: flex;
        gap: 16px;

        .stat-item {
            color: #606266;
            font-size: 14px;

            strong {
                color: #303133;
                font-weight: 600;
            }
        }
    }
}
</style>
