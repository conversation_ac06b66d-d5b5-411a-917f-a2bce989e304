<template>
    <!-- Admin 面包屑包装组件 -->
    <BackendBreadcrumb
        :breadcrumb-config="adminBreadcrumbConfig"
        :max-level="adminBreadcrumbConfig.maxLevel"
        :separator="adminBreadcrumbConfig.separator"
        :show-actions="adminBreadcrumbConfig.showActions"
        :show-home-icon="adminBreadcrumbConfig.showHomeIcon"
    />
</template>

<script>
// 导入全局面包屑组件
import BackendBreadcrumb from '@layouts/BackendLayout/components/BackendBreadcrumb.vue'

/**
 * Admin 面包屑包装组件
 *
 * 功能说明：
 * - 为 Admin 系统提供专用的面包屑配置
 * - 管理 Admin 特定的导航路径和显示规则
 * - 处理 Admin 特有的面包屑交互逻辑
 * - 可以添加 Admin 特定的导航功能（如快速导航、路径收藏等）
 *
 * 设计原则：
 * - 封装 Admin 特定的面包屑逻辑
 * - 保持与全局 BackendBreadcrumb 的兼容性
 * - 提供灵活的配置和扩展能力
 */
export default {
    name: 'AdminBreadcrumbWrapper',
    components: {
        BackendBreadcrumb
    },

    /**
     * 组件属性
     */
    props: {
        /**
         * 外部传入的面包屑配置
         */
        breadcrumbConfig: {
            type: Object,
            default: null
        }
    },

    /**
     * 组件数据
     */
    data() {
        return {
            // Admin 面包屑默认配置
            defaultAdminBreadcrumbConfig: {
                // 显示配置
                showActions: true,
                showHomeIcon: true,
                showCopyPath: true,
                showBackButton: true,

                // 导航配置
                separator: '/',
                maxLevel: 5,
                enableNavigation: true,

                // 首页配置
                showHome: true,
                homeText: '管理首页',
                homePath: '/admin/home/<USER>',
                homeIcon: 'fas fa-home',

                // Admin 特定配置
                adminPrefix: '管理后台',
                showSystemBadge: true,
                enableQuickNavigation: true,

                // 样式配置
                showTooltip: true,
                animationEnabled: true,
                compactMode: false
            },

            // Admin 快速导航收藏夹
            adminQuickNavigation: [
                {
                    name: '系统首页',
                    path: '/admin/home/<USER>',
                    icon: 'fas fa-tachometer-alt'
                },
                {
                    name: '用户管理',
                    path: '/admin/user/user',
                    icon: 'fas fa-users'
                },
                {
                    name: '系统设置',
                    path: '/admin/site/site',
                    icon: 'fas fa-cog'
                }
            ]
        }
    },

    /**
     * 计算属性
     */
    computed: {
        /**
         * Admin 面包屑配置
         * 合并默认配置和外部传入的配置
         */
        adminBreadcrumbConfig() {
            return {
                ...this.defaultAdminBreadcrumbConfig,
                ...this.breadcrumbConfig
            }
        },

        /**
         * 当前路由信息
         */
        currentRouteInfo() {
            return {
                path: this.$route.path,
                name: this.$route.name,
                meta: this.$route.meta,
                params: this.$route.params,
                query: this.$route.query
            }
        },

        /**
         * 是否显示 Admin 系统徽章
         */
        shouldShowSystemBadge() {
            return this.adminBreadcrumbConfig.showSystemBadge &&
                this.currentRouteInfo.path.startsWith('/admin')
        }
    },

    /**
     * 组件方法
     */
    methods: {
        /**
         * 处理 Admin 面包屑点击
         */
        handleAdminBreadcrumbClick(breadcrumbItem) {
            try {
                // Admin 特定的面包屑点击处理
                this.logAdminNavigationAction(breadcrumbItem)

                // 触发导航统计
                this.trackAdminNavigation(breadcrumbItem)

                // 向父组件传递事件
                this.$emit('breadcrumb-click', breadcrumbItem)

            } catch (error) {
                console.error('Admin 面包屑点击处理失败:', error)
            }
        },

        /**
         * 记录 Admin 导航操作日志
         */
        logAdminNavigationAction(breadcrumbItem) {
            if (process.env.NODE_ENV === 'development') {
                console.log('Admin 面包屑导航:', {
                    item: breadcrumbItem,
                    from: this.currentRouteInfo.path,
                    timestamp: new Date().toISOString(),
                    user: 'admin'
                })
            }
        },

        /**
         * 跟踪 Admin 导航使用情况
         */
        trackAdminNavigation(breadcrumbItem) {
            // 这里可以添加导航使用统计逻辑
            if (window.AdminAnalytics) {
                window.AdminAnalytics.track('breadcrumb_navigation', {
                    from_path: this.currentRouteInfo.path,
                    to_path: breadcrumbItem.path,
                    breadcrumb_name: breadcrumbItem.name,
                    timestamp: Date.now()
                })
            }
        },

        /**
         * 添加到快速导航
         */
        addToQuickNavigation(routeInfo = null) {
            const route = routeInfo || this.currentRouteInfo

            // 检查是否已存在
            const exists = this.adminQuickNavigation.some(item => item.path === route.path)
            if (exists) {
                this.$message.warning('该页面已在快速导航中')
                return
            }

            // 添加到快速导航
            const quickNavItem = {
                name: route.meta?.title || route.name || '未命名页面',
                path: route.path,
                icon: route.meta?.icon || 'fas fa-bookmark'
            }

            this.adminQuickNavigation.push(quickNavItem)

            // 保存到本地存储
            this.saveQuickNavigation()

            this.$message.success('已添加到快速导航')
        },

        /**
         * 从快速导航中移除
         */
        removeFromQuickNavigation(path) {
            const index = this.adminQuickNavigation.findIndex(item => item.path === path)
            if (index > -1) {
                this.adminQuickNavigation.splice(index, 1)
                this.saveQuickNavigation()
                this.$message.success('已从快速导航中移除')
            }
        },

        /**
         * 保存快速导航到本地存储
         */
        saveQuickNavigation() {
            try {
                localStorage.setItem('admin_quick_navigation', JSON.stringify(this.adminQuickNavigation))
            } catch (error) {
                console.error('保存快速导航失败:', error)
            }
        },

        /**
         * 从本地存储加载快速导航
         */
        loadQuickNavigation() {
            try {
                const saved = localStorage.getItem('admin_quick_navigation')
                if (saved) {
                    const parsed = JSON.parse(saved)
                    if (Array.isArray(parsed)) {
                        this.adminQuickNavigation = [...this.adminQuickNavigation, ...parsed]
                    }
                }
            } catch (error) {
                console.error('加载快速导航失败:', error)
            }
        },

        /**
         * 初始化 Admin 面包屑特有功能
         */
        initAdminBreadcrumbFeatures() {
            // 加载快速导航
            this.loadQuickNavigation()

            // 设置 Admin 特有的快捷键
            this.setupAdminBreadcrumbShortcuts()
        },

        /**
         * 设置 Admin 面包屑快捷键
         */
        setupAdminBreadcrumbShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+Shift+B 添加到快速导航
                if (e.ctrlKey && e.shiftKey && e.key === 'B') {
                    e.preventDefault()
                    this.addToQuickNavigation()
                }

                // Ctrl+Shift+H 快速回到首页
                if (e.ctrlKey && e.shiftKey && e.key === 'H') {
                    e.preventDefault()
                    this.$router.push(this.adminBreadcrumbConfig.homePath)
                }
            })
        }
    },

    /**
     * 组件挂载后执行
     */
    mounted() {
        this.initAdminBreadcrumbFeatures()
    }
}
</script>

<style lang="scss" scoped>
/**
 * Admin 面包屑包装组件样式
 *
 * 注意：
 * - 主要样式由 BackendBreadcrumb 提供
 * - 这里只添加 Admin 特定的样式扩展
 * - 保持样式的简洁和一致性
 */

/* Admin 特定的面包屑样式可以在这里添加 */
/* 例如：Admin 徽章样式、快速导航按钮样式等 */
</style>
