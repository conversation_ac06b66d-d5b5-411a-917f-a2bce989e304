<template>
    <!-- Admin 头部包装组件 -->
    <BackendHeader :header-config="adminHeaderConfig"/>
</template>

<script>
import BackendHeader from '@layouts/BackendLayout/components/BackendHeader.vue'

/**
 * Admin 头部包装组件
 *
 * 功能说明：
 * - 为 Admin 系统提供特定的头部配置
 * - 管理 Admin 特定的路由配置
 * - 封装 Admin 业务逻辑，保持通用组件的纯净性
 *
 * 配置管理：
 * - 路由配置：Admin 特定的页面路由
 * - 功能配置：Admin 特定的功能开关
 *
 * 使用场景：
 * - Admin 后台管理系统的头部区域
 * - 与其他后台系统（商家管理中心等）区分
 */
export default {
    name: 'AdminHeaderWrapper',
    components: {
        BackendHeader
    },

    computed: {
        /**
         * Admin 头部配置
         * 定义 Admin 系统特定的头部行为和路由
         */
        adminHeaderConfig() {
            return {
                // Admin 特定路由配置
                routes: {
                    notifications: '/admin/notifications',
                    profile: '/admin/profile',
                    settings: '/admin/settings',
                    login: '/login'
                },

                // Admin 特定功能配置
                features: {
                    showNotifications: true,
                    showThemeToggle: true,
                    showUserMenu: true,
                    showBreadcrumbs: true
                },

                // Admin 特定样式配置
                theme: {
                    variant: 'admin',
                    primaryColor: '#5247ef'
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
// Admin 头部包装组件样式
// 这里可以添加 Admin 特定的样式覆盖
</style>
