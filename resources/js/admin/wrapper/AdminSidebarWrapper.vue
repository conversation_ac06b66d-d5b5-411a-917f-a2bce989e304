<template>
    <!-- Admin 侧边栏包装组件 -->
    <BackendSidebar
        :active-menu="activeMenu"
        :allow-multiple-open="adminAllowMultipleOpen"
        :logo-config="adminLogoConfig"
        :main-menu-items="adminMainMenuItems"
        :sidebar-config="adminSidebarExtendedConfig"
        :system-menu-items="adminSystemMenuItems"
        :theme="adminSidebarTheme"
        @menu-select="handleAdminMenuSelect"
    />
</template>

<script>
// 导入全局侧边栏组件
import BackendSidebar from '@layouts/BackendLayout/components/BackendSidebar.vue'
// 导入 Admin 菜单配置
import {MenuUtils} from '@/admin/config/menuConfig'

/**
 * Admin 侧边栏包装组件
 *
 * 功能说明：
 * - 为 Admin 系统提供专用的侧边栏配置
 * - 管理 Admin 特定的菜单数据和行为
 * - 处理 Admin 特有的菜单交互逻辑
 * - 可以添加 Admin 特定的菜单功能（如快捷操作、系统状态等）
 *
 * 设计原则：
 * - 封装 Admin 特定的侧边栏逻辑
 * - 保持与全局 BackendSidebar 的兼容性
 * - 提供灵活的配置和扩展能力
 */
export default {
    name: 'AdminSidebarWrapper',
    components: {
        BackendSidebar
    },

    /**
     * 组件属性
     */
    props: {
        /**
         * 当前激活的菜单项
         */
        activeMenu: {
            type: String,
            default: ''
        }
    },

    /**
     * 组件数据
     */
    data() {
        return {
            // Admin 侧边栏特定配置
            adminSidebarConfig: {
                // 主题配置
                theme: 'light',

                // 菜单行为配置
                allowMultipleOpen: true,
                autoCollapse: false,

                // Admin 特有功能
                showQuickActions: true,
                showSystemStatus: process.env.NODE_ENV === 'development',
                enableMenuSearch: false, // Admin 暂不启用菜单搜索

                // Logo 配置
                logo: {
                    icon: 'fas fa-gem',
                    title: '管理系统',
                    subtitle: 'Admin Panel',
                    homeRoute: '/admin/home/<USER>'
                }
            }
        }
    },

    /**
     * 计算属性
     */
    computed: {
        /**
         * Admin 侧边栏主题
         */
        adminSidebarTheme() {
            return this.adminSidebarConfig.theme
        },

        /**
         * 是否允许多个菜单同时展开
         */
        adminAllowMultipleOpen() {
            return this.adminSidebarConfig.allowMultipleOpen
        },

        /**
         * Admin 菜单数据
         */
        adminMenuData() {
            try {
                const menuData = MenuUtils.getProcessedMenuConfig({
                    includeDisabled: false,
                    includeHidden: false,
                    filter: (item) => {
                        // Admin 特定的菜单过滤逻辑
                        return item.visible !== false && !item.adminHidden
                    }
                })

                // 临时调试信息
                if (process.env.NODE_ENV === 'development') {
                    console.log('AdminSidebarWrapper - adminMenuData:', menuData)
                    console.log('AdminSidebarWrapper - mainMenuItems count:', menuData?.mainMenuItems?.length || 0)
                }

                return menuData
            } catch (error) {
                console.error('Admin 菜单数据获取失败:', error)
                return {
                    mainMenuItems: [],
                    systemMenuItems: []
                }
            }
        },

        /**
         * Admin 主要功能菜单项
         */
        adminMainMenuItems() {
            const items = this.adminMenuData?.mainMenuItems || []

            // 临时调试信息
            if (process.env.NODE_ENV === 'development') {
                console.log('AdminSidebarWrapper - adminMainMenuItems:', items)
                console.log('AdminSidebarWrapper - adminMainMenuItems length:', items.length)
            }

            return items
        },

        /**
         * Admin 系统工具菜单项
         */
        adminSystemMenuItems() {
            return this.adminMenuData?.systemMenuItems || []
        },

        /**
         * Admin Logo 配置
         */
        adminLogoConfig() {
            return {
                ...this.adminSidebarConfig.logo,
                homeRoute: this.adminSidebarConfig.logo.homeRoute
            }
        },

        /**
         * Admin 侧边栏扩展配置
         */
        adminSidebarExtendedConfig() {
            return {
                showCart: true, // Admin 系统不显示购物车
                cartItemCount: 0
            }
        }
    },

    /**
     * 组件方法
     */
    methods: {
        /**
         * 处理 Admin 菜单选择
         * 可以在这里添加 Admin 特定的菜单选择逻辑
         */
        handleAdminMenuSelect(menuData) {
            try {
                // Admin 特定的菜单选择处理
                this.logAdminMenuAction(menuData)

                // 触发统计或分析
                this.trackAdminMenuUsage(menuData)

                // 向父组件传递事件
                this.$emit('menu-select', menuData)

            } catch (error) {
                console.error('Admin 菜单选择处理失败:', error)
            }
        },

        /**
         * 记录 Admin 菜单操作日志
         */
        logAdminMenuAction(menuData) {
            if (this.adminSidebarConfig.showSystemStatus) {
                console.log('Admin 菜单操作:', {
                    menu: menuData,
                    timestamp: new Date().toISOString(),
                    user: 'admin', // 可以从用户 store 获取
                    session: 'admin-session'
                })
            }
        },

        /**
         * 跟踪 Admin 菜单使用情况
         */
        trackAdminMenuUsage(menuData) {
            // 这里可以添加菜单使用统计逻辑
            // 例如：发送到分析服务、更新使用计数等
            if (window.AdminAnalytics) {
                window.AdminAnalytics.track('menu_click', {
                    menu_id: menuData.id || menuData.route,
                    menu_name: menuData.name,
                    timestamp: Date.now()
                })
            }
        },

        /**
         * 初始化 Admin 侧边栏特有功能
         */
        initAdminSidebarFeatures() {
            // 设置 Admin 特有的快捷键
            this.setupAdminShortcuts()

            // 初始化系统状态监控（如果启用）
            if (this.adminSidebarConfig.showSystemStatus) {
                this.initSystemStatusMonitor()
            }
        },

        /**
         * 设置 Admin 快捷键
         */
        setupAdminShortcuts() {
            // 例如：Ctrl+Shift+H 快速回到首页
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.shiftKey && e.key === 'H') {
                    e.preventDefault()
                    this.$router.push(this.adminSidebarConfig.logo.homeRoute)
                }
            })
        },

        /**
         * 初始化系统状态监控
         */
        initSystemStatusMonitor() {
            // 这里可以添加系统状态监控逻辑
            console.log('Admin 系统状态监控已启用')
        }
    },

    /**
     * 组件挂载后执行
     */
    mounted() {
        this.initAdminSidebarFeatures()
    }
}
</script>

<style lang="scss" scoped>
/**
 * Admin 侧边栏包装组件样式
 *
 * 注意：
 * - 主要样式由 BackendSidebar 提供
 * - 这里只添加 Admin 特定的样式扩展
 * - 保持样式的简洁和一致性
 */

/* Admin 特定的侧边栏样式可以在这里添加 */
/* 例如：Admin 特有的菜单项样式、快捷操作按钮样式等 */
</style>
