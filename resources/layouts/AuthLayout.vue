<template>
    <div class="auth-page">
        <!-- 轮播背景 -->
        <div class="carousel-background">
            <div
                v-for="(slide, index) in carouselSlides"
                :key="index"
                :class="['carousel-slide', { active: currentSlide === index }]"
                :style="{ backgroundImage: `url(${slide.image})` }"
            >
                <div class="slide-overlay">
                    <div class="slide-content">
                        <h2 class="slide-title">{{ slide.title }}</h2>
                        <p class="slide-description">{{ slide.description }}</p>
                    </div>
                </div>
            </div>
            <div class="carousel-indicators">
                <button
                    v-for="(slide, index) in carouselSlides"
                    :key="index"
                    :class="['indicator', { active: currentSlide === index }]"
                    @click="goToSlide(index)"
                ></button>
            </div>
        </div>

        <div class="auth-container">
            <!-- 左侧装饰区域 -->
            <div class="auth-decoration">
                <div class="decoration-content">
                    <div class="decoration-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h2 class="decoration-title">欢迎使用管理系统</h2>
                    <p class="decoration-subtitle">安全、高效、便捷的管理平台</p>
                    <div class="decoration-features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>多种登录方式</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>安全可靠</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>快速便捷</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧表单区域 - 使用插槽 -->
            <div class="auth-form-section">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AuthLayout',
    data() {
        return {
            // 轮播背景
            currentSlide: 0,
            carouselTimer: null,
            carouselSlides: [
                {
                    image: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                    title: '现代化管理平台',
                    description: '基于最新技术栈构建的企业级管理系统'
                },
                {
                    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                    title: '数据驱动决策',
                    description: '实时数据分析，助力企业智能化转型'
                },
                {
                    image: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
                    title: '团队协作无界',
                    description: '高效的团队协作工具，提升工作效率'
                }
            ]
        }
    },

    mounted() {
        // 启动轮播
        this.startCarousel()

        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize)
    },

    beforeUnmount() {
        // 清理定时器
        if (this.carouselTimer) {
            clearInterval(this.carouselTimer)
        }

        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize)
    },

    methods: {
        // 轮播相关方法
        startCarousel() {
            this.carouselTimer = setInterval(() => {
                this.nextSlide()
            }, 5000) // 5秒切换一次
        },

        nextSlide() {
            this.currentSlide = (this.currentSlide + 1) % this.carouselSlides.length
        },

        goToSlide(index) {
            if (index === this.currentSlide) return

            // 清除自动轮播，用户手动切换后重新开始
            if (this.carouselTimer) {
                clearInterval(this.carouselTimer)
            }

            this.currentSlide = index

            // 重新开始自动轮播
            setTimeout(() => {
                this.startCarousel()
            }, 3000) // 3秒后重新开始自动轮播
        },

        // 窗口大小变化处理
        handleResize() {
            // 可以在这里处理响应式逻辑
        }
    }
}
</script>

<style lang="scss" scoped>
@use 'sass:color';

// 简化的淡入动画 - 企业级风格
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.auth-page {
    height: calc(100vh - 70px);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: $font-family-primary;
    box-sizing: border-box;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;

    // 简化的淡入动画
    animation: fadeIn 0.5s ease-in-out;
}

// 轮播背景
.carousel-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .carousel-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 0;
        transition: opacity 1.5s ease-in-out;

        &.active {
            opacity: 0.3;
        }

        .slide-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                    135deg,
                    rgba(102, 126, 234, 0.8) 0%,
                    rgba(118, 75, 162, 0.8) 100%
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide-content {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 0 20px;

            .slide-title {
                font-size: 3rem;
                font-weight: 700;
                margin-bottom: 1rem;
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                line-height: 1.2;

                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }

            .slide-description {
                font-size: 1.2rem;
                font-weight: 300;
                opacity: 0.9;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                line-height: 1.6;

                @media (max-width: 768px) {
                    font-size: 1rem;
                }
            }
        }
    }

    .carousel-indicators {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        z-index: 1;

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.5);
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: rgba(255, 255, 255, 0.8);
            }

            &.active {
                background: white;
                border-color: white;
            }
        }
    }
}

.auth-container {
    position: relative;
    z-index: 2;
    display: flex;
    width: 100%;
    max-width: min(1200px, 90vw);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-radius: $border-radius-xl;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
    animation: fadeIn 0.6s ease-in-out 0.1s both;
}

// 左侧装饰区域
.auth-decoration {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: $border-radius-xl 0 0 $border-radius-xl;
    padding: 60px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    // 简化的装饰性背景图案
    &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    }

    .decoration-content {
        text-align: center;
        color: white;
        position: relative;
        z-index: 1;

        .decoration-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.9;

            i {
                filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
            }
        }

        .decoration-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            line-height: 1.2;
        }

        .decoration-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 3rem;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .decoration-features {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            .feature-item {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.8rem;
                font-size: 1rem;
                opacity: 0.9;

                i {
                    font-size: 1.2rem;
                    color: #4ade80;
                    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
                }
            }
        }
    }
}

// 右侧表单区域
.auth-form-section {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: white;
    border-radius: 0 $border-radius-xl $border-radius-xl 0;
    position: relative;

    // 添加微妙的纹理
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.01) 100%);
        pointer-events: none;
    }
}


// 响应式设计
@media (max-width: 1024px) {
    .auth-container {
        max-width: 95vw;
    }

    .auth-decoration {
        padding: 40px 30px;

        .decoration-content {
            .decoration-title {
                font-size: 2rem;
            }

            .decoration-subtitle {
                font-size: 1rem;
            }
        }
    }

    .auth-form-section {
        padding: 40px 30px;
    }
}

@media (max-width: 768px) {
    .auth-page {
        padding: 10px;
        height: 100vh;
    }

    .auth-container {
        flex-direction: column;
        max-width: 100%;
        max-height: 95vh;
        overflow-y: auto;
    }

    .auth-decoration {
        border-radius: $border-radius-xl $border-radius-xl 0 0;
        padding: 30px 20px;
        flex: none;
        min-height: 200px;

        .decoration-content {
            .decoration-icon {
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }

            .decoration-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .decoration-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1.5rem;
            }

            .decoration-features {
                flex-direction: row;
                justify-content: center;
                gap: 1rem;

                .feature-item {
                    font-size: 0.8rem;

                    i {
                        font-size: 1rem;
                    }
                }
            }
        }
    }

    .auth-form-section {
        border-radius: 0 0 $border-radius-xl $border-radius-xl;
        padding: 30px 20px;
        flex: 1;
    }
}

@media (max-width: 480px) {
    .auth-decoration {
        .decoration-features {
            flex-direction: column;
            gap: 0.8rem;
        }
    }

    .auth-form-section {
        padding: 20px 15px;
    }
}
</style>
