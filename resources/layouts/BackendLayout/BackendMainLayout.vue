<template>
    <div class="backend-main-layout">
        <!-- 侧边栏插槽 -->
        <slot :current-active-menu="currentActiveMenu" :handle-menu-select="handleMenuSelect" name="sidebar">
            <!-- 默认侧边栏 -->
            <BackendSidebar
                :active-menu="currentActiveMenu"
                :allow-multiple-open="sidebarConfig?.allowMultipleOpen !== false"
                :logo-config="sidebarConfig?.logo || {}"
                :main-menu-items="menuConfig?.mainMenuItems || []"
                :sidebar-config="sidebarConfig || {}"
                :system-menu-items="menuConfig?.systemMenuItems || []"
                :theme="sidebarConfig?.theme || 'light'"
                @menu-select="handleMenuSelect"
            />
        </slot>
        <!-- 主要内容区域 -->
        <div class="backend-main">
            <!-- 内容区域 -->
            <div class="backend-content">
                <!-- 内容容器 -->
                <div class="content-container">
                    <!-- 面包屑导航插槽 -->
                    <div v-if="showBreadcrumbArea" class="breadcrumb-wrapper">
                        <slot :breadcrumb-config="breadcrumbConfig" name="breadcrumb">
                            <!-- 默认面包屑 -->
                            <BackendBreadcrumb
                                :breadcrumb-config="breadcrumbConfig"
                                :show-actions="breadcrumbConfig?.showActions !== false"
                            />
                        </slot>

                        <!-- 头部右侧区域插槽 -->
                        <slot :current-time="currentTime" :is-refreshing="isRefreshing" :last-updated="lastUpdated"
                              :refresh-page="refreshPage" name="header-right">
                            <!-- 默认头部右侧内容 -->
                            <div class="header-right">
                                <div class="header-info">
                                    <div class="header-time-content">
                                        <span class="current-time">
                                            <i class="fas fa-clock"></i>
                                            {{ currentTime }}
                                        </span>
                                        <span class="last-updated">
                                            上次更新：{{ lastUpdated }}
                                        </span>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <el-tooltip content="刷新页面" placement="bottom">
                                        <el-button :loading="isRefreshing" circle class="refresh-btn" size="small"
                                                   @click="refreshPage">
                                            <el-icon>
                                                <Refresh/>
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </slot>
                    </div>
                    <!-- 加载状态 使用Element Plus骨架屏 -->
                    <div v-if="isContentLoading" class="content-loading">
                        <div class="skeleton-container">
                            <!-- 头部信息骨架 -->
                            <div class="skeleton-header">
                                <el-skeleton animated>
                                    <template #template>
                                        <div class="skeleton-header-content">
                                            <el-skeleton-item style="width: 60px; height: 60px;" variant="circle"/>
                                            <div class="skeleton-text-area">
                                                <el-skeleton-item style="width: 40%; margin-bottom: 12px;"
                                                                  variant="h1"/>
                                                <el-skeleton-item style="width: 60%;" variant="text"/>
                                            </div>
                                        </div>
                                    </template>
                                </el-skeleton>
                            </div>
                            <!-- 内容卡片骨架 -->
                            <div class="skeleton-cards">
                                <div v-for="i in 3" :key="i" class="skeleton-card">
                                    <el-skeleton animated>
                                        <template #template>
                                            <el-skeleton-item style="width: 50%; margin-bottom: 16px;" variant="h3"/>
                                            <el-skeleton-item style="margin-bottom: 8px;" variant="text"/>
                                            <el-skeleton-item style="width: 80%; margin-bottom: 8px;" variant="text"/>
                                            <el-skeleton-item style="width: 60%; margin-bottom: 16px;" variant="text"/>
                                            <div class="skeleton-actions">
                                                <el-skeleton-item style="width: 80px; height: 32px; margin-right: 12px;"
                                                                  variant="button"/>
                                                <el-skeleton-item style="width: 60px; height: 32px;" variant="button"/>
                                            </div>
                                        </template>
                                    </el-skeleton>
                                </div>
                            </div>
                            <!-- 表格骨架 -->
                            <div class="skeleton-table">
                                <el-skeleton animated>
                                    <template #template>
                                        <div class="table-header">
                                            <el-skeleton-item style="width: 15%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 25%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 20%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 15%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 10%;" variant="text"/>
                                        </div>
                                        <div v-for="row in 5" :key="row" class="table-row">
                                            <el-skeleton-item style="width: 15%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 25%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 20%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 15%; margin-right: 5%;" variant="text"/>
                                            <el-skeleton-item style="width: 10%; height: 24px;" variant="button"/>
                                        </div>
                                    </template>
                                </el-skeleton>
                            </div>
                        </div>
                    </div>
                    <!-- 错误状态 -->
                    <div v-if="hasError" class="error-container">
                        <el-result :sub-title="errorMessage" :title="errorTitle" icon="error">
                            <template #extra>
                                <div class="error-actions">
                                    <el-button :loading="isRetrying" class="retry-btn" type="primary"
                                               @click="retryLoad">
                                        <el-icon v-if="!isRetrying">
                                            <Refresh/>
                                        </el-icon>
                                        {{ isRetrying ? '重新加载中...' : '重新加载' }}
                                    </el-button>
                                    <el-button class="back-btn" @click="goBack">
                                        <el-icon>
                                            <ArrowLeft/>
                                        </el-icon>
                                        返回上页
                                    </el-button>
                                    <el-button text @click="goHome">
                                        回到首页
                                    </el-button>
                                </div>
                            </template>
                        </el-result>
                    </div>
                    <!-- 正常内容 -->
                    <div v-if="!isContentLoading && !hasError" class="content-area">
                        <router-view v-slot="{ Component, route }">
                            <transition mode="out-in" name="fade-slide">
                                <component
                                    :is="Component"
                                    v-if="Component"
                                    :key="`${route.path}-${refreshKey}-${refreshTimestamp}`"
                                    class="page-component"
                                />
                            </transition>
                        </router-view>
                    </div>
                    <!-- 底部插槽 -->
                    <slot :footer-config="footerConfig" name="footer">
                        <!-- 默认底部 -->
                        <BackendFooter :footer-config="footerConfig"/>
                    </slot>
                </div>
            </div>
        </div>
        <!-- 返回顶部按钮 -->
        <transition name="fab-scale">
            <div v-if="showBackToTop" class="back-to-top-fab" @click="scrollToTop">
                <div :style="{ transform: `rotate(${scrollProgress * 360}deg)` }" class="fab-progress">
                    <svg class="progress-ring" height="56" width="56">
                        <circle class="progress-ring-background" cx="28" cy="28" fill="none" r="24"
                                stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                        <circle :stroke-dasharray="150.8" :stroke-dashoffset="150.8 * (1 - scrollProgress)"
                                class="progress-ring-progress" cx="28" cy="28" fill="none" r="24" stroke="#409eff"
                                stroke-width="2" transform="rotate(-90 28 28)"/>
                    </svg>
                </div>
                <el-icon :size="20" class="fab-icon">
                    <ArrowUp/>
                </el-icon>
            </div>
        </transition>
    </div>
</template>
<script>
import {useAppStoreSystem} from '@stores/app/useAppStoreSystem.js'
import {useBreadcrumbStore} from '@stores/global/useBreadcrumbStore.js'
import {ElMessage} from 'element-plus'
import {ArrowLeft, ArrowUp, Loading, Refresh} from '@element-plus/icons-vue'
// 移除 MenuUtils 依赖，让组件变得完全通用
import BackendSidebar from './components/BackendSidebar.vue'
import BackendFooter from './components/BackendFooter.vue'
import BackendBreadcrumb from './components/BackendBreadcrumb.vue'

export default {
    name: 'BackendMainLayout',
    components: {
        BackendSidebar,
        BackendFooter,
        BackendBreadcrumb,
        ArrowLeft,
        ArrowUp,
        Loading,
        Refresh
    },

    /**
     * 组件属性定义
     * 支持通过 props 传递配置数据，实现更好的组件复用
     */
    props: {
        /**
         * 菜单配置数据
         * @type {Object}
         * @default null
         */
        menuConfig: {
            type: Object,
            default: null
        },

        /**
         * 面包屑配置
         * @type {Object}
         * @default null
         */
        breadcrumbConfig: {
            type: Object,
            default: null
        },

        /**
         * 底部信息配置
         * @type {Object}
         * @default null
         */
        footerConfig: {
            type: Object,
            default: null
        },

        /**
         * 侧边栏配置
         * @type {Object}
         * @default null
         */
        sidebarConfig: {
            type: Object,
            default: null
        },

        /**
         * 布局主题配置
         * @type {String}
         * @default 'default'
         */
        layoutTheme: {
            type: String,
            default: 'default',
            validator: (value) => ['default', 'admin', 'merchant'].includes(value)
        }
    },
    data() {
        return {
            // 错误状态
            hasError: false,
            errorTitle: '页面加载失败',
            errorMessage: '请稍后重试或联系管理员',
            loadingText: '加载中...',
            isRetrying: false,
            isRefreshing: false,
            // 内容加载状态 - 只在菜单点击时显示
            isContentLoading: false,
            //
            currentTime: '',
            lastUpdated: '',
            // 返回顶部
            showBackToTop: false,
            scrollProgress: 0,
            // 内部菜单配置缓存（当没有通过 props 传入时使用）
            internalMenuConfig: null,
            // 刷新相关状态
            refreshKey: 0, // 用于强制刷新组件的key
            refreshTimestamp: Date.now() // 刷新时间戳
        }
    },
    computed: {
        // Store实例
        breadcrumbStore() {
            return useBreadcrumbStore()
        },
        AppStoreSystem() {
            return useAppStoreSystem()
        },

        /**
         * 是否显示面包屑区域
         * 可以通过 props 或 breadcrumbStore 控制
         */
        showBreadcrumbArea() {
            // 如果通过 props 明确设置了不显示，则不显示
            if (this.breadcrumbConfig?.show === false) {
                return false
            }

            // 否则根据 breadcrumbStore 的状态决定
            return this.breadcrumbStore.breadcrumbs?.length > 0
        },

        // 当前激活的菜单项
        currentActiveMenu() {
            const currentPath = this.$route.path

            if (!currentPath) return ''

            try {
                const validMenuPaths = this.getAllValidMenuPaths()

                // 首先尝试精确匹配
                if (validMenuPaths.includes(currentPath)) {
                    return currentPath
                }

                // 处理嵌套路由，从最长路径到最短路径匹配
                const pathSegments = currentPath.split('/').filter(Boolean)

                for (let i = pathSegments.length; i >= 2; i--) {
                    const testPath = '/' + pathSegments.slice(0, i).join('/')

                    if (validMenuPaths.includes(testPath)) {
                        return testPath
                    }
                }

                // 查找最相似的菜单项
                for (const menuPath of validMenuPaths) {
                    if (currentPath.startsWith(menuPath + '/')) {
                        return menuPath
                    }
                }

                return ''

            } catch (error) {
                console.error('计算当前激活菜单失败:', error)
                return currentPath
            }
        }
    },
    watch: {
        // 监听路由变化
        $route: {
            handler(to, from) {
                // 检查路由错误
                this.checkRouteError(to)

                // 更新面包屑 - 使用优化后的方法并传入菜单配置
                this.breadcrumbStore.setCurrentRoute(to)
                this.breadcrumbStore.generateBreadcrumbsFromRoute(to, this.getMenuConfig())

                // 设置页面标题 - 使用优化后的拼接模式
                if (to.meta?.title) {
                    this.breadcrumbStore.setPageTitle(to.meta.title)
                }

                // 如果不是404错误，重置错误状态
                if (to.name !== 'NotFound' && to.name !== '404') {
                    this.hasError = false
                }

                // 检查是否为刷新操作导致的路由变化
                const isRefreshNavigation = to.query._refresh && to.query._refresh !== from?.query?._refresh

                // 如果是菜单点击导致的路由切换，关闭加载状态
                if (this.isContentLoading && from && !isRefreshNavigation) {
                    // 延迟关闭加载状态，确保新页面已渲染
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.isContentLoading = false
                        }, 500)
                    })
                }

                // 如果是刷新操作，更新时间戳但不关闭加载状态
                if (isRefreshNavigation) {
                    this.updateLastUpdated()
                }

                // 路由变化时重置部分刷新状态（但保留refreshKey用于组件重渲染）
                if (to.path !== from?.path) {
                    // 只有在路径真正改变时才重置refreshKey
                    this.refreshTimestamp = Date.now()
                }
            },
            immediate: true
        }
    },
    mounted() {
        this.init()

        // 暴露刷新方法给全局使用
        this.$nextTick(() => {
            // 将刷新方法挂载到全局，供其他组件调用
            if (window.BackendLayout) {
                window.BackendLayout.refreshContent = this.forceRefreshContent
                window.BackendLayout.refreshPage = this.refreshPage
            } else {
                window.BackendLayout = {
                    refreshContent: this.forceRefreshContent,
                    refreshPage: this.refreshPage
                }
            }
        })
    },
    unmounted() {
        this.cleanup()

        // 清理全局方法
        if (window.BackendLayout) {
            delete window.BackendLayout.refreshContent
            delete window.BackendLayout.refreshPage
        }
    },
    methods: {
        /**
         * 初始化组件
         */
        async init() {
            try {
                // 初始化菜单配置
                this.initMenuConfig()
                //
                this.updateCurrentTime()
                this.updateLastUpdated()
                this.startTimeUpdate()
                // 添加滚动事件监听
                window.addEventListener('scroll', this.handleScroll, {passive: true})
            } catch (error) {
                console.error('布局初始化错误:', error)
            }
        },

        /**
         * 初始化内部菜单配置
         * 仅在没有通过 props 传入菜单配置时使用
         * 现在返回空配置，因为菜单数据应该通过 props 传入
         */
        initMenuConfig() {
            // 不再依赖 MenuUtils，菜单数据应该通过 props 传入
            this.internalMenuConfig = {
                mainMenuItems: [],
                systemMenuItems: []
            }
        },

        /**
         * 获取菜单配置
         * 优先使用 props 传入的配置，否则使用内部初始化的配置
         */
        getMenuConfig() {
            // 优先使用 props 传入的菜单配置
            if (this.menuConfig && typeof this.menuConfig === 'object') {
                return this.menuConfig
            }

            // 降级到内部菜单配置
            if (!this.internalMenuConfig) {
                this.initMenuConfig()
            }
            return this.internalMenuConfig
        },

        /**
         * 清理资源
         */
        cleanup() {
            // 移除事件监听
            window.removeEventListener('scroll', this.handleScroll)
        },

        /**
         * 检查路由错误（通用方法，由包装组件提供具体逻辑）
         * @param {Object} to - 目标路由
         */
        checkRouteError(to) {
            // 通用的路由错误检查，具体的业务逻辑由包装组件处理
            // 这里只处理通用的错误状态
            if (to.name === 'NotFound' || to.name === '404') {
                this.hasError = true
                this.errorTitle = '页面不存在'
                this.errorMessage = `页面 "${to.path}" 不存在，请检查URL是否正确。`

                // 更新面包屑显示404信息
                this.breadcrumbStore.setPageTitle('页面不存在')
            }
        },

        /**
         * 处理菜单选择 - 只在点击侧边栏菜单时显示加载状态
         * @param {Object} payload - 菜单选择数据
         */
        handleMenuSelect(payload) {
            try {
                // 显示内容加载状态
                this.isContentLoading = true

                // 设置定时器自动关闭加载状态（防止路由跳转失败时一直显示）
                setTimeout(() => {
                    this.isContentLoading = false
                }, 3000)

                console.log('菜单选择:', payload)
            } catch (error) {
                console.error('处理菜单选择错误:', error)
                this.isContentLoading = false
            }
        },

        /**
         * 获取所有有效菜单路径
         * 现在从 props 传入的菜单配置中提取路径
         */
        getAllValidMenuPaths() {
            try {
                const menuConfig = this.getMenuConfig()
                if (!menuConfig) {
                    return []
                }

                const paths = new Set()

                // 递归提取路径的函数
                const extractPaths = (menuItems) => {
                    if (!Array.isArray(menuItems)) return

                    menuItems.forEach(item => {
                        // 如果有路由路径，添加到集合中
                        if (item.route && typeof item.route === 'string') {
                            paths.add(item.route)
                        }

                        // 递归处理子菜单
                        if (item.children && Array.isArray(item.children)) {
                            extractPaths(item.children)
                        }
                    })
                }

                // 提取主菜单和系统菜单的所有路径
                extractPaths(menuConfig.mainMenuItems || [])
                extractPaths(menuConfig.systemMenuItems || [])

                return Array.from(paths)
            } catch (error) {
                console.warn('获取菜单路径失败:', error)
                // 返回空数组，让包装组件处理路径逻辑
                return []
            }
        },

        /**
         * 返回上一页（通用方法）
         */
        async goBack() {
            try {
                if (window.history.length > 1) {
                    this.$router.back()
                } else {
                    // 默认回到根路径，具体的首页路径由包装组件决定
                    await this.$router.push('/')
                }
            } catch (error) {
                console.error('导航错误:', error)
                ElMessage.error('导航失败，请重试')
            }
        },

        /**
         * 回到首页（通用方法，具体路径由包装组件提供）
         */
        async goHome() {
            try {
                // 使用侧边栏配置中的首页路径，或默认根路径
                const homeRoute = this.sidebarConfig?.logo?.homeRoute || '/'
                await this.$router.push(homeRoute)
            } catch (error) {
                console.error('导航错误:', error)
                ElMessage.error('导航失败，请重试')
            }
        },
        // 更新当前时间
        updateCurrentTime() {
            const now = new Date()
            this.currentTime = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        },

        // 开始时间更新
        startTimeUpdate() {
            this.timeTimer = setInterval(() => {
                this.updateCurrentTime()
            }, 1000)
        },

        // 更新最后更新时间
        updateLastUpdated() {
            const now = new Date()
            this.lastUpdated = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            })
        },
        /**
         * 刷新页面 - 优化版本，支持局部内容刷新
         */
        async refreshPage() {
            this.isRefreshing = true
            this.isContentLoading = true

            try {
                // 更新刷新相关状态
                this.refreshKey++
                this.refreshTimestamp = Date.now()
                this.updateLastUpdated()

                // 显示加载状态
                await this.$nextTick()

                // 模拟加载延迟，确保用户能看到刷新效果
                await new Promise(resolve => setTimeout(resolve, 800))

                // 触发路由刷新（可选，用于重置路由状态）
                if (this.$route.query._refresh !== this.refreshTimestamp.toString()) {
                    await this.$router.replace({
                        path: this.$route.path,
                        query: {...this.$route.query, _refresh: this.refreshTimestamp.toString()}
                    })
                }

                ElMessage.success('页面内容已刷新')

            } catch (error) {
                console.error('刷新错误:', error)
                ElMessage.error('刷新失败，请重试')

                // 发生错误时重置状态
                this.hasError = true
                this.errorTitle = '刷新失败'
                this.errorMessage = '页面刷新时发生错误，请稍后重试'

            } finally {
                // 延迟关闭加载状态，确保新内容已渲染
                setTimeout(() => {
                    this.isRefreshing = false
                    this.isContentLoading = false
                }, 300)
            }
        },

        /**
         * 重试加载页面 - 优化版本
         */
        async retryLoad() {
            this.isRetrying = true
            this.hasError = false
            this.errorTitle = '页面加载失败'
            this.errorMessage = '请稍后重试或联系管理员'

            try {
                this.loadingText = '重新加载中...'

                // 重置刷新状态
                this.refreshKey++
                this.refreshTimestamp = Date.now()

                // 模拟重试延迟
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 执行刷新操作
                await this.refreshPage()

            } catch (error) {
                console.error('重试错误:', error)
                ElMessage.error('重试失败，请检查网络连接')
                this.hasError = true
                this.errorTitle = '重试失败'
                this.errorMessage = '网络连接异常或服务器暂时不可用，请稍后再试'
            } finally {
                this.isRetrying = false
                this.loadingText = '加载中...'
            }
        },

        /**
         * 处理滚动事件
         */
        handleScroll() {
            //
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop
            const windowHeight = window.innerHeight
            const documentHeight = Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            ) - windowHeight

            // 计算滚动进度
            this.scrollProgress = Math.min(scrollTop / documentHeight, 1)

            // 控制返回顶部按钮显示
            this.showBackToTop = scrollTop > 300
        },

        /**
         * 滚动到顶部
         */
        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        },

        /**
         * 强制刷新内容区域
         * 提供给外部组件调用的方法
         */
        forceRefreshContent() {
            this.refreshKey++
            this.refreshTimestamp = Date.now()
            this.updateLastUpdated()

            // 触发内容重新渲染
            this.$nextTick(() => {
                ElMessage.success('内容已刷新')
            })
        },

        /**
         * 重置刷新状态
         * 用于清理刷新相关的状态
         */
        resetRefreshState() {
            this.refreshKey = 0
            this.refreshTimestamp = Date.now()
            this.isRefreshing = false
            this.isContentLoading = false
        },

        /**
         * 检查是否需要刷新
         * 基于时间间隔判断是否需要自动刷新
         */
        shouldAutoRefresh() {
            const now = Date.now()
            const timeDiff = now - this.refreshTimestamp
            // 如果超过5分钟没有刷新，建议刷新
            return timeDiff > 5 * 60 * 1000
        }
    }
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

/* === 主布局容器 === */
.backend-main-layout {
    @include flex-between;
    background: $body-bg;
    min-height: 100vh;
    font-family: $font-family-primary;
    position: relative;
    @include no-animation; // 企业级无动画

    /* 主题切换 */
    &.theme-dark {
        background: $body-bg-dark;
        color: $text-inverse;
    }

    /* 减少动画 - 企业级设计 */
    &.reduced-motion * {
        @include no-animation;
    }

    /* 自定义加载样式 - 使用企业级样式 */
    :deep(.el-loading-mask) {
        backdrop-filter: blur(8px);
        @include no-animation;
        background-color: $bg-overlay;
    }

    :deep(.el-loading-spinner) {
        .path {
            stroke: $primary-color;
            @include no-animation; // 企业级无动画
        }

        .el-loading-text {
            color: $text-secondary;
            font-size: $font-size-sm;
            margin-top: $spacing-base;
            font-weight: $font-weight-medium;
        }
    }
}

@keyframes loading-dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }

    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }

    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

/* === 主内容区域 === */
.backend-main {
    flex: 1;
    margin-top: 10px;
    margin-left: 170px; // 使用固定值替代变量
    @include flex-column;
    min-height: 100vh;
    @include no-animation; // 企业级无动画
}

/* === 内容区域 === */
.backend-content {
    flex: 1;
    @include flex-column;
    position: relative;
}

/* === 面包屑包装器 === */
.breadcrumb-wrapper {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: $spacing-xs $spacing-base;
    margin-bottom: $spacing-xs;
    border-bottom: 1px solid $border-color-light;
    @include flex-between;
    position: sticky;
    top: 0;
    z-index: $z-index-sticky;
    @include no-animation; // 企业级无动画

    .header-right {
        @include flex-center;
        gap: $spacing-lg;

        .header-info {
            text-align: right;

            .header-time-content {
                margin-bottom: $spacing-xxs;
                @include flex-center;
                gap: $spacing-xs;

                .current-time {
                    @include enterprise-text('base', 'semibold', $text-primary);

                    i {
                        color: $primary-color;
                    }
                }
            }

            .last-updated {
                @include enterprise-text('sm', 'normal', $text-secondary);
            }
        }

        .header-actions {
            @include flex-center;
            gap: $spacing-xs;

            .refresh-btn {
                @include enterprise-button;
                border: none;
                background: $primary-alpha-10;
                color: $primary-color;
                @include no-animation; // 企业级无动画

                &:hover {
                    background: $primary-alpha-15;
                    @include no-animation; // 企业级无动画
                }
            }
        }
    }
}

/* === 内容容器 === */
.content-container {
    flex: 1;
    padding: $spacing-base;
    margin-bottom: $spacing-base;
    position: relative;
    max-width: 100%;
    overflow-x: hidden;

    // 移动端优化 - 使用企业级响应式
    @include responsive(md) {
        padding: 0 $spacing-lg;
        margin-bottom: $spacing-lg;
    }

    @include responsive(sm) {
        padding: 0 $spacing-base;
        margin-bottom: $spacing-base;
    }
}

/* === Element Plus 骨架屏优化 === */
.content-loading {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(10px);
    padding: 32px;
    border-radius: 4px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    min-height: 500px;
    position: relative;

    .skeleton-container {
        .skeleton-header {
            margin-bottom: 32px;

            .skeleton-header-content {
                display: flex;
                align-items: center;
                gap: 16px;

                .skeleton-text-area {
                    flex: 1;
                }
            }
        }

        .skeleton-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;

            .skeleton-card {
                background: rgba(255, 255, 255, 0.8);
                border-radius: 4px;
                padding: 24px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
                }

                .skeleton-actions {
                    display: flex;
                    gap: 12px;
                    margin-top: 16px;
                }
            }
        }

        .skeleton-table {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

            .table-header,
            .table-row {
                display: flex;
                align-items: center;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .table-header {
                padding-bottom: 12px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                margin-bottom: 16px;
            }
        }
    }

    .loading-indicator {
        position: absolute;
        bottom: 32px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;

        .loading-spinner {
            margin-bottom: 16px;

            .el-icon {
                color: #409eff;
                animation: spin 2s linear infinite;
            }
        }

        .loading-text {
            color: #606266;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .loading-tips {
            color: #909399;
            font-size: 14px;
            opacity: 0.8;
            transition: opacity 0.5s ease;
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* === 增强的错误状态 === */
.error-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(252, 252, 252, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-radius: 4px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    min-height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 48px 32px;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 59, 48, 0.03) 0%, transparent 70%);
        animation: error-pulse 3s ease-in-out infinite;
    }

    .error-illustration {
        margin-bottom: 32px;

        .error-icon {
            width: 120px;
            height: 120px;
            filter: drop-shadow(0 4px 8px rgba(231, 76, 60, 0.2));
        }
    }

    .error-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        justify-content: center;
        margin-top: 24px;

        .retry-btn {
            background: linear-gradient(135deg, #409eff 0%, #79bbff 100%);
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
            }
        }

        .back-btn {
            background: rgba(144, 147, 153, 0.1);
            border: 1px solid rgba(144, 147, 153, 0.2);
            color: #606266;
            padding: 12px 20px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(144, 147, 153, 0.15);
                transform: translateY(-1px);
            }
        }
    }
}

@keyframes error-pulse {

    0%,
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }

    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.7;
    }
}

/* === 内容区域 === */
.content-area {
    border-radius: 4px;
    min-height: 500px;
    overflow: hidden;
    position: relative;
}

.page-component {
    width: 100%;
    min-height: 500px;
}

/* === 页面切换遮罩 === */
.transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    .overlay-content {
        text-align: center;
        color: #606266;

        .transition-spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto 16px;
            border: 3px solid rgba(64, 158, 255, 0.2);
            border-top-color: #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .transition-text {
            font-size: 14px;
            font-weight: 500;
            opacity: 0.8;
        }
    }
}

/* === 返回顶部按钮 === */
.back-to-top-fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #409eff 0%, #79bbff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;

    &:hover {
        transform: translateY(-4px) scale(1.1);
        box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4);

        .fab-icon {
            transform: translateY(-2px);
        }
    }

    &:active {
        transform: translateY(-2px) scale(1.05);
    }

    .fab-progress {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transition: transform 0.1s ease-out;
    }

    .progress-ring {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);

        .progress-ring-background {
            opacity: 0.3;
        }

        .progress-ring-progress {
            transition: stroke-dashoffset 0.1s ease-out;
            stroke-linecap: round;
        }
    }

    .fab-icon {
        position: relative;
        z-index: 2;
        color: white;
        transition: all 0.3s ease;
    }

    // 移动端优化
    @media (max-width: 768px) {
        bottom: 20px;
        right: 20px;
        width: 48px;
        height: 48px;

        .fab-icon {
            font-size: 18px;
        }
    }

    @media (max-width: 480px) {
        bottom: 16px;
        right: 16px;
        width: 44px;
        height: 44px;

        .fab-icon {
            font-size: 16px;
        }
    }
}

/* === 返回顶部按钮动画 === */
.fab-scale-enter-active,
.fab-scale-leave-active {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fab-scale-enter-from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
}

.fab-scale-leave-to {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
}

/* === 页面切换动画 === */
.fade-slide-enter-active,
.fade-slide-leave-active {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fade-slide-enter-from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
}

.fade-slide-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(1.02);
}

.slide-left-enter-active,
.slide-left-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-left-enter-from {
    opacity: 0;
    transform: translateX(30px) scale(0.98);
}

.slide-left-leave-to {
    opacity: 0;
    transform: translateX(-30px) scale(0.98);
}

.slide-right-enter-active,
.slide-right-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-right-enter-from {
    opacity: 0;
    transform: translateX(-30px) scale(0.98);
}

.slide-right-leave-to {
    opacity: 0;
    transform: translateX(30px) scale(0.98);
}

.fade-transform-enter-active,
.fade-transform-leave-active {
    transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fade-transform-enter-from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}

/* === 简化动画 (无障碍) === */
.fade-simple-enter-active,
.fade-simple-leave-active {
    transition: opacity 0.2s ease;
}

.fade-simple-enter-from,
.fade-simple-leave-to {
    opacity: 0;
}

/* === 错误状态动画 === */
.error-slide-up-enter-active {
    animation: error-slide-up 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.error-slide-up-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.error-slide-up-leave-to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
}

@keyframes error-slide-up {
    0% {
        opacity: 0;
        transform: translateY(40px) scale(0.9);
    }

    50% {
        opacity: 0.8;
        transform: translateY(-8px) scale(1.02);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* === 初始加载动画 === */
.initial-load-enter-active {
    transition: all 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.initial-load-leave-active {
    transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.initial-load-enter-from {
    opacity: 0;
}

.initial-load-leave-to {
    opacity: 0;
    transform: scale(1.1);
}

/* === 遮罩动画 === */
.overlay-fade-enter-active,
.overlay-fade-leave-active {
    transition: all 0.3s ease;
}

.overlay-fade-enter-from,
.overlay-fade-leave-to {
    opacity: 0;
}
</style>
