<template>
    <div class="page-list-layout">
        <!-- 页面头部工具栏 -->
        <el-card v-if="showHeader" class="layout-header-card" shadow="never">
            <div class="header-content">
                <!-- 左侧操作区域 -->
                <div class="header-left-section">
                    <!-- 操作按钮插槽（新增按钮） -->
                    <slot name="header-left"></slot>
                    <!-- 默认操作按钮 -->
                    <div v-if="showDefaultActions" class="default-actions"></div>
                    <!-- 自动刷新控制 -->
                    <div v-if="enableAutoRefresh" class="auto-refresh-control">
                        <el-switch
                            :value="autoRefreshEnabled"
                            active-text="自动刷新"
                            inactive-text=""
                            size="small"
                            @change="handleAutoRefreshToggle"
                        />
                    </div>
                </div>
                <!-- 右侧工具区域 -->
                <div class="header-right-section">
                    <slot name="header-right">
                        <!-- 顶部分页 -->
                        <div v-if="showTopPagination && total > 0" class="top-pagination">
                            <el-pagination
                                v-if="total > 0"
                                :current-page="currentPage"
                                :page-size="pageSize"
                                :total="total"
                                background
                                v-bind="paginationConfig"
                                @current-change="handlePageChange"
                                @size-change="handlePageSizeChange"
                            />
                        </div>
                    </slot>
                </div>
            </div>
            <!-- 高级搜索面板 -->
            <div v-if="showAdvancedSearch" class="advanced-search-panel">
                <div class="panel-header">
                    <span class="panel-title">高级搜索</span>
                    <el-button
                        class="panel-toggle"
                        text
                        @click="handleToggleAdvancedSearch"
                    >
                        <i class="fas fa-times"></i>
                    </el-button>
                </div>
                <div class="panel-content">
                    <slot name="advanced-search">
                        <div class="default-advanced-search">
                            <p class="placeholder-text">请在此插槽中添加高级搜索表单</p>
                        </div>
                    </slot>
                </div>
            </div>
        </el-card>
        <!-- 主内容区域 -->
        <el-card
            v-loading="v_loading"
            :element-loading-text="loadingText"
            class="layout-content-card"
            element-loading-background="rgba(255, 255, 255, 0.8)"
            shadow="never"
        >
            <!-- Tab和搜索工具栏 -->
            <div class="content-toolbar">
                <!-- Tab选项卡区域 -->
                <div v-if="showTabs && tabOptions.length > 0" class="tab-section">
                    <el-tabs
                        :model-value="activeTabValue"
                        class="page-tabs"
                        type="card"
                        @tab-click="handleTabClick"
                    >
                        <el-tab-pane
                            v-for="tab in tabOptions"
                            :key="tab.name"
                            :disabled="tab.disabled"
                            :name="tab.name"
                        >
                            <template #label>
								<span class="tab-label">
									<i v-if="tab.icon" :class="tab.icon" class="tab-icon"></i>
									<span class="tab-text">{{ tab.label }}</span>
									<el-badge
                                        v-if="shouldShowBadge(tab)"
                                        :max="999"
                                        :type="getBadgeType(tab)"
                                        :value="formatBadge(tab.badge)"
                                        class="tab-badge"
                                    />
								</span>
                            </template>
                        </el-tab-pane>
                    </el-tabs>
                </div>

                <!-- 搜索和筛选区域 -->
                <div class="search-section">
                    <div class="search-controls">
                        <!-- 主搜索框 -->
                        <el-input
                            :model-value="searchQuery"
                            :placeholder="searchPlaceholder"
                            class="main-search-input"
                            clearable
                            @clear="handleSearchClear"
                            @input="handleSearchInput"
                            @keyup.enter="handleSearchEnter"
                        >
                            <template #prefix>
                                <i class="fas fa-search search-icon"></i>
                            </template>
                        </el-input>
                        <!-- 高级搜索按钮 -->
                        <el-button
                            v-if="enableAdvancedSearch"
                            :type="showAdvancedSearch ? 'primary' : 'default'"
                            class="advanced-search-btn"
                            @click="handleToggleAdvancedSearch"
                        >
                            <i class="fas fa-filter"></i>
                            高级搜索
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <slot name="content">
                    <slot></slot>
                </slot>
            </div>

            <!-- 空状态 -->
            <div v-if="showEmptyState" class="empty-state">
                <slot name="empty">
                    <div class="default-empty">
                        <i class="fas fa-inbox empty-icon"></i>
                        <p class="empty-text">{{ emptyText }}</p>
                        <el-button v-if="enableEmptyRefresh" type="primary" @click="handleRefresh">
                            重新加载
                        </el-button>
                    </div>
                </slot>
            </div>
        </el-card>

        <!-- 底部信息和分页 -->
        <el-card v-if="showFooter" class="layout-footer-card" shadow="never">
            <div class="footer-content">
                <!-- 左侧信息区域 -->
                <div class="footer-left-section">
                    <slot name="footer-left">
                        <div v-if="total > 0" class="data-summary">
							<span class="summary-text">
								共 <strong>{{ formatNumber(total) }}</strong> 条数据，
								当前第 <strong>{{ currentPage }}</strong> 页
							</span>
                            <span v-if="pageSize" class="page-size-info">
								，每页显示 <strong>{{ pageSize }}</strong> 条
							</span>
                        </div>
                    </slot>
                </div>

                <!-- 右侧分页区域 -->
                <div v-if="showBottomPagination && total > 0" class="footer-right-section">
                    <el-pagination
                        v-if="total > 0"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        :total="total"
                        background
                        v-bind="paginationConfig"
                        @current-change="handlePageChange"
                        @size-change="handlePageSizeChange"
                    />
                </div>
            </div>
        </el-card>
    </div>
</template>
<script>
/**
 * 企业级列表布局组件
 * 专为企业SaaS系统设计，无动画效果，严肃商务风格
 * 支持Tab切换、搜索、分页等功能
 *
 * @version 2.0.0
 * <AUTHOR> Agent
 */
export default {
    name: 'BackendPageListLayout',
    props: {
        // ==================== 基础配置 ====================
        // 加载状态
        v_loading: {
            type: Boolean,
            default: false
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        refreshing: {
            type: Boolean,
            default: false
        },
        exporting: {
            type: Boolean,
            default: false
        },
        batchOperating: {
            type: Boolean,
            default: false
        },

        // 加载文本
        loadingText: {
            type: String,
            default: '数据加载中...'
        },

        // ==================== 显示控制 ====================
        // 显示头部
        showHeader: {
            type: Boolean,
            default: false
        },
        // 显示底部
        showFooter: {
            type: Boolean,
            default: false
        },
        // 显示默认操作按钮
        showDefaultActions: {
            type: Boolean,
            default: false
        },
        // 显示批量操作工具栏
        showBatchToolbar: {
            type: Boolean,
            default: false
        },

        // ==================== 功能开关 ====================
        // 启用自动刷新
        enableAutoRefresh: {
            type: Boolean,
            default: false
        },
        // 启用高级搜索
        enableAdvancedSearch: {
            type: Boolean,
            default: false
        },
        // 启用空状态刷新按钮
        enableEmptyRefresh: {
            type: Boolean,
            default: false
        },

        // ==================== Tab相关 ====================
        // 显示Tab
        showTabs: {
            type: Boolean,
            default: false
        },
        // Tab选项
        tabOptions: {
            type: Array,
            default: () => []
        },
        // 当前活动Tab
        activeTabValue: {
            type: String,
            default: ''
        },

        // ==================== 搜索相关 ====================
        // 搜索关键词
        searchQuery: {
            type: String,
            default: ''
        },
        // 搜索占位符
        searchPlaceholder: {
            type: String,
            default: '请输入搜索关键词'
        },
        // 显示高级搜索面板
        showAdvancedSearch: {
            type: Boolean,
            default: false
        },

        // ==================== 分页相关 ====================
        // 当前页码
        currentPage: {
            type: Number,
            default: 1
        },
        // 每页条数
        pageSize: {
            type: Number,
            default: 20
        },
        // 总条数
        total: {
            type: Number,
            default: 0
        },
        // 最后一页
        lastPage: {
            type: Number,
            default: 1
        },
        // 显示顶部分页
        showTopPagination: {
            type: Boolean,
            default: false
        },
        // 显示底部分页
        showBottomPagination: {
            type: Boolean,
            default: true
        },
        // 分页布局
        paginationLayout: {
            type: String,
            default: 'total, sizes, prev, pager, next, jumper'
        },
        // 分页大小选项
        pageSizes: {
            type: Array,
            default: () => [10, 20, 30, 50]
        },

        // ==================== 空状态相关 ====================
        // 显示空状态
        showEmptyState: {
            type: Boolean,
            default: false
        },
        // 空状态文本
        emptyText: {
            type: String,
            default: '暂无数据'
        },

        // ==================== 自动刷新相关 ====================
        // 自动刷新启用状态
        autoRefreshEnabled: {
            type: Boolean,
            default: false
        }
    },

    computed: {
        // 分页配置
        paginationConfig() {
            return {
                layout: this.paginationLayout,
                pageSizes: this.pageSizes,
                hideOnSinglePage: false
            }
        }
    },

    methods: {
        // ==================== 事件处理方法 ====================
        /**
         * 处理Tab点击
         */
        handleTabClick(tab) {
            this.$emit('tab-change', {
                name: tab.props.name,
                label: tab.props.label,
                index: tab.index
            })
        },

        /**
         * 处理页码变化
         */
        handlePageChange(page) {
            this.$emit('page-change', page)
        },

        /**
         * 处理每页条数变化
         */
        handlePageSizeChange(size) {
            this.$emit('page-size-change', size)
        },

        /**
         * 处理搜索输入
         */
        handleSearchInput(value) {
            this.$emit('search-input', value)
        },

        /**
         * 处理搜索清空
         */
        handleSearchClear() {
            this.$emit('search-clear')
        },

        /**
         * 处理搜索回车
         */
        handleSearchEnter() {
            this.$emit('search-enter')
        },

        /**
         * 处理刷新
         */
        handleRefresh() {
            this.$emit('refresh')
        },

        /**
         * 切换高级搜索面板
         */
        handleToggleAdvancedSearch() {
            this.$emit('toggle-advanced-search')
        },

        /**
         * 处理自动刷新切换
         */
        handleAutoRefreshToggle(enabled) {
            this.$emit('auto-refresh-toggle', enabled)
        },

        // ==================== 工具方法 ====================
        /**
         * 格式化徽章显示
         */
        formatBadge(badge) {
            if (typeof badge === 'number') {
                return badge > 999 ? '999+' : badge.toString()
            }
            return badge
        },

        /**
         * 是否显示徽章
         */
        shouldShowBadge(tab) {
            return tab.badge !== undefined && tab.badge !== null && tab.badge !== 0
        },

        /**
         * 获取徽章类型
         */
        getBadgeType(tab) {
            return tab.badgeType || 'info'
        },

        /**
         * 格式化数字
         */
        formatNumber(num) {
            if (num === null || num === undefined) return '0'
            return num.toLocaleString()
        }
    }
}
</script>

<style lang="scss" scoped>
/**
 * 企业级列表布局样式
 * 严肃商务风格，无动画效果
 */
@use '@css/variables' as *;
@use 'sass:color';


// ==================== 主容器 ====================
.page-list-layout {
    // 移除所有动画和过渡效果
    * {
        transition: none !important;
        animation: none !important;
    }

    // 卡片间距
    .layout-header-card,
    .layout-footer-card {
        margin-bottom: 8px;
        border-radius: 6px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

// ==================== 头部工具栏 ====================
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;

    .header-left-section {
        display: flex;
        align-items: center;
        flex: 1;

        .default-actions {
            display: flex;
            gap: 12px;

            .action-btn {
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;

                i {
                    margin-right: 6px;
                }
            }
        }
    }

    .header-right-section {
        display: flex;
        align-items: center;
        gap: 16px;

        .auto-refresh-control {
            display: flex;
            align-items: center;

            :deep(.el-switch) {
                .el-switch__core {
                    transition: none;
                }
            }
        }

        .top-pagination {
            :deep(.el-pagination) {
                .el-pager li,
                .btn-prev,
                .btn-next {
                    transition: none;
                }
            }
        }
    }
}

// ==================== 高级搜索面板 ====================
.advanced-search-panel {
    margin-top: 16px;

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0 8px;

        .panel-title {
            font-weight: 600;
            color: $text-primary;
            font-size: 14px;
        }

        .panel-toggle {
            padding: 4px;
            color: $text-secondary;

            &:hover {
                color: $text-primary;
            }
        }
    }

    .panel-content {
        padding: 16px;
        background-color: $bg-light;
        border-radius: 4px;

        .default-advanced-search {
            text-align: center;

            .placeholder-text {
                color: $text-placeholder;
                margin: 0;
            }
        }
    }
}

// ==================== 内容工具栏 ====================
.content-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 16px;

    .tab-section {
        flex: 1;

        .page-tabs {
            :deep(.el-tabs__header) {
                margin-bottom: 0;

                .el-tabs__nav-wrap {
                    &::after {
                        height: 1px;
                        background-color: $border-color;
                    }
                }

                .el-tabs__item {
                    transition: none;
                    padding: 0 16px;
                    height: 40px;
                    line-height: 40px;
                    border-radius: 4px 4px 0 0;

                    &:hover {
                        color: $primary-color;
                    }

                    &.is-active {
                        color: $primary-color;
                        background-color: $bg-white;
                        border-color: $border-color;
                        border-bottom-color: $bg-white;
                    }
                }

                .el-tabs__active-bar {
                    transition: none;
                    background-color: $primary-color;
                }
            }

            .tab-label {
                display: flex;
                align-items: center;
                gap: 6px;

                .tab-icon {
                    font-size: 14px;
                }

                .tab-text {
                    font-weight: 500;
                }

                .tab-badge {
                    :deep(.el-badge__content) {
                        font-size: 11px;
                        padding: 0 5px;
                        height: 16px;
                        line-height: 16px;
                        border-radius: 8px;
                    }
                }
            }
        }
    }

    .search-section {
        flex-shrink: 0;

        .search-controls {
            display: flex;
            gap: 12px;
            align-items: center;

            .main-search-input {
                width: 280px;

                :deep(.el-input__wrapper) {
                    border-radius: 4px;

                    .el-input__inner {
                        transition: none;
                    }
                }

                .search-icon {
                    color: $text-secondary;
                    font-size: 14px;
                }
            }

            .advanced-search-btn {
                padding: 8px 16px;
                border-radius: 4px;

                i {
                    margin-right: 6px;
                }
            }
        }
    }
}

// ==================== 批量操作工具栏 ====================
.batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #e8f4fd;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
        .selected-count {
            color: $info-color;
            font-weight: 600;
            font-size: 14px;
        }
    }

    .batch-actions {
        display: flex;
        gap: 8px;

        .el-button {
            padding: 6px 12px;
            font-size: 13px;

            i {
                margin-right: 4px;
            }
        }
    }
}

// ==================== 主要内容区域 ====================
.main-content {
    min-height: 200px;
}

// ==================== 空状态 ====================
.empty-state {
    text-align: center;
    padding: 60px 20px;

    .default-empty {
        .empty-icon {
            font-size: 48px;
            color: $text-placeholder;
            margin-bottom: 16px;
        }

        .empty-text {
            color: $text-secondary;
            font-size: 16px;
            margin: 0 0 20px;
        }
    }
}

// ==================== 底部信息和分页 ====================
.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 32px;

    .footer-left-section {
        display: flex;
        align-items: center;

        .data-summary {
            color: $text-regular;
            font-size: 14px;

            .summary-text {
                strong {
                    color: $text-primary;
                    font-weight: 600;
                }
            }

            .page-size-info {
                color: $text-secondary;
            }
        }

        .no-data-info {
            .no-data-text {
                color: $text-secondary;
                font-size: 14px;
            }
        }
    }

    .footer-right-section {
        display: flex;
        align-items: center;

        :deep(.el-pagination) {
            .el-pager li,
            .btn-prev,
            .btn-next {
                transition: none;
                border-radius: 4px;

                &:hover {
                    color: $primary-color;
                }

                &.is-active {
                    background-color: $primary-color;
                    color: white;
                }
            }

            .el-pagination__sizes {
                .el-select {
                    .el-select__wrapper {
                        transition: none;
                    }
                }
            }

            .el-pagination__jump {
                .el-input {
                    .el-input__wrapper {
                        transition: none;
                    }
                }
            }
        }
    }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
    .content-toolbar {
        .search-section {
            .search-controls {
                .main-search-input {
                    width: 240px;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .content-toolbar {
        flex-direction: column;
        gap: 16px;

        .tab-section {
            width: 100%;
        }

        .search-section {
            width: 100%;

            .search-controls {
                width: 100%;

                .main-search-input {
                    flex: 1;
                    width: auto;
                }
            }
        }
    }

    .header-content,
    .footer-content {
        flex-direction: column;
        gap: 12px;

        .header-right-section,
        .footer-right-section {
            width: 100%;
            justify-content: center;
        }
    }

    .batch-toolbar {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}
</style>
