<template>
    <header class="backend-header">
        <div class="header-left">
            <!-- 侧边栏切换按钮 -->
            <el-button
                class="sidebar-toggle-btn"
                size="large"
                @click="toggleSidebar"
            >
                <i class="fas fa-bars"></i>
            </el-button>

            <!-- 面包屑导航 -->
            <div v-if="breadcrumbs.length > 0" class="breadcrumb-section">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item
                        v-for="(item, index) in breadcrumbs"
                        :key="index"
                        :to="item.to || undefined"
                    >
                        <i v-if="item.icon" :class="item.icon"></i>
                        {{ item.text }}
                    </el-breadcrumb-item>
                </el-breadcrumb>
            </div>
        </div>

        <div class="header-right">
            <!-- 通知中心 -->
            <div class="notification-center">
                <el-dropdown
                    placement="bottom-end"
                    @command="handleNotificationCommand"
                >
                    <el-button class="notification-btn" size="large">
                        <el-badge
                            :hidden="totalUnreadCount === 0"
                            :value="totalUnreadCount"
                            type="danger"
                        >
                            <i class="fas fa-bell"></i>
                        </el-badge>
                    </el-button>

                    <template #dropdown>
                        <el-dropdown-menu class="notification-dropdown">
                            <!-- 通知标题 -->
                            <!--							<div class="notification-header">-->
                            <!--								<h3>通知中心</h3>-->
                            <!--								<div class="notification-stats">-->
                            <!--									<span class="stat-item critical" v-if="criticalCount > 0">-->
                            <!--										紧急 {{ criticalCount }}-->
                            <!--									</span>-->
                            <!--									<span class="stat-item important" v-if="importantCount > 0">-->
                            <!--										重要 {{ importantCount }}-->
                            <!--									</span>-->
                            <!--									<span class="stat-item normal" v-if="normalCount > 0">-->
                            <!--										一般 {{ normalCount }}-->
                            <!--									</span>-->
                            <!--								</div>-->
                            <!--							</div>-->

                            <!-- 通知列表 -->
                            <div class="notification-list">
                                <!-- 高优先级通知 -->
                                <div v-if="criticalNotifications.length > 0"
                                     class="notification-section critical-section">
                                    <div class="section-title">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        紧急通知
                                    </div>
                                    <div
                                        v-for="notification in criticalNotifications.slice(0, 3)"
                                        :key="notification.id"
                                        class="notification-item critical-item"
                                        @click="handleNotificationClick(notification)"
                                    >
                                        <div class="item-icon">
                                            <i :class="notification.icon"></i>
                                        </div>
                                        <div class="item-content">
                                            <div class="item-title">{{ notification.title }}</div>
                                            <div class="item-message">{{ notification.message }}</div>
                                            <div class="item-time">{{ formatTime(notification.timestamp) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 重要通知 -->
                                <div v-if="importantNotifications.length > 0"
                                     class="notification-section important-section">
                                    <div class="section-title">
                                        <i class="fas fa-exclamation-circle"></i>
                                        重要通知
                                    </div>
                                    <div
                                        v-for="notification in importantNotifications.slice(0, 2)"
                                        :key="notification.id"
                                        class="notification-item important-item"
                                        @click="handleNotificationClick(notification)"
                                    >
                                        <div class="item-icon">
                                            <i :class="notification.icon"></i>
                                        </div>
                                        <div class="item-content">
                                            <div class="item-title">{{ notification.title }}</div>
                                            <div class="item-message">{{ notification.message }}</div>
                                            <div class="item-time">{{ formatTime(notification.timestamp) }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 空状态 -->
                                <div v-if="totalUnreadCount === 0" class="empty-notifications">
                                    <i class="fas fa-bell-slash"></i>
                                    <p>暂无新通知</p>
                                </div>
                            </div>

                            <!-- 通知操作 -->
                            <div class="notification-actions">
                                <el-button
                                    :disabled="totalUnreadCount === 0"
                                    size="small"
                                    @click="markAllAsRead"
                                >
                                    全部已读
                                </el-button>
                                <el-button
                                    size="small"
                                    type="primary"
                                    @click="viewAllNotifications"
                                >
                                    查看全部
                                </el-button>
                            </div>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>

            <!-- 主题切换 -->
            <el-button
                class="theme-toggle-btn"
                size="large"
                @click="toggleTheme"
            >
                <i :class="themeIcon"></i>
            </el-button>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand">
                <div class="user-avatar">
                    <img
                        :alt="userInfo.name || '用户'"
                        :src="userInfo.avatar || '/default-avatar.png'"
                        class="avatar-img"
                    >
                    <span class="user-name">{{ userInfo.name || '未登录' }}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>

                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="profile">
                            <i class="fas fa-user"></i>
                            个人资料
                        </el-dropdown-item>
                        <el-dropdown-item command="settings">
                            <i class="fas fa-cog"></i>
                            系统设置
                        </el-dropdown-item>
                        <el-dropdown-item command="logout" divided>
                            <i class="fas fa-sign-out-alt"></i>
                            退出登录
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </header>
</template>

<script>
import {useAppStoreSystem} from '@stores/app/useAppStoreSystem.js'
import {useUserStore} from '@stores/user/useUserStore'
import {useNotificationStore} from '@stores/notification/useNotificationStore.js'
import {useThemeStore} from '@stores/global/useThemeStore'
import {computed} from 'vue'
import {useRouter} from 'vue-router'

export default {
    name: 'BackendHeader',
    props: {
        /**
         * 头部配置
         * @type {Object}
         * @default null
         */
        headerConfig: {
            type: Object,
            default: () => ({
                routes: {
                    notifications: '/notifications',
                    profile: '/profile',
                    settings: '/settings',
                    login: '/login'
                }
            })
        }
    },
    setup(props) {
        const appStore = useAppStoreSystem()
        const userStore = useUserStore()
        const NotificationStore = useNotificationStore()
        const themeStore = useThemeStore()
        const router = useRouter()

        // 计算属性
        const breadcrumbs = computed(() => appStore.breadcrumbs || [])
        const userInfo = computed(() => userStore.user || {})

        // 通知相关计算属性
        const criticalNotifications = computed(() =>
            NotificationStore.critical.unreadNotifications
        )
        const importantNotifications = computed(() =>
            NotificationStore.important.unreadNotifications
        )
        const normalNotifications = computed(() =>
            NotificationStore.normal.unreadNotifications
        )

        // const criticalCount = computed(() => NotificationStore.critical.unreadCount)
        // const importantCount = computed(() => NotificationStore.important.unreadCount)
        // const normalCount = computed(() => NotificationStore.normal.unreadCount)

        // const totalUnreadCount = computed(() =>
        // 	criticalCount.value + importantCount.value + normalCount.value
        // )

        // 主题相关
        const currentTheme = computed(() => themeStore.currentTheme)
        const themeIcon = computed(() =>
            currentTheme.value === 'dark' ? 'fas fa-sun' : 'fas fa-moon'
        )

        // 方法
        const toggleSidebar = () => {
            appStore.toggleSidebar()
        }

        const toggleTheme = () => {
            themeStore.toggleTheme()
        }

        const handleNotificationClick = (notification) => {
            // 标记为已读
            if (notification.priority === 'critical') {
                NotificationStore.critical.markAsRead(notification.id)
            } else if (notification.priority === 'important') {
                NotificationStore.important.markAsRead(notification.id)
            } else {
                NotificationStore.normal.markAsRead(notification.id)
            }

            // 处理点击事件
            if (notification.onClick) {
                notification.onClick(notification)
            }
        }

        const handleNotificationCommand = (command) => {
            switch (command) {
                case 'mark-all-read':
                    markAllAsRead()
                    break
                case 'view-all':
                    viewAllNotifications()
                    break
            }
        }

        const markAllAsRead = () => {
            NotificationStore.markAllAsRead()
        }

        const viewAllNotifications = () => {
            const notificationsRoute = props.headerConfig?.routes?.notifications || '/notifications'
            router.push(notificationsRoute)
        }

        const formatTime = (timestamp) => {
            if (!timestamp) return ''
            const date = new Date(timestamp)
            const now = new Date()
            const diff = now - date

            if (diff < 60000) { // 1分钟内
                return '刚刚'
            } else if (diff < 3600000) { // 1小时内
                return `${Math.floor(diff / 60000)}分钟前`
            } else if (diff < 86400000) { // 24小时内
                return `${Math.floor(diff / 3600000)}小时前`
            } else {
                return date.toLocaleDateString('zh-CN')
            }
        }

        const handleUserCommand = async (command) => {
            const routes = props.headerConfig?.routes || {}

            switch (command) {
                case 'profile':
                    router.push(routes.profile || '/profile')
                    break
                case 'settings':
                    router.push(routes.settings || '/settings')
                    break
                case 'logout':
                    try {
                        await userStore.logout()
                        router.push(routes.login || '/login')
                    } catch (error) {
                        console.error('退出登录失败:', error)
                    }
                    break
            }
        }

        // return {
        // 	breadcrumbs,
        // 	userInfo,
        // 	criticalNotifications,
        // 	importantNotifications,
        // 	normalNotifications,
        // 	criticalCount,
        // 	importantCount,
        // 	normalCount,
        // 	totalUnreadCount,
        // 	currentTheme,
        // 	themeIcon,
        // 	toggleSidebar,
        // 	toggleTheme,
        // 	handleNotificationClick,
        // 	handleNotificationCommand,
        // 	markAllAsRead,
        // 	viewAllNotifications,
        // 	formatTime,
        // 	handleUserCommand
        // }
    }
}
</script>
<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

.backend-header {
    height: 70px;
    background: white;
    border-bottom: 1px solid #f0f1f7;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
}

.sidebar-toggle-btn {
    border: none;
    background: none;
    color: #6e6b7b;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
        background: #f8f9fa;
        color: $primary-color;
    }

    i {
        font-size: 18px;
    }
}

.breadcrumb-section {
    :deep(.el-breadcrumb) {
        .el-breadcrumb__item {
            font-size: 13px;

            .el-breadcrumb__inner {
                color: #6e6b7b;

                &:hover {
                    color: $primary-color;
                }

                i {
                    margin-right: 4px;
                }
            }
        }
    }
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.notification-center {
    .notification-btn {
        border: none;
        background: none;
        color: #6e6b7b;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
            background: #f8f9fa;
            color: $primary-color;
        }

        i {
            font-size: 18px;
        }
    }
}

.notification-dropdown {
    width: 360px;
    max-height: 500px;

    .notification-header {
        padding: 16px;
        border-bottom: 1px solid #f0f1f7;

        h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .notification-stats {
            display: flex;
            gap: 12px;

            .stat-item {
                font-size: 12px;
                padding: 2px 8px;
                border-radius: 12px;
                font-weight: 500;

                &.critical {
                    background: #fef2f2;
                    color: #dc2626;
                }

                &.important {
                    background: #fef3c7;
                    color: #d97706;
                }

                &.normal {
                    background: #f0fdf4;
                    color: #16a34a;
                }
            }
        }
    }

    .notification-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .notification-section {
        border-bottom: 1px solid #f0f1f7;

        .section-title {
            padding: 12px 16px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #6e6b7b;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 10px;
            }
        }
    }

    .notification-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 16px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
            background: #f8f9fa;
        }

        .item-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;

            i {
                font-size: 13px;
                color: white;
            }
        }

        .item-content {
            flex: 1;
            min-width: 0;

            .item-title {
                font-size: 13px;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 2px;
                line-height: 1.4;
            }

            .item-message {
                font-size: 12px;
                color: #6e6b7b;
                margin-bottom: 4px;
                line-height: 1.4;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .item-time {
                font-size: 11px;
                color: #95a5a6;
            }
        }

        &.critical-item .item-icon {
            background: linear-gradient(135deg, #ff4757, #ff3838);
        }

        &.important-item .item-icon {
            background: linear-gradient(135deg, #ffa502, #ff9500);
        }
    }

    .empty-notifications {
        padding: 32px 16px;
        text-align: center;
        color: #95a5a6;

        i {
            font-size: 24px;
            margin-bottom: 8px;
        }

        p {
            margin: 0;
            font-size: 13px;
        }
    }

    .notification-actions {
        padding: 12px 16px;
        border-top: 1px solid #f0f1f7;
        display: flex;
        justify-content: space-between;

        .el-button {
            font-size: 12px;
        }
    }
}

.theme-toggle-btn {
    border: none;
    background: none;
    color: #6e6b7b;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
        background: #f8f9fa;
        color: $primary-color;
    }

    i {
        font-size: 18px;
    }
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background: #f8f9fa;
    }

    .avatar-img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }

    .user-name {
        font-size: 13px;
        font-weight: 500;
        color: #2c3e50;
    }

    .fas {
        font-size: 12px;
        color: #95a5a6;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .backend-header {
        padding: 0 16px;
    }

    .header-left {
        gap: 12px;
    }

    .breadcrumb-section {
        display: none;
    }

    .user-name {
        display: none;
    }

    .notification-dropdown {
        width: 300px;
    }
}
</style>
