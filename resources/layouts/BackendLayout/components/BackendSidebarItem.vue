<template>
    <div>
        <!-- 有子菜单的情况 - 使用递归组件渲染子菜单 -->
        <el-sub-menu v-if="hasChildren" :disabled="item.disabled" :index="item.id" @click="handleMenuClick">
            <template #title>
                <!-- 菜单图标 - 支持FontAwesome和Element Plus图标 -->
                <i v-if="item.icon" :aria-hidden="true" :class="item.icon" class="menu-icon"></i>
                <!-- 菜单文本 - 支持文本截断显示 -->
                <span :title="item.name" class="menu-text">{{ item.name }}</span>
                <!-- 徽章显示 - 支持多种类型和颜色 -->
                <span v-if="item.badge" :aria-label="`徽章: ${item.badge}`" :class="getBadgeClass(item.badge)"
                      class="menu-badge">{{ item.badge }}</span>
            </template>
            <!-- 递归渲染子菜单项 - 使用v-memo优化性能 -->
            <BackendSidebarItem v-for="child in item.children" :key="`${item.id}-${child.id}`"
                                v-memo="[child.id, child.name, child.badge, child.disabled]" :item="child"/>
        </el-sub-menu>

        <!-- 无子菜单的情况 - 普通菜单项 -->
        <el-menu-item v-else :class="{
			'menu-item-external': item.type === 'EXTERNAL_LINK',
			'menu-item-disabled': item.disabled
		}" :disabled="item.disabled" :index="item.route || item.id" @click="handleMenuClick">
            <!-- 菜单图标 -->
            <i v-if="item.icon" :aria-hidden="true" :class="item.icon" class="menu-icon"></i>
            <!-- 菜单文本 -->
            <span :title="item.name" class="menu-text">{{ item.name }}</span>
            <!-- 外部链接图标 -->
            <i v-if="item.type === 'EXTERNAL_LINK'" :aria-label="'外部链接'"
               class="fas fa-external-link-alt menu-external-icon"></i>
            <!-- 徽章显示 -->
            <span v-if="item.badge" :aria-label="`徽章: ${item.badge}`" :class="getBadgeClass(item.badge)"
                  class="menu-badge">{{ item.badge }}</span>
        </el-menu-item>
    </div>
</template>
<script>
/**
 * 管理员侧边栏菜单项组件
 * 功能：递归渲染菜单项，支持多级嵌套、徽章显示、外部链接等
 */
export default {
    name: 'BackendSidebarItem',
    // 组件属性定义
    props: {
        /**
         * 菜单项数据对象
         * @type {Object}
         * @required true
         * @description 包含菜单的基本信息如id、name、route、icon、badge等
         */
        item: {
            type: Object,
            required: true,
            validator(value) {
                // 验证菜单项数据的完整性
                if (!value || typeof value !== 'object') {
                    console.warn('BackendSidebarItem: item prop must be an object')
                    return false
                }

                if (typeof value.id === 'undefined' || !value.name) {
                    console.warn('BackendSidebarItem: item must have id and name properties')
                    return false
                }

                // 验证子菜单格式
                if (value.children && !Array.isArray(value.children)) {
                    console.warn('BackendSidebarItem: children must be an array')
                    return false
                }

                return true
            }
        }
    },

    // 计算属性 - 用于性能优化和数据处理
    computed: {
        /**
         * 判断是否有子菜单
         * @returns {boolean} 是否存在有效的子菜单
         */
        hasChildren() {
            return this.item.children &&
                Array.isArray(this.item.children) &&
                this.item.children.length > 0
        }
    },

    // 组件方法
    methods: {
        /**
         * 获取徽章样式类名
         * @param {string|number} badge - 徽章内容
         * @returns {string} 对应的CSS类名
         * @description 根据徽章内容自动判断样式类型
         */
        getBadgeClass(badge) {
            // 空值检查
            if (!badge) return ''

            try {
                const badgeStr = badge.toString().toLowerCase().trim()

                // 文本类徽章
                if (badgeStr === 'new' || badgeStr === '新' || badgeStr === '最新') {
                    return 'badge-new'
                }
                if (badgeStr === 'hot' || badgeStr === '热' || badgeStr === '热门') {
                    return 'badge-hot'
                }
                if (badgeStr === '警告' || badgeStr === 'warning' || badgeStr === 'warn') {
                    return 'badge-warning'
                }
                if (badgeStr === '重要' || badgeStr === 'important' || badgeStr === 'urgent') {
                    return 'badge-danger'
                }

                // 数字类徽章 - 根据数值大小分配不同颜色
                if (/^\d+$/.test(badgeStr)) {
                    const num = parseInt(badgeStr, 10)
                    if (num > 100) return 'badge-danger'    // 大于100用红色
                    if (num > 50) return 'badge-orange'     // 大于50用橙色
                    if (num > 10) return 'badge-warning'    // 大于10用黄色
                    return 'badge-primary'                  // 小数字用主色
                }

                // 默认样式
                return 'badge-primary'

            } catch (error) {
                console.warn('BackendSidebarItem: Error processing badge:', error)
                return 'badge-primary'
            }
        },

        /**
         * 处理菜单点击事件
         * @param {Event} event - 点击事件对象
         * @description 处理菜单项点击，支持外部链接和路由跳转
         */
        handleMenuClick(event) {
            try {
                // 如果菜单项被禁用，则阻止默认行为
                if (this.item.disabled) {
                    event.preventDefault()
                    event.stopPropagation()
                    return
                }

                // 处理外部链接
                if (this.item.type === 'EXTERNAL_LINK' && this.item.url) {
                    event.preventDefault()
                    window.open(this.item.url, '_blank', 'noopener,noreferrer')
                    return
                }

                // 触发自定义事件，让父组件可以监听
                this.$emit('menu-click', {
                    item: this.item,
                    event: event
                })

                // 记录菜单点击统计（可用于分析）
                if (typeof window !== 'undefined' && window.gtag) {
                    window.gtag('event', 'menu_click', {
                        menu_id: this.item.id,
                        menu_name: this.item.name
                    })
                }

            } catch (error) {
                console.error('BackendSidebarItem: Error handling menu click:', error)
            }
        }
    },

    // 组件事件定义
    emits: ['menu-click']
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

/**
 * 菜单图标样式
 * 固定宽度确保对齐，支持响应式调整
 */
.menu-icon {
    width: 16px;
    font-size: 14px;
    margin-right: 12px;
    text-align: center;
    flex-shrink: 0;

    transition: all 0.2s ease;

    // 图标悬停效果
    &:hover {
        transform: scale(1.1);
    }
}

/**
 * 菜单文本样式
 * 支持文本截断和省略号显示
 */
.menu-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    min-width: 0; // 确保flex子项能够收缩
}

/**
 * 外部链接图标样式
 */
.menu-external-icon {
    font-size: 10px;
    margin-left: 4px;
    opacity: 0.6;

}

/**
 * 菜单徽章样式
 * 支持多种颜色和动画效果
 */
.menu-badge {
    right: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: 700;
    line-height: 1;
    min-width: 18px;
    height: 18px;
    text-align: center;
    white-space: nowrap;
    letter-spacing: 0.2px;
    z-index: 100;
    transition: all 0.2s ease;

    // NEW 徽章 - 绿色主题
    &.badge-new {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    // HOT 徽章 - 橙红色主题
    &.badge-hot {
        background: linear-gradient(135deg, #ff6b35, #ff8c42);
        color: white;
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }

    // 警告徽章 - 红色主题，带脉冲动画
    &.badge-warning {
        background: linear-gradient(135deg, #dc3545, #e63946);
        color: white;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    // 危险/重要徽章 - 深红色主题
    &.badge-danger {
        background: linear-gradient(135deg, #dc3545, #c92a2a);
        color: white;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
    }

    // 橙色徽章 - 中等重要性
    &.badge-orange {
        background: linear-gradient(135deg, #fd7e14, #ff922b);
        color: white;
        box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
    }

    // 主色徽章 - 默认样式
    &.badge-primary {
        background: linear-gradient(135deg, $primary-color, #9c88ff);
        color: white;
        box-shadow: 0 2px 8px rgba(115, 103, 240, 0.3);
    }
}

/**
 * 脉冲动画效果
 * 用于重要提醒的徽章
 */
@keyframes pulse {

    0%,
    100% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }

    50% {
        transform: translateY(-50%) scale(1.05);
        opacity: 0.9;
    }
}

/**
 * Element Plus 菜单项样式重写
 * 确保徽章正确定位和显示
 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
    position: relative;

    // 菜单项悬停状态
    &:hover {
        .menu-icon {
            color: $primary-color;
        }
    }

    // 菜单项激活状态
    &.is-active {
        .menu-icon {
            color: $primary-color;
        }
    }

    // 禁用状态样式
    &.menu-item-disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
            background: transparent;

        }
    }

    // 外部链接样式
    &.menu-item-external {
        .menu-text {
            margin-right: 16px; // 为外部链接图标留出空间
        }
    }

    // 徽章定位
    .menu-badge {
        top: 50%;
        position: absolute;
        right: 12px;
        transform: translateY(-50%);
    }
}

/**
 * 深色主题支持（可选）
 */
@media (prefers-color-scheme: dark) {
    .menu-badge {
        &.badge-new {
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.5);
        }

        &.badge-hot {
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.5);
        }

        &.badge-primary {
            box-shadow: 0 2px 8px rgba(115, 103, 240, 0.5);
        }
    }
}

/**
 * 无障碍访问优化
 */
@media (prefers-reduced-motion: reduce) {

    .menu-icon,
    .menu-badge {
        transition: none;
        animation: none;
    }

    .menu-badge.badge-warning {
        animation: none;
    }
}
</style>
