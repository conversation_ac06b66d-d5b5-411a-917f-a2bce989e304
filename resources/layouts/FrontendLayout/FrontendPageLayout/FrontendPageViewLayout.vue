<template>
    <div class="client-page-view-layout">
        <main class="main-content">
            <slot/>
        </main>
    </div>
</template>
<script>
export default {
    name: 'FrontendPageViewLayout',
}
</script>

<style lang="scss" scoped>
@use 'sass:color';

.client-page-view-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}
</style>
