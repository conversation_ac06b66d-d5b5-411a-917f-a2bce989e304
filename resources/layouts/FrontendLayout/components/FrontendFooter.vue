<template>
    <footer class="app-footer">
        <!-- 背景装饰元素 - 简化设计 -->
        <div class="footer-decoration">
            <div class="decoration-line line-1"></div>
            <div class="decoration-line line-2"></div>
        </div>

        <div class="footer-container">
            <div class="footer-main">
                <!-- 品牌信息区域 -->
                <div class="footer-brand">
                    <div class="brand-logo">
                        <div class="logo-icon">
                            <span class="logo-icon-main"></span>
                        </div>
                        <h3 class="brand-title">商智云</h3>
                    </div>
                    <p class="brand-desc">
                        专业数字化解决方案提供商，<br>
                        全面助力企业数字化转型升级
                    </p>

                    <!-- 联系信息 -->
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <span><a class="email-address" data-email-part1="Service" data-email-part2="xiaoxinkeji.com"
                                     href="mailto:<EMAIL>"></a></span>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <span>长沙市岳麓区桔子洲街道后湖艺术园B区8栋</span>
                        </div>
                    </div>

                    <!-- 社交媒体 -->
                    <div class="footer-social">
                        <h4 class="social-title">关注我们</h4>
                        <div class="social-links">
                            <a
                                aria-label="微信公众号"
                                class="social-link"
                                href="#"
                                rel="noopener noreferrer"
                                @click.prevent="handleSocialClick('wechat')"
                            >
                                <i class="fab fa-weixin"></i>
                                <span class="tooltip">微信公众号</span>
                            </a>
                            <a
                                aria-label="新浪微博"
                                class="social-link"
                                href="#"
                                rel="noopener noreferrer"
                                @click.prevent="handleSocialClick('weibo')"
                            >
                                <i class="fab fa-weibo"></i>
                                <span class="tooltip">新浪微博</span>
                            </a>
                            <a
                                aria-label="官方QQ群"
                                class="social-link"
                                href="#"
                                rel="noopener noreferrer"
                                @click.prevent="handleSocialClick('qq')"
                            >
                                <i class="fab fa-qq"></i>
                                <span class="tooltip">官方QQ群</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 链接区域 -->
                <div class="footer-links-container">
                    <div class="footer-section">
                        <h4 class="section-title">
                            <i class="fas fa-th-large section-icon"></i>
                            产品功能
                        </h4>
                        <ul class="footer-links">
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#pdm'); trackFooterClick('Product', 'Click', 'PDM')">PDM商品管理</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#oms'); trackFooterClick('Product', 'Click', 'OMS')">OMS订单管理</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#ai'); trackFooterClick('Product', 'Click', 'AI')">AI智能分析</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#features'); trackFooterClick('Product', 'Click', 'Features')">功能特性</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#benefits'); trackFooterClick('Product', 'Click', 'Benefits')">核心优势</a>
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="section-title">
                            <i class="fas fa-cogs section-icon"></i>
                            解决方案
                        </h4>
                        <ul class="footer-links">
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#use-cases'); trackFooterClick('Solution', 'Click', 'UseCases')">使用场景</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Solution', 'Click', 'Platforms')">平台支持</a>
                            </li>
                            <li>
                                <router-link to="/merchant" @click="trackFooterClick('Solution', 'Click', 'Merchant')">
                                    商家入驻
                                </router-link>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#stats'); trackFooterClick('Solution', 'Click', 'Stats')">成功案例</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#hero'); trackFooterClick('Solution', 'Click', 'Trial')">立即体验</a>
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="section-title">
                            <i class="fas fa-store section-icon"></i>
                            支持平台
                        </h4>
                        <ul class="footer-links">
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Platform', 'Click', 'Taobao')">淘宝天猫</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Platform', 'Click', 'JD')">京东商城</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Platform', 'Click', 'PDD')">拼多多</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Platform', 'Click', 'Douyin')">抖音快手</a>
                            </li>
                            <li><a href="#"
                                   @click.prevent="handleAnchorClick('#platforms'); trackFooterClick('Platform', 'Click', '1688')">1688阿里</a>
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="section-title">
                            <i class="fas fa-headset section-icon"></i>
                            帮助支持
                        </h4>
                        <ul class="footer-links">
                            <li>
                                <router-link to="/help" @click="trackFooterClick('Help', 'Click', 'Home')">帮助中心
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/help/list" @click="trackFooterClick('Help', 'Click', 'Docs')">
                                    文档教程
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/help" @click="trackFooterClick('Help', 'Click', 'Video')">平台公告
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/help" @click="trackFooterClick('Help', 'Click', 'Training')">
                                    在线培训
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/help" @click="trackFooterClick('Help', 'Click', 'Support')">技术支持
                                </router-link>
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="section-title">
                            <i class="fas fa-building section-icon"></i>
                            关于我们
                        </h4>
                        <ul class="footer-links">
                            <li>
                                <router-link to="/about" @click="trackFooterClick('About', 'Click', 'Company')">
                                    公司介绍
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/about" @click="trackFooterClick('About', 'Click', 'News')">新闻动态
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/about" @click="trackFooterClick('About', 'Click', 'Jobs')">招贤纳士
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/about" @click="trackFooterClick('About', 'Click', 'Partners')">
                                    合作伙伴
                                </router-link>
                            </li>
                            <li>
                                <router-link to="/about" @click="trackFooterClick('About', 'Click', 'Contact')">
                                    联系我们
                                </router-link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 页脚底部区域 -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <!-- 版权和法律链接 -->
                    <div class="footer-left">
                        <div class="copyright">
                            <p>Copyright © 2015-2025 New Technology Services Limited</p>
                        </div>

                        <!-- 法律链接 - 单行显示 -->
                        <div class="legal-links">
                            <router-link to="/privacy-policy">隐私政策</router-link>
                            <span class="separator">|</span>
                            <router-link to="/user-service-agreement">用户协议</router-link>
                            <span class="separator">|</span>
                            <router-link to="/legal-statement">法律声明</router-link>
                            <span class="separator">|</span>
                            <router-link to="/responsibility-notice">责任公告</router-link>
                            <span class="separator">|</span>
                            <router-link to="/merchant-service-agreement">商家协议</router-link>
                            <span class="separator">|</span>
                            <router-link to="/merchant-credit-rules">信用规则</router-link>
                            <span class="separator">|</span>
                            <router-link to="/certification-rules">亮证办法</router-link>
                            <span class="separator">|</span>
                            <router-link to="/product-publishing-rules">商品发布</router-link>
                            <span class="separator">|</span>
                            <router-link to="/self-discipline-code">自律公约</router-link>
                            <span class="separator">|</span>
                            <router-link to="/ip-protection">知识产权</router-link>
                            <span class="separator">|</span>
                            <router-link to="/dispute-resolution-rules">争议处理</router-link>
                            <span class="separator">|</span>
                            <router-link to="/sitemap">网站地图</router-link>
                        </div>
                        <!-- 备案信息 - 单行显示 -->
                        <div class="icp-info">
                            <span class="icp-item">
                                <i class="fas fa-certificate"></i>
                                湘ICP备2021011225号
                            </span>
                            <span class="icp-item">
                                <i class="fas fa-shield-alt"></i>
                                湘公网安备43010402001166号
                            </span>
                            <span class="icp-item">
                                <i class="fas fa-check-circle"></i>
                                电子营业执照
                            </span>
                            <span class="icp-item">
                                <i class="fas fa-award"></i>
                                增值电信业务经营许可证: 湘B2-202103301号
                            </span>
                        </div>
                    </div>
                    <!-- 备案信息和支持标识 -->
                    <div class="footer-right">
                        <!-- 支持标识 -->
                        <div class="support-badge">
                            <span class="badge-icon"><i class="fas fa-headset"></i></span>
                            <span class="badge-text">7×24小时在线支持</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</template>

<script>
export default {
    name: 'FrontendFooter',
    data() {
        return {
            showBackToTop: false,
            currentYear: new Date().getFullYear(),
            scrollTicking: false, // 滚动节流标志
            isVisible: false, // 组件可见性标志
            performanceMetrics: {
                loadStartTime: performance.now(),
                fontsLoaded: false,
                componentsReady: false
            }
        }
    },
    mounted() {
        // 企业级组件初始化优化
        this.initializeComponent();
    },

    beforeUnmount() {
        // 企业级清理优化
        this.cleanupComponent();
    },
    methods: {
        // ========================================
        // 企业级组件生命周期管理
        // ========================================
        initializeComponent() {
            try {
                // 性能监控开始
                this.performanceMetrics.loadStartTime = performance.now();

                // 添加滚动监听（使用被动监听优化性能）
                window.addEventListener('scroll', this.handleScroll, {passive: true});

                // 添加可见性检测
                this.setupIntersectionObserver();

                // 延迟加载非关键资源
                this.$nextTick(() => {
                    this.loadFontAwesome();
                    this.initContactInfo();
                    this.addStructuredData();

                    // 标记组件就绪
                    this.performanceMetrics.componentsReady = true;
                    this.logPerformanceMetrics();
                });

                // 添加错误边界处理
                window.addEventListener('error', this.handleGlobalError);

            } catch (error) {
                console.error('组件初始化失败:', error);
                this.handleComponentError(error);
            }
        },

        cleanupComponent() {
            try {
                // 移除事件监听器
                window.removeEventListener('scroll', this.handleScroll);
                window.removeEventListener('error', this.handleGlobalError);

                // 清理 Intersection Observer
                if (this.intersectionObserver) {
                    this.intersectionObserver.disconnect();
                    this.intersectionObserver = null;
                }

                // 清理性能监控
                this.performanceMetrics = null;

                console.log('FrontendFooter 组件已清理');
            } catch (error) {
                console.warn('组件清理过程中出现错误:', error);
            }
        },

        // 企业级可见性检测
        setupIntersectionObserver() {
            if ('IntersectionObserver' in window) {
                this.intersectionObserver = new IntersectionObserver(
                    (entries) => {
                        entries.forEach(entry => {
                            this.isVisible = entry.isIntersecting;
                            if (this.isVisible) {
                                // 组件进入视口时的优化处理
                                this.onComponentVisible();
                            }
                        });
                    },
                    {
                        threshold: 0.1, // 10% 可见时触发
                        rootMargin: '50px' // 提前 50px 触发
                    }
                );

                // 观察页脚元素
                this.$nextTick(() => {
                    const footerElement = this.$el;
                    if (footerElement) {
                        this.intersectionObserver.observe(footerElement);
                    }
                });
            }
        },

        // 组件可见时的处理
        onComponentVisible() {
            if (!this.performanceMetrics.componentsReady) {
                return;
            }

            // 可以在这里添加懒加载逻辑
            console.log('页脚组件进入视口');
        },

        // 企业级错误处理
        handleGlobalError(event) {
            if (event.filename && event.filename.includes('FrontendFooter')) {
                console.error('页脚组件相关错误:', event.error);
                this.handleComponentError(event.error);
            }
        },

        handleComponentError(error) {
            try {
                // 企业级错误上报（可以集成到错误监控系统）
                const errorInfo = {
                    component: 'FrontendFooter',
                    error: error.message,
                    stack: error.stack,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };

                console.error('组件错误详情:', errorInfo);

                // 这里可以发送到错误监控服务
                // this.sendErrorToMonitoring(errorInfo);

            } catch (reportError) {
                console.warn('错误上报失败:', reportError);
            }
        },

        // 性能指标记录
        logPerformanceMetrics() {
            try {
                const loadTime = performance.now() - this.performanceMetrics.loadStartTime;
                console.log(`页脚组件加载完成，耗时: ${loadTime.toFixed(2)}ms`);

                // 记录到性能监控
                if (typeof performance !== 'undefined' && performance.mark) {
                    performance.mark('frontend-footer-ready');
                    performance.measure('frontend-footer-load', 'navigationStart', 'frontend-footer-ready');
                }
            } catch (error) {
                console.warn('性能指标记录失败:', error);
            }
        },
        // ========================================
        // 滚动相关方法 - 企业级性能优化
        // ========================================
        handleScroll() {
            // 企业级节流优化：使用 requestAnimationFrame 提升性能
            if (!this.scrollTicking) {
                requestAnimationFrame(() => {
                    const scrollY = window.scrollY || window.pageYOffset;
                    this.showBackToTop = scrollY > 500;
                    this.scrollTicking = false;
                });
                this.scrollTicking = true;
            }
        },

        scrollToTop() {
            try {
                // 企业级滚动优化：检测浏览器支持
                const supportsSmooth = 'scrollBehavior' in document.documentElement.style;

                if (supportsSmooth) {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                } else {
                    // 企业级降级处理：自定义平滑滚动
                    this.smoothScrollToTop();
                }

                this.trackFooterClick('Navigation', 'Click', 'BackToTop');
            } catch (error) {
                // 最终降级处理：直接跳转到顶部
                window.scrollTo(0, 0);
                console.warn('滚动处理失败，使用直接跳转:', error);
            }
        },

        // 企业级自定义平滑滚动实现
        smoothScrollToTop() {
            const startPosition = window.pageYOffset;
            const startTime = performance.now();
            const duration = 500; // 500ms 滚动时间

            const easeInOutQuad = (t) => {
                return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
            };

            const animateScroll = (currentTime) => {
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                const ease = easeInOutQuad(progress);

                window.scrollTo(0, startPosition * (1 - ease));

                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            };

            requestAnimationFrame(animateScroll);
        },

        // ========================================
        // 资源加载方法
        // ========================================
        loadFontAwesome() {
            // 动态加载FontAwesome - 企业级优化增强版
            if (!document.getElementById('fontawesome-css')) {
                const link = document.createElement('link');
                link.id = 'fontawesome-css';
                link.rel = 'stylesheet';
                link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css';
                link.integrity = 'sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==';
                link.crossOrigin = 'anonymous';
                link.referrerPolicy = 'no-referrer';

                // 企业级加载优化
                link.onload = () => {
                    console.log('FontAwesome 加载成功');
                    // 标记字体已加载，用于性能监控
                    if (typeof performance !== 'undefined' && performance.mark) {
                        performance.mark('fontawesome-loaded');
                    }
                };

                link.onerror = () => {
                    console.warn('FontAwesome 加载失败，尝试备用方案');
                    // 企业级降级处理：移除依赖图标的功能或使用文字替代
                    this.handleFontAwesomeFallback();
                };

                // 设置加载超时
                setTimeout(() => {
                    if (!link.sheet) {
                        console.warn('FontAwesome 加载超时');
                        this.handleFontAwesomeFallback();
                    }
                }, 10000); // 10秒超时

                document.head.appendChild(link);
            }
        },

        // 企业级字体加载失败处理
        handleFontAwesomeFallback() {
            try {
                // 为图标元素添加文字备用方案
                const iconElements = document.querySelectorAll('.fas, .fab, .far');
                iconElements.forEach(icon => {
                    if (!icon.textContent) {
                        // 根据图标类名添加文字备用
                        const fallbackText = this.getIconFallbackText(icon.className);
                        if (fallbackText) {
                            icon.textContent = fallbackText;
                            icon.style.fontFamily = 'inherit';
                        }
                    }
                });
            } catch (error) {
                console.warn('图标降级处理失败:', error);
            }
        },

        // 获取图标的文字备用方案
        getIconFallbackText(className) {
            const iconMap = {
                'fa-envelope': '✉',
                'fa-map-marker-alt': '📍',
                'fa-weixin': '微信',
                'fa-weibo': '微博',
                'fa-qq': 'QQ',
                'fa-th-large': '产品',
                'fa-cogs': '方案',
                'fa-store': '平台',
                'fa-headset': '支持',
                'fa-building': '关于',
                'fa-chevron-up': '↑',
                'fa-certificate': '证',
                'fa-shield-alt': '盾',
                'fa-check-circle': '✓',
                'fa-award': '奖'
            };

            for (const [iconClass, fallback] of Object.entries(iconMap)) {
                if (className.includes(iconClass)) {
                    return fallback;
                }
            }
            return '';
        },

        // ========================================
        // 联系信息初始化方法
        // ========================================
        initContactInfo() {
            // 动态设置联系方式，防止爬虫抓取 - 企业级安全优化
            this.$nextTick(() => {
                const phoneElement = document.querySelector('.phone-number');
                const emailElement = document.querySelector('.email-address');

                if (phoneElement) {
                    const part1 = phoneElement.dataset.phonePart1;
                    const part2 = phoneElement.dataset.phonePart2;
                    const part3 = phoneElement.dataset.phonePart3;
                    const part4 = phoneElement.dataset.phonePart4;
                    if (part1 && part2 && part3 && part4) {
                        phoneElement.textContent = `${part1}-${part2}-${part3}-${part4}`;
                        phoneElement.href = `tel:${part1}${part2}${part3}${part4}`;
                    }
                }

                if (emailElement) {
                    const part1 = emailElement.dataset.emailPart1;
                    const part2 = emailElement.dataset.emailPart2;
                    if (part1 && part2) {
                        const email = `${part1.toLowerCase()}@${part2}`;
                        emailElement.textContent = email;
                        emailElement.href = `mailto:${email}`;
                    }
                }
            });
        },
        // ========================================
        // 导航和链接处理方法
        // ========================================
        handleAnchorClick(anchor) {
            try {
                if (this.$route.name === 'Home') {
                    // 如果在首页，直接滚动到对应锚点
                    this.scrollToAnchor(anchor);
                } else {
                    // 如果不在首页，先跳转到首页再滚动
                    this.$router.push('/').then(() => {
                        this.$nextTick(() => {
                            setTimeout(() => {
                                this.scrollToAnchor(anchor);
                            }, 500);
                        });
                    }).catch(error => {
                        console.warn('路由跳转失败:', error);
                    });
                }
            } catch (error) {
                console.warn('锚点导航失败:', error);
            }
        },

        scrollToAnchor(anchor) {
            try {
                const element = document.getElementById(anchor.replace('#', ''));
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else {
                    console.warn(`未找到锚点元素: ${anchor}`);
                }
            } catch (error) {
                console.warn('锚点滚动失败:', error);
            }
        },

        // ========================================
        // 外部链接处理方法
        // ========================================
        handleExternalLink(url) {
            try {
                if (url && typeof url === 'string') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                } else {
                    console.warn('无效的外部链接:', url);
                }
            } catch (error) {
                console.warn('外部链接打开失败:', error);
            }
        },

        // ========================================
        // 统计追踪方法
        // ========================================
        trackFooterClick(category, action, label) {
            try {
                // 企业级统计追踪 - 可以在这里添加Google Analytics或其他统计代码
                console.log('Footer click tracked:', {
                    category,
                    action,
                    label,
                    timestamp: new Date().toISOString()
                });

                // 示例：Google Analytics 4 事件追踪
                if (typeof gtag !== 'undefined') {
                    gtag('event', action, {
                        event_category: category,
                        event_label: label,
                        custom_parameter_1: 'frontend_footer'
                    });
                }
            } catch (error) {
                console.warn('统计追踪失败:', error);
            }
        },

        // ========================================
        // 社交媒体处理方法
        // ========================================
        handleSocialClick(social) {
            try {
                this.trackFooterClick('Social', 'Click', social);

                switch (social) {
                    case 'wechat':
                        this.showWeChatQR();
                        break;
                    case 'weibo':
                        this.handleExternalLink('https://weibo.com/');
                        break;
                    case 'qq':
                        this.showQQGroup();
                        break;
                    default:
                        console.warn(`未处理的社交媒体类型: ${social}`);
                }
            } catch (error) {
                console.warn('社交媒体点击处理失败:', error);
            }
        },

        showWeChatQR() {
            // 企业级微信二维码展示 - 优化用户体验
            try {
                // 检查是否在移动设备上
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

                if (isMobile) {
                    // 移动端：尝试打开微信
                    const wechatUrl = 'weixin://';
                    const fallbackMessage = '微信公众号：商智云\n请在微信中搜索关注获取最新产品动态';

                    // 尝试打开微信应用
                    const link = document.createElement('a');
                    link.href = wechatUrl;
                    link.click();

                    // 延迟显示备用信息
                    setTimeout(() => {
                        if (confirm(fallbackMessage + '\n\n是否复制公众号名称到剪贴板？')) {
                            this.copyToClipboard('商智云');
                        }
                    }, 1000);
                } else {
                    // 桌面端：显示详细信息
                    const message = '微信公众号：商智云\n\n请使用微信扫描二维码或搜索关注\n获取最新产品动态和技术支持';
                    if (confirm(message + '\n\n是否复制公众号名称到剪贴板？')) {
                        this.copyToClipboard('商智云');
                    }
                }
            } catch (error) {
                console.warn('微信二维码展示失败:', error);
                // 降级处理
                alert('微信公众号：商智云\n请搜索关注获取最新产品动态');
            }
        },

        showQQGroup() {
            // 企业级QQ群信息展示 - 优化用户体验
            try {
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                const qqGroupNumber = '545952755';

                if (isMobile) {
                    // 移动端：尝试打开QQ应用
                    const qqUrl = `mqqapi://card/show_pslcard?src_type=internal&version=1&uin=${qqGroupNumber}&card_type=group&source=qrcode`;

                    const link = document.createElement('a');
                    link.href = qqUrl;
                    link.click();

                    // 延迟显示备用信息
                    setTimeout(() => {
                        const message = `官方QQ群：${qqGroupNumber}\n\n加群获取技术支持和产品咨询`;
                        if (confirm(message + '\n\n是否复制群号到剪贴板？')) {
                            this.copyToClipboard(qqGroupNumber);
                        }
                    }, 1000);
                } else {
                    // 桌面端：显示详细信息
                    const message = `官方QQ群：${qqGroupNumber}\n\n请添加QQ群获取：\n• 技术支持和产品咨询\n• 最新功能更新通知\n• 用户交流和经验分享`;
                    if (confirm(message + '\n\n是否复制群号到剪贴板？')) {
                        this.copyToClipboard(qqGroupNumber);
                    }
                }
            } catch (error) {
                console.warn('QQ群信息展示失败:', error);
                // 降级处理
                alert('官方QQ群：545952755\n加群获取技术支持和产品咨询');
            }
        },

        // 企业级剪贴板复制功能
        async copyToClipboard(text) {
            try {
                // 现代浏览器使用 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    this.showCopySuccess(text);
                } else {
                    // 降级处理：使用传统方法
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);

                    if (successful) {
                        this.showCopySuccess(text);
                    } else {
                        throw new Error('复制命令执行失败');
                    }
                }
            } catch (error) {
                console.warn('复制到剪贴板失败:', error);
                // 最终降级：显示文本供用户手动复制
                prompt('请手动复制以下内容:', text);
            }
        },

        // 复制成功提示
        showCopySuccess(text) {
            // 这里可以使用 Element Plus 的 Message 组件
            console.log(`已复制到剪贴板: ${text}`);

            // 简单的成功提示（可以替换为更好的UI组件）
            const notification = document.createElement('div');
            notification.textContent = `已复制: ${text}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #67C23A;
                color: white;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 9999;
                font-size: 14px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        },
        // ========================================
        // SEO 优化方法 - 企业级增强版
        // ========================================
        addStructuredData() {
            try {
                // 检查是否已存在结构化数据，避免重复添加
                if (document.querySelector('script[type="application/ld+json"][data-source="frontend-footer"]')) {
                    return;
                }

                // 企业级结构化数据 - 完整的 SEO 优化
                const organizationData = this.createOrganizationSchema();
                const websiteData = this.createWebsiteSchema();
                const breadcrumbData = this.createBreadcrumbSchema();

                // 添加多个结构化数据
                this.addSchemaToHead(organizationData, 'organization');
                this.addSchemaToHead(websiteData, 'website');
                this.addSchemaToHead(breadcrumbData, 'breadcrumb');

                // 添加 Open Graph 和 Twitter Card 元数据
                this.addSocialMetaTags();

                console.log('SEO 结构化数据添加完成');
            } catch (error) {
                console.warn('结构化数据添加失败:', error);
            }
        },

        // 创建组织结构化数据
        createOrganizationSchema() {
            return {
                '@context': 'https://schema.org',
                '@type': 'Organization',
                'name': '商智云',
                'alternateName': 'WorkHub Core',
                'url': window.location.origin,
                'logo': {
                    '@type': 'ImageObject',
                    'url': `${window.location.origin}/logo.png`,
                    'width': 200,
                    'height': 60
                },
                'description': '专业数字化解决方案提供商，全面助力企业数字化转型升级',
                'foundingDate': '2015',
                'address': {
                    '@type': 'PostalAddress',
                    'streetAddress': '桔子洲街道后湖艺术园B区8栋',
                    'addressLocality': '长沙市',
                    'addressRegion': '湖南省',
                    'postalCode': '410000',
                    'addressCountry': 'CN'
                },
                'contactPoint': [
                    {
                        '@type': 'ContactPoint',
                        'contactType': 'customer service',
                        'email': '<EMAIL>',
                        'availableLanguage': ['zh-CN', 'en-US'],
                        'areaServed': 'CN',
                        'hoursAvailable': {
                            '@type': 'OpeningHoursSpecification',
                            'dayOfWeek': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
                            'opens': '00:00',
                            'closes': '23:59'
                        }
                    }
                ],
                'sameAs': [
                    'https://weibo.com/',
                    'https://www.weixin.qq.com/'
                ],
                'serviceArea': {
                    '@type': 'Country',
                    'name': 'China'
                },
                'knowsAbout': [
                    'E-commerce Solutions',
                    'Digital Transformation',
                    'Product Management',
                    'Order Management',
                    'AI Analytics'
                ],
                'slogan': '全面助力企业数字化转型升级'
            };
        },

        // 创建网站结构化数据
        createWebsiteSchema() {
            return {
                '@context': 'https://schema.org',
                '@type': 'WebSite',
                'name': '商智云 - 专业数字化解决方案',
                'url': window.location.origin,
                'description': '专业数字化解决方案提供商，提供PDM商品管理、OMS订单管理、AI智能分析等企业级服务',
                'inLanguage': 'zh-CN',
                'potentialAction': {
                    '@type': 'SearchAction',
                    'target': `${window.location.origin}/search?q={search_term_string}`,
                    'query-input': 'required name=search_term_string'
                },
                'publisher': {
                    '@type': 'Organization',
                    'name': '商智云',
                    'url': window.location.origin
                }
            };
        },

        // 创建面包屑导航结构化数据
        createBreadcrumbSchema() {
            const currentPath = window.location.pathname;
            const breadcrumbItems = [
                {
                    '@type': 'ListItem',
                    'position': 1,
                    'name': '首页',
                    'item': window.location.origin
                }
            ];

            // 根据当前路径添加面包屑项
            if (currentPath !== '/' && currentPath !== '') {
                const pathSegments = currentPath.split('/').filter(segment => segment);
                pathSegments.forEach((segment, index) => {
                    const segmentPath = '/' + pathSegments.slice(0, index + 1).join('/');
                    breadcrumbItems.push({
                        '@type': 'ListItem',
                        'position': index + 2,
                        'name': this.getPageTitle(segment),
                        'item': window.location.origin + segmentPath
                    });
                });
            }

            return {
                '@context': 'https://schema.org',
                '@type': 'BreadcrumbList',
                'itemListElement': breadcrumbItems
            };
        },

        // 获取页面标题
        getPageTitle(segment) {
            const titleMap = {
                'about': '关于我们',
                'help': '帮助中心',
                'merchant': '商家入驻',
                'contact': '联系我们',
                'privacy-policy': '隐私政策',
                'user-service-agreement': '用户协议'
            };
            return titleMap[segment] || segment;
        },

        // 添加结构化数据到页面头部
        addSchemaToHead(schemaData, type) {
            try {
                const script = document.createElement('script');
                script.type = 'application/ld+json';
                script.setAttribute('data-source', 'frontend-footer');
                script.setAttribute('data-schema-type', type);
                script.textContent = JSON.stringify(schemaData, null, 2);
                document.head.appendChild(script);
            } catch (error) {
                console.warn(`${type} 结构化数据添加失败:`, error);
            }
        },

        // 添加社交媒体元标签
        addSocialMetaTags() {
            try {
                const metaTags = [
                    // Open Graph
                    {property: 'og:type', content: 'website'},
                    {property: 'og:site_name', content: '商智云'},
                    {property: 'og:title', content: '商智云 - 专业数字化解决方案提供商'},
                    {
                        property: 'og:description',
                        content: '全面助力企业数字化转型升级，提供PDM商品管理、OMS订单管理、AI智能分析等企业级服务'
                    },
                    {property: 'og:url', content: window.location.href},
                    {property: 'og:image', content: `${window.location.origin}/og-image.png`},
                    {property: 'og:image:width', content: '1200'},
                    {property: 'og:image:height', content: '630'},
                    {property: 'og:locale', content: 'zh_CN'},

                    // Twitter Card
                    {name: 'twitter:card', content: 'summary_large_image'},
                    {name: 'twitter:title', content: '商智云 - 专业数字化解决方案提供商'},
                    {name: 'twitter:description', content: '全面助力企业数字化转型升级'},
                    {name: 'twitter:image', content: `${window.location.origin}/twitter-image.png`},

                    // 其他 SEO 元标签
                    {name: 'robots', content: 'index, follow'},
                    {name: 'googlebot', content: 'index, follow'},
                    {name: 'bingbot', content: 'index, follow'}
                ];

                metaTags.forEach(tag => {
                    // 检查是否已存在相同的元标签
                    const existingTag = document.querySelector(
                        tag.property ? `meta[property="${tag.property}"]` : `meta[name="${tag.name}"]`
                    );

                    if (!existingTag) {
                        const meta = document.createElement('meta');
                        if (tag.property) {
                            meta.setAttribute('property', tag.property);
                        } else {
                            meta.setAttribute('name', tag.name);
                        }
                        meta.setAttribute('content', tag.content);
                        meta.setAttribute('data-source', 'frontend-footer');
                        document.head.appendChild(meta);
                    }
                });
            } catch (error) {
                console.warn('社交媒体元标签添加失败:', error);
            }
        }
    }
}
</script>

<style lang="scss" scoped>
// ========================================
// FrontendFooter.vue 企业级页脚样式系统
// 技术栈: Laravel Blade + Vue 3 + Element Plus + Tailwind CSS + Vite + SCSS
// 设计风格: Modern, Clean, Professional, Enterprise-grade
// 中文注释用于中国合规
// ========================================

@use '@css/variables' as *;

// ========================================
// 1. 页脚主容器样式 - 企业级严肃设计
// ========================================

.app-footer {
    position: relative;
    background: linear-gradient(180deg, #111827 0%, rgb(9.2589285714, 13.0714285714, 21.2410714286) 100%);
    color: #e2e8f0; // 更柔和的浅色文字
    overflow: hidden;
    margin-top: auto;
    flex-shrink: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1); // 顶部边框增强层次感
}

// ========================================
// 2. 背景装饰元素 - 企业级严肃设计
// ========================================

.footer-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
    opacity: 0.05; // 适度透明度，保持严肃感

    .decoration-line {
        position: absolute;
        background: linear-gradient(45deg, #3b82f6, #1e40af); // 专业蓝色渐变
        border-radius: 2px; // 改为方形，更严肃

        &.line-1 {
            width: 200px;
            height: 2px;
            top: 15%;
            left: 5%;
        }

        &.line-2 {
            width: 150px;
            height: 2px;
            bottom: 25%;
            right: 8%;
        }
    }
}

// ========================================
// 3. 波浪背景元素 - 企业级简化
// ========================================

.footer-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    z-index: 2;
    opacity: 0.6; // 降低透明度，符合企业级设计

    svg {
        width: 100%;
        height: 100%;
        display: block;
    }
}

// ========================================
// 4. 页脚容器布局 - 企业级 1760px 最大宽度
// ========================================

.footer-container {
    position: relative;
    z-index: 3;
    max-width: $container-max-width; // 1760px
    margin: 0 auto;
    padding: $footer-padding $spacing-lg; // 使用全局间距变量

    @media (max-width: $breakpoint-lg) { // 1200px
        padding: $spacing-xl $spacing-md; // 24px 16px
    }

    @media (max-width: $breakpoint-sm) { // 768px
        padding: $spacing-lg $spacing-sm; // 20px 8px
    }
}

// ========================================
// 5. 页脚主要内容区域
// ========================================

.footer-main {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: $spacing-2xl; // 32px
    margin-bottom: $spacing-2xl; // 32px

    @media (max-width: $breakpoint-lg) { // 1200px
        grid-template-columns: 1fr;
        gap: $spacing-xl; // 24px
        margin-bottom: $spacing-xl; // 24px
    }
}

// ========================================
// 6. 品牌区域样式 - 企业级设计
// ========================================

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg; // 20px
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: $spacing-md; // 16px
    margin-bottom: $spacing-sm; // 8px
}

.logo-icon {
    position: relative;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;

    .logo-icon-main {
        position: absolute;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, $primary-color, $primary-lighter);
        border-radius: 50%;
        box-shadow: $box-shadow-sm;
    }
}

.brand-title {
    font-size: $font-size-xl; // 使用全局字体变量 24px
    font-weight: $font-weight-bold; // 使用全局字重变量 700
    color: $white; // 使用全局变量，纯白色标题
    margin: 0;
    line-height: $line-height-tight; // 使用全局行高变量
    letter-spacing: 0.5px; // 增加字母间距，更专业
}

.brand-desc {
    font-size: $font-size-sm; // 使用全局字体变量 14px
    color: $text-muted-light; // 使用全局变量，更柔和的灰色
    line-height: $line-height-relaxed; // 使用全局行高变量
    margin: 0;
    max-width: 320px; // 稍宽的描述区域
    font-weight: $font-weight-normal; // 使用全局字重变量
}

// ========================================
// 7. 联系信息样式 - 企业级设计
// ========================================

.contact-info {
    display: flex;
    flex-direction: column;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: $spacing-base; // 使用全局间距变量 12px
    font-size: $font-size-xs; // 使用全局字体变量 12px
    color: $text-muted-light; // 使用全局变量，更柔和的文字颜色
    padding: $spacing-xs 0; // 使用全局间距变量，垂直间距

    .contact-icon {
        width: $spacing-xl; // 使用全局间距变量 24px
        height: $spacing-xl; // 使用全局间距变量 24px
        display: flex;
        align-items: center;
        justify-content: center;
        color: $primary-color; // 使用全局主色变量
        flex-shrink: 0;
        background: $primary-alpha-10; // 使用全局透明度变量
        border-radius: $border-radius-sm; // 使用全局圆角变量

        i {
            font-size: $font-size-xs; // 使用全局字体变量 12px
        }
    }

    a {
        color: inherit;
        text-decoration: none;
        @include no-animation; // 使用企业级无动画 mixin

        // 企业级严肃专业悬停效果 - 仅颜色变化
        &:hover {
            color: $primary-color; // 使用全局主色变量
            text-decoration: none;
        }
    }
}

// ========================================
// 8. 社交媒体区域样式 - 企业级设计
// ========================================

.footer-social {
    .social-title {
        font-size: $font-size-base; // 16px
        font-weight: $font-weight-semibold; // 600
        color: $white; // 深色背景下的白色标题
        margin: 0 0 $spacing-sm 0; // 0 0 8px 0
    }
}

.social-links {
    display: flex;
    gap: $spacing-sm; // 8px
    flex-wrap: wrap;
}

.social-link {
    position: relative;
    width: 44px; // 社交按钮尺寸
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $primary-alpha-10; // 使用全局透明度变量
    color: $primary-color; // 使用全局主色变量
    border-radius: $border-radius-md; // 使用全局圆角变量
    text-decoration: none;
    border: 1px solid $primary-alpha-20; // 使用全局透明度变量
    @include no-animation; // 使用企业级无动画 mixin

    // 企业级严肃专业悬停效果 - 简化设计
    &:hover {
        background: $primary-alpha-15; // 使用全局透明度变量，轻微背景变化
        color: $primary-color; // 保持主色
        border-color: $primary-alpha-30; // 使用全局透明度变量
        // 移除阴影和过度效果，保持严肃专业

        .tooltip {
            opacity: 1;
            visibility: visible;
        }
    }

    &:focus {
        outline: 2px solid $primary-color; // 使用全局主色变量
        outline-offset: 2px;
    }

    i {
        font-size: $font-size-md; // 使用全局字体变量 18px
    }

    .tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        background: $gray-900; // 使用全局变量
        color: $white; // 使用全局变量
        padding: $spacing-xs $spacing-sm; // 使用全局间距变量
        border-radius: $border-radius-xs; // 使用全局圆角变量
        font-size: $font-size-xxs; // 使用全局字体变量
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        margin-bottom: $spacing-xs; // 使用全局间距变量
        z-index: 10;
        @include no-animation; // 使用企业级无动画 mixin

        &::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            border: 4px solid transparent;
            border-top-color: $gray-900; // 使用全局变量
        }
    }
}

// ========================================
// 9. 链接区域容器样式 - 企业级网格布局
// ========================================

.footer-links-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: $spacing-xl; // 24px

    @media (max-width: $breakpoint-xl) { // 1440px
        grid-template-columns: repeat(3, 1fr);
        gap: $spacing-lg; // 20px
    }

    @media (max-width: $breakpoint-lg) { // 1200px
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-md; // 16px
    }

    @media (max-width: $breakpoint-sm) { // 768px
        grid-template-columns: 1fr;
        gap: $spacing-lg; // 20px
    }
}

// ========================================
// 10. 页脚链接区域样式 - 企业级设计优化
// ========================================

.footer-section {
    .section-title {
        font-size: $font-size-sm; // 使用全局变量 14px
        font-weight: $font-weight-semibold; // 使用全局变量 600
        color: $white; // 使用全局变量
        margin: 0 0 $spacing-lg 0; // 使用全局间距变量 20px
        display: flex;
        align-items: center;
        gap: $spacing-sm; // 使用全局间距变量 8px
        letter-spacing: 0.3px; // 字母间距增强专业感

        .section-icon {
            font-size: $font-size-sm; // 使用全局变量 14px
            color: $primary-color; // 使用全局主色变量
            width: $spacing-lg; // 使用全局间距变量 20px
            text-align: center;
            flex-shrink: 0;
        }
    }
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    // 关键对齐优化：添加左侧内边距，与标题图标+间距对齐
    padding-left: calc(#{$spacing-lg} + #{$spacing-sm}); // 20px + 8px = 28px，与标题图标宽度+间距对齐

    li {
        margin: 0;

        a,
        router-link {
            display: block;
            font-size: $font-size-xs; // 使用全局变量 12px
            color: $text-muted-light; // 使用全局变量，更柔和的链接文字
            text-decoration: none;
            padding: $spacing-sm 0; // 使用全局间距变量，垂直间距
            line-height: $line-height-normal; // 使用全局行高变量
            border-radius: $border-radius-sm; // 使用全局圆角变量
            @include no-animation; // 使用企业级无动画 mixin

            // 企业级严肃专业悬停效果 - 仅颜色变化，无背景和偏移
            &:hover {
                color: $primary-color; // 使用全局主色变量
                text-decoration: none;
                // 移除背景变色和偏移效果，保持严肃专业
            }

            &:focus {
                color: $primary-color; // 使用全局主色变量
                outline: 2px solid $primary-color;
                outline-offset: 2px;
                border-radius: $border-radius-sm; // 使用全局圆角变量
            }
        }
    }
}

// ========================================
// 11. 页脚底部区域样式 - 企业级严肃设计
// ========================================

.footer-bottom {
    border-top: 1px solid $white-alpha-15; // 使用全局透明度变量
    padding-top: $spacing-xl; // 使用全局间距变量 24px
    margin-top: $spacing-2xl; // 使用全局间距变量 32px
    background: $black-alpha-10; // 使用全局透明度变量
    position: relative;

    // 企业级渐变分割线效果
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            $white-alpha-20 20%,
            $white-alpha-30 50%,
            $white-alpha-20 80%,
            transparent 100%
        );
    }
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: $spacing-2xl; // 使用全局间距变量 32px
    position: relative;
    z-index: 2; // 确保内容在渐变线之上

    @media (max-width: $breakpoint-lg) { // 使用全局断点变量
        flex-direction: column;
        gap: $spacing-xl; // 使用全局间距变量 24px
        align-items: center;
        text-align: center;
    }

    @media (max-width: $breakpoint-sm) { // 使用全局断点变量
        gap: $spacing-lg; // 使用全局间距变量 20px
    }
}

// ========================================
// 12. 版权信息样式 - 企业级设计
// ========================================

.footer-left {
    flex: 1;
}

.copyright {
    margin-bottom: $spacing-md; // 使用全局间距变量 16px

    p {
        font-size: $font-size-xs; // 使用全局字体变量 12px
        color: $text-muted-light; // 使用全局变量，更亮的版权文字
        margin: 0;
        font-weight: $font-weight-medium; // 使用全局字重变量 500
        letter-spacing: 0.2px;
    }
}

.legal-links {
    margin-top: 0; // 移除顶部间距
}

.legal-links,
.legal-links-single-line {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: $spacing-xs; // 使用全局间距变量 4px
    font-size: $font-size-xs; // 使用全局变量 12px
    justify-content: flex-start;

    @media (max-width: $breakpoint-lg) { // 使用全局断点变量
        justify-content: center;
    }

    @media (max-width: $breakpoint-sm) { // 使用全局断点变量
        justify-content: center;
        gap: $spacing-xxs; // 使用全局间距变量 2px
        font-size: $font-size-xxs; // 使用全局变量 8px
    }

    a {
        color: $text-muted-light; // 使用全局变量，更柔和的链接文字
        text-decoration: none;
        padding: $spacing-xxs $spacing-xs; // 使用全局间距变量
        border-radius: $border-radius-sm; // 使用全局圆角变量
        white-space: nowrap;
        @include no-animation; // 使用企业级无动画 mixin

        // 企业级严肃专业悬停效果 - 仅颜色变化，无背景效果
        &:hover {
            color: $primary-color; // 使用全局主色变量
            text-decoration: none;
            // 移除背景变色效果，保持严肃专业
        }

        &:focus {
            color: $primary-color; // 使用全局主色变量
            outline: 2px solid $primary-color;
            outline-offset: 1px;
        }
    }

    .separator {
        color: $text-lighter; // 使用全局变量，更明显的分隔符
        font-weight: $font-weight-normal; // 使用全局字重变量
        margin: 0 $spacing-xxs; // 使用全局间距变量
        flex-shrink: 0;
        user-select: none;

        &:last-child {
            display: none;
        }

        @media (max-width: $breakpoint-sm) { // 使用全局断点变量
            margin: 0 $spacing-xxs; // 使用全局间距变量
        }
    }
}

// ========================================
// 13. 页脚右侧区域样式 - 企业级设计
// ========================================

.footer-right {
    display: flex;
    flex-direction: column;
    gap: $spacing-md; // 使用全局间距变量 16px
    align-items: flex-end;

    @media (max-width: $breakpoint-lg) { // 使用全局断点变量
        align-items: center;
        width: 100%;
    }

    @media (max-width: $breakpoint-sm) { // 使用全局断点变量
        gap: $spacing-sm; // 使用全局间距变量 8px
    }
}

.icp-info {
    display: flex;
    flex-direction: row; // 改为单行显示
    flex-wrap: wrap;
    gap: $spacing-md; // 使用全局间距变量 16px
    margin-bottom: $spacing-base; // 使用全局间距变量 12px

    @media (max-width: $breakpoint-lg) { // 使用全局断点变量
        justify-content: center;
    }

    @media (max-width: $breakpoint-sm) { // 使用全局断点变量
        flex-direction: column;
        gap: $spacing-sm; // 使用全局间距变量 8px
        align-items: center;
    }
}

.icp-item {
    display: flex;
    align-items: center;
    gap: $spacing-xs; // 使用全局间距变量 4px
    font-size: $font-size-xxs; // 使用全局字体变量 8px
    color: $text-muted-light; // 使用全局变量，更柔和的认证信息文字
    white-space: nowrap; // 防止换行

    i {
        font-size: $font-size-xxs; // 使用全局字体变量 8px
        color: $success-color; // 使用全局成功色变量，专业绿色图标
        width: $spacing-sm; // 使用全局间距变量 8px
        text-align: center;
        flex-shrink: 0;
    }

    &:not(:last-child)::after {
        content: '|';
        color: $text-lighter; // 使用全局变量
        margin-left: $spacing-base; // 使用全局间距变量 12px
        font-weight: $font-weight-light; // 使用全局字重变量 300
    }

    @media (max-width: $breakpoint-sm) { // 使用全局断点变量
        &:not(:last-child)::after {
            display: none;
        }
    }
}

.support-badge {
    display: flex;
    align-items: center;
    gap: $spacing-sm; // 使用全局间距变量 8px
    background: $success-alpha-10; // 使用全局透明度变量，专业绿色背景
    color: $success-color; // 使用全局成功色变量
    padding: $spacing-sm $spacing-md; // 使用全局间距变量
    border-radius: $border-radius-md; // 使用全局圆角变量
    font-size: $font-size-xs; // 使用全局字体变量 12px
    font-weight: $font-weight-medium; // 使用全局字重变量 500
    border: 1px solid $success-alpha-20; // 使用全局透明度变量
    box-shadow: $box-shadow-xs; // 使用全局阴影变量

    .badge-icon {
        i {
            font-size: $font-size-sm; // 使用全局字体变量 14px
        }
    }

    .badge-text {
        white-space: nowrap;
        letter-spacing: 0.2px;
    }
}

// ========================================
// 14. 过渡动画样式 - 企业级简化
// ========================================

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

// ========================================
// 16. 无障碍访问优化 - 企业级增强
// ========================================

.app-footer {
    // 确保键盘导航的焦点可见
    :focus-visible {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
        border-radius: 4px;
    }

    // 高对比度模式支持
    @media (prefers-contrast: more) {
        .footer-links a,
        .social-link,
        .contact-item a {
            border: 1px solid transparent;

            &:focus,
            &:hover {
                border-color: $primary-color;
                background-color: rgba(96, 165, 250, 0.1);
            }
        }
    }

    // 减少动画偏好支持 - 企业级设计
    @media (prefers-reduced-motion: reduce) {
        transition: none;

        *,
        *::before,
        *::after {
            transition: none !important;
            animation: none !important;
            transform: none !important;
        }
    }
}

// ========================================
// 17. 响应式设计优化 - 企业级断点系统
// ========================================

// 超大屏幕优化 (1920px+)
@media (min-width: $breakpoint-2xl) { // 1920px
    .footer-container {
        padding: $footer-padding $spacing-2xl; // 使用更大的内边距
    }

    .footer-links-container {
        grid-template-columns: repeat(5, 1fr);
        gap: $spacing-2xl; // 32px
    }
}

// 大屏幕优化 (1440px-1919px)
@media (min-width: $breakpoint-xl) and (max-width: #{$breakpoint-2xl - 1px}) {
    .footer-links-container {
        grid-template-columns: repeat(4, 1fr);
    }
}

// 中等屏幕优化 (1024px-1199px)
@media (min-width: $breakpoint-md) and (max-width: #{$breakpoint-lg - 1px}) {
    .footer-main {
        grid-template-columns: 1fr 1.5fr;
        gap: $spacing-lg; // 20px
    }

    .footer-links-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

// 小屏幕优化 (640px-767px)
@media (min-width: $breakpoint-xs) and (max-width: #{$breakpoint-sm - 1px}) {
    .footer-container {
        padding: $spacing-lg $spacing-sm; // 20px 8px
    }

    .social-links {
        justify-content: center;
    }

    .legal-links-single-line {
        justify-content: center;
        text-align: center;
        font-size: 10px; // 移动端更小字体
    }

    // 移动端链接对齐优化
    .footer-links {
        padding-left: calc(#{$spacing-md} + #{$spacing-xs}); // 16px + 4px = 20px，移动端较小的对齐
    }
}

// 超小屏幕优化 (639px以下) - 隐藏链接区域，优化公司介绍布局
@media (max-width: #{$breakpoint-xs - 1px}) {
    .footer-container {
        padding: $spacing-lg $spacing-sm; // 20px 8px，增加内边距
    }

    // 隐藏整个链接容器区域
    .footer-links-container {
        display: none !important;
    }

    // 优化主要布局为单列
    .footer-main {
        grid-template-columns: 1fr !important;
        gap: $spacing-lg !important; // 20px
    }

    // 优化公司介绍区域布局
    .footer-brand {
        text-align: center; // 居中对齐
        max-width: 100%;
        margin: 0 auto;
        padding: $spacing-lg 0; // 增加上下内边距
    }

    .brand-title {
        font-size: $font-size-xl; // 24px，保持较大字体突出品牌
        margin-bottom: $spacing-md; // 16px
        line-height: $line-height-tight; // 紧凑行高
    }

    .brand-desc {
        font-size: $font-size-sm; // 14px，适中的描述文字
        line-height: $line-height-relaxed; // 1.6，舒适的行高
        margin-bottom: $spacing-xl; // 24px，增加与下方内容的间距
        max-width: 280px; // 限制最大宽度，提升可读性
        margin-left: auto;
        margin-right: auto;
    }

    // 优化联系信息布局
    .contact-info {
        display: flex;
        flex-direction: column;
        gap: $spacing-sm; // 8px
        align-items: center;
        margin-bottom: $spacing-xl; // 24px
    }

    .contact-item {
        justify-content: center;
        text-align: center;
        font-size: $font-size-sm; // 14px，稍大的联系信息字体

        .contact-icon {
            width: $spacing-lg; // 20px
            height: $spacing-lg; // 20px
        }
    }

    // 优化社交媒体区域
    .social-section {
        margin-top: $spacing-xl; // 24px

        .section-title {
            justify-content: center;
            margin-bottom: $spacing-md; // 16px
        }
    }

    .social-links {
        justify-content: center;
        gap: $spacing-md; // 16px，增加社交图标间距
    }

    .social-link {
        width: 48px; // 稍大的社交按钮
        height: 48px;
    }

    // 优化底部区域
    .footer-bottom {
        margin-top: $spacing-xl; // 24px
        padding-top: $spacing-lg; // 20px
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: $spacing-md; // 16px
        text-align: center;
    }

    .icp-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: $spacing-xs; // 4px
    }

    .support-badge {
        justify-content: center;
        text-align: center;
        margin-top: $spacing-sm; // 8px
    }

    // 优化法律链接为垂直布局
    .legal-links,
    .legal-links-single-line {
        flex-direction: column;
        gap: $spacing-xs; // 4px
        align-items: center;

        .separator {
            display: none !important;
        }

        a {
            padding: $spacing-xs 0; // 4px 0，垂直间距
            font-size: $font-size-xs; // 12px
        }
    }
}

// ========================================
// 18. 企业级暗色主题支持
// ========================================

@media (prefers-color-scheme: dark) {
    .app-footer {
        // 暗色主题下的颜色调整
        background: linear-gradient(180deg, #0f172a 0%, #020617 100%);

        .footer-decoration .decoration-line {
            background: linear-gradient(45deg, $primary-color, $secondary-color);
            opacity: 0.08;
        }

        .footer-bottom {
            background: $black-alpha-20;
            border-top-color: $white-alpha-10;

            &::before {
                background: linear-gradient(90deg,
                    transparent 0%,
                    $white-alpha-10 20%,
                    $white-alpha-20 50%,
                    $white-alpha-10 80%,
                    transparent 100%
                );
            }
        }

        .legal-links a,
        .icp-item {
            color: $gray-300;

            &:hover {
                color: $primary-light;
            }
        }
    }
}

// ========================================
// 19. 企业级高对比度支持增强
// ========================================

@media (prefers-contrast: more) {
    .app-footer {
        background: $black !important;
        color: $white !important;

        .footer-links a,
        .social-link,
        .contact-item a,
        .legal-links a {
            border: 2px solid transparent !important;
            background: $black !important;
            color: $white !important;

            &:hover,
            &:focus {
                border-color: $white !important;
                background: $primary-color !important;
                color: $white !important;
            }
        }

        .footer-decoration,
        .footer-waves {
            display: none !important;
        }
    }
}

// ========================================
// 20. 打印样式优化 - 企业级设计增强
// ========================================

@media print {
    .app-footer {
        // 移除所有装饰性元素
        .footer-decoration,
        .footer-waves,
        .social-links,
        .support-badge {
            display: none !important;
        }

        // 重置背景和颜色
        background: $white !important;
        color: $black !important;
        box-shadow: none !important;

        .footer-container {
            max-width: none !important;
            padding: $spacing-md !important;
            margin: 0 !important;
        }

        .footer-main {
            grid-template-columns: 1fr !important;
            gap: $spacing-md !important;
        }

        .footer-links-container {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: $spacing-sm !important;
        }

        // 优化链接显示
        a {
            color: $black !important;
            text-decoration: underline !important;

            &::after {
                content: " (" attr(href) ")" !important;
                font-size: 0.8em !important;
                color: $gray-600 !important;
            }
        }

        // 隐藏不必要的内容
        .footer-bottom-content {
            flex-direction: column !important;
            gap: $spacing-sm !important;

            .footer-right {
                display: none !important;
            }
        }

        // 优化文字大小
        .section-title {
            font-size: $font-size-base !important;
            font-weight: $font-weight-bold !important;
        }

        .footer-links a {
            font-size: $font-size-sm !important;
        }

        .legal-links {
            flex-direction: column !important;
            align-items: flex-start !important;

            a {
                margin: $spacing-xxs 0 !important;
                padding: 0 !important;
            }

            .separator {
                display: none !important;
            }
        }

        // 添加打印页眉页脚信息
        &::before {
            content: "商智云 - 专业数字化解决方案提供商" !important;
            display: block !important;
            text-align: center !important;
            font-weight: $font-weight-bold !important;
            font-size: $font-size-lg !important;
            margin-bottom: $spacing-lg !important;
            padding-bottom: $spacing-sm !important;
            border-bottom: 2px solid $black !important;
        }

        &::after {
            content: "打印时间: " attr(data-print-time) !important;
            display: block !important;
            text-align: center !important;
            font-size: $font-size-xs !important;
            margin-top: $spacing-lg !important;
            padding-top: $spacing-sm !important;
            border-top: 1px solid $gray-400 !important;
            color: $gray-600 !important;
        }
    }
}
</style>
