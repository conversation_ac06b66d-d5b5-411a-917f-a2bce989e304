<template>
    <header :class="{ 'header-scrolled': isScrolled }" class="frontend-header frontend-no-animation">
        <div class="header-container">
            <!-- Logo区域 -->
            <div class="header-left">
                <router-link
                    aria-label="返回首页"
                    class="logo-container"
                    to="/"
                >
                    <div class="logo-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div class="logo-text">
                        <span class="logo-title">{{ logoConfig?.title || 'Frontend System' }}</span>
                        <span v-if="logoConfig?.showSubtitle !== false" class="logo-subtitle">
                            {{ logoConfig?.subtitle || '前台系统' }}
                        </span>
                    </div>
                </router-link>
            </div>

            <!-- 右侧功能区域 -->
            <div class="header-right">
                <!-- 桌面端导航菜单 -->
                <nav aria-label="主导航" class="header-nav desktop-nav">
                    <el-menu
                        :active-text-color="'#5247ef'"
                        :default-active="currentRoutePath"
                        class="nav-menu"
                        mode="horizontal"
                        @select="handleMenuSelect"
                    >
                        <el-menu-item :class="{ active: currentRoutePath === '/' }" index="/">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </el-menu-item>
                        <el-menu-item :class="{ active: currentRoutePath === '/market' }" index="/market">
                            <i class="fas fa-box"></i>
                            <span>市场</span>
                        </el-menu-item>
                        <el-menu-item :class="{ active: currentRoutePath === '/flow' }" index="/flow">
                            <i class="fas fa-cogs"></i>
                            <span>流量</span>
                        </el-menu-item>
                    </el-menu>
                </nav>
                <!-- 移动端菜单按钮 -->
                <el-button
                    :aria-expanded="showMobileMenu.toString()"
                    aria-controls="mobile-menu"
                    aria-label="打开菜单"
                    class="mobile-menu-btn mobile-only"
                    text
                    @click="toggleMobileMenu"
                >
                    <i class="fas fa-bars"></i>
                </el-button>
            </div>
        </div>

        <!-- 移动端菜单抽屉 -->
        <el-drawer
            v-model="showMobileMenu"
            :modal="true"
            :show-close="false"
            :size="280"
            class="mobile-drawer"
            direction="rtl"
            title=""
        >
            <template #header>
                <div class="mobile-menu-header">
                    <div class="mobile-logo">
                        <i :class="logoConfig?.icon || 'fas fa-cube'"></i>
                        <span>{{ logoConfig?.title || 'Frontend System' }}</span>
                    </div>
                    <el-button
                        aria-label="关闭菜单"
                        class="mobile-menu-btn mobile-only"
                        icon="Close"
                        size="small"
                        text
                        @click="toggleMobileMenu"
                    >
                    </el-button>
                </div>
            </template>

            <div class="mobile-menu-content">
                <!-- 移动端导航菜单 -->
                <el-menu
                    :default-active="currentRoutePath"
                    class="mobile-nav"
                    mode="vertical"
                    @select="handleMobileMenuSelect"
                >
                    <el-menu-item index="/"><i class="fas fa-home"></i><span>首页</span></el-menu-item>
                    <el-menu-item index="/market"><i class="fas fa-box"></i><span>市场</span></el-menu-item>
                    <el-menu-item index="/flow"><i class="fas fa-cogs"></i><span>流量</span></el-menu-item>
                </el-menu>

                <div class="mobile-menu-footer">
                    <p class="copyright">© {{ currentYear }}
                        {{ footerConfig?.company || logoConfig?.title || 'Frontend System' }}</p>
                </div>
            </div>
        </el-drawer>
    </header>
</template>

<script>
export default {
    name: 'FrontendHeader',
    data() {
        return {
            showMobileMenu: false,  // 控制移动端菜单显示或隐藏
            isScrolled: false,      // 控制滚动状态样式
        };
    },
    computed: {
        // 获取当前路由路径
        currentRoutePath() {
            return this.$route.path;
        },
        // 当前年份
        currentYear() {
            return new Date().getFullYear()
        },
    },
    mounted() {
        // 组件加载后，监听滚动事件
        window.addEventListener('scroll', this.handleScroll);
        // 监听窗口大小变化，自动关闭移动端菜单
        window.addEventListener('resize', this.handleResize);
    },
    beforeUnmount() {
        // 组件销毁前，移除事件监听
        window.removeEventListener('scroll', this.handleScroll);
        window.removeEventListener('resize', this.handleResize);
    },
    methods: {
        // 处理桌面端菜单选择
        handleMenuSelect(index) {
            if (index !== this.currentRoutePath) {
                this.$router.push(index);  // 路由跳转
            }
        },

        // 处理移动端菜单选择
        handleMobileMenuSelect(index) {
            if (index !== this.currentRoutePath) {
                this.$router.push(index);  // 路由跳转
            }
            // 选择后关闭移动端菜单
            this.showMobileMenu = false;
        },

        // 切换移动端菜单的显示和隐藏
        toggleMobileMenu() {
            this.showMobileMenu = !this.showMobileMenu;
        },

        // 处理滚动事件
        handleScroll() {
            this.isScrolled = window.scrollY > 10;
        },

        // 处理窗口大小变化
        handleResize() {
            // 当窗口变大时自动关闭移动端菜单
            if (window.innerWidth > 768 && this.showMobileMenu) {
                this.showMobileMenu = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
// ========================================
// 前台头部导航样式 - 企业级设计系统集成
// 使用全局变量和样式系统
// ========================================

@use '@css/variables' as *;

.frontend-header {
    margin-top: $nav-height-top; // 30px
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: $z-index-fixed; // 1000
    background: rgba($white, 0.95);
    backdrop-filter: saturate(180%) blur(20px);
    -webkit-backdrop-filter: saturate(180%) blur(20px);
    height: 64px;
    border-bottom: 1px solid $border-color-light; // #ebeef5

    // 企业级样式：简化过渡效果
    &:not(.frontend-no-animation) {
        transition: all 0.15s ease;
    }

    // 滚动时的样式变化
    &.header-scrolled {
        background: rgba($white, 0.98);
        box-shadow: $box-shadow-md; // 0 2px 16px rgba(0, 0, 0, 0.08)
        border-bottom-color: $border-color; // #e4e7ed
    }

    @media (max-width: $breakpoint-sm) { // 768px
        height: 64px;
        background: $white;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: $container-max-width; // 1760px
    margin: 0 auto;
    padding: 0 $spacing-base; // 12px
    position: relative;

    @media (max-width: $breakpoint-sm) { // 768px
        padding: 0 $spacing-md; // 16px
    }
}

// ========================================
// Logo区域样式 - 企业级设计系统集成
// ========================================

.header-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    height: 100%;
    min-width: 200px;
}

.logo-container {
    display: flex;
    align-items: center;
    text-decoration: none;
    height: 100%;
    padding: $spacing-sm $spacing-base; // 8px 12px
    border-radius: $border-radius-sm; // 4px

    // 企业级样式：简化过渡效果
    &:not(.frontend-no-animation) {
        transition: all 0.15s ease;
    }

    &:hover {
        background-color: rgba($primary-color, 0.03);
        transform: none;
    }

    &:focus {
        outline: 2px solid $primary-color; // #5247ef
        outline-offset: 2px;
        border-radius: $border-radius-sm; // 4px
    }
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, $primary-color, $primary-lighter); // #5247ef, #a396f7
    border-radius: $border-radius-base; // 6px
    margin-right: $spacing-md; // 16px
    box-shadow: $box-shadow-sm; // 0 2px 8px rgba(115, 103, 240, 0.15)
    position: relative;

    // 添加微妙的内阴影效果
    &::before {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        bottom: 1px;
        background: linear-gradient(135deg, rgba($white, 0.2), transparent);
        border-radius: $border-radius-sm; // 4px
        pointer-events: none;
    }

    i {
        font-size: $font-size-xl - 0.167rem; // 22px (24px - 2px)
        color: $white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: $breakpoint-sm) { // 768px
        width: 40px;
        height: 40px;
        margin-right: $spacing-base; // 12px

        i {
            font-size: $font-size-lg; // 20px
        }
    }
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: $line-height-tight; // 1.2

    @media (max-width: 480px) {
        .logo-subtitle {
            display: none;
        }
    }
}

.logo-title {
    font-size: $font-size-lg; // 20px
    font-weight: $font-weight-bold; // 700
    color: $text-lighter; // #999
    margin-bottom: $spacing-xxs; // 2px
    letter-spacing: -0.02em;

    @media (max-width: $breakpoint-sm) { // 768px
        font-size: $font-size-md; // 18px
    }
}

.logo-subtitle {
    font-size: $font-size-xs; // 12px
    color: $text-secondary; // #909399
    font-weight: $font-weight-medium; // 500
    letter-spacing: 0.02em;

    @media (max-width: $breakpoint-sm) { // 768px
        font-size: $font-size-xxs + 0.25rem; // 11px (8px + 3px)
    }
}

// ========================================
// 桌面端导航菜单样式 - 企业级设计系统集成
// ========================================

.header-nav {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    max-width: 650px;
    margin: 0 $spacing-lg; // 20px

    @media (max-width: $breakpoint-sm) { // 768px
        display: none;
    }
}

.nav-menu {
    border: none;
    background: transparent;
    border-bottom: none !important;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: right;
    min-width: 480px;
    width: 100%;

    :deep(.el-menu-item) {
        height: 100%;
        padding: 0 $spacing-lg; // 20px
        color: $text-regular; // #606266
        font-weight: $font-weight-medium; // 500
        font-size: $font-size-sm; // 14px
        border-bottom: none !important;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border-radius: $border-radius-sm; // 4px
        margin: 0 $spacing-xxs; // 2px
        min-width: 80px;

        // 企业级样式：简化过渡效果
        &:not(.frontend-no-animation) {
            transition: background-color 0.15s ease, color 0.15s ease;
        }

        &:hover {
            background-color: rgba($primary-color, 0.04);
            color: $primary-color; // #5247ef

            i {
                color: $primary-color;
            }

            span {
                color: $primary-color;
            }
        }

        &:focus {
            background-color: rgba($primary-color, 0.04);
            color: $primary-color; // #5247ef
            outline: none;

            i {
                color: $primary-color;
            }

            span {
                color: $primary-color;
            }
        }

        // 激活状态样式
        &.is-active,
        &.active {
            background-color: rgba($primary-color, 0.08);
            color: $primary-color; // #5247ef
            font-weight: $font-weight-semibold; // 600

            // 下划线覆盖整个菜单项宽度
            &::after {
                content: '';
                position: absolute;
                bottom: -1px;
                left: 0;
                right: 0;
                height: 2px;
                background: $primary-color; // #5247ef
                border-radius: $spacing-xxs $spacing-xxs 0 0; // 2px 2px 0 0
            }
        }

        i {
            margin-right: $spacing-sm; // 8px
            font-size: $font-size-base; // 16px
            width: $font-size-base; // 16px
            text-align: center;
            flex-shrink: 0;
        }

        span {
            font-size: $font-size-sm; // 14px
            white-space: nowrap;
            flex-shrink: 0;
        }
    }

    // 鼠标悬停效果优化
    :deep(.el-menu-item:hover) {
        color: $primary-color; // #5247ef

        // 非激活状态下的悬停下划线
        &:not(.is-active):not(.active)::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 1px;
            background: rgba($primary-color, 0.3);
            border-radius: $spacing-xxs $spacing-xxs 0 0; // 2px 2px 0 0
        }
    }

    :deep(.el-menu-item:focus) {
        color: $primary-color; // #5247ef
        border-radius: $border-radius-sm; // 4px
        outline: none;
    }
}

// ========================================
// 右侧功能区域样式 - 企业级设计系统集成
// ========================================

.header-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    height: 100%;
    min-width: 60px;
    justify-content: flex-end;
}

.mobile-menu-btn {
    display: none;
    width: 44px;
    height: 44px;
    border-radius: $border-radius-sm; // 4px
    background: transparent;
    border: 1px solid $border-color; // #e4e7ed
    color: $text-regular; // #606266

    // 企业级样式：简化过渡效果
    &:not(.frontend-no-animation) {
        transition: all 0.15s ease;
    }

    &:hover {
        background-color: rgba($primary-color, 0.04);
        border-color: $primary-color; // #5247ef
        color: $primary-color; // #5247ef
        transform: none;
    }

    &:focus {
        outline: 2px solid $primary-color; // #5247ef
        outline-offset: 2px;
        border-radius: $border-radius-sm; // 4px
    }

    &:active {
        background-color: rgba($primary-color, 0.08);
        transform: none;
    }

    i {
        font-size: $font-size-md; // 18px
    }

    @media (max-width: $breakpoint-sm) { // 768px
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

// ========================================
// 移动端菜单抽屉样式 - 企业级设计系统集成
// ========================================

:deep(.mobile-drawer) {
    .el-drawer {
        background: $white;
        box-shadow: $box-shadow-xl; // 0 8px 32px rgba(0, 0, 0, 0.12)
    }

    .el-drawer__header {
        padding: 0;
        margin-bottom: 0;
        border-bottom: 1px solid $border-color-light; // #ebeef5
    }

    .el-drawer__body {
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    // 修复抽屉遮罩层
    .el-overlay {
        background-color: rgba($black, 0.5);
    }
}

.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-md; // 16px
    background: $white;
    border-bottom: 1px solid $border-color-light; // #ebeef5
    flex-shrink: 0;
}

.mobile-logo {
    display: flex;
    align-items: center;
    font-size: $font-size-md; // 18px
    font-weight: $font-weight-bold; // 700
    color: $text-lighter; // #999

    i {
        width: 36px;
        height: 36px;
        background: linear-gradient(135deg, $primary-color, $primary-lighter); // #5247ef, #a396f7
        border-radius: $border-radius-sm; // 4px
        display: flex;
        align-items: center;
        justify-content: center;
        color: $white;
        font-size: $font-size-md; // 18px
        margin-right: $spacing-md; // 16px
        box-shadow: $box-shadow-sm; // 0 2px 8px rgba(115, 103, 240, 0.15)
    }
}


.mobile-menu-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    overflow-y: auto;
}

.mobile-nav {
    flex: 1;
    border: none;
    background: transparent;
    padding: $spacing-base 0; // 12px 0

    :deep(.el-menu-item) {
        height: 52px;
        padding: 0 $spacing-xl; // 24px
        margin: 0 $spacing-md $spacing-xs; // 0 16px 4px
        color: $text-regular; // #606266
        font-weight: $font-weight-medium; // 500
        font-size: $font-size-sm + 0.083rem; // 15px (14px + 1px)
        border-bottom: none !important;
        border-radius: $border-radius-sm; // 4px
        display: flex;
        align-items: center;
        position: relative;

        // 企业级样式：简化过渡效果
        &:not(.frontend-no-animation) {
            transition: background-color 0.15s ease, color 0.15s ease;
        }

        &:hover {
            background-color: rgba($primary-color, 0.04);
            color: $primary-color; // #5247ef
        }

        &:focus {
            background-color: rgba($primary-color, 0.04);
            color: $primary-color; // #5247ef
            outline: none;
        }

        &.is-active {
            background-color: rgba($primary-color, 0.08);
            color: $primary-color; // #5247ef
            font-weight: $font-weight-semibold; // 600

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                height: 28px;
                width: $spacing-xs; // 4px
                background: $primary-color; // #5247ef
                border-radius: 0 $spacing-xxs $spacing-xxs 0; // 0 2px 2px 0
            }
        }

        i {
            margin-right: $spacing-md; // 16px
            font-size: $font-size-md; // 18px
            width: $spacing-lg; // 20px
            text-align: center;
            flex-shrink: 0;
        }

        span {
            font-size: $font-size-sm + 0.083rem; // 15px (14px + 1px)
            flex-shrink: 0;
        }
    }
}

.mobile-menu-footer {
    padding: $spacing-lg $spacing-xl; // 20px 24px
    border-top: 1px solid $border-color-light; // #ebeef5
    background: $bg-light; // #f1f3f6
    margin-top: auto;
    flex-shrink: 0;
}

.copyright {
    font-size: $font-size-xs; // 12px
    color: $text-secondary; // #909399
    text-align: center;
    margin: 0;
    font-weight: $font-weight-medium; // 500
}

// ========================================
// 响应式工具类 - 企业级设计系统集成
// ========================================

.desktop-nav {
    @media (max-width: $breakpoint-sm) { // 768px
        display: none;
    }
}

.mobile-only {
    @media (min-width: #{$breakpoint-sm + 1px}) { // 769px
        display: none !important;
    }
}

// ========================================
// 无障碍访问优化 - 企业级增强
// ========================================

.frontend-header {
    // 确保键盘导航的焦点可见
    :focus-visible {
        outline: 2px solid $primary-color; // #5247ef
        outline-offset: 2px;
        border-radius: $border-radius-sm; // 4px
    }

    // 高对比度模式支持
    @media (prefers-contrast: more) {
        border-bottom-width: 2px;

        .nav-menu :deep(.el-menu-item) {
            border: 1px solid transparent;

            &:focus,
            &.is-active {
                border-color: $primary-color; // #5247ef
            }
        }
    }

    // 减少动画偏好支持
    @media (prefers-reduced-motion: reduce) {
        &:not(.frontend-no-animation) {
            transition: none;
        }

        * {
            transition: none !important;
            animation: none !important;
        }
    }
}

// ========================================
// 打印样式优化
// ========================================

@media print {
    .frontend-header {
        display: none;
    }
}
</style>
