<template>
    <div class="backend-not-found">
        <div class="not-found-container">
            <div class="not-found-icon">
                <i class="fas fa-search"></i>
            </div>
            <h2 class="not-found-title">页面未找到</h2>
            <p class="not-found-message">抱歉，您访问的页面不存在或已被移除。</p>
            <div class="not-found-actions">
                <el-button type="primary" @click="goBack">
                    <i class="fas fa-arrow-left"></i>
                    返回上一页
                </el-button>
                <el-button @click="goHome">
                    <i class="fas fa-home"></i>
                    返回首页
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'BackendNotFound',
    methods: {
        goBack() {
            this.$router.go(-1)
        },
        goHome() {
            this.$router.push('/admin/dashboard')
        }
    }
}
</script>

<style lang="scss" scoped>
.backend-not-found {
    .not-found-container {
        text-align: center;
        min-height: 500px;
        padding: 3rem 2rem;
        background: white;
        border-radius: 4px;

        .not-found-icon {
            margin-bottom: 2rem;

            i {
                font-size: 4rem;
                color: $primary-color;
                opacity: 0.6;
            }
        }

        .not-found-title {
            margin: 0 0 1rem 0;
            font-size: 1.75rem;
            font-weight: 600;
            color: #5e5873;
        }

        .not-found-message {
            margin: 0 0 2rem 0;
            font-size: 1rem;
            color: #6e6b7b;
            line-height: 1.5;
        }

        .not-found-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;

            .el-button {
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 500;

                i {
                    margin-right: 0.5rem;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .backend-not-found {
        padding: 1rem;

        .not-found-container {
            padding: 2rem 1rem;

            .not-found-actions {
                flex-direction: column;

                .el-button {
                    width: 100%;
                }
            }
        }
    }
}
</style>
