<template>
    <div class="error-page forbidden">
        <div class="error-container">
            <div class="error-content">
                <div class="error-illustration">
                    <i class="fas fa-ban"></i>
                    <div class="error-code">403</div>
                </div>

                <div class="error-info">
                    <h1 class="error-title">访问被拒绝</h1>
                    <p class="error-description">
                        抱歉，您没有权限访问此页面。
                        请联系管理员获取相应权限，或返回您有权限访问的页面。
                    </p>

                    <div class="error-actions">
                        <el-button type="primary" @click="goHome">
                            <i class="fas fa-home"></i>
                            返回首页
                        </el-button>
                        <el-button @click="goBack">
                            <i class="fas fa-arrow-left"></i>
                            返回上页
                        </el-button>
                    </div>
                </div>
            </div>

            <div class="error-suggestions">
                <h3>可能的原因：</h3>
                <ul>
                    <li>
                        <i class="fas fa-exclamation-circle"></i>
                        您的账户权限不足
                    </li>
                    <li>
                        <i class="fas fa-clock"></i>
                        登录会话已过期
                    </li>
                    <li>
                        <i class="fas fa-user-times"></i>
                        账户已被禁用或暂停
                    </li>
                    <li>
                        <i class="fas fa-cog"></i>
                        系统权限配置问题
                    </li>
                </ul>

                <div class="contact-admin">
                    <p>如需帮助，请联系系统管理员</p>
                    <el-button size="small" type="info">
                        <i class="fas fa-envelope"></i>
                        联系管理员
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'Forbidden'
}
</script>

<style lang="scss" scoped>
@use '@css/variables' as *;
@use 'sass:color';

.error-page {
    min-height: 100vh;
    background: linear-gradient(135deg, color.scale($danger-color, $lightness: 100%) 0%, color.scale($danger-color, $lightness: 92.96875%) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.error-container {
    max-width: 800px;
    width: 100%;
    background-color: white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-lg;
    overflow: hidden;
}

.error-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 400px;
}

.error-illustration {
    background: linear-gradient(135deg, $danger-color 0%, color.scale($danger-color, $lightness: -16.0377358491%) 100%);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    position: relative;

    i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
    }

    .error-code {
        font-size: 6rem;
        font-weight: $font-weight-bold;
        line-height: 1;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
}

.error-info {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .error-title {
        font-size: 2rem;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin: 0 0 1rem 0;
    }

    .error-description {
        color: $text-secondary;
        line-height: 1.6;
        margin-bottom: 2rem;
        font-size: 1rem;
    }

    .error-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .el-button {
            i {
                margin-right: 0.5rem;
            }
        }
    }
}

.error-suggestions {
    padding: 2rem 3rem 3rem;
    border-top: 1px solid $border-light;
    background-color: $light-color;

    h3 {
        font-size: 1.125rem;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin: 0 0 1rem 0;
    }

    ul {
        list-style: none;
        padding: 0;
        margin: 0 0 2rem 0;

        li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: $text-secondary;
            font-size: 0.875rem;

            &:last-child {
                margin-bottom: 0;
            }

            i {
                color: $warning-color;
                width: 1rem;
                text-align: center;
            }
        }
    }

    .contact-admin {
        text-align: center;
        padding-top: 1.5rem;
        border-top: 1px solid $border-light;

        p {
            margin: 0 0 1rem 0;
            color: $text-secondary;
            font-size: 0.875rem;
        }

        .el-button {
            i {
                margin-right: 0.5rem;
            }
        }
    }
}

@media (max-width: 768px) {
    .error-content {
        grid-template-columns: 1fr;
    }

    .error-illustration {
        padding: 2rem;

        i {
            font-size: 3rem;
        }

        .error-code {
            font-size: 4rem;
        }
    }

    .error-info {
        padding: 2rem;

        .error-title {
            font-size: 1.5rem;
        }

        .error-actions {
            flex-direction: column;

            .el-button {
                width: 100%;
                justify-content: center;
            }
        }
    }

    .error-suggestions {
        padding: 1.5rem 2rem 2rem;
    }
}

@media (max-width: 480px) {
    .error-page {
        padding: 1rem;
    }

    .error-illustration {
        padding: 1.5rem;
    }

    .error-info {
        padding: 1.5rem;
    }

    .error-suggestions {
        padding: 1rem 1.5rem 1.5rem;
    }
}
</style>
