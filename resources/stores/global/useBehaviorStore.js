import {defineStore} from 'pinia'

/**
 * 用户行为追踪存储
 * 专注于用户体验优化的行为数据收集和分析
 * 不包含缓存、性能监测等功能，专注于用户行为模式识别
 */
export const useBehaviorStore = defineStore('behavior', {
    state: () => ({
        // === 核心行为数据 ===
        behaviorLogs: [],
        visitHistory: [],

        // === 用户偏好数据 ===
        userPreferences: {
            favoritePages: [],     // 收藏的页面
            recentActions: [],     // 最近操作
            searchHistory: [],     // 搜索历史
            commonPaths: [],       // 常用路径
            preferredFeatures: []  // 偏好功能
        },

        // === 交互统计 ===
        interactionStats: {
            clickCount: 0,
            viewCount: 0,
            searchCount: 0,
            navigationCount: 0,
            errorCount: 0,
            successCount: 0
        },

        // === 用户体验指标 ===
        uxMetrics: {
            averagePageStayTime: 0,
            bouncePages: [],           // 跳出率高的页面
            popularFeatures: [],       // 受欢迎的功能
            problemAreas: [],          // 问题区域
            userSatisfactionScore: 0   // 用户满意度评分
        },

        // === 会话行为 ===
        sessionBehavior: {
            sessionId: null,
            startTime: null,
            pageViews: 0,
            interactions: 0,
            currentPath: '',
            referrer: '',
            userAgent: navigator.userAgent
        },

        // === 行为模式 ===
        behaviorPatterns: {
            commonSequences: [],       // 常见操作序列
            timePatterns: [],          // 时间模式
            navigationPatterns: [],    // 导航模式
            errorPatterns: []          // 错误模式
        },

        // === 配置选项 ===
        config: {
            maxBehaviorLogs: 500,
            maxVisitHistory: 200,
            maxRecentActions: 50,
            enableDetailedTracking: true,
            enableUserFeedback: true,
            enablePatternAnalysis: true,
            trackingIntervalMs: 1000
        }
    }),

    getters: {
        // === 基础数据获取器 ===
        getRecentVisits: (state) => {
            return state.visitHistory.slice(-10).reverse()
        },

        getRecentActions: (state) => {
            return state.userPreferences.recentActions.slice(-10).reverse()
        },

        // === 页面分析获取器 ===
        getMostVisitedPages: (state) => {
            const pageCounts = {}
            state.visitHistory.forEach(visit => {
                pageCounts[visit.path] = (pageCounts[visit.path] || 0) + 1
            })
            return Object.entries(pageCounts).sort((a, b) => b[1] - a[1]).slice(0, 10).map(([path, count]) => ({
                path,
                count,
                percentage: (count / state.visitHistory.length * 100).toFixed(1)
            }))
        },

        // === 用户偏好获取器 ===
        getUserPreferredFeatures: (state) => {
            return state.userPreferences.preferredFeatures.slice().sort((a, b) => b.usage - a.usage)
        },

        getFavoritePages: (state) => {
            return state.userPreferences.favoritePages
        },

        // === 交互统计获取器 ===
        getTotalInteractions: (state) => {
            return Object.values(state.interactionStats).reduce((sum, count) => sum + count, 0)
        },

        getSuccessRate: (state) => {
            const total = state.interactionStats.successCount + state.interactionStats.errorCount
            return total > 0 ? (state.interactionStats.successCount / total * 100).toFixed(1) : 100
        },

        // === 用户体验指标获取器 ===
        getAveragePageStayTime: (state) => {
            return Math.round(state.uxMetrics.averagePageStayTime / 1000) // 转换为秒
        },

        getPopularFeatures: (state) => {
            return state.uxMetrics.popularFeatures.slice(0, 5)
        },

        getProblemAreas: (state) => {
            return state.uxMetrics.problemAreas.filter(area => area.severity > 0.3)
        },

        // === 行为模式获取器 ===
        getCommonNavigationPaths: (state) => {
            return state.behaviorPatterns.navigationPatterns.slice(0, 5)
        },

        // === 会话信息获取器 ===
        getCurrentSession: (state) => {
            return {
                ...state.sessionBehavior,
                duration: state.sessionBehavior.startTime ? Date.now() - state.sessionBehavior.startTime : 0
            }
        },

        // === 智能推荐获取器 ===
        getRecommendedPages: (state) => {
            const mostVisited = state.visitHistory.filter(visit => visit.path !== state.sessionBehavior.currentPath).reduce((acc, visit) => {
                acc[visit.path] = (acc[visit.path] || 0) + 1
                return acc
            }, {})

            return Object.entries(mostVisited).sort((a, b) => b[1] - a[1]).slice(0, 3).map(([path]) => path)
        }
    },

    actions: {
        /**
         * 初始化行为追踪
         */
        initializeBehaviorTracking() {
            this.sessionBehavior.sessionId = this.generateSessionId()
            this.sessionBehavior.startTime = Date.now()
            this.sessionBehavior.referrer = document.referrer

            // 启动行为分析
            if (this.config.enablePatternAnalysis) {
                this.startPatternAnalysis()
            }

            console.log('📊 用户行为追踪已启动')
        },

        /**
         * 生成会话ID
         */
        generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        },

        /**
         * 记录页面访问
         */
        recordPageVisit(routeInfo) {
            const visitLog = {
                path: routeInfo.path,
                name: routeInfo.name,
                title: routeInfo.meta?.title || routeInfo.name,
                timestamp: new Date().toISOString(),
                sessionId: this.sessionBehavior.sessionId,
                from: routeInfo.from || '',
                stayTime: 0,
                id: Date.now() + Math.random()
            }

            this.visitHistory.push(visitLog)
            this.sessionBehavior.pageViews++
            this.sessionBehavior.currentPath = routeInfo.path
            this.interactionStats.viewCount++

            // 更新上一个页面的停留时间
            this.updatePreviousPageStayTime()

            // 维护历史记录大小
            this.maintainHistorySize()

            // 分析导航模式
            this.analyzeNavigationPattern(routeInfo)

            // 更新用户体验指标
            this.updateUXMetrics()
        },

        /**
         * 更新上一个页面的停留时间
         */
        updatePreviousPageStayTime() {
            if (this.visitHistory.length >= 2) {
                const currentVisit = this.visitHistory[this.visitHistory.length - 1]
                const previousVisit = this.visitHistory[this.visitHistory.length - 2]

                const stayTime = new Date(currentVisit.timestamp).getTime() - new Date(previousVisit.timestamp).getTime()
                previousVisit.stayTime = stayTime

                // 更新平均停留时间
                this.updateAverageStayTime(stayTime)
            }
        },

        /**
         * 更新平均停留时间
         */
        updateAverageStayTime(stayTime) {
            const validStayTimes = this.visitHistory.filter(visit => visit.stayTime > 0 && visit.stayTime < 300000) // 过滤异常值（5分钟以上）
                .map(visit => visit.stayTime)

            if (validStayTimes.length > 0) {
                this.uxMetrics.averagePageStayTime = validStayTimes.reduce((sum, time) => sum + time, 0) / validStayTimes.length
            }
        },

        /**
         * 记录用户操作
         */
        recordUserAction(action) {
            const actionLog = {
                type: action.type,
                target: action.target,
                details: action.details || {},
                timestamp: new Date().toISOString(),
                sessionId: this.sessionBehavior.sessionId,
                path: this.sessionBehavior.currentPath,
                id: Date.now() + Math.random()
            }

            this.behaviorLogs.push(actionLog)
            this.userPreferences.recentActions.push(actionLog)
            this.sessionBehavior.interactions++

            // 更新交互统计
            this.updateInteractionStats(action.type)

            // 分析操作序列
            this.analyzeActionSequence(actionLog)

            // 维护日志大小
            this.maintainLogSize()
        },

        /**
         * 更新交互统计
         */
        updateInteractionStats(actionType) {
            switch (actionType) {
                case 'click':
                case 'user_activity':
                    this.interactionStats.clickCount++
                    break
                case 'search':
                    this.interactionStats.searchCount++
                    break
                case 'navigation':
                case 'page_visit':
                    this.interactionStats.navigationCount++
                    break
                case 'error':
                    this.interactionStats.errorCount++
                    break
                case 'success':
                    this.interactionStats.successCount++
                    break
            }
        },

        /**
         * 记录搜索行为
         */
        recordSearch(searchTerm, results = 0) {
            const searchLog = {
                term: searchTerm,
                results,
                timestamp: new Date().toISOString(),
                sessionId: this.sessionBehavior.sessionId,
                path: this.sessionBehavior.currentPath,
                id: Date.now() + Math.random()
            }

            this.userPreferences.searchHistory.push(searchLog)
            this.interactionStats.searchCount++

            // 记录为用户操作
            this.recordUserAction({
                type: 'search',
                target: 'search_input',
                details: {term: searchTerm, results}
            })

            // 维护搜索历史大小
            if (this.userPreferences.searchHistory.length > 100) {
                this.userPreferences.searchHistory.shift()
            }

            // 分析搜索模式
            this.analyzeSearchPattern(searchLog)
        },

        /**
         * 添加收藏页面
         */
        addFavoritePage(pageInfo) {
            const favorite = {
                path: pageInfo.path,
                name: pageInfo.name,
                title: pageInfo.title,
                timestamp: new Date().toISOString(),
                visitCount: this.getPageVisitCount(pageInfo.path)
            }

            // 检查是否已存在
            const exists = this.userPreferences.favoritePages.some(
                fav => fav.path === pageInfo.path
            )

            if (!exists) {
                this.userPreferences.favoritePages.push(favorite)
                this.recordUserAction({
                    type: 'favorite_added',
                    target: 'page',
                    details: {path: pageInfo.path, title: pageInfo.title}
                })
            }
        },

        /**
         * 移除收藏页面
         */
        removeFavoritePage(path) {
            const index = this.userPreferences.favoritePages.findIndex(
                fav => fav.path === path
            )
            if (index > -1) {
                this.userPreferences.favoritePages.splice(index, 1)
                this.recordUserAction({
                    type: 'favorite_removed',
                    target: 'page',
                    details: {path}
                })
            }
        },

        /**
         * 获取页面访问次数
         */
        getPageVisitCount(path) {
            return this.visitHistory.filter(visit => visit.path === path).length
        },

        /**
         * 记录功能使用
         */
        recordFeatureUsage(featureName, context = {}) {
            const existingFeature = this.userPreferences.preferredFeatures.find(f => f.name === featureName)

            if (existingFeature) {
                existingFeature.usage++
                existingFeature.lastUsed = Date.now()
                existingFeature.contexts.push(context)

                // 保持上下文数组大小
                if (existingFeature.contexts.length > 10) {
                    existingFeature.contexts.shift()
                }
            } else {
                this.userPreferences.preferredFeatures.push({
                    name: featureName,
                    usage: 1,
                    firstUsed: Date.now(),
                    lastUsed: Date.now(),
                    contexts: [context]
                })
            }

            this.recordUserAction({
                type: 'feature_usage',
                target: featureName,
                details: context
            })
        },

        /**
         * 记录用户反馈
         */
        recordUserFeedback(feedback) {
            if (!this.config.enableUserFeedback) return

            const feedbackLog = {
                type: feedback.type || 'general',
                rating: feedback.rating,
                comment: feedback.comment,
                page: this.sessionBehavior.currentPath,
                timestamp: new Date().toISOString(),
                sessionId: this.sessionBehavior.sessionId
            }

            this.recordUserAction({
                type: 'user_feedback',
                target: 'feedback_form',
                details: feedbackLog
            })

            // 更新用户满意度评分
            this.updateUserSatisfactionScore(feedback.rating)
        },

        /**
         * 更新用户满意度评分
         */
        updateUserSatisfactionScore(rating) {
            const feedbacks = this.behaviorLogs.filter(log => log.type === 'user_feedback' && log.details.rating).map(log => log.details.rating)

            if (feedbacks.length > 0) {
                this.uxMetrics.userSatisfactionScore = feedbacks.reduce((sum, r) => sum + r, 0) / feedbacks.length
            }
        },

        /**
         * 分析导航模式
         */
        analyzeNavigationPattern(routeInfo) {
            const recentVisits = this.visitHistory.slice(-5).map(visit => visit.path)
            const pattern = recentVisits.join(' -> ')

            const existingPattern = this.behaviorPatterns.navigationPatterns.find(p => p.pattern === pattern)
            if (existingPattern) {
                existingPattern.frequency++
            } else if (recentVisits.length >= 3) {
                this.behaviorPatterns.navigationPatterns.push({
                    pattern,
                    frequency: 1,
                    lastSeen: Date.now()
                })
            }

            // 保持模式数组大小
            if (this.behaviorPatterns.navigationPatterns.length > 50) {
                this.behaviorPatterns.navigationPatterns.sort((a, b) => b.frequency - a.frequency)
                this.behaviorPatterns.navigationPatterns = this.behaviorPatterns.navigationPatterns.slice(0, 30)
            }
        },

        /**
         * 分析操作序列
         */
        analyzeActionSequence(actionLog) {
            const recentActions = this.behaviorLogs.slice(-3).map(log => log.type)
            const sequence = recentActions.join(' -> ')

            const existingSequence = this.behaviorPatterns.commonSequences.find(s => s.sequence === sequence)
            if (existingSequence) {
                existingSequence.frequency++
            } else if (recentActions.length >= 2) {
                this.behaviorPatterns.commonSequences.push({
                    sequence,
                    frequency: 1,
                    lastSeen: Date.now()
                })
            }

            // 保持序列数组大小
            if (this.behaviorPatterns.commonSequences.length > 100) {
                this.behaviorPatterns.commonSequences.sort((a, b) => b.frequency - a.frequency)
                this.behaviorPatterns.commonSequences = this.behaviorPatterns.commonSequences.slice(0, 50)
            }
        },

        /**
         * 分析搜索模式
         */
        analyzeSearchPattern(searchLog) {
            // 分析搜索词频率
            const searchTerms = this.userPreferences.searchHistory.map(s => s.term.toLowerCase())
            const termFrequency = {}

            searchTerms.forEach(term => {
                termFrequency[term] = (termFrequency[term] || 0) + 1
            })

            // 识别常用搜索词
            const popularSearches = Object.entries(termFrequency).filter(([term, freq]) => freq >= 3).sort((a, b) => b[1] - a[1]).slice(0, 10).map(([term, freq]) => ({
                term,
                frequency: freq
            }))

            // 更新用户偏好
            this.userPreferences.commonSearchTerms = popularSearches
        },

        /**
         * 启动模式分析
         */
        startPatternAnalysis() {
            setInterval(() => {
                this.performPatternAnalysis()
            }, 60000) // 每分钟分析一次
        },

        /**
         * 执行模式分析
         */
        performPatternAnalysis() {
            this.analyzeTimePatterns()
            this.identifyProblemAreas()
            this.updatePopularFeatures()
        },

        /**
         * 分析时间模式
         */
        analyzeTimePatterns() {
            const now = new Date()
            const hour = now.getHours()

            const timePattern = this.behaviorPatterns.timePatterns.find(p => p.hour === hour) || {
                hour,
                actions: 0,
                commonActivities: []
            }

            timePattern.actions++

            // 记录当前时间段的常见活动
            const recentActions = this.behaviorLogs.filter(log => new Date(log.timestamp).getHours() === hour).slice(-10).map(log => log.type)

            timePattern.commonActivities = [...new Set(recentActions)]

            // 更新或添加时间模式
            const existingIndex = this.behaviorPatterns.timePatterns.findIndex(p => p.hour === hour)
            if (existingIndex >= 0) {
                this.behaviorPatterns.timePatterns[existingIndex] = timePattern
            } else {
                this.behaviorPatterns.timePatterns.push(timePattern)
            }
        },

        /**
         * 识别问题区域
         */
        identifyProblemAreas() {
            // 分析错误频发的页面
            const errorPages = this.behaviorLogs.filter(log => log.type === 'error').reduce((acc, log) => {
                acc[log.path] = (acc[log.path] || 0) + 1
                return acc
            }, {})

            // 分析跳出率高的页面
            const shortStayPages = this.visitHistory.filter(visit => visit.stayTime > 0 && visit.stayTime < 5000) // 少于5秒
                .reduce((acc, visit) => {
                    acc[visit.path] = (acc[visit.path] || 0) + 1
                    return acc
                }, {})

            // 合并问题区域
            const problemAreas = []

            Object.entries(errorPages).forEach(([path, errorCount]) => {
                const visitCount = this.getPageVisitCount(path)
                const severity = errorCount / visitCount

                problemAreas.push({
                    path,
                    type: 'high_error_rate',
                    severity,
                    details: {errorCount, visitCount}
                })
            })

            Object.entries(shortStayPages).forEach(([path, bounceCount]) => {
                const visitCount = this.getPageVisitCount(path)
                const bounceRate = bounceCount / visitCount

                if (bounceRate > 0.3) { // 跳出率超过30%
                    problemAreas.push({
                        path,
                        type: 'high_bounce_rate',
                        severity: bounceRate,
                        details: {bounceCount, visitCount, bounceRate}
                    })
                }
            })

            this.uxMetrics.problemAreas = problemAreas
        },

        /**
         * 更新热门功能
         */
        updatePopularFeatures() {
            const featureUsage = this.behaviorLogs.filter(log => log.type === 'feature_usage').reduce((acc, log) => {
                acc[log.target] = (acc[log.target] || 0) + 1
                return acc
            }, {})

            this.uxMetrics.popularFeatures = Object.entries(featureUsage).sort((a, b) => b[1] - a[1]).slice(0, 10).map(([feature, usage]) => ({
                feature,
                usage
            }))
        },

        /**
         * 更新用户体验指标
         */
        updateUXMetrics() {
            // 这个方法在其他分析方法中被调用，用于触发整体UX指标更新
            const totalActions = Object.values(this.interactionStats).reduce((sum, count) => sum + count, 0)

            // 计算参与度评分
            const sessionDuration = Date.now() - this.sessionBehavior.startTime
            const engagementScore = totalActions / (sessionDuration / 60000) // 每分钟操作数

            // 更新UX指标
            this.uxMetrics.engagementScore = engagementScore
        },

        /**
         * 维护历史记录大小
         */
        maintainHistorySize() {
            if (this.visitHistory.length > this.config.maxVisitHistory) {
                this.visitHistory.shift()
            }
        },

        /**
         * 维护日志大小
         */
        maintainLogSize() {
            if (this.behaviorLogs.length > this.config.maxBehaviorLogs) {
                this.behaviorLogs.shift()
            }

            if (this.userPreferences.recentActions.length > this.config.maxRecentActions) {
                this.userPreferences.recentActions.shift()
            }
        },

        /**
         * 清除行为数据
         */
        clearBehaviorData(type = 'all') {
            if (type === 'all' || type === 'logs') {
                this.behaviorLogs = []
                this.visitHistory = []
            }

            if (type === 'all' || type === 'preferences') {
                this.userPreferences = {
                    favoritePages: [],
                    recentActions: [],
                    searchHistory: [],
                    commonPaths: [],
                    preferredFeatures: []
                }
            }

            if (type === 'all' || type === 'stats') {
                this.interactionStats = {
                    clickCount: 0,
                    viewCount: 0,
                    searchCount: 0,
                    navigationCount: 0,
                    errorCount: 0,
                    successCount: 0
                }
            }

            this.recordUserAction({
                type: 'data_cleared',
                target: 'behavior_store',
                details: {type}
            })
        },

        /**
         * 导出行为数据
         */
        exportBehaviorData() {
            return {
                sessionInfo: this.getCurrentSession,
                statistics: this.interactionStats,
                uxMetrics: this.uxMetrics,
                preferences: this.userPreferences,
                patterns: this.behaviorPatterns,
                exportTime: new Date().toISOString()
            }
        },

        /**
         * 更新配置
         */
        updateConfig(newConfig) {
            this.config = {...this.config, ...newConfig}
            this.recordUserAction({
                type: 'config_updated',
                target: 'behavior_store',
                details: {config: newConfig}
            })
        }
    },

    persist: {
        key: 'user-behavior-data',
        storage: localStorage,
        paths: ['userPreferences', 'interactionStats', 'uxMetrics', 'config']
    }
})
