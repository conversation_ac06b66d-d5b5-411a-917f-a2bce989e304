import {defineStore} from 'pinia'

/**
 * 网络状态管理Store
 * 专注于网络连接监控、质量评估和用户体验优化
 * 不包含缓存等功能，专注于实时网络状态反馈
 */
export const useNetworkStore = defineStore('network', {
    state: () => ({
        // === 基础网络状态 ===
        isOnline: navigator.onLine,
        connectionType: 'unknown', // wifi, cellular, ethernet, none, unknown
        effectiveType: 'unknown',  // slow-2g, 2g, 3g, 4g

        // === 网络质量指标 ===
        quality: 'unknown', // excellent, good, fair, poor, unknown
        latency: null,
        downloadSpeed: null,
        uploadSpeed: null,

        // === 连接历史记录 ===
        connectionHistory: [],
        qualityHistory: [],

        // === 网络事件统计 ===
        networkStats: {
            totalDisconnections: 0,
            totalReconnections: 0,
            longestDisconnection: 0,
            averageLatency: 0,
            qualityChanges: 0
        },

        // === 用户体验相关 ===
        userNotifications: {
            showQualityChanges: true,
            showDisconnections: true,
            showSlowConnection: true,
            autoHideDelay: 5000
        },

        // === 自适应功能 ===
        adaptiveSettings: {
            enableDataSaver: false,      // 数据节省模式
            reduceAnimations: false,     // 减少动画
            optimizeImages: false,       // 图片优化
            enableOfflineMode: false     // 离线模式准备
        },

        // === 监控配置 ===
        config: {
            checkInterval: 30000,        // 检测间隔（毫秒）
            timeoutDuration: 5000,       // 超时时间
            testEndpoint: '/api/health', // 测试端点
            maxHistoryItems: 100,        // 最大历史记录
            qualityThresholds: {
                excellent: 100,          // 延迟小于100ms
                good: 300,               // 延迟小于300ms
                fair: 1000,              // 延迟小于1000ms
                poor: 1000               // 延迟大于1000ms
            }
        },

        // === 状态标识 ===
        isMonitoring: false,
        lastCheckTime: null,
        monitoringTimer: null,

        // === 错误处理 ===
        lastError: null,
        errorCount: 0,

        // === 实时状态 ===
        currentTest: {
            isRunning: false,
            startTime: null,
            progress: 0
        }
    }),

    getters: {
        // === 基础状态获取器 ===
        getNetworkStatus: (state) => {
            if (!state.isOnline) return '离线'

            switch (state.quality) {
                case 'excellent':
                    return '网络优秀'
                case 'good':
                    return '网络良好'
                case 'fair':
                    return '网络一般'
                case 'poor':
                    return '网络较差'
                default:
                    return '检测中...'
            }
        },

        getConnectionTypeText: (state) => {
            const types = {
                wifi: 'WiFi',
                cellular: '移动数据',
                ethernet: '有线网络',
                none: '无连接',
                unknown: '未知连接'
            }
            return types[state.connectionType] || '未知'
        },

        // === 网络质量获取器 ===
        isNetworkUsable: (state) => {
            return state.isOnline && state.quality !== 'poor'
        },

        getQualityIcon: (state) => {
            if (!state.isOnline) return 'fas fa-wifi-slash'

            switch (state.quality) {
                case 'excellent':
                    return 'fas fa-wifi text-success'
                case 'good':
                    return 'fas fa-wifi text-info'
                case 'fair':
                    return 'fas fa-wifi text-warning'
                case 'poor':
                    return 'fas fa-wifi text-danger'
                default:
                    return 'fas fa-wifi text-muted'
            }
        },

        getQualityColor: (state) => {
            if (!state.isOnline) return '#dc3545'

            switch (state.quality) {
                case 'excellent':
                    return '#28a745'
                case 'good':
                    return '#17a2b8'
                case 'fair':
                    return '#ffc107'
                case 'poor':
                    return '#dc3545'
                default:
                    return '#6c757d'
            }
        },

        // === 统计数据获取器 ===
        getReliabilityScore: (state) => {
            const total = state.networkStats.totalDisconnections + state.networkStats.totalReconnections
            if (total === 0) return 100

            const disconnectionRate = state.networkStats.totalDisconnections / total
            return Math.max(0, (1 - disconnectionRate) * 100).toFixed(1)
        },

        getAverageLatency: (state) => {
            return state.networkStats.averageLatency || 0
        },

        // === 历史数据获取器 ===
        getRecentQualityTrend: (state) => {
            return state.qualityHistory.slice(-10).map(item => ({
                quality: item.quality,
                timestamp: item.timestamp,
                latency: item.latency
            }))
        },

        getConnectionUptime: (state) => {
            const recentHistory = state.connectionHistory.slice(-20)
            const onlineCount = recentHistory.filter(h => h.type === 'online').length
            return recentHistory.length > 0 ? (onlineCount / recentHistory.length * 100).toFixed(1) : 100
        },

        // === 用户建议获取器 ===
        getNetworkAdvice: (state) => {
            if (!state.isOnline) {
                return '网络连接已断开，请检查网络设置或稍后再试'
            }

            switch (state.quality) {
                case 'poor':
                    return '网络质量较差，建议：1）刷新页面重试 2）检查网络环境 3）联系网络服务商'
                case 'fair':
                    return '网络质量一般，可能会影响使用体验，建议减少并发操作'
                case 'good':
                    return '网络连接良好，可以正常使用'
                case 'excellent':
                    return '网络连接优秀，享受最佳体验'
                default:
                    return '正在检测网络质量，请稍候...'
            }
        },

        // === 自适应建议获取器 ===
        getAdaptiveSuggestions: (state) => {
            const suggestions = []

            if (state.quality === 'poor' || state.quality === 'fair') {
                if (!state.adaptiveSettings.enableDataSaver) {
                    suggestions.push({
                        type: 'data_saver',
                        title: '启用数据节省模式',
                        description: '减少数据传输，提升加载速度'
                    })
                }

                if (!state.adaptiveSettings.reduceAnimations) {
                    suggestions.push({
                        type: 'reduce_animations',
                        title: '减少动画效果',
                        description: '降低资源消耗，提升响应速度'
                    })
                }
            }

            return suggestions
        },

        // === 状态检查获取器 ===
        shouldShowNetworkWarning: (state) => {
            return !state.isOnline || state.quality === 'poor'
        },

        isTestingConnection: (state) => {
            return state.currentTest.isRunning
        }
    },

    actions: {
        /**
         * 初始化网络监听
         */
        initNetworkMonitoring() {
            console.log('🌐 开始初始化网络监控...')

            // 监听在线/离线事件
            window.addEventListener('online', this.handleOnline)
            window.addEventListener('offline', this.handleOffline)

            // 监听连接变化
            if ('connection' in navigator) {
                navigator.connection.addEventListener('change', this.handleConnectionChange)
                this.updateConnectionInfo()
            }

            // 开始定期检测网络质量
            this.startQualityMonitoring()

            // 初始化检测
            this.checkNetworkQuality()

            this.isMonitoring = true
            console.log('✅ 网络监控已启动')
        },

        /**
         * 停止网络监听
         */
        stopNetworkMonitoring() {
            console.log('🛑 停止网络监控...')

            window.removeEventListener('online', this.handleOnline)
            window.removeEventListener('offline', this.handleOffline)

            if ('connection' in navigator) {
                navigator.connection.removeEventListener('change', this.handleConnectionChange)
            }

            if (this.monitoringTimer) {
                clearInterval(this.monitoringTimer)
                this.monitoringTimer = null
            }

            this.isMonitoring = false
        },

        /**
         * 处理上线事件
         */
        handleOnline() {
            console.log('🟢 网络已连接')

            this.isOnline = true
            this.networkStats.totalReconnections++

            this.recordConnectionEvent('online', {
                message: '网络连接已恢复'
            })

            // 立即检测网络质量
            this.checkNetworkQuality()

            // 触发网络恢复事件
            this.emitNetworkEvent('network:online')
        },

        /**
         * 处理离线事件
         */
        handleOffline() {
            console.log('🔴 网络已断开')

            const disconnectionStart = Date.now()
            this.isOnline = false
            this.quality = 'unknown'
            this.networkStats.totalDisconnections++

            this.recordConnectionEvent('offline', {
                message: '网络连接已断开',
                disconnectionStart
            })

            // 更新最长断线时间
            this.updateLongestDisconnection(disconnectionStart)

            // 触发网络断开事件
            this.emitNetworkEvent('network:offline')
        },

        /**
         * 更新最长断线时间
         */
        updateLongestDisconnection(disconnectionStart) {
            // 这个方法会在重新连接时被调用来计算断线时长
            const checkDisconnection = () => {
                if (!this.isOnline) {
                    setTimeout(checkDisconnection, 1000)
                } else {
                    const disconnectionDuration = Date.now() - disconnectionStart
                    if (disconnectionDuration > this.networkStats.longestDisconnection) {
                        this.networkStats.longestDisconnection = disconnectionDuration
                    }
                }
            }
            checkDisconnection()
        },

        /**
         * 处理连接变化
         */
        handleConnectionChange() {
            console.log('🔄 网络连接类型已变化')

            this.updateConnectionInfo()
            this.checkNetworkQuality()

            this.recordConnectionEvent('connection_change', {
                connectionType: this.connectionType,
                effectiveType: this.effectiveType
            })
        },

        /**
         * 更新连接信息
         */
        updateConnectionInfo() {
            if ('connection' in navigator) {
                const connection = navigator.connection
                this.connectionType = connection.type || 'unknown'
                this.effectiveType = connection.effectiveType || 'unknown'

                // 根据连接类型预估质量
                this.estimateQualityFromConnection()
            }
        },

        /**
         * 根据连接类型预估质量
         */
        estimateQualityFromConnection() {
            if (!this.isOnline) return

            const effectiveTypeMap = {
                'slow-2g': 'poor',
                '2g': 'poor',
                '3g': 'fair',
                '4g': 'good'
            }

            const estimatedQuality = effectiveTypeMap[this.effectiveType]
            if (estimatedQuality && estimatedQuality !== this.quality) {
                this.quality = estimatedQuality
                this.recordQualityChange(estimatedQuality, 'connection_based')
            }
        },

        /**
         * 开始质量监控
         */
        startQualityMonitoring() {
            if (this.monitoringTimer) {
                clearInterval(this.monitoringTimer)
            }

            this.monitoringTimer = setInterval(() => {
                if (this.isOnline && !this.currentTest.isRunning) {
                    this.checkNetworkQuality()
                }
            }, this.config.checkInterval)
        },

        /**
         * 检测网络质量
         */
        async checkNetworkQuality() {
            if (!this.isOnline || this.currentTest.isRunning) return

            try {
                this.currentTest.isRunning = true
                this.currentTest.startTime = Date.now()
                this.currentTest.progress = 0

                const startTime = performance.now()

                // 模拟进度更新
                const progressInterval = setInterval(() => {
                    if (this.currentTest.progress < 90) {
                        this.currentTest.progress += 10
                    }
                }, 100)

                // 发送测试请求
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeoutDuration)

                const response = await fetch(this.config.testEndpoint, {
                    method: 'HEAD',
                    cache: 'no-cache',
                    signal: controller.signal
                })

                clearTimeout(timeoutId)
                clearInterval(progressInterval)

                const endTime = performance.now()
                const latency = Math.round(endTime - startTime)

                this.latency = latency
                this.lastCheckTime = Date.now()
                this.errorCount = 0 // 重置错误计数

                this.evaluateQuality(latency)
                this.updateAverageLatency(latency)
                this.recordQualityChange(this.quality, 'latency_test', {latency})

                this.currentTest.progress = 100

            } catch (error) {
                this.handleTestError(error)
            } finally {
                // 延迟重置测试状态，让用户看到完成状态
                setTimeout(() => {
                    this.currentTest.isRunning = false
                    this.currentTest.progress = 0
                }, 500)
            }
        },

        /**
         * 处理测试错误
         */
        handleTestError(error) {
            this.errorCount++
            this.lastError = error.message

            console.warn('网络质量检测失败:', error)

            // 根据错误类型判断网络质量
            if (error.name === 'AbortError') {
                this.quality = 'poor'
                this.latency = this.config.timeoutDuration
            } else {
                this.quality = 'unknown'
                this.latency = null
            }

            this.recordConnectionEvent('test_error', {
                error: error.message,
                errorCount: this.errorCount
            })
        },

        /**
         * 评估网络质量
         */
        evaluateQuality(latency) {
            const thresholds = this.config.qualityThresholds
            let newQuality

            if (latency < thresholds.excellent) {
                newQuality = 'excellent'
            } else if (latency < thresholds.good) {
                newQuality = 'good'
            } else if (latency < thresholds.fair) {
                newQuality = 'fair'
            } else {
                newQuality = 'poor'
            }

            if (newQuality !== this.quality) {
                this.networkStats.qualityChanges++
                this.quality = newQuality
            }
        },

        /**
         * 更新平均延迟
         */
        updateAverageLatency(latency) {
            const recentLatencies = this.qualityHistory.slice(-10).map(h => h.latency).filter(l => l != null)

            recentLatencies.push(latency)

            this.networkStats.averageLatency = Math.round(
                recentLatencies.reduce((sum, l) => sum + l, 0) / recentLatencies.length
            )
        },

        /**
         * 记录连接事件
         */
        recordConnectionEvent(type, details = {}) {
            const event = {
                type,
                timestamp: new Date().toISOString(),
                connectionType: this.connectionType,
                effectiveType: this.effectiveType,
                quality: this.quality,
                latency: this.latency,
                details,
                id: Date.now() + Math.random()
            }

            this.connectionHistory.unshift(event)

            // 保持历史记录在合理范围内
            if (this.connectionHistory.length > this.config.maxHistoryItems) {
                this.connectionHistory.pop()
            }
        },

        /**
         * 记录质量变化
         */
        recordQualityChange(quality, source, details = {}) {
            const qualityEvent = {
                quality,
                source,
                timestamp: new Date().toISOString(),
                latency: this.latency,
                connectionType: this.connectionType,
                details,
                id: Date.now() + Math.random()
            }

            this.qualityHistory.unshift(qualityEvent)

            // 保持质量历史在合理范围内
            if (this.qualityHistory.length > this.config.maxHistoryItems) {
                this.qualityHistory.pop()
            }
        },

        /**
         * 触发网络事件
         */
        emitNetworkEvent(eventType, data = {}) {
            const event = new CustomEvent(eventType, {
                detail: {
                    isOnline: this.isOnline,
                    quality: this.quality,
                    latency: this.latency,
                    connectionType: this.connectionType,
                    timestamp: Date.now(),
                    ...data
                }
            })
            window.dispatchEvent(event)
        },

        /**
         * 手动测试网络
         */
        async manualNetworkTest() {
            if (this.currentTest.isRunning) {
                console.log('⏳ 网络测试正在进行中...')
                return
            }

            console.log('🧪 开始手动网络测试...')
            await this.checkNetworkQuality()

            return {
                isOnline: this.isOnline,
                quality: this.quality,
                latency: this.latency,
                connectionType: this.connectionType,
                timestamp: Date.now()
            }
        },

        /**
         * 应用自适应设置
         */
        applyAdaptiveSetting(settingType, enabled = true) {
            if (settingType in this.adaptiveSettings) {
                this.adaptiveSettings[settingType] = enabled

                // 触发设置变化事件
                this.emitNetworkEvent('network:adaptive_setting_changed', {
                    settingType,
                    enabled,
                    settings: this.adaptiveSettings
                })

                this.recordConnectionEvent('adaptive_setting_changed', {
                    settingType,
                    enabled
                })

                console.log(`🔧 ${enabled ? '启用' : '禁用'}了 ${settingType}`)
            }
        },

        /**
         * 获取网络诊断信息
         */
        getNetworkDiagnostics() {
            return {
                // 基础状态
                basicStatus: {
                    isOnline: this.isOnline,
                    quality: this.quality,
                    latency: this.latency,
                    connectionType: this.connectionType,
                    effectiveType: this.effectiveType
                },

                // 统计数据
                statistics: {...this.networkStats},

                // 可靠性指标
                reliability: {
                    uptime: this.getConnectionUptime,
                    reliabilityScore: this.getReliabilityScore,
                    averageLatency: this.getAverageLatency
                },

                // 最近事件
                recentEvents: this.connectionHistory.slice(0, 10),

                // 质量趋势
                qualityTrend: this.getRecentQualityTrend,

                // 配置信息
                configuration: {...this.config},

                // 自适应设置
                adaptiveSettings: {...this.adaptiveSettings},

                // 诊断时间
                diagnosticTime: new Date().toISOString()
            }
        },

        /**
         * 更新配置
         */
        updateConfig(newConfig) {
            this.config = {...this.config, ...newConfig}

            // 如果更新了检测间隔，重启监控
            if (newConfig.checkInterval && this.isMonitoring) {
                this.startQualityMonitoring()
            }

            this.recordConnectionEvent('config_updated', {config: newConfig})
        },

        /**
         * 更新用户通知设置
         */
        updateNotificationSettings(settings) {
            this.userNotifications = {...this.userNotifications, ...settings}
            this.recordConnectionEvent('notification_settings_updated', {settings})
        },

        /**
         * 重置网络统计
         */
        resetNetworkStats() {
            this.networkStats = {
                totalDisconnections: 0,
                totalReconnections: 0,
                longestDisconnection: 0,
                averageLatency: 0,
                qualityChanges: 0
            }

            this.connectionHistory = []
            this.qualityHistory = []
            this.errorCount = 0
            this.lastError = null

            this.recordConnectionEvent('stats_reset')
            console.log('📊 网络统计已重置')
        },

        /**
         * 清理资源
         */
        cleanup() {
            this.stopNetworkMonitoring()
            console.log('🧹 网络监控资源已清理')
        }
    },

    persist: {
        key: 'network-state',
        storage: localStorage,
        paths: ['networkStats', 'userNotifications', 'adaptiveSettings', 'config']
    }
})
