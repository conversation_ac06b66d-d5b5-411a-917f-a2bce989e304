import {defineStore} from 'pinia'

/**
 * 用户设置管理
 * 用于管理用户偏好设置，诸如语言、主题等
 */
export const useSettingsStore = defineStore('settings', {
    state: () => ({
        // 界面设置
        ui: {
            showBreadcrumb: true,     // 显示面包屑
            showTabs: true,           // 显示标签页
            showFooter: true,         // 显示页脚
            showLogo: true,           // 显示Logo
            fixedHeader: true,        // 固定头部
            sidebarLogo: true,        // 侧边栏Logo
            multiTab: true,           // 多标签页
            keepAlive: true           // 页面缓存
        },

        // 功能设置
        features: {
            enableNotification: true,  // 启用通知
            enableSound: true,         // 启用声音
            enableAnimation: true,     // 启用动画
            enableWatermark: false,    // 启用水印
            enableFullscreen: true,    // 启用全屏
            enableShortcut: true,      // 启用快捷键
            enableAutoSave: true,      // 启用自动保存
            enableDragSort: true       // 启用拖拽排序
        },

        // 表格设置
        table: {
            pageSize: 20,             // 每页显示数量
            showIndex: true,          // 显示序号
            showSelection: true,      // 显示选择框
            stripe: true,             // 斑马纹
            border: true,             // 边框
            size: 'default',          // 尺寸: large, default, small
            showPagination: true,     // 显示分页
            paginationLayout: 'total, sizes, prev, pager, next, jumper'
        },

        // 表单设置
        form: {
            labelWidth: '120px',      // 标签宽度
            labelPosition: 'right',   // 标签位置: left, right, top
            size: 'default',          // 尺寸: large, default, small
            validateOnRuleChange: true, // 规则改变时验证
            hideRequiredAsterisk: false, // 隐藏必填星号
            showMessage: true,        // 显示错误信息
            inlineMessage: false      // 行内显示错误信息
        },

        // 开发者设置
        developer: {
            showConsoleLog: false,    // 显示控制台日志
            enableDebugMode: false,   // 启用调试模式
            showPerformance: false,   // 显示性能监控
            enableMock: false,        // 启用Mock数据
            showApiLog: false,        // 显示API日志
            enableHotReload: true     // 启用热重载
        },

        // 安全设置
        security: {
            sessionTimeout: 30,       // 会话超时时间（分钟）
            autoLock: false,         // 自动锁屏
            lockTimeout: 15,         // 锁屏超时时间（分钟）
            enableTwoFactor: false,  // 启用双因子认证
            rememberLogin: true,     // 记住登录状态
            maxLoginAttempts: 5      // 最大登录尝试次数
        },

        // 个人偏好
        preferences: {
            startPage: '/dashboard',  // 起始页面
            itemsPerPage: [10, 20, 50, 100], // 分页选项
            dateFormat: 'YYYY-MM-DD', // 日期格式
            timeFormat: 'HH:mm:ss',   // 时间格式
            numberFormat: 'en-US',    // 数字格式
            currency: 'CNY',          // 货币单位
            timezone: 'Asia/Shanghai' // 时区
        },

        // 快捷键设置
        shortcuts: {
            search: 'Ctrl+K',        // 搜索
            refresh: 'F5',           // 刷新
            fullscreen: 'F11',       // 全屏
            save: 'Ctrl+S',          // 保存
            close: 'Ctrl+W',         // 关闭
            newTab: 'Ctrl+T',        // 新标签
            closeTab: 'Ctrl+W',      // 关闭标签
            prevTab: 'Ctrl+Shift+Tab', // 上一个标签
            nextTab: 'Ctrl+Tab'      // 下一个标签
        }
    }),

    getters: {
        // 获取界面设置
        getUiSettings: (state) => state.ui,

        // 获取功能设置
        getFeatureSettings: (state) => state.features,

        // 获取表格设置
        getTableSettings: (state) => state.table,

        // 获取表单设置
        getFormSettings: (state) => state.form,

        // 获取开发者设置
        getDeveloperSettings: (state) => state.developer,

        // 获取安全设置
        getSecuritySettings: (state) => state.security,

        // 获取个人偏好
        getPreferences: (state) => state.preferences,

        // 获取快捷键设置
        getShortcuts: (state) => state.shortcuts,

        // 检查功能是否启用
        isFeatureEnabled: (state) => (feature) => {
            return state.features[feature] || false
        }
    },

    actions: {
        /**
         * 更新界面设置
         */
        updateUiSettings(settings) {
            this.ui = {...this.ui, ...settings}
        },

        /**
         * 更新功能设置
         */
        updateFeatureSettings(settings) {
            this.features = {...this.features, ...settings}
        },

        /**
         * 更新表格设置
         */
        updateTableSettings(settings) {
            this.table = {...this.table, ...settings}
        },

        /**
         * 更新表单设置
         */
        updateFormSettings(settings) {
            this.form = {...this.form, ...settings}
        },

        /**
         * 更新开发者设置
         */
        updateDeveloperSettings(settings) {
            this.developer = {...this.developer, ...settings}
        },

        /**
         * 更新安全设置
         */
        updateSecuritySettings(settings) {
            this.security = {...this.security, ...settings}
        },

        /**
         * 更新个人偏好
         */
        updatePreferences(preferences) {
            this.preferences = {...this.preferences, ...preferences}
        },

        /**
         * 更新快捷键设置
         */
        updateShortcuts(shortcuts) {
            this.shortcuts = {...this.shortcuts, ...shortcuts}
        },

        /**
         * 切换功能开关
         */
        toggleFeature(feature) {
            if (this.features.hasOwnProperty(feature)) {
                this.features[feature] = !this.features[feature]
            }
        },

        /**
         * 切换界面功能
         */
        toggleUiFeature(feature) {
            if (this.ui.hasOwnProperty(feature)) {
                this.ui[feature] = !this.ui[feature]
            }
        },

        /**
         * 重置所有设置
         */
        resetAllSettings() {
            this.ui = {
                showBreadcrumb: true,
                showTabs: true,
                showFooter: true,
                showLogo: true,
                fixedHeader: true,
                sidebarLogo: true,
                multiTab: true,
                keepAlive: true
            }

            this.features = {
                enableNotification: true,
                enableSound: true,
                enableAnimation: true,
                enableWatermark: false,
                enableFullscreen: true,
                enableShortcut: true,
                enableAutoSave: true,
                enableDragSort: true
            }

            this.table = {
                pageSize: 20,
                showIndex: true,
                showSelection: true,
                stripe: true,
                border: true,
                size: 'default',
                showPagination: true,
                paginationLayout: 'total, sizes, prev, pager, next, jumper'
            }

            this.form = {
                labelWidth: '120px',
                labelPosition: 'right',
                size: 'default',
                validateOnRuleChange: true,
                hideRequiredAsterisk: false,
                showMessage: true,
                inlineMessage: false
            }
        },

        /**
         * 导出设置
         */
        exportSettings() {
            const settings = {
                ui: this.ui,
                features: this.features,
                table: this.table,
                form: this.form,
                developer: this.developer,
                security: this.security,
                preferences: this.preferences,
                shortcuts: this.shortcuts,
                exportTime: new Date().toISOString()
            }

            return JSON.stringify(settings, null, 2)
        },

        /**
         * 导入设置
         */
        importSettings(settingsJson) {
            try {
                const settings = JSON.parse(settingsJson)

                if (settings.ui) this.ui = {...this.ui, ...settings.ui}
                if (settings.features) this.features = {...this.features, ...settings.features}
                if (settings.table) this.table = {...this.table, ...settings.table}
                if (settings.form) this.form = {...this.form, ...settings.form}
                if (settings.developer) this.developer = {...this.developer, ...settings.developer}
                if (settings.security) this.security = {...this.security, ...settings.security}
                if (settings.preferences) this.preferences = {...this.preferences, ...settings.preferences}
                if (settings.shortcuts) this.shortcuts = {...this.shortcuts, ...settings.shortcuts}

                return true
            } catch (error) {
                console.error('导入设置失败:', error)
                return false
            }
        }
    },

    persist: {
        key: 'user-settings',
        storage: localStorage
    }
}) 