import {defineStore} from 'pinia'
import {useNotificationStore} from './useNotificationStore'

/**
 * 严重通知管理
 * 管理紧急的、需要用户立即注意的通知（如系统崩溃）
 */
export const useCriticalNotificationStore = defineStore('criticalNotification', {
    state: () => ({
        // 严重通知列表
        criticalNotifications: [],
        // 严重通知配置
        config: {
            persistent: true,        // 持久显示，不自动消失
            requireConfirmation: true, // 需要用户确认
            blockUI: false,          // 是否阻塞UI
            playAlarm: true,         // 播放警报声
            flashScreen: false       // 闪烁屏幕
        }
    }),

    getters: {
        // 获取未处理的严重通知
        getPendingCriticalNotifications: (state) => {
            return state.criticalNotifications.filter(n => !n.confirmed)
        },

        // 获取严重通知数量
        getCriticalCount: (state) => {
            return state.criticalNotifications.filter(n => !n.confirmed).length
        }
    },

    actions: {
        /**
         * 添加严重通知
         */
        addCriticalNotification(notification) {
            const notificationStore = useNotificationStore()

            const criticalNotification = {
                id: Date.now() + Math.random(),
                level: 'critical',
                title: notification.title || '严重警告',
                message: notification.message,
                timestamp: new Date().toISOString(),
                confirmed: false,
                source: notification.source || 'system',
                actionRequired: notification.actionRequired || false,
                ...notification
            }

            this.criticalNotifications.push(criticalNotification)

            // 通过主通知系统显示
            notificationStore.addNotification({
                type: 'error',
                title: criticalNotification.title,
                message: criticalNotification.message,
                duration: this.config.persistent ? 0 : 10000,
                priority: 'critical'
            })

            // 播放警报声（如果启用）
            if (this.config.playAlarm) {
                this.playAlarmSound()
            }

            return criticalNotification.id
        },

        /**
         * 确认严重通知
         */
        confirmCriticalNotification(id) {
            const notification = this.criticalNotifications.find(n => n.id === id)
            if (notification) {
                notification.confirmed = true
                notification.confirmedAt = new Date().toISOString()
            }
        },

        /**
         * 播放警报声
         */
        playAlarmSound() {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance('系统严重警告')
                utterance.rate = 0.8
                utterance.pitch = 1.2
                speechSynthesis.speak(utterance)
            }
        },

        /**
         * 清除已确认的严重通知
         */
        clearConfirmedNotifications() {
            this.criticalNotifications = this.criticalNotifications.filter(n => !n.confirmed)
        }
    }
})
