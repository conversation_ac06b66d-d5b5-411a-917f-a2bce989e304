import {defineStore} from 'pinia'
import {useNotificationStore} from './useNotificationStore'

/**
 * 一般通知管理
 * 管理一般通知（如操作成功、系统反馈等）
 */
export const useNormalNotificationStore = defineStore('normalNotification', {
    state: () => ({
        // 一般通知列表
        normalNotifications: [],
        // 一般通知配置
        config: {
            duration: 4500,          // 显示时长
            autoClose: true,         // 自动关闭
            showProgress: true,      // 显示进度条
            enableSound: false       // 启用声音
        }
    }),

    getters: {
        // 获取最近的一般通知
        getRecentNormalNotifications: (state) => (limit = 10) => {
            return state.normalNotifications.slice(0, limit)
        }
    },

    actions: {
        /**
         * 添加一般通知
         */
        addNormalNotification(notification) {
            const notificationStore = useNotificationStore()

            const normalNotification = {
                id: Date.now() + Math.random(),
                level: 'normal',
                title: notification.title || '系统通知',
                message: notification.message,
                timestamp: new Date().toISOString(),
                category: notification.category || 'general',
                ...notification
            }

            this.normalNotifications.unshift(normalNotification)

            // 保持数量在合理范围内
            if (this.normalNotifications.length > 50) {
                this.normalNotifications.pop()
            }

            // 通过主通知系统显示
            notificationStore.addNotification({
                type: notification.type || 'success',
                title: normalNotification.title,
                message: normalNotification.message,
                duration: this.config.duration,
                priority: 'normal'
            })

            return normalNotification.id
        },

        /**
         * 清除一般通知历史
         */
        clearNormalNotifications() {
            this.normalNotifications = []
        }
    }
})
