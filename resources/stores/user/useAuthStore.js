import {defineStore} from 'pinia'

/**
 * 用户认证管理
 * 管理用户登录状态、权限、会话等认证相关功能
 */
export const useAuthStore = defineStore('auth', {
    state: () => ({
        // === 认证状态 ===
        isAuthenticated: false,
        token: null,
        refreshToken: null,
        tokenExpiry: null,

        // === 用户信息 ===
        user: {
            id: null,
            username: '',
            nickname: '',
            email: '',
            phone: '',
            avatar: '',
            status: 'active'
        },

        // === 权限和角色 ===
        permissions: [],
        roles: [],

        // === 登录配置 ===
        rememberMe: false,
        loginTime: null,
        lastActiveTime: null,

        // === 会话管理 ===
        sessionTimeout: 30 * 60 * 1000, // 30分钟
        sessionTimer: null,

        // === 双因子认证 ===
        twoFactorEnabled: false,
        twoFactorVerified: false,

        // === 登录历史 ===
        loginHistory: [],
        maxHistoryCount: 10
    }),

    getters: {
        // 检查是否已认证
        isLoggedIn: (state) => state.isAuthenticated && !!state.token,

        // 获取用户显示名称
        displayName: (state) => state.user.nickname || state.user.username || '未知用户',

        // 获取用户头像
        userAvatar: (state) => state.user.avatar || '/default-avatar.png',

        // 检查token是否过期
        isTokenExpired: (state) => {
            if (!state.tokenExpiry) return false
            return Date.now() > state.tokenExpiry
        },

        // 检查是否需要刷新token
        needsTokenRefresh: (state) => {
            if (!state.tokenExpiry) return false
            // 提前5分钟刷新token
            return Date.now() > (state.tokenExpiry - 5 * 60 * 1000)
        },

        // 获取用户完整信息
        userInfo: (state) => ({
            ...state.user,
            displayName: state.user.nickname || state.user.username || '未知用户',
            avatarUrl: state.user.avatar || '/default-avatar.png'
        }),

        // 检查是否有特定权限
        hasPermission: (state) => (permission) => {
            if (!state.permissions || state.permissions.length === 0) return false
            return state.permissions.includes(permission)
        },

        // 检查是否有特定角色
        hasRole: (state) => (role) => {
            if (!state.roles || state.roles.length === 0) return false
            return state.roles.includes(role)
        },

        // 检查是否有任一权限
        hasAnyPermission: (state) => (permissions) => {
            if (!state.permissions || state.permissions.length === 0) return false
            return permissions.some(permission => state.permissions.includes(permission))
        },

        // 检查是否有任一角色
        hasAnyRole: (state) => (roles) => {
            if (!state.roles || state.roles.length === 0) return false
            return roles.some(role => state.roles.includes(role))
        },

        // 获取会话剩余时间
        sessionTimeRemaining: (state) => {
            if (!state.lastActiveTime) return 0
            const elapsed = Date.now() - state.lastActiveTime
            return Math.max(0, state.sessionTimeout - elapsed)
        },

        // 检查会话是否即将过期
        isSessionExpiring: (state) => {
            const remaining = state.sessionTimeout - (Date.now() - (state.lastActiveTime || 0))
            return remaining < 5 * 60 * 1000 // 少于5分钟
        }
    },

    actions: {
        /**
         * 设置认证数据
         * @param {Object} authData - 认证数据
         */
        setAuthData(authData) {
            const {token, refreshToken, user, permissions = [], roles = []} = authData

            this.isAuthenticated = true
            this.token = token
            this.refreshToken = refreshToken || null
            this.user = {...this.user, ...user}
            this.permissions = permissions
            this.roles = roles
            this.loginTime = Date.now()
            this.lastActiveTime = Date.now()

            // 设置token过期时间（默认24小时）
            this.tokenExpiry = Date.now() + 24 * 60 * 60 * 1000

            // 记录登录历史
            this.addLoginHistory({
                loginTime: this.loginTime,
                userAgent: navigator.userAgent,
                ip: 'unknown' // 实际项目中可以从后端获取
            })

            // 启动会话管理
            this.startSessionManagement()
        },

        /**
         * 设置记住我选项
         * @param {Boolean} remember - 是否记住登录状态
         */
        setRememberMe(remember) {
            this.rememberMe = remember
        },

        /**
         * 更新用户信息
         * @param {Object} userInfo - 用户信息
         */
        updateUserInfo(userInfo) {
            this.user = {...this.user, ...userInfo}
        },

        /**
         * 更新权限
         * @param {Array} permissions - 权限列表
         */
        updatePermissions(permissions) {
            this.permissions = permissions || []
        },

        /**
         * 更新角色
         * @param {Array} roles - 角色列表
         */
        updateRoles(roles) {
            this.roles = roles || []
        },

        /**
         * 刷新token
         * @param {String} newToken - 新的token
         * @param {String} newRefreshToken - 新的刷新token
         */
        refreshAuthToken(newToken, newRefreshToken = null) {
            this.token = newToken
            if (newRefreshToken) {
                this.refreshToken = newRefreshToken
            }
            this.tokenExpiry = Date.now() + 24 * 60 * 60 * 1000
            this.lastActiveTime = Date.now()
        },

        /**
         * 更新最后活跃时间
         */
        updateLastActiveTime() {
            this.lastActiveTime = Date.now()
        },

        /**
         * 启动会话管理
         */
        startSessionManagement() {
            // 清除之前的定时器
            if (this.sessionTimer) {
                clearInterval(this.sessionTimer)
            }

            // 每分钟检查一次会话状态
            this.sessionTimer = setInterval(() => {
                const now = Date.now()
                const elapsed = now - (this.lastActiveTime || 0)

                // 如果超过会话超时时间，自动登出
                if (elapsed > this.sessionTimeout) {
                    this.logout('会话已过期，请重新登录')
                }
            }, 60 * 1000)
        },

        /**
         * 停止会话管理
         */
        stopSessionManagement() {
            if (this.sessionTimer) {
                clearInterval(this.sessionTimer)
                this.sessionTimer = null
            }
        },

        /**
         * 添加登录历史记录
         * @param {Object} record - 登录记录
         */
        addLoginHistory(record) {
            this.loginHistory.unshift(record)

            // 保持历史记录数量限制
            if (this.loginHistory.length > this.maxHistoryCount) {
                this.loginHistory = this.loginHistory.slice(0, this.maxHistoryCount)
            }
        },

        /**
         * 设置双因子认证状态
         * @param {Boolean} enabled - 是否启用双因子认证
         * @param {Boolean} verified - 是否已验证
         */
        setTwoFactorAuth(enabled, verified = false) {
            this.twoFactorEnabled = enabled
            this.twoFactorVerified = verified
        },

        /**
         * 登出
         * @param {String} reason - 登出原因
         */
        logout(reason = '用户主动登出') {
            // 停止会话管理
            this.stopSessionManagement()

            // 清除认证状态
            this.isAuthenticated = false
            this.token = null
            this.refreshToken = null
            this.tokenExpiry = null

            // 清除用户信息
            this.user = {
                id: null,
                username: '',
                nickname: '',
                email: '',
                phone: '',
                avatar: '',
                status: 'active'
            }

            // 清除权限和角色
            this.permissions = []
            this.roles = []

            // 清除会话信息
            this.loginTime = null
            this.lastActiveTime = null

            // 清除双因子认证状态
            this.twoFactorEnabled = false
            this.twoFactorVerified = false

            // 如果不是记住我，清除所有数据
            if (!this.rememberMe) {
                this.loginHistory = []
            }

            console.log('用户登出:', reason)
        },

        /**
         * 清除所有数据
         */
        clearAllData() {
            this.logout()
            this.rememberMe = false
            this.loginHistory = []
            this.sessionTimeout = 30 * 60 * 1000
        },

        /**
         * 检查并刷新token
         * @returns {Boolean} 是否需要重新登录
         */
        async checkAndRefreshToken() {
            // 如果token已过期
            if (this.isTokenExpired) {
                if (this.refreshToken) {
                    try {
                        // 这里应该调用刷新token的API
                        // const result = await authService.refreshToken(this.refreshToken)
                        // this.refreshAuthToken(result.token, result.refreshToken)
                        console.log('Token已过期，需要刷新')
                        return false // 需要重新登录
                    } catch (error) {
                        console.error('刷新token失败:', error)
                        this.logout('Token刷新失败')
                        return false
                    }
                } else {
                    this.logout('Token已过期')
                    return false
                }
            }

            // 如果需要刷新token
            if (this.needsTokenRefresh) {
                try {
                    // 这里应该调用刷新token的API
                    console.log('Token即将过期，建议刷新')
                } catch (error) {
                    console.error('预刷新token失败:', error)
                }
            }

            return true
        },

        /**
         * 验证当前认证状态
         * @returns {Boolean} 认证状态是否有效
         */
        validateAuthState() {
            if (!this.isAuthenticated || !this.token) {
                return false
            }

            if (this.isTokenExpired) {
                this.logout('Token已过期')
                return false
            }

            // 更新最后活跃时间
            this.updateLastActiveTime()

            return true
        },

        /**
         * 获取认证头部
         * @returns {Object} 认证头部对象
         */
        getAuthHeaders() {
            if (!this.token) {
                return {}
            }

            return {
                'Authorization': `Bearer ${this.token}`,
                'X-Requested-With': 'XMLHttpRequest'
            }
        },

        /**
         * 初始化认证状态
         * 从持久化存储中恢复认证状态
         */
        initializeAuth() {
            // 如果有token但已过期，清除认证状态
            if (this.token && this.isTokenExpired) {
                this.logout('Token已过期')
                return
            }

            // 如果有有效的认证状态，启动会话管理
            if (this.isAuthenticated && this.token) {
                this.startSessionManagement()
                this.updateLastActiveTime()
            }
        }
    },

    // 持久化配置
    persist: {
        key: 'auth-store',
        storage: localStorage,
        paths: [
            'isAuthenticated',
            'token',
            'refreshToken',
            'tokenExpiry',
            'user',
            'permissions',
            'roles',
            'rememberMe',
            'loginTime',
            'twoFactorEnabled',
            'loginHistory'
        ]
    }
})
