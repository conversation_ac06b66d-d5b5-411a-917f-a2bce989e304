/**
 * 本地存储工具类
 * 提供统一的本地存储接口，支持自动序列化和反序列化
 */

const STORAGE_KEYS = {
    TOKEN: 'admin_token',
    USER_INFO: 'admin_user_info',
    REFRESH_TOKEN: 'admin_refresh_token',
    LOGIN_REMEMBER: 'admin_login_remember',
    THEME_SETTINGS: 'admin_theme_settings',
    LAYOUT_SETTINGS: 'admin_layout_settings'
}

export const storageUtils = {
    // === Token 相关 ===
    getToken() {
        try {
            return localStorage.getItem(STORAGE_KEYS.TOKEN) ||
                sessionStorage.getItem(STORAGE_KEYS.TOKEN)
        } catch (error) {
            console.error('获取token失败:', error)
            return null
        }
    },

    setToken(token, remember = true) {
        try {
            if (remember) {
                localStorage.setItem(STORAGE_KEYS.TOKEN, token)
                sessionStorage.removeItem(STORAGE_KEYS.TOKEN)
            } else {
                sessionStorage.setItem(STORAGE_KEYS.TOKEN, token)
                localStorage.removeItem(STORAGE_KEYS.TOKEN)
            }
        } catch (error) {
            console.error('保存token失败:', error)
        }
    },

    removeToken() {
        try {
            localStorage.removeItem(STORAGE_KEYS.TOKEN)
            sessionStorage.removeItem(STORAGE_KEYS.TOKEN)
        } catch (error) {
            console.error('清除token失败:', error)
        }
    },

    // === 刷新Token 相关 ===
    getRefreshToken() {
        try {
            return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
        } catch (error) {
            console.error('获取refresh token失败:', error)
            return null
        }
    },

    setRefreshToken(refreshToken) {
        try {
            localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken)
        } catch (error) {
            console.error('保存refresh token失败:', error)
        }
    },

    removeRefreshToken() {
        try {
            localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
        } catch (error) {
            console.error('清除refresh token失败:', error)
        }
    },

    // === 用户信息 ===
    getUserInfo() {
        try {
            const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO) ||
                sessionStorage.getItem(STORAGE_KEYS.USER_INFO)
            return userInfo ? JSON.parse(userInfo) : null
        } catch (error) {
            console.error('获取用户信息失败:', error)
            return null
        }
    },

    setUserInfo(userInfo, remember = true) {
        try {
            const userInfoStr = JSON.stringify(userInfo)
            if (remember) {
                localStorage.setItem(STORAGE_KEYS.USER_INFO, userInfoStr)
                sessionStorage.removeItem(STORAGE_KEYS.USER_INFO)
            } else {
                sessionStorage.setItem(STORAGE_KEYS.USER_INFO, userInfoStr)
                localStorage.removeItem(STORAGE_KEYS.USER_INFO)
            }
        } catch (error) {
            console.error('保存用户信息失败:', error)
        }
    },

    removeUserInfo() {
        try {
            localStorage.removeItem(STORAGE_KEYS.USER_INFO)
            sessionStorage.removeItem(STORAGE_KEYS.USER_INFO)
        } catch (error) {
            console.error('清除用户信息失败:', error)
        }
    },

    // === 记住登录状态 ===
    getRememberLogin() {
        try {
            return localStorage.getItem(STORAGE_KEYS.LOGIN_REMEMBER) === 'true'
        } catch (error) {
            console.error('获取记住登录状态失败:', error)
            return false
        }
    },

    setRememberLogin(remember) {
        try {
            localStorage.setItem(STORAGE_KEYS.LOGIN_REMEMBER, remember.toString())
        } catch (error) {
            console.error('保存记住登录状态失败:', error)
        }
    },

    // === 主题设置 ===
    getThemeSettings() {
        try {
            const settings = localStorage.getItem(STORAGE_KEYS.THEME_SETTINGS)
            return settings ? JSON.parse(settings) : null
        } catch (error) {
            console.error('获取主题设置失败:', error)
            return null
        }
    },

    setThemeSettings(settings) {
        try {
            localStorage.setItem(STORAGE_KEYS.THEME_SETTINGS, JSON.stringify(settings))
        } catch (error) {
            console.error('保存主题设置失败:', error)
        }
    },

    // === 布局设置 ===
    getLayoutSettings() {
        try {
            const settings = localStorage.getItem(STORAGE_KEYS.LAYOUT_SETTINGS)
            return settings ? JSON.parse(settings) : null
        } catch (error) {
            console.error('获取布局设置失败:', error)
            return null
        }
    },

    setLayoutSettings(settings) {
        try {
            localStorage.setItem(STORAGE_KEYS.LAYOUT_SETTINGS, JSON.stringify(settings))
        } catch (error) {
            console.error('保存布局设置失败:', error)
        }
    },

    // === 通用方法 ===
    get(key, defaultValue = null) {
        try {
            const value = localStorage.getItem(key) || sessionStorage.getItem(key)
            if (value === null) return defaultValue

            try {
                return JSON.parse(value)
            } catch {
                return value
            }
        } catch (error) {
            console.error(`获取存储数据失败 (${key}):`, error)
            return defaultValue
        }
    },

    set(key, value, useSession = false) {
        try {
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value)
            if (useSession) {
                sessionStorage.setItem(key, valueStr)
            } else {
                localStorage.setItem(key, valueStr)
            }
        } catch (error) {
            console.error(`保存存储数据失败 (${key}):`, error)
        }
    },

    remove(key) {
        try {
            localStorage.removeItem(key)
            sessionStorage.removeItem(key)
        } catch (error) {
            console.error(`清除存储数据失败 (${key}):`, error)
        }
    },

    // === 清理方法 ===
    clear() {
        try {
            localStorage.clear()
            sessionStorage.clear()
        } catch (error) {
            console.error('清空存储失败:', error)
        }
    },

    clearAuth() {
        this.removeToken()
        this.removeRefreshToken()
        this.removeUserInfo()
    },

    clearSettings() {
        this.remove(STORAGE_KEYS.THEME_SETTINGS)
        this.remove(STORAGE_KEYS.LAYOUT_SETTINGS)
    },

    // === 存储信息 ===
    getStorageInfo() {
        try {
            const localStorage_used = new Blob(Object.values(localStorage)).size
            const sessionStorage_used = new Blob(Object.values(sessionStorage)).size

            return {
                localStorage: {
                    used: localStorage_used,
                    keys: Object.keys(localStorage).length
                },
                sessionStorage: {
                    used: sessionStorage_used,
                    keys: Object.keys(sessionStorage).length
                },
                total_used: localStorage_used + sessionStorage_used
            }
        } catch (error) {
            console.error('获取存储信息失败:', error)
            return null
        }
    },

    // === 检查存储可用性 ===
    isAvailable() {
        try {
            const testKey = '__storage_test__'
            localStorage.setItem(testKey, 'test')
            localStorage.removeItem(testKey)
            return true
        } catch (error) {
            console.warn('localStorage不可用:', error)
            return false
        }
    }
}

// 默认导出
export default storageUtils
