{{-- 企业级用户资料管理页面 - 基于 app.blade.php 和 guest.blade.php 布局模板完善 --}}
@extends('user')
{{-- 页面特定样式 --}}
@push('styles')
    {{-- 页面特定样式 --}}
    @vite(['resources/css/client-layout.scss'])
@endpush
{{-- 企业级仪表板页面 - 基于 app.blade.php 布局模板 --}}
@section('title', '工作台 - ' . config('app.name'))
@section('description', '企业级工作台，提供全面的数据概览、快速操作和系统状态监控')
@section('keywords', '工作台,仪表板,数据统计,企业管理,系统监控')

{{-- 页面特定样式 --}}
@push('styles')
    <style>
        /* 企业级仪表板样式增强 */
        .dashboard-container {
            padding: 1.5rem 0;
        }

        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-title {
            font-size: 1.875rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 1rem;
        }

        .dashboard-welcome {
            background: linear-gradient(135deg, #5247ef 0%, #5247ef 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .dashboard-welcome-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .dashboard-welcome-text {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: none; /* 企业级无动画 */
        }

        .dashboard-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dashboard-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .dashboard-card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .dashboard-card-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: #5247ef;
        }

        .dashboard-card-content {
            color: #4b5563;
            line-height: 1.6;
        }

        .dashboard-stat {
            text-align: center;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .dashboard-stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #5247ef;
            display: block;
        }

        .dashboard-stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .dashboard-quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .dashboard-action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: #5247ef;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: none; /* 企业级无动画 */
        }

        .dashboard-action-btn:hover {
            background: #4338ca;
            color: white;
        }

        .dashboard-action-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.2);
        }

        .dashboard-user-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .dashboard-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: #f9fafb;
            border-radius: 6px;
        }

        .dashboard-info-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .dashboard-info-value {
            font-size: 0.875rem;
            color: #1f2937;
            font-weight: 600;
        }

        .dashboard-status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .dashboard-status-online {
            background: #d1fae5;
            color: #065f46;
        }

        .dashboard-status-offline {
            background: #fee2e2;
            color: #991b1b;
        }

        .dashboard-recent-activity {
            max-height: 300px;
            overflow-y: auto;
        }

        .dashboard-activity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .dashboard-activity-item:last-child {
            border-bottom: none;
        }

        .dashboard-activity-icon {
            width: 2rem;
            height: 2rem;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .dashboard-activity-content {
            flex: 1;
        }

        .dashboard-activity-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .dashboard-activity-time {
            font-size: 0.75rem;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-quick-actions {
                grid-template-columns: 1fr;
            }

            .dashboard-user-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
@endpush

@section('main')
    {{-- 企业级主要内容区域 - 完整响应式容器系统 --}}
    <main class="flex-1 responsive-main enterprise-main" id="main-content" role="main" aria-label="主要内容区域">
        {{-- 企业级页面内容 - 响应式间距和布局 --}}
        <div class="container-1760 space-y-6 responsive-content enterprise-content">
            {{-- Vue管理后台应用挂载点 --}}
            <div class="dashboard-container">
                {{-- 欢迎区域 --}}
                <div class="dashboard-welcome">
                    <div class="dashboard-welcome-title">
                        <i class="fas fa-sun mr-2" aria-hidden="true"></i>
                        欢迎回来，{{ Auth::user()->name }}！
                    </div>
                    <div class="dashboard-welcome-text">
                        今天是 {{ now()->format('Y年m月d日 H:i') }}，祝您工作愉快！
                    </div>
                </div>

                {{-- 统计数据卡片 --}}
                <div class="dashboard-grid">
                    {{-- 用户信息卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-user dashboard-card-icon" aria-hidden="true"></i>
                                个人信息
                            </h3>
                            <span class="dashboard-status-indicator dashboard-status-online">
                                    <i class="fas fa-circle" aria-hidden="true"></i>
                                    在线
                                </span>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="dashboard-user-info">
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">用户名</span>
                                    <span class="dashboard-info-value">{{ Auth::user()->name }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">邮箱地址</span>
                                    <span class="dashboard-info-value">{{ Auth::user()->email }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">注册时间</span>
                                    <span
                                        class="dashboard-info-value">{{ Auth::user()->created_at->format('Y-m-d') }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">最后登录</span>
                                    <span class="dashboard-info-value">{{ now()->format('Y-m-d H:i') }}</span>
                                </div>
                                @if(Auth::user()->email_verified_at)
                                    <div class="dashboard-info-item">
                                        <span class="dashboard-info-label">邮箱状态</span>
                                        <span class="dashboard-info-value text-green-600">
                                <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                                已验证
                            </span>
                                    </div>
                                @else
                                    <div class="dashboard-info-item">
                                        <span class="dashboard-info-label">邮箱状态</span>
                                        <span class="dashboard-info-value text-orange-600">
                                <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                                未验证
                            </span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{-- 系统统计卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-chart-bar dashboard-card-icon" aria-hidden="true"></i>
                                系统统计
                            </h3>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="dashboard-stat">
                                <span class="dashboard-stat-number">{{ \App\Models\UserCenter\User::count() }}</span>
                                <span class="dashboard-stat-label">注册用户</span>
                            </div>
                            <div class="dashboard-stat">
                                <span
                                    class="dashboard-stat-number">{{ \App\Models\UserCenter\User::whereDate('created_at', today())->count() }}</span>
                                <span class="dashboard-stat-label">今日新增</span>
                            </div>
                            <div class="dashboard-stat">
                                <span
                                    class="dashboard-stat-number">{{ \App\Models\UserCenter\User::where('created_at', '>=', now()->subDays(7))->count() }}</span>
                                <span class="dashboard-stat-label">本周活跃</span>
                            </div>
                        </div>
                    </div>

                    {{-- 快速操作卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-bolt dashboard-card-icon" aria-hidden="true"></i>
                                快速操作
                            </h3>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="dashboard-quick-actions">
                                <a href="{{ route('profile.edit') }}" class="dashboard-action-btn">
                                    <i class="fas fa-user-edit" aria-hidden="true"></i>
                                    编辑资料
                                </a>
                                @if(Route::has('projects.index'))
                                    <a href="{{ route('projects.index') }}" class="dashboard-action-btn">
                                        <i class="fas fa-project-diagram" aria-hidden="true"></i>
                                        项目管理
                                    </a>
                                @endif
                                @if(Route::has('tasks.index'))
                                    <a href="{{ route('tasks.index') }}" class="dashboard-action-btn">
                                        <i class="fas fa-tasks" aria-hidden="true"></i>
                                        任务中心
                                    </a>
                                @endif
                                @if(Route::has('reports.index'))
                                    <a href="{{ route('reports.index') }}" class="dashboard-action-btn">
                                        <i class="fas fa-chart-line" aria-hidden="true"></i>
                                        数据报表
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{-- 系统状态卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-server dashboard-card-icon" aria-hidden="true"></i>
                                系统状态
                            </h3>
                            <span class="dashboard-status-indicator dashboard-status-online">
                    <i class="fas fa-circle" aria-hidden="true"></i>
                    正常运行
                </span>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="dashboard-user-info">
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">服务器时间</span>
                                    <span class="dashboard-info-value">{{ now()->format('Y-m-d H:i:s') }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">PHP 版本</span>
                                    <span class="dashboard-info-value">{{ PHP_VERSION }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">Laravel 版本</span>
                                    <span class="dashboard-info-value">{{ app()->version() }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">运行环境</span>
                                    <span class="dashboard-info-value">{{ config('app.env') }}</span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">数据库连接</span>
                                    <span class="dashboard-info-value text-green-600">
                            <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                            正常
                        </span>
                                </div>
                                <div class="dashboard-info-item">
                                    <span class="dashboard-info-label">缓存状态</span>
                                    <span class="dashboard-info-value text-green-600">
                            <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                            已启用
                        </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- 最近活动卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-history dashboard-card-icon" aria-hidden="true"></i>
                                最近活动
                            </h3>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="dashboard-recent-activity">
                                <div class="dashboard-activity-item">
                                    <div class="dashboard-activity-icon">
                                        <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                                    </div>
                                    <div class="dashboard-activity-content">
                                        <div class="dashboard-activity-title">用户登录</div>
                                        <div class="dashboard-activity-time">{{ now()->format('Y-m-d H:i:s') }}</div>
                                    </div>
                                </div>

                                @if(Auth::user()->created_at->isToday())
                                    <div class="dashboard-activity-item">
                                        <div class="dashboard-activity-icon">
                                            <i class="fas fa-user-plus" aria-hidden="true"></i>
                                        </div>
                                        <div class="dashboard-activity-content">
                                            <div class="dashboard-activity-title">账户注册</div>
                                            <div
                                                class="dashboard-activity-time">{{ Auth::user()->created_at->format('Y-m-d H:i:s') }}</div>
                                        </div>
                                    </div>
                                @endif

                                <div class="dashboard-activity-item">
                                    <div class="dashboard-activity-icon">
                                        <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                                    </div>
                                    <div class="dashboard-activity-content">
                                        <div class="dashboard-activity-title">访问工作台</div>
                                        <div class="dashboard-activity-time">{{ now()->format('Y-m-d H:i:s') }}</div>
                                    </div>
                                </div>

                                <div class="dashboard-activity-item">
                                    <div class="dashboard-activity-icon">
                                        <i class="fas fa-shield-alt" aria-hidden="true"></i>
                                    </div>
                                    <div class="dashboard-activity-content">
                                        <div class="dashboard-activity-title">安全检查通过</div>
                                        <div
                                            class="dashboard-activity-time">{{ now()->subMinutes(5)->format('Y-m-d H:i:s') }}</div>
                                    </div>
                                </div>

                                <div class="dashboard-activity-item">
                                    <div class="dashboard-activity-icon">
                                        <i class="fas fa-sync-alt" aria-hidden="true"></i>
                                    </div>
                                    <div class="dashboard-activity-content">
                                        <div class="dashboard-activity-title">数据同步完成</div>
                                        <div
                                            class="dashboard-activity-time">{{ now()->subMinutes(10)->format('Y-m-d H:i:s') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- 安全提醒卡片 --}}
                    <div class="dashboard-card">
                        <div class="dashboard-card-header">
                            <h3 class="dashboard-card-title">
                                <i class="fas fa-shield-alt dashboard-card-icon" aria-hidden="true"></i>
                                安全提醒
                            </h3>
                        </div>
                        <div class="dashboard-card-content">
                            <div class="space-y-3">
                                @if(!Auth::user()->email_verified_at)
                                    <div class="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-orange-500 mr-2"
                                               aria-hidden="true"></i>
                                            <div>
                                                <div class="font-medium text-orange-800">邮箱未验证</div>
                                                <div class="text-sm text-orange-600 mt-1">
                                                    请验证您的邮箱地址以确保账户安全
                                                    <a href="{{ route('verification.notice') }}" class="underline ml-1">立即验证</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2" aria-hidden="true"></i>
                                        <div>
                                            <div class="font-medium text-green-800">账户安全</div>
                                            <div class="text-sm text-green-600 mt-1">
                                                您的账户受到企业级安全保护
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-500 mr-2" aria-hidden="true"></i>
                                        <div>
                                            <div class="font-medium text-blue-800">隐私保护</div>
                                            <div class="text-sm text-blue-600 mt-1">
                                                我们严格遵守数据保护法规，保护您的隐私
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- 帮助和支持区域 --}}
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <h3 class="dashboard-card-title">
                            <i class="fas fa-question-circle dashboard-card-icon" aria-hidden="true"></i>
                            帮助和支持
                        </h3>
                    </div>
                    <div class="dashboard-card-content">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <i class="fas fa-book text-2xl text-blue-500 mb-2" aria-hidden="true"></i>
                                <h4 class="font-medium text-gray-900 mb-1">使用指南</h4>
                                <p class="text-sm text-gray-600 mb-3">查看详细的使用说明和操作指南</p>
                                <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">查看指南</a>
                            </div>

                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <i class="fas fa-headset text-2xl text-green-500 mb-2" aria-hidden="true"></i>
                                <h4 class="font-medium text-gray-900 mb-1">在线客服</h4>
                                <p class="text-sm text-gray-600 mb-3">7×24小时在线客服支持</p>
                                <a href="#" class="text-green-600 hover:text-green-700 text-sm font-medium">联系客服</a>
                            </div>

                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <i class="fas fa-envelope text-2xl text-purple-500 mb-2" aria-hidden="true"></i>
                                <h4 class="font-medium text-gray-900 mb-1">意见反馈</h4>
                                <p class="text-sm text-gray-600 mb-3">提交您的宝贵意见和建议</p>
                                <a href="#"
                                   class="text-purple-600 hover:text-purple-700 text-sm font-medium">提交反馈</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection
{{-- 页面特定脚本 --}}
@push('scripts')
    <script>
        // 企业级仪表板管理器
        class EnterpriseDashboardManager {
            constructor() {
                this.isInitialized = false;
                this.refreshInterval = null;
                this.lastUpdateTime = Date.now();

                this.init();
            }

            init() {
                if (this.isInitialized) return;

                // 初始化实时数据更新
                this.initRealTimeUpdates();

                // 初始化用户活动监控
                this.initActivityMonitoring();

                // 初始化性能监控
                this.initPerformanceMonitoring();

                // 初始化快捷键支持
                this.initKeyboardShortcuts();

                // 标记为已初始化
                this.isInitialized = true;

                console.log('企业级仪表板管理器初始化完成');
            }

            initRealTimeUpdates() {
                // 每30秒更新一次时间显示
                this.refreshInterval = setInterval(() => {
                    this.updateTimeDisplays();
                    this.updateSystemStatus();
                }, 30000);
            }

            updateTimeDisplays() {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // 更新所有时间显示
                const timeElements = document.querySelectorAll('[data-time="current"]');
                timeElements.forEach(element => {
                    element.textContent = timeString;
                });
            }

            updateSystemStatus() {
                // 模拟系统状态检查
                const statusIndicators = document.querySelectorAll('.dashboard-status-indicator');
                statusIndicators.forEach(indicator => {
                    // 保持在线状态（实际项目中可以通过 API 检查）
                    if (!indicator.classList.contains('dashboard-status-online')) {
                        indicator.classList.remove('dashboard-status-offline');
                        indicator.classList.add('dashboard-status-online');
                        indicator.innerHTML = '<i class="fas fa-circle" aria-hidden="true"></i> 正常运行';
                    }
                });
            }

            initActivityMonitoring() {
                // 监控用户活动
                let activityCount = 0;

                // 监控点击事件
                document.addEventListener('click', (e) => {
                    activityCount++;
                    this.logActivity('点击操作', e.target.tagName);
                });

                // 监控键盘事件
                document.addEventListener('keydown', (e) => {
                    if (e.key !== 'Tab' && e.key !== 'Shift') {
                        activityCount++;
                        this.logActivity('键盘操作', e.key);
                    }
                });

                // 每分钟记录活动统计
                setInterval(() => {
                    if (activityCount > 0) {
                        console.log(`用户活动统计: ${activityCount} 次操作`);
                        activityCount = 0;
                    }
                }, 60000);
            }

            logActivity(type, detail) {
                // 记录用户活动（实际项目中可以发送到服务器）
                const activity = {
                    type: type,
                    detail: detail,
                    timestamp: new Date().toISOString(),
                    page: 'dashboard'
                };

                // 存储到本地存储（用于调试）
                const activities = JSON.parse(localStorage.getItem('user_activities') || '[]');
                activities.push(activity);

                // 只保留最近100条记录
                if (activities.length > 100) {
                    activities.splice(0, activities.length - 100);
                }

                localStorage.setItem('user_activities', JSON.stringify(activities));
            }

            initPerformanceMonitoring() {
                // 监控页面性能
                if ('performance' in window) {
                    window.addEventListener('load', () => {
                        setTimeout(() => {
                            const perfData = performance.getEntriesByType('navigation')[0];
                            if (perfData) {
                                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                                const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;

                                console.log('仪表板性能指标:', {
                                    loadTime: Math.round(loadTime),
                                    domContentLoaded: Math.round(domContentLoaded),
                                    totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart)
                                });
                            }
                        }, 1000);
                    });
                }
            }

            initKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Ctrl/Cmd + D: 刷新仪表板
                    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                        e.preventDefault();
                        this.refreshDashboard();
                    }

                    // Ctrl/Cmd + P: 打开个人资料
                    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                        e.preventDefault();
                        const profileLink = document.querySelector('a[href*="profile"]');
                        if (profileLink) {
                            profileLink.click();
                        }
                    }

                    // Ctrl/Cmd + H: 显示帮助
                    if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
                        e.preventDefault();
                        this.showKeyboardShortcuts();
                    }
                });
            }

            refreshDashboard() {
                // 刷新仪表板数据
                console.log('刷新仪表板数据...');

                // 显示加载状态
                const cards = document.querySelectorAll('.dashboard-card');
                cards.forEach(card => {
                    card.style.opacity = '0.7';
                });

                // 模拟数据刷新
                setTimeout(() => {
                    cards.forEach(card => {
                        card.style.opacity = '1';
                    });

                    // 更新时间显示
                    this.updateTimeDisplays();

                    console.log('仪表板数据刷新完成');
                }, 1000);
            }

            showKeyboardShortcuts() {
                const shortcuts = [
                    'Ctrl+D: 刷新仪表板',
                    'Ctrl+P: 打开个人资料',
                    'Ctrl+H: 显示快捷键帮助'
                ];

                alert('键盘快捷键:\n\n' + shortcuts.join('\n'));
            }

            // 清理资源
            destroy() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }

                this.isInitialized = false;
                console.log('仪表板管理器已清理');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            window.EnterpriseDashboardManager = new EnterpriseDashboardManager();

            // 添加页面可见性变化监听
            document.addEventListener('visibilitychange', function () {
                if (document.hidden) {
                    console.log('仪表板页面隐藏');
                } else {
                    console.log('仪表板页面显示');
                    // 页面重新显示时更新数据
                    if (window.EnterpriseDashboardManager) {
                        window.EnterpriseDashboardManager.updateTimeDisplays();
                    }
                }
            });
        });

        // 页面卸载前清理
        window.addEventListener('beforeunload', function () {
            if (window.EnterpriseDashboardManager) {
                window.EnterpriseDashboardManager.destroy();
            }
        });
    </script>
@endpush
