<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
      class="{{ $htmlClass ?? '' }}"
      data-theme="{{ $theme ?? 'light' }}"
      data-env="{{ config('app.env') }}"
      data-layout="desktop">
{{-- 企业级桌面端页面头部组件 - 完整 SEO、Meta 标签、资源文件优化，专为桌面端设计 --}}
<head>
    {{-- 基础 Meta 标签 - 企业级桌面端支持 --}}
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=3, user-scalable=yes, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=yes, email=yes, address=yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'WorkHub') }} Desktop">
    <meta name="application-name" content="{{ config('app.name', 'WorkHub') }} Desktop">
    <meta name="theme-color" content="#5247ef">
    <meta name="msapplication-TileColor" content="#5247ef">
    <meta name="msapplication-navbutton-color" content="#5247ef">

    {{-- CSRF Token 配置 --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="api-token"
          content="{{ Auth::check() ? (class_exists('Laravel\Sanctum\HasApiTokens') && method_exists(Auth::user(), 'createToken') ? Auth::user()->createToken('desktop-access')->plainTextToken : session()->token()) : '' }}">

    {{-- 企业级桌面端动态页面标题系统 --}}
    <title>
        @if(isset($title))
            {{ $title }} - {{ config('app.name', 'WorkHub') }} Desktop
        @elseif(isset($pageTitle))
            {{ $pageTitle }} - {{ config('app.name', 'WorkHub') }} Desktop
        @elseif(isset($breadcrumbs) && count($breadcrumbs) > 0)
            {{ end($breadcrumbs)['title'] ?? '' }} - {{ config('app.name', 'WorkHub') }} Desktop
        @else
            {{ config('app.name', 'WorkHub') }} - 企业级桌面端工作平台
        @endif
    </title>

    {{-- 企业级 SEO Meta 标签系统 --}}
    <meta name="description"
          content="{{ $description ?? $seoDescription ?? '现代化的企业工作协作平台桌面端，提供高效的团队协作、项目管理、文档管理和业务流程优化解决方案，专为桌面端用户优化' }}">
    <meta name="keywords"
          content="{{ $keywords ?? $seoKeywords ?? '企业工作平台,团队协作,项目管理,文档管理,业务流程,数字化转型,企业办公,工作效率,桌面端' }}">
    <meta name="author" content="{{ $author ?? config('app.company.name', config('app.name', 'WorkHub')) }}">
    <meta name="publisher" content="{{ config('app.company.name', config('app.name', 'WorkHub')) }}">
    <meta name="robots" content="{{ $robots ?? ($noIndex ?? false ? 'noindex,nofollow' : 'index,follow') }}">
    <meta name="googlebot" content="{{ $googlebot ?? 'index,follow,snippet,archive' }}">
    <meta name="bingbot" content="{{ $bingbot ?? 'index,follow,snippet' }}">

    {{-- 企业级 Open Graph 标签系统 (社交媒体分享优化) --}}
    <meta property="og:title" content="{{ $ogTitle ?? ($title ?? config('app.name') . ' Desktop') }}">
    <meta property="og:description"
          content="{{ $ogDescription ?? ($description ?? '现代化的企业工作协作平台桌面端') }}">
    <meta property="og:type" content="{{ $ogType ?? 'website' }}">
    <meta property="og:url" content="{{ $ogUrl ?? request()->url() }}">
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-desktop.jpg') }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="{{ $ogImageAlt ?? config('app.name') . ' - 企业工作协作平台桌面端' }}">
    <meta property="og:site_name" content="{{ config('app.name', 'WorkHub') }} Desktop">
    <meta property="og:locale" content="zh_CN">

    {{-- Twitter Card 标签 --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $twitterTitle ?? ($title ?? config('app.name') . ' Desktop') }}">
    <meta name="twitter:description"
          content="{{ $twitterDescription ?? ($description ?? '现代化的企业工作协作平台桌面端') }}">
    <meta name="twitter:image" content="{{ $twitterImage ?? asset('images/twitter-desktop.jpg') }}">

    {{-- 企业级网站图标系统 --}}
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">
    <link rel="manifest" href="{{ asset('site.webmanifest') }}">
    <link rel="mask-icon" href="{{ asset('images/safari-pinned-tab.svg') }}" color="#5247ef">

    {{-- 企业级性能优化 - DNS 预解析和资源预加载 --}}
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//{{ parse_url(config('app.url'), PHP_URL_HOST) }}">
    <link rel="preconnect" href="https://fonts.bunny.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    {{-- 企业级字体系统 - 性能优化加载 --}}
    <link rel="preload" href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap">
    </noscript>

    {{-- 企业级图标库 - Font Awesome 6.4.0 --}}
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
              integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
              crossorigin="anonymous"
              referrerpolicy="no-referrer">
    </noscript>

    {{-- 企业级桌面端样式系统 - Vite 优化加载，使用全局样式 --}}
    @vite(['resources/css/variables.scss','resources/css/base.scss','resources/css/global.scss','resources/css/app.scss','resources/css/layouts.scss','resources/css/client-layout.scss','resources/css/desktop.scss'])

    {{-- 页面特定样式栈 --}}
    @stack('styles')

    {{-- 企业级 JavaScript 预加载配置 --}}
    @stack('head-scripts')

    {{-- 企业级结构化数据系统 (JSON-LD SEO 优化) --}}
    @if(isset($structuredData))
        <script type="application/ld+json">
            {!! json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
        </script>
    @endif

    {{-- 默认企业级桌面端结构化数据 --}}
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "{{ config('app.name', 'WorkHub') }} Desktop",
            "url": "{{ request()->url() }}",
            "description": "{{ $description ?? '企业级桌面端工作协作平台' }}",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web",
            "browserRequirements": "Requires JavaScript. Optimized for desktop browsers.",
            "provider": {
                "@type": "Organization",
                "name": "{{ config('app.name', 'WorkHub') }}",
                "url": "{{ config('app.url') }}"
            }
        }
    </script>

    {{-- 自定义 Meta 标签栈 --}}
    @stack('meta')

    {{-- 企业级无障碍支持声明 --}}
    <meta name="accessibility" content="WCAG 2.1 AA compliant">
    <meta name="color-scheme" content="light dark">
</head>

{{-- 企业级桌面端页面主体 - 完整响应式优化，专为桌面端设计，使用全局样式 --}}
<body class="enterprise-desktop-body responsive-body enterprise-desktop {{ $bodyClass ?? '' }}"
      data-page="{{ request()->route()->getName() ?? 'desktop' }}"
      data-user-id="{{ Auth::id() ?? 'guest' }}"
      data-user-role="{{ Auth::check() ? (Auth::user()->role ?? 'user') : 'guest' }}"
      data-theme="{{ $theme ?? 'light' }}"
      data-layout="desktop"
      data-viewport-width=""
      data-viewport-height=""
      data-device-type="desktop"
      data-connection-type="">

{{-- 企业级无障碍跳转链接 --}}
<a href="#main-content"
   class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50 transition-none">
    跳转到主要内容
</a>

{{-- 主要页面内容 --}}
<main id="main-content" role="main" aria-label="主要内容区域">
    @yield('content')
</main>

{{-- 企业级 JavaScript 资源 - Vite 优化加载 --}}
@vite(['resources/js/app.js'])

{{-- 页面特定脚本栈 --}}
@stack('scripts')

{{-- 企业级页面底部脚本 - 最后加载的脚本 --}}
@stack('bottom-scripts')

{{-- 企业级桌面端页面初始化脚本 --}}
<script>
    // 企业级桌面端页面管理器
    class EnterpriseDesktopPageManager {
        constructor() {
            this.isInitialized = false;
            this.init();
        }

        init() {
            if (this.isInitialized) return;

            // 设置桌面端特定配置
            this.initDesktopFeatures();

            // 初始化响应式系统
            this.initResponsiveSystem();

            // 标记为已初始化
            this.isInitialized = true;

            console.log('企业级桌面端页面管理器初始化完成');
        }

        initDesktopFeatures() {
            const body = document.body;

            // 设置桌面端标识
            body.classList.add('is-desktop-layout');
            body.dataset.deviceType = 'desktop';

            // 桌面端特定优化
            if (window.innerWidth >= 1200) {
                body.classList.add('is-large-desktop');
            }
        }

        initResponsiveSystem() {
            const self = this;

            function updateDesktopState() {
                const width = window.innerWidth;
                const height = window.innerHeight;
                const body = document.body;

                // 更新视口信息
                body.dataset.viewportWidth = width;
                body.dataset.viewportHeight = height;

                // 桌面端响应式类
                body.classList.toggle('is-large-desktop', width >= 1760);
                body.classList.toggle('is-standard-desktop', width >= 1200 && width < 1760);
                body.classList.toggle('is-small-desktop', width >= 1024 && width < 1200);

                // 触发桌面端响应式变化事件
                window.dispatchEvent(new CustomEvent('desktopViewportChanged', {
                    detail: {width, height}
                }));
            }

            // 初始化桌面端响应式状态
            updateDesktopState();

            // 监听窗口大小变化
            let resizeTimer;
            window.addEventListener('resize', function () {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(updateDesktopState, 100);
            });
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        window.EnterpriseDesktopPageManager = new EnterpriseDesktopPageManager();
    });
</script>

</body>
</html>
