{{-- 企业级管理后台顶部导航栏 - 无动画设计，完整功能 --}}
<header class="admin-header" role="banner" aria-label="管理后台顶部导航">
    {{-- 左侧区域 - 侧边栏切换和面包屑 --}}
    <div class="flex items-center">
        {{-- 侧边栏切换按钮 --}}
        <button type="button"
                class="admin-sidebar-toggle mr-4 p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-600 no-focus-outline"
                onclick="window.dispatchEvent(new CustomEvent('toggleSidebar'))"
                aria-label="切换侧边栏">
            <i class="fas fa-bars" aria-hidden="true"></i>
        </button>

        {{-- 页面标题 --}}
        @if(isset($pageTitle))
            <h1 class="text-lg font-semibold text-white">{{ $pageTitle }}</h1>
        @elseif(isset($title))
            <h1 class="text-lg font-semibold text-white">{{ $title }}</h1>
        @else
            <h1 class="text-lg font-semibold text-white">管理后台</h1>
        @endif

        {{-- 页面描述 (可选) --}}
        @if(isset($pageDescription))
            <span class="ml-3 text-sm text-gray-400">{{ $pageDescription }}</span>
        @endif
    </div>

    {{-- 右侧区域 - 搜索、通知、用户菜单 --}}
    <div class="flex items-center space-x-4">
        {{-- 全局搜索 --}}
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400" aria-hidden="true"></i>
            </div>
            <input type="text"
                   class="admin-search-input w-64 pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                   placeholder="搜索用户、内容、设置..."
                   aria-label="全局搜索">
        </div>

        {{-- 快捷操作 --}}
        <div class="flex items-center space-x-2">
            {{-- 清除缓存 --}}
            <button type="button"
                    class="admin-header-action"
                    onclick="AdminUtils.confirm('确定要清除所有缓存吗？', function() { window.location.href = '/admin/cache/clear'; })"
                    aria-label="清除缓存">
                <i class="fas fa-broom" aria-hidden="true"></i>
            </button>

            {{-- 系统监控 --}}
            <a href="{{ route('admin.monitor') ?? '/admin/monitor' }}"
               class="admin-header-action"
               aria-label="系统监控">
                <i class="fas fa-heartbeat" aria-hidden="true"></i>
            </a>

            {{-- 返回前台 --}}
            <a href="{{ url('/') }}"
               class="admin-header-action"
               target="_blank"
               aria-label="返回前台网站">
                <i class="fas fa-external-link-alt" aria-hidden="true"></i>
            </a>
        </div>

        {{-- 通知中心 --}}
        <div class="relative">
            <button type="button"
                    class="admin-header-action relative"
                    id="notifications-toggle"
                    aria-label="通知中心">
                <i class="fas fa-bell" aria-hidden="true"></i>
                {{-- 未读通知数量 --}}
                @if(isset($unreadNotifications) && $unreadNotifications > 0)
                    <span
                        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
                        aria-label="未读通知 {{ $unreadNotifications }} 条">
                        {{ $unreadNotifications > 99 ? '99+' : $unreadNotifications }}
                    </span>
                @endif
            </button>

            {{-- 通知下拉菜单 (可选实现) --}}
            <div id="notifications-dropdown"
                 class="hidden absolute right-0 mt-2 w-80 bg-gray-800 rounded-md shadow-lg border border-gray-700 z-50">
                <div class="p-4 border-b border-gray-700">
                    <h3 class="text-white font-medium">通知中心</h3>
                </div>
                <div class="max-h-64 overflow-y-auto">
                    {{-- 通知列表将通过 JavaScript 动态加载 --}}
                    <div class="p-4 text-center text-gray-400">
                        <i class="fas fa-bell-slash mb-2"></i>
                        <p class="text-sm">暂无新通知</p>
                    </div>
                </div>
                <div class="p-3 border-t border-gray-700">
                    <a href="{{ route('admin.notifications.index') ?? '/admin/notifications' }}"
                       class="text-primary-400 hover:text-primary-300 text-sm">
                        查看所有通知
                    </a>
                </div>
            </div>
        </div>

        {{-- 用户菜单 --}}
        @auth
            <div class="relative">
                <button type="button"
                        class="flex items-center space-x-3 p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-600 no-focus-outline"
                        id="user-menu-toggle"
                        aria-label="用户菜单">
                    @if(Auth::user()->avatar)
                        <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}"
                             class="w-8 h-8 rounded-full">
                    @else
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <div class="text-left">
                        <p class="text-sm font-medium text-white">{{ Auth::user()->name }}</p>
                        <p class="text-xs text-gray-400">{{ Auth::user()->role ?? '管理员' }}</p>
                    </div>
                    <i class="fas fa-chevron-down text-xs" aria-hidden="true"></i>
                </button>

                {{-- 用户下拉菜单 (可选实现) --}}
                <div id="user-menu-dropdown"
                     class="hidden absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg border border-gray-700 z-50">
                    <div class="py-1">
                        <a href="{{ route('admin.profile') ?? '/admin/profile' }}"
                           class="admin-dropdown-item">
                            <i class="fas fa-user-cog mr-2" aria-hidden="true"></i>
                            个人设置
                        </a>
                        <a href="{{ route('admin.preferences') ?? '/admin/preferences' }}"
                           class="admin-dropdown-item">
                            <i class="fas fa-cog mr-2" aria-hidden="true"></i>
                            偏好设置
                        </a>
                        <a href="{{ route('admin.security') ?? '/admin/security' }}"
                           class="admin-dropdown-item">
                            <i class="fas fa-shield-alt mr-2" aria-hidden="true"></i>
                            安全设置
                        </a>
                        <div class="border-t border-gray-700 my-1"></div>
                        <a href="{{ route('admin.help') ?? '/admin/help' }}"
                           class="admin-dropdown-item">
                            <i class="fas fa-question-circle mr-2" aria-hidden="true"></i>
                            帮助中心
                        </a>
                        <div class="border-t border-gray-700 my-1"></div>
                        <form method="POST" action="{{ route('admin.logout') ?? '/admin/logout' }}">
                            @csrf
                            <button type="submit"
                                    class="admin-dropdown-item w-full text-left text-red-400 hover:text-red-300">
                                <i class="fas fa-sign-out-alt mr-2" aria-hidden="true"></i>
                                退出登录
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        @endauth
    </div>
</header>

{{-- 头部样式 --}}
<style>
    /* 侧边栏切换按钮 */
    .admin-sidebar-toggle {
        transition: none;
    }

    /* 搜索输入框 */
    .admin-search-input {
        transition: none;
    }

    .admin-search-input:focus {
        box-shadow: 0 0 0 2px rgba(82, 71, 239, 0.5);
    }

    /* 头部操作按钮 */
    .admin-header-action {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        color: #94a3b8;
        background-color: transparent;
        border: none;
        border-radius: 0.375rem;
        text-decoration: none;
        transition: none;
        cursor: pointer;
    }

    .admin-header-action:hover {
        color: #f8fafc;
        background-color: #374151;
    }

    /* 下拉菜单项 */
    .admin-dropdown-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.5rem 1rem;
        color: #f8fafc;
        text-decoration: none;
        font-size: 0.875rem;
        transition: none;
        border: none;
        background: none;
        cursor: pointer;
    }

    .admin-dropdown-item:hover {
        background-color: #374151;
    }

    /* 响应式设计 */
    @media (max-width: 1199px) {
        .admin-search-input {
            width: 12rem;
        }
    }

    @media (max-width: 767px) {
        .admin-search-input {
            display: none;
        }

        .admin-header {
            padding: 0 1rem;
        }
    }
</style>

{{-- 头部交互脚本 --}}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 通知下拉菜单切换
        const notificationsToggle = document.getElementById('notifications-toggle');
        const notificationsDropdown = document.getElementById('notifications-dropdown');

        if (notificationsToggle && notificationsDropdown) {
            notificationsToggle.addEventListener('click', function (e) {
                e.stopPropagation();
                notificationsDropdown.classList.toggle('hidden');

                // 关闭用户菜单
                const userMenuDropdown = document.getElementById('user-menu-dropdown');
                if (userMenuDropdown) {
                    userMenuDropdown.classList.add('hidden');
                }
            });
        }

        // 用户菜单下拉切换
        const userMenuToggle = document.getElementById('user-menu-toggle');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');

        if (userMenuToggle && userMenuDropdown) {
            userMenuToggle.addEventListener('click', function (e) {
                e.stopPropagation();
                userMenuDropdown.classList.toggle('hidden');

                // 关闭通知菜单
                if (notificationsDropdown) {
                    notificationsDropdown.classList.add('hidden');
                }
            });
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function () {
            if (notificationsDropdown) {
                notificationsDropdown.classList.add('hidden');
            }
            if (userMenuDropdown) {
                userMenuDropdown.classList.add('hidden');
            }
        });

        // 搜索功能
        const searchInput = document.querySelector('.admin-search-input');
        if (searchInput) {
            searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim();
                    if (query) {
                        window.location.href = `/admin/search?q=${encodeURIComponent(query)}`;
                    }
                }
            });
        }
    });
</script>
