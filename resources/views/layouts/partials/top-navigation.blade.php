{{-- 企业级顶部导航栏 - 最大宽度1760px，无动画设计，桌面端优化 --}}
<nav class="top-nav desktop-optimized" role="navigation" aria-label="顶部导航">
    <div class="top-nav-container">
        {{-- 左侧用户区域 - 企业级简洁设计 --}}
        <div class="top-nav-left-section" role="group" aria-label="用户登录区域">
            @guest
                <a href="{{ route('login') }}"
                   class="top-nav-link no-focus-outline"
                   aria-label="用户登录">
                    <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                    <span class="top-nav-text">登录</span>
                </a>
                <span class="top-nav-separator" aria-hidden="true">|</span>
                <a href="{{ route('register') }}"
                   class="top-nav-link no-focus-outline"
                   aria-label="用户注册">
                    <i class="fas fa-user-plus" aria-hidden="true"></i>
                    <span class="top-nav-text">注册</span>
                </a>
            @else
                <div class="top-nav-user-section">
                    <span class="top-nav-user-info" aria-label="当前用户">
                        <i class="fas fa-user" aria-hidden="true"></i>
                        <span class="top-nav-welcome">欢迎，</span>
                        <span class="top-nav-username">{{ Auth::user()->name }}</span>
                    </span>
                    <span class="top-nav-separator" aria-hidden="true">|</span>

                    {{-- 管理中心链接 - 根据用户权限显示 --}}
                    @php
                        // 临时权限检查 - 可以根据实际需求调整
                        $user = Auth::user();
                        $showAdmin = true; // 暂时显示所有链接，后续可以根据用户角色调整
                        $showLesson = true;
                        $showChain = true;
                        $showMonitor = true;

                        // 如果已配置 Laratrust，可以使用以下方式检查权限：
                        // $showAdmin = $user->hasRole(['superadministrator', 'administrator']) || $user->hasPermission('admin-access');
                        // $showLesson = $user->hasRole(['superadministrator', 'administrator', 'lesson-manager']) || $user->hasPermission('lesson-access');
                        // $showChain = $user->hasRole(['superadministrator', 'administrator', 'chain-manager']) || $user->hasPermission('chain-access');
                        // $showMonitor = $user->hasRole(['superadministrator', 'administrator', 'monitor-manager']) || $user->hasPermission('monitor-access');
                    @endphp

                    @if($showAdmin)
                        <a href="{{ route('admin.home.dashboard.index') }}"
                           class="top-nav-link no-focus-outline"
                           aria-label="后台管理">
                            <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                            <span class="top-nav-text">后台管理</span>
                        </a>
                        <span class="top-nav-separator" aria-hidden="true">|</span>
                    @endif

                    @if($showLesson)
                        <a href="{{ route('lesson.dashboard.index') }}"
                           class="top-nav-link no-focus-outline"
                           aria-label="运营中心">
                            <i class="fas fa-graduation-cap" aria-hidden="true"></i>
                            <span class="top-nav-text">运营中心</span>
                        </a>
                        <span class="top-nav-separator" aria-hidden="true">|</span>
                    @endif

                    @if($showChain)
                        <a href="{{ route('chain.dashboard.index') }}"
                           class="top-nav-link no-focus-outline"
                           aria-label="渠道中心">
                            <i class="fas fa-network-wired" aria-hidden="true"></i>
                            <span class="top-nav-text">渠道中心</span>
                        </a>
                        <span class="top-nav-separator" aria-hidden="true">|</span>
                    @endif

                    @if($showMonitor)
                        <a href="{{ route('monitor.dashboard.index') }}"
                           class="top-nav-link no-focus-outline"
                           aria-label="数据中心">
                            <i class="fas fa-chart-line" aria-hidden="true"></i>
                            <span class="top-nav-text">数据中心</span>
                        </a>
                        <span class="top-nav-separator" aria-hidden="true">|</span>
                    @endif

                    {{-- 个人设置链接 --}}
                    <a href="{{ route('profile.edit') }}"
                       class="top-nav-link no-focus-outline"
                       aria-label="个人设置">
                        <i class="fas fa-user-cog" aria-hidden="true"></i>
                        <span class="top-nav-text">设置</span>
                    </a>
                    <span class="top-nav-separator" aria-hidden="true">|</span>

                    {{-- 退出登录 --}}
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit"
                                class="top-nav-link top-nav-button no-focus-outline"
                                aria-label="退出登录">
                            <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                            <span class="top-nav-text">退出</span>
                        </button>
                    </form>
                </div>
            @endguest
        </div>

        {{-- 右侧功能区域 - 企业级服务入口，响应式文本显示 --}}
        <div class="top-nav-right-section" role="group" aria-label="服务功能区域">
            {{-- 帮助中心 - 始终显示 --}}
            <a href="{{ route('help.center') ?? '#' }}"
               class="top-nav-link no-focus-outline"
               aria-label="帮助中心">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
                <span class="top-nav-text" data-full="帮助中心" data-short="帮助">帮助中心</span>
            </a>
            <span class="top-nav-separator" aria-hidden="true">|</span>

            {{-- 官方客服 - 始终显示 --}}
            <a href="{{ route('customer.service') ?? '#' }}"
               class="top-nav-link no-focus-outline"
               aria-label="官方客服">
                <i class="fas fa-headset" aria-hidden="true"></i>
                <span class="top-nav-text" data-full="官方客服" data-short="客服">官方客服</span>
            </a>
            <span class="top-nav-separator top-nav-separator-responsive" aria-hidden="true">|</span>

            {{-- 商家客服 - 中等屏幕以上显示 --}}
            <a href="{{ route('merchant.service') ?? '#' }}"
               class="top-nav-link no-focus-outline top-nav-link-collapsible"
               aria-label="商家客服">
                <i class="fas fa-store" aria-hidden="true"></i>
                <span class="top-nav-text" data-full="商家客服" data-short="商家">商家客服</span>
            </a>
            <span class="top-nav-separator top-nav-separator-collapsible" aria-hidden="true">|</span>

            {{-- 消息中心 - 登录用户显示，支持未读消息提醒 --}}
            @auth
                <a href="{{ route('message.center') ?? '#' }}"
                   class="top-nav-link no-focus-outline relative"
                   aria-label="消息中心">
                    <i class="fas fa-envelope" aria-hidden="true"></i>
                    <span class="top-nav-text" data-full="消息中心" data-short="消息">消息中心</span>
                    {{-- 企业级消息提醒 - 简洁数字显示 --}}
                    @php
                        // 获取未读消息数量 - 安全的方式获取
                        $unreadMessages = 0;
                        try {
                            // 优先使用传递的变量
                            if (isset($unreadMessages)) {
                                $unreadMessages = (int) $unreadMessages;
                            }
                            // 其次尝试从用户通知获取
                            elseif (Auth::check() && method_exists(Auth::user(), 'unreadNotifications')) {
                                $unreadMessages = Auth::user()->unreadNotifications()->count();
                            }
                            // 最后可以从其他数据源获取，如缓存、数据库等
                            else {
                                $unreadMessages = 0; // 默认值
                            }
                        } catch (Exception $e) {
                            $unreadMessages = 0; // 出错时默认为0
                        }
                    @endphp
                    @if($unreadMessages > 0)
                        <span class="top-nav-badge"
                              aria-label="未读消息 {{ $unreadMessages }} 条">
                            {{ $unreadMessages > 99 ? '99+' : $unreadMessages }}
                        </span>
                    @endif
                </a>
                <span class="top-nav-separator" aria-hidden="true">|</span>
            @endauth

            {{-- 意见反馈 - 始终显示 --}}
            <a href="{{ route('feedback.center') ?? '#' }}"
               class="top-nav-link no-focus-outline"
               aria-label="意见反馈">
                <i class="fas fa-comment-alt" aria-hidden="true"></i>
                <span class="top-nav-text" data-full="意见反馈" data-short="反馈">意见反馈</span>
            </a>
            <span class="top-nav-separator top-nav-separator-collapsible" aria-hidden="true">|</span>

            {{-- 举报中心 - 大屏幕显示 --}}
            <a href="{{ route('report.center') ?? '#' }}"
               class="top-nav-link no-focus-outline top-nav-link-collapsible"
               aria-label="举报中心">
                <i class="fas fa-flag" aria-hidden="true"></i>
                <span class="top-nav-text" data-full="举报中心" data-short="举报">举报中心</span>
            </a>
        </div>
    </div>
</nav>
