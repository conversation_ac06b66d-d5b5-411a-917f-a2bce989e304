{{-- 企业级用户资料管理页面 - 基于 app.blade.php 和 guest.blade.php 布局模板完善 --}}
@extends('user')
{{-- 页面特定样式 --}}
@push('styles')
    {{-- 页面特定样式 --}}
    @vite(['resources/css/client-layout.scss'])
@endpush
@section('title', '个人资料管理')
@section('description', '管理您的个人资料信息、账户安全设置和隐私配置，确保账户信息的准确性和安全性')
@section('keywords', '个人资料,账户管理,密码修改,账户安全,用户信息,隐私设置')

{{-- 页面特定样式 --}}
@push('styles')
    <style>
        /* 企业级个人资料页面样式 */
        .profile-container {
            min-height: calc(100vh - 200px);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .profile-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .profile-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .profile-section-header {
            border-bottom: 2px solid #f3f4f6;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .profile-form-group {
            margin-bottom: 1.5rem;
        }

        .profile-avatar-container {
            position: relative;
            display: inline-block;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #5247ef;
            object-fit: cover;
            background: linear-gradient(135deg, #5247ef, #5247ef);
        }

        .profile-avatar-upload {
            position: absolute;
            bottom: 0;
            right: 0;
            background: #5247ef;
            color: white;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 3px solid white;
            transition: all 0.3s ease;
        }

        .profile-avatar-upload:hover {
            background: #4338ca;
            transform: scale(1.1);
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .profile-stat-item {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .profile-stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #5247ef;
            display: block;
        }

        .profile-stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .profile-container {
                padding: 1rem;
            }

            .profile-card {
                margin-bottom: 1rem;
            }

            .profile-avatar {
                width: 100px;
                height: 100px;
            }

            .profile-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
@endpush

@section('main')
    {{-- 企业级主容器 - 完整响应式布局系统 --}}
    <div class="min-h-screen flex flex-col responsive-container enterprise-container">
        {{-- 企业级顶部导航栏 - 黑色主题 --}}
        {{--        @include('layouts.partials.top-navigation')--}}
        {{--       --}}
        {{--        --}}{{-- 企业级主要内容区域 - 完整响应式容器系统 --}}
        {{--        <main class="flex-1 page-content responsive-main enterprise-main" id="main-content" role="main" aria-label="主要内容区域">--}}
        {{-- 企业级页面内容 - 响应式间距和布局 --}}
        <div class="container-1760 space-y-6 responsive-content enterprise-content">
            {{-- Vue管理后台应用挂载点 --}}
            {{-- 企业级面包屑导航 --}}
            <div class="bg-white border-b border-gray-200 mb-6">
                <div class="container-1760">
                    <div class="py-4">
                        <nav class="flex items-center space-x-2 text-sm text-gray-600" aria-label="面包屑导航">
                            <a href="{{ route('dashboard') }}"
                               class="hover:text-primary-600 transition-colors no-focus-outline">
                                <i class="fas fa-home mr-1" aria-hidden="true"></i>
                                工作台
                            </a>
                            <i class="fas fa-chevron-right text-gray-400" aria-hidden="true"></i>
                            <span class="text-gray-900 font-medium">个人资料</span>
                        </nav>
                    </div>
                </div>
            </div>

            {{-- 企业级页面标题区域 --}}
            <div class="bg-white border-b border-gray-200 mb-8">
                <div class="container-1760">
                    <div class="py-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="profile-avatar-container">
                                    @if(Auth::user()->avatar)
                                        <img src="{{ Auth::user()->avatar }}" alt="用户头像" class="profile-avatar">
                                    @else
                                        <div
                                            class="profile-avatar flex items-center justify-center text-white text-3xl font-bold">
                                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                        </div>
                                    @endif
                                    <div class="profile-avatar-upload" title="更换头像">
                                        <i class="fas fa-camera text-sm" aria-hidden="true"></i>
                                    </div>
                                </div>
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900">{{ Auth::user()->name }}</h1>
                                    <p class="text-gray-600 mt-1">{{ Auth::user()->email }}</p>
                                    <div class="flex items-center mt-2 space-x-4">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                                已验证
                            </span>
                                        <span class="text-sm text-gray-500">
                                <i class="fas fa-calendar-alt mr-1" aria-hidden="true"></i>
                                注册于 {{ Auth::user()->created_at->format('Y年m月d日') }}
                            </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button type="button" class="btn btn-outline-primary">
                                    <i class="fas fa-download mr-2" aria-hidden="true"></i>
                                    导出数据
                                </button>
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-cog mr-2" aria-hidden="true"></i>
                                    账户设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- 企业级用户统计信息 --}}
            <div class="container-1760 mb-8">
                <div class="profile-stats">
                    <div class="profile-stat-item">
                        <span class="profile-stat-value">{{ Auth::user()->projects_count ?? 0 }}</span>
                        <span class="profile-stat-label">参与项目</span>
                    </div>
                    <div class="profile-stat-item">
                        <span class="profile-stat-value">{{ Auth::user()->tasks_completed ?? 0 }}</span>
                        <span class="profile-stat-label">完成任务</span>
                    </div>
                    <div class="profile-stat-item">
                        <span class="profile-stat-value">{{ Auth::user()->documents_created ?? 0 }}</span>
                        <span class="profile-stat-label">创建文档</span>
                    </div>
                    <div class="profile-stat-item">
                        <span class="profile-stat-value">{{ Auth::user()->login_count ?? 0 }}</span>
                        <span class="profile-stat-label">登录次数</span>
                    </div>
                </div>
            </div>

            {{-- 企业级主要内容区域 --}}
            <div class="profile-container">
                <div class="container-1760">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {{-- 左侧主要内容 --}}
                        <div class="lg:col-span-2 space-y-8">
                            {{-- 个人信息管理卡片 --}}
                            <div class="profile-card">
                                <div class="p-8">
                                    <div class="profile-section-header">
                                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                            <i class="fas fa-user-edit mr-3 text-primary-600" aria-hidden="true"></i>
                                            个人信息管理
                                        </h2>
                                        <p class="text-gray-600 mt-2">更新您的账户基本信息和联系方式</p>
                                    </div>
                                    @include('user.profile.partials.update-profile-information-form')
                                </div>
                            </div>

                            {{-- 密码安全管理卡片 --}}
                            <div class="profile-card">
                                <div class="p-8">
                                    <div class="profile-section-header">
                                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                            <i class="fas fa-shield-alt mr-3 text-warning-600" aria-hidden="true"></i>
                                            密码安全管理
                                        </h2>
                                        <p class="text-gray-600 mt-2">定期更新密码以确保账户安全</p>
                                    </div>
                                    @include('user.profile.partials.update-password-form')
                                </div>
                            </div>
                        </div>

                        {{-- 右侧边栏 --}}
                        <div class="space-y-8">
                            {{-- 账户安全状态卡片 --}}
                            <div class="profile-card">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <i class="fas fa-security mr-2 text-success-600" aria-hidden="true"></i>
                                        安全状态
                                    </h3>
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-gray-600">邮箱验证</span>
                                            @if(Auth::user()->email_verified_at)
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1" aria-hidden="true"></i>
                                        已验证
                                    </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times mr-1" aria-hidden="true"></i>
                                        未验证
                                    </span>
                                            @endif
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-gray-600">双因素认证</span>
                                            <span
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times mr-1" aria-hidden="true"></i>
                                    未启用
                                </span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm text-gray-600">最后登录</span>
                                            <span class="text-sm text-gray-900">
                                    {{ Auth::user()->last_login_at ? Auth::user()->last_login_at->diffForHumans() : '首次登录' }}
                                </span>
                                        </div>
                                    </div>
                                    <div class="mt-6">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-full">
                                            <i class="fas fa-cog mr-2" aria-hidden="true"></i>
                                            安全设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {{-- 快速操作卡片 --}}
                            <div class="profile-card">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <i class="fas fa-bolt mr-2 text-primary-600" aria-hidden="true"></i>
                                        快速操作
                                    </h3>
                                    <div class="space-y-3">
                                        <button type="button"
                                                class="btn btn-outline-secondary btn-sm w-full justify-start">
                                            <i class="fas fa-download mr-2" aria-hidden="true"></i>
                                            下载个人数据
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-secondary btn-sm w-full justify-start">
                                            <i class="fas fa-history mr-2" aria-hidden="true"></i>
                                            登录历史
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-secondary btn-sm w-full justify-start">
                                            <i class="fas fa-bell mr-2" aria-hidden="true"></i>
                                            通知设置
                                        </button>
                                        <button type="button"
                                                class="btn btn-outline-secondary btn-sm w-full justify-start">
                                            <i class="fas fa-palette mr-2" aria-hidden="true"></i>
                                            主题设置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {{-- 账户危险操作卡片 --}}
                            <div class="profile-card border-red-200">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold text-red-900 mb-4 flex items-center">
                                        <i class="fas fa-exclamation-triangle mr-2 text-red-600" aria-hidden="true"></i>
                                        危险操作
                                    </h3>
                                    <p class="text-sm text-red-700 mb-4">
                                        以下操作将对您的账户产生不可逆的影响，请谨慎操作。
                                    </p>
                                    @include('user.profile.partials.delete-user-form')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- 企业级页面底部操作栏 --}}
            <div class="bg-white border-t border-gray-200 mt-12">
                <div class="container-1760">
                    <div class="py-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                <span>最后更新：{{ Auth::user()->updated_at->format('Y年m月d日 H:i') }}</span>
                                <span>•</span>
                                <span>账户ID：{{ Auth::user()->id }}</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <button type="button" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo mr-2" aria-hidden="true"></i>
                                    重置更改
                                </button>
                                <button type="button" class="btn btn-success">
                                    <i class="fas fa-save mr-2" aria-hidden="true"></i>
                                    保存所有更改
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        {{--        </main>--}}
        {{--        --}}{{-- 企业级底部版权栏 - 与顶部导航栏风格一致 --}}
        {{--        @include('layouts.partials.footer-enterprise')--}}
        {{--        @include('layouts.partials.bottom-copyright')--}}
    </div>
@endsection
{{-- 页面特定脚本 --}}
@push('scripts')
    <script>
        // 企业级个人资料页面管理器
        class EnterpriseProfileManager {
            constructor() {
                this.isInitialized = false;
                this.formChanged = false;
                this.autoSaveTimer = null;
                this.init();
            }

            init() {
                if (this.isInitialized) return;

                // 初始化头像上传功能
                this.initAvatarUpload();

                // 初始化表单变更监听
                this.initFormChangeTracking();

                // 初始化自动保存
                this.initAutoSave();

                // 初始化快捷键支持
                this.initKeyboardShortcuts();

                // 初始化提示信息
                this.initTooltips();

                this.isInitialized = true;
                console.log('企业级个人资料页面管理器初始化完成');
            }

            // 初始化头像上传功能
            initAvatarUpload() {
                const avatarUpload = document.querySelector('.profile-avatar-upload');
                if (avatarUpload) {
                    avatarUpload.addEventListener('click', () => {
                        // 创建隐藏的文件输入
                        const fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.accept = 'image/*';
                        fileInput.style.display = 'none';

                        fileInput.addEventListener('change', (e) => {
                            const file = e.target.files[0];
                            if (file) {
                                this.handleAvatarUpload(file);
                            }
                        });

                        document.body.appendChild(fileInput);
                        fileInput.click();
                        document.body.removeChild(fileInput);
                    });
                }
            }

            // 处理头像上传
            handleAvatarUpload(file) {
                // 验证文件类型和大小
                if (!file.type.startsWith('image/')) {
                    this.showNotification('请选择有效的图片文件', 'error');
                    return;
                }

                if (file.size > 5 * 1024 * 1024) { // 5MB
                    this.showNotification('图片文件大小不能超过5MB', 'error');
                    return;
                }

                // 显示预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    const avatarImg = document.querySelector('.profile-avatar');
                    if (avatarImg.tagName === 'IMG') {
                        avatarImg.src = e.target.result;
                    } else {
                        // 如果是div，替换为img
                        const newImg = document.createElement('img');
                        newImg.src = e.target.result;
                        newImg.alt = '用户头像';
                        newImg.className = 'profile-avatar';
                        avatarImg.parentNode.replaceChild(newImg, avatarImg);
                    }
                };
                reader.readAsDataURL(file);

                // 这里可以添加实际的上传逻辑
                this.uploadAvatar(file);
            }

            // 上传头像到服务器
            async uploadAvatar(file) {
                const formData = new FormData();
                formData.append('avatar', file);
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

                try {
                    const response = await fetch('/profile/avatar', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        this.showNotification('头像更新成功', 'success');
                    } else {
                        throw new Error('上传失败');
                    }
                } catch (error) {
                    this.showNotification('头像上传失败，请重试', 'error');
                    console.error('头像上传错误:', error);
                }
            }

            // 初始化表单变更监听
            initFormChangeTracking() {
                const forms = document.querySelectorAll('form');
                forms.forEach(form => {
                    const inputs = form.querySelectorAll('input, textarea, select');
                    inputs.forEach(input => {
                        input.addEventListener('input', () => {
                            this.formChanged = true;
                            this.showUnsavedChangesIndicator();
                        });
                    });
                });
            }

            // 显示未保存更改指示器
            showUnsavedChangesIndicator() {
                // 可以在页面上显示一个小的指示器
                console.log('表单已修改，有未保存的更改');
            }

            // 初始化自动保存
            initAutoSave() {
                // 每30秒自动保存一次（如果有更改）
                this.autoSaveTimer = setInterval(() => {
                    if (this.formChanged) {
                        this.autoSave();
                    }
                }, 30000);
            }

            // 自动保存功能
            autoSave() {
                console.log('执行自动保存...');
                // 这里可以实现自动保存逻辑
            }

            // 初始化键盘快捷键
            initKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Ctrl+S 保存
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        this.saveAllChanges();
                    }

                    // Ctrl+Z 撤销
                    if (e.ctrlKey && e.key === 'z') {
                        e.preventDefault();
                        this.undoChanges();
                    }
                });
            }

            // 保存所有更改
            saveAllChanges() {
                console.log('保存所有更改...');
                this.showNotification('正在保存更改...', 'info');
                // 实现保存逻辑
            }

            // 撤销更改
            undoChanges() {
                if (confirm('确定要撤销所有未保存的更改吗？')) {
                    location.reload();
                }
            }

            // 初始化提示信息
            initTooltips() {
                // 可以使用第三方库如 Tippy.js 或自定义实现
                console.log('初始化提示信息...');
            }

            // 显示通知
            showNotification(message, type = 'info') {
                // 这里可以集成企业级通知系统
                console.log(`${type.toUpperCase()}: ${message}`);

                // 简单的通知实现
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md shadow-lg z-50 ${
                    type === 'success' ? 'bg-green-500 text-white' :
                        type === 'error' ? 'bg-red-500 text-white' :
                            type === 'warning' ? 'bg-yellow-500 text-white' :
                                'bg-blue-500 text-white'
                }`;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            // 清理资源
            destroy() {
                if (this.autoSaveTimer) {
                    clearInterval(this.autoSaveTimer);
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            window.profileManager = new EnterpriseProfileManager();
        });

        // 页面卸载前清理
        window.addEventListener('beforeunload', function (e) {
            if (window.profileManager) {
                if (window.profileManager.formChanged) {
                    e.preventDefault();
                    e.returnValue = '您有未保存的更改，确定要离开吗？';
                    return e.returnValue;
                }
                window.profileManager.destroy();
            }
        });
    </script>
@endpush
