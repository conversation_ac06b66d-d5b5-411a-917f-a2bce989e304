{{-- 企业级账户删除表单 - 增强安全性和用户确认流程 --}}
<section class="enterprise-delete-form space-y-6">
    {{-- 危险操作警告 --}}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-exclamation-triangle text-red-600 mt-1 mr-3" aria-hidden="true"></i>
            <div class="flex-1">
                <h4 class="text-sm font-semibold text-red-900 mb-2">账户删除警告</h4>
                <div class="text-sm text-red-800 space-y-2">
                    <p>删除账户是不可逆的操作，将会导致以下后果：</p>
                    <ul class="list-disc list-inside space-y-1 ml-4">
                        <li>所有个人数据将被永久删除</li>
                        <li>参与的项目和团队将失去您的贡献记录</li>
                        <li>创建的文档和评论将被匿名化处理</li>
                        <li>无法恢复任何已删除的信息</li>
                        <li>相关的订阅和服务将被取消</li>
                    </ul>
                    <p class="font-medium">在删除账户前，请确保已下载所有需要保留的数据。</p>
                </div>
            </div>
        </div>
    </div>

    {{-- 数据导出提醒 --}}
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-download text-blue-600 mt-1 mr-3" aria-hidden="true"></i>
            <div class="flex-1">
                <h4 class="text-sm font-semibold text-blue-900 mb-2">数据导出建议</h4>
                <p class="text-sm text-blue-800 mb-3">
                    在删除账户前，建议您先导出个人数据以备份重要信息。
                </p>
                <button type="button" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download mr-2" aria-hidden="true"></i>
                    导出个人数据
                </button>
            </div>
        </div>
    </div>

    {{-- 删除账户按钮 --}}
    <div class="text-center">
        <button
            type="button"
            class="btn btn-danger"
            onclick="openDeleteConfirmModal()"
        >
            <i class="fas fa-trash-alt mr-2" aria-hidden="true"></i>
            删除我的账户
        </button>
    </div>

    {{-- 删除确认模态框 --}}
    <div id="delete-confirm-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title"
         role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {{-- 背景遮罩 --}}
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"
                 onclick="closeDeleteConfirmModal()"></div>

            {{-- 模态框内容 --}}
            <div
                class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div
                        class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600" aria-hidden="true"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            确认删除账户
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 mb-4">
                                您即将永久删除您的账户。此操作无法撤销，所有数据将被永久删除。
                            </p>

                            {{-- 删除确认步骤 --}}
                            <div class="space-y-4">
                                {{-- 步骤1：确认理解后果 --}}
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <label class="flex items-start">
                                        <input
                                            type="checkbox"
                                            id="understand-consequences"
                                            class="form-checkbox h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mt-1"
                                            onchange="checkDeleteRequirements()"
                                        >
                                        <span class="ml-3 text-sm text-gray-700">
                                            我理解删除账户的后果，并确认要继续此操作
                                        </span>
                                    </label>
                                </div>

                                {{-- 步骤2：输入确认文本 --}}
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <label for="delete-confirmation-text"
                                           class="block text-sm font-medium text-gray-700 mb-2">
                                        请输入 "<span class="font-mono text-red-600">删除我的账户</span>" 以确认：
                                    </label>
                                    <input
                                        type="text"
                                        id="delete-confirmation-text"
                                        class="form-input w-full"
                                        placeholder="删除我的账户"
                                        onchange="checkDeleteRequirements()"
                                        oninput="checkDeleteRequirements()"
                                    >
                                </div>

                                {{-- 步骤3：输入密码 --}}
                                <form method="post" action="{{ route('profile.destroy') }}" id="delete-account-form">
                                    @csrf
                                    @method('delete')

                                    <div class="border border-gray-200 rounded-lg p-3">
                                        <label for="delete-password"
                                               class="block text-sm font-medium text-gray-700 mb-2">
                                            输入您的密码以最终确认：
                                        </label>
                                        <div class="relative">
                                            <input
                                                id="delete-password"
                                                name="password"
                                                type="password"
                                                class="form-input w-full @error('password', 'userDeletion') form-input-error @enderror"
                                                placeholder="请输入当前密码"
                                                required
                                                onchange="checkDeleteRequirements()"
                                                oninput="checkDeleteRequirements()"
                                            >
                                            <button
                                                type="button"
                                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                                onclick="toggleDeletePassword()"
                                            >
                                                <i id="delete-password-icon"
                                                   class="fas fa-eye text-gray-400 hover:text-gray-600"
                                                   aria-hidden="true"></i>
                                            </button>
                                        </div>
                                        @error('password', 'userDeletion')
                                        <p class="form-error mt-2">
                                            <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                            {{ $message }}
                                        </p>
                                        @enderror
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- 模态框操作按钮 --}}
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button
                        type="button"
                        class="btn btn-danger w-full sm:w-auto sm:ml-3"
                        id="final-delete-btn"
                        disabled
                        onclick="submitDeleteForm()"
                    >
                        <i class="fas fa-trash-alt mr-2" aria-hidden="true"></i>
                        永久删除账户
                    </button>
                    <button
                        type="button"
                        class="btn btn-outline-secondary w-full sm:w-auto mt-3 sm:mt-0"
                        onclick="closeDeleteConfirmModal()"
                    >
                        <i class="fas fa-times mr-2" aria-hidden="true"></i>
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- 删除表单样式 --}}
<style>
    .enterprise-delete-form .form-checkbox {
        border-radius: 0.25rem;
        border: 1px solid #d1d5db;
    }

    .enterprise-delete-form .form-checkbox:checked {
        background-color: #ef4444;
        border-color: #ef4444;
    }

    .enterprise-delete-form .form-checkbox:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .enterprise-delete-form .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background-color: #ffffff;
    }

    .enterprise-delete-form .form-input:focus {
        outline: none;
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .enterprise-delete-form .form-input-error {
        border-color: #ef4444;
        background-color: #fef2f2;
    }

    .enterprise-delete-form .form-error {
        color: #ef4444;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
    }

    .enterprise-delete-form #delete-confirm-modal {
        backdrop-filter: blur(4px);
    }

    .enterprise-delete-form .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .enterprise-delete-form .btn:disabled:hover {
        background-color: inherit;
        border-color: inherit;
        color: inherit;
    }
</style>

{{-- 删除表单脚本 --}}
<script>
    // 打开删除确认模态框
    function openDeleteConfirmModal() {
        const modal = document.getElementById('delete-confirm-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // 聚焦到第一个复选框
            const firstCheckbox = document.getElementById('understand-consequences');
            if (firstCheckbox) {
                setTimeout(() => firstCheckbox.focus(), 100);
            }
        }
    }

    // 关闭删除确认模态框
    function closeDeleteConfirmModal() {
        const modal = document.getElementById('delete-confirm-modal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';

            // 重置表单
            resetDeleteForm();
        }
    }

    // 重置删除表单
    function resetDeleteForm() {
        const checkbox = document.getElementById('understand-consequences');
        const textInput = document.getElementById('delete-confirmation-text');
        const passwordInput = document.getElementById('delete-password');
        const submitBtn = document.getElementById('final-delete-btn');

        if (checkbox) checkbox.checked = false;
        if (textInput) textInput.value = '';
        if (passwordInput) passwordInput.value = '';
        if (submitBtn) submitBtn.disabled = true;
    }

    // 检查删除要求是否满足
    function checkDeleteRequirements() {
        const checkbox = document.getElementById('understand-consequences');
        const textInput = document.getElementById('delete-confirmation-text');
        const passwordInput = document.getElementById('delete-password');
        const submitBtn = document.getElementById('final-delete-btn');

        if (!checkbox || !textInput || !passwordInput || !submitBtn) return;

        const isCheckboxChecked = checkbox.checked;
        const isTextCorrect = textInput.value.trim() === '删除我的账户';
        const hasPassword = passwordInput.value.trim().length > 0;

        const allRequirementsMet = isCheckboxChecked && isTextCorrect && hasPassword;

        submitBtn.disabled = !allRequirementsMet;

        // 更新按钮样式
        if (allRequirementsMet) {
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }

    // 切换密码显示
    function toggleDeletePassword() {
        const passwordInput = document.getElementById('delete-password');
        const icon = document.getElementById('delete-password-icon');

        if (!passwordInput || !icon) return;

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // 提交删除表单
    function submitDeleteForm() {
        const submitBtn = document.getElementById('final-delete-btn');
        const form = document.getElementById('delete-account-form');

        if (!form || !submitBtn) return;

        // 最后确认
        const finalConfirm = confirm(
            '这是最后的确认步骤。\n\n' +
            '点击"确定"将立即删除您的账户，此操作无法撤销。\n' +
            '点击"取消"可以中止删除操作。\n\n' +
            '您确定要继续吗？'
        );

        if (!finalConfirm) {
            return;
        }

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2" aria-hidden="true"></i>正在删除...';

        // 提交表单
        form.submit();
    }

    // 键盘事件处理
    document.addEventListener('keydown', function (e) {
        const modal = document.getElementById('delete-confirm-modal');
        if (!modal || modal.classList.contains('hidden')) return;

        // ESC键关闭模态框
        if (e.key === 'Escape') {
            e.preventDefault();
            closeDeleteConfirmModal();
        }

        // Enter键在密码输入框中提交表单
        if (e.key === 'Enter' && e.target.id === 'delete-password') {
            e.preventDefault();
            const submitBtn = document.getElementById('final-delete-btn');
            if (submitBtn && !submitBtn.disabled) {
                submitDeleteForm();
            }
        }
    });

    // 页面加载时检查是否有删除错误
    document.addEventListener('DOMContentLoaded', function () {
        @if($errors->userDeletion->isNotEmpty())
        // 如果有删除错误，自动打开模态框
        openDeleteConfirmModal();
        @endif
    });

    // 防止意外离开页面
    let deleteFormOpened = false;

    document.getElementById('delete-confirm-modal')?.addEventListener('transitionend', function () {
        deleteFormOpened = !this.classList.contains('hidden');
    });

    window.addEventListener('beforeunload', function (e) {
        if (deleteFormOpened) {
            e.preventDefault();
            e.returnValue = '您正在进行账户删除操作，确定要离开吗？';
            return e.returnValue;
        }
    });
</script>
