{{-- 企业级密码更新表单 - 增强安全性和用户体验 --}}
<section class="enterprise-password-form">
    {{-- 密码安全提示 --}}
    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-start">
            <i class="fas fa-shield-alt text-blue-600 mt-1 mr-3" aria-hidden="true"></i>
            <div>
                <h4 class="text-sm font-semibold text-blue-900 mb-2">密码安全建议</h4>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li class="flex items-center">
                        <i class="fas fa-check text-blue-600 mr-2 text-xs" aria-hidden="true"></i>
                        至少8个字符，建议12个字符以上
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check text-blue-600 mr-2 text-xs" aria-hidden="true"></i>
                        包含大小写字母、数字和特殊字符
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check text-blue-600 mr-2 text-xs" aria-hidden="true"></i>
                        避免使用个人信息和常见密码
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check text-blue-600 mr-2 text-xs" aria-hidden="true"></i>
                        定期更换密码以确保账户安全
                    </li>
                </ul>
            </div>
        </div>
    </div>

    {{-- 密码更新表单 --}}
    <form method="post" action="{{ route('password.update') }}" class="space-y-6" id="password-update-form">
        @csrf
        @method('put')

        {{-- 当前密码 --}}
        <div class="password-form-group">
            <label for="update_password_current_password" class="form-label required">
                <i class="fas fa-lock mr-2 text-gray-400" aria-hidden="true"></i>
                当前密码
            </label>
            <div class="relative">
                <input
                    id="update_password_current_password"
                    name="current_password"
                    type="password"
                    class="form-input password-input @error('current_password', 'updatePassword') form-input-error @enderror"
                    autocomplete="current-password"
                    placeholder="请输入当前密码"
                    required
                >
                <button
                    type="button"
                    class="password-toggle absolute inset-y-0 right-0 pr-3 flex items-center"
                    data-target="update_password_current_password"
                >
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" aria-hidden="true"></i>
                </button>
            </div>
            @error('current_password', 'updatePassword')
            <p class="form-error">
                <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                {{ $message }}
            </p>
            @enderror
            <p class="form-help">输入您当前使用的密码以验证身份</p>
        </div>

        {{-- 新密码 --}}
        <div class="password-form-group">
            <label for="update_password_password" class="form-label required">
                <i class="fas fa-key mr-2 text-gray-400" aria-hidden="true"></i>
                新密码
            </label>
            <div class="relative">
                <input
                    id="update_password_password"
                    name="password"
                    type="password"
                    class="form-input password-input @error('password', 'updatePassword') form-input-error @enderror"
                    autocomplete="new-password"
                    placeholder="请输入新密码"
                    required
                    minlength="8"
                >
                <button
                    type="button"
                    class="password-toggle absolute inset-y-0 right-0 pr-3 flex items-center"
                    data-target="update_password_password"
                >
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" aria-hidden="true"></i>
                </button>
            </div>
            @error('password', 'updatePassword')
            <p class="form-error">
                <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                {{ $message }}
            </p>
            @enderror

            {{-- 密码强度指示器 --}}
            <div class="password-strength mt-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-600">密码强度：</span>
                    <span id="password-strength-text" class="text-sm font-medium text-gray-500">未设置</span>
                </div>
                <div class="password-strength-bar">
                    <div id="password-strength-fill" class="password-strength-fill"></div>
                </div>
                <div class="password-requirements mt-3 space-y-1">
                    <div class="requirement" data-requirement="length">
                        <i class="fas fa-times text-red-500 mr-2" aria-hidden="true"></i>
                        <span class="text-sm text-gray-600">至少8个字符</span>
                    </div>
                    <div class="requirement" data-requirement="uppercase">
                        <i class="fas fa-times text-red-500 mr-2" aria-hidden="true"></i>
                        <span class="text-sm text-gray-600">包含大写字母</span>
                    </div>
                    <div class="requirement" data-requirement="lowercase">
                        <i class="fas fa-times text-red-500 mr-2" aria-hidden="true"></i>
                        <span class="text-sm text-gray-600">包含小写字母</span>
                    </div>
                    <div class="requirement" data-requirement="number">
                        <i class="fas fa-times text-red-500 mr-2" aria-hidden="true"></i>
                        <span class="text-sm text-gray-600">包含数字</span>
                    </div>
                    <div class="requirement" data-requirement="special">
                        <i class="fas fa-times text-red-500 mr-2" aria-hidden="true"></i>
                        <span class="text-sm text-gray-600">包含特殊字符</span>
                    </div>
                </div>
            </div>
        </div>

        {{-- 确认新密码 --}}
        <div class="password-form-group">
            <label for="update_password_password_confirmation" class="form-label required">
                <i class="fas fa-check-double mr-2 text-gray-400" aria-hidden="true"></i>
                确认新密码
            </label>
            <div class="relative">
                <input
                    id="update_password_password_confirmation"
                    name="password_confirmation"
                    type="password"
                    class="form-input password-input @error('password_confirmation', 'updatePassword') form-input-error @enderror"
                    autocomplete="new-password"
                    placeholder="请再次输入新密码"
                    required
                >
                <button
                    type="button"
                    class="password-toggle absolute inset-y-0 right-0 pr-3 flex items-center"
                    data-target="update_password_password_confirmation"
                >
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" aria-hidden="true"></i>
                </button>
            </div>
            @error('password_confirmation', 'updatePassword')
            <p class="form-error">
                <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                {{ $message }}
            </p>
            @enderror

            {{-- 密码匹配指示器 --}}
            <div id="password-match-indicator" class="mt-2 hidden">
                <div class="flex items-center text-sm">
                    <i id="password-match-icon" class="mr-2" aria-hidden="true"></i>
                    <span id="password-match-text"></span>
                </div>
            </div>
        </div>

        {{-- 额外安全选项 --}}
        <div class="password-form-group">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-cog mr-2 text-gray-600" aria-hidden="true"></i>
                    安全选项
                </h4>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input
                            type="checkbox"
                            name="logout_other_devices"
                            value="1"
                            class="form-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-3 text-sm text-gray-700">
                            更新密码后注销其他设备的登录会话
                        </span>
                    </label>
                    <label class="flex items-center">
                        <input
                            type="checkbox"
                            name="send_notification"
                            value="1"
                            checked
                            class="form-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <span class="ml-3 text-sm text-gray-700">
                            密码更新成功后发送邮件通知
                        </span>
                    </label>
                </div>
            </div>
        </div>

        {{-- 表单操作按钮 --}}
        <div class="form-actions">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button type="submit" class="btn btn-warning" id="update-password-btn">
                        <i class="fas fa-shield-alt mr-2" aria-hidden="true"></i>
                        更新密码
                    </button>
                    <button type="button" class="btn btn-outline-secondary"
                            onclick="this.form.reset(); resetPasswordForm()">
                        <i class="fas fa-undo mr-2" aria-hidden="true"></i>
                        重置
                    </button>
                </div>

                {{-- 密码更新成功提示 --}}
                @if (session('status') === 'password-updated')
                    <div class="flex items-center text-success-600 animate-fade-in">
                        <i class="fas fa-check-circle mr-2" aria-hidden="true"></i>
                        <span class="text-sm font-medium">密码已成功更新</span>
                    </div>
                @endif
            </div>
        </div>
    </form>
</section>

{{-- 密码表单样式 --}}
<style>
    .enterprise-password-form .password-form-group {
        margin-bottom: 1.5rem;
    }

    .enterprise-password-form .form-label {
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .enterprise-password-form .form-label.required::after {
        content: '*';
        color: #ef4444;
        margin-left: 0.25rem;
    }

    .enterprise-password-form .form-input {
        width: 100%;
        padding: 0.75rem;
        padding-right: 3rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background-color: #ffffff;
    }

    .enterprise-password-form .form-input:focus {
        outline: none;
        border-color: #5247ef;
        box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.1);
    }

    .enterprise-password-form .form-input-error {
        border-color: #ef4444;
        background-color: #fef2f2;
    }

    .enterprise-password-form .form-error {
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }

    .enterprise-password-form .form-help {
        color: #6b7280;
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    .enterprise-password-form .password-toggle {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .enterprise-password-form .password-toggle:hover i {
        color: #374151 !important;
    }

    .enterprise-password-form .password-strength-bar {
        width: 100%;
        height: 4px;
        background-color: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }

    .enterprise-password-form .password-strength-fill {
        height: 100%;
        width: 0%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .enterprise-password-form .password-strength-fill.weak {
        background-color: #ef4444;
        width: 25%;
    }

    .enterprise-password-form .password-strength-fill.fair {
        background-color: #f59e0b;
        width: 50%;
    }

    .enterprise-password-form .password-strength-fill.good {
        background-color: #10b981;
        width: 75%;
    }

    .enterprise-password-form .password-strength-fill.strong {
        background-color: #059669;
        width: 100%;
    }

    .enterprise-password-form .requirement {
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
    }

    .enterprise-password-form .requirement.met i {
        color: #10b981 !important;
    }

    .enterprise-password-form .requirement.met i::before {
        content: '\f00c'; /* fa-check */
    }

    .enterprise-password-form .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e5e7eb;
    }

    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fade-in 0.3s ease-out;
    }
</style>

{{-- 密码表单脚本 --}}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 密码显示/隐藏切换
        document.querySelectorAll('.password-toggle').forEach(toggle => {
            toggle.addEventListener('click', function () {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                const icon = this.querySelector('i');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });

        // 密码强度检测
        const passwordInput = document.getElementById('update_password_password');
        const confirmInput = document.getElementById('update_password_password_confirmation');
        const strengthFill = document.getElementById('password-strength-fill');
        const strengthText = document.getElementById('password-strength-text');
        const matchIndicator = document.getElementById('password-match-indicator');
        const matchIcon = document.getElementById('password-match-icon');
        const matchText = document.getElementById('password-match-text');

        if (passwordInput) {
            passwordInput.addEventListener('input', function () {
                checkPasswordStrength(this.value);
                checkPasswordMatch();
            });
        }

        if (confirmInput) {
            confirmInput.addEventListener('input', function () {
                checkPasswordMatch();
            });
        }

        function checkPasswordStrength(password) {
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
            };

            // 更新要求指示器
            Object.keys(requirements).forEach(req => {
                const element = document.querySelector(`[data-requirement="${req}"]`);
                if (element) {
                    if (requirements[req]) {
                        element.classList.add('met');
                    } else {
                        element.classList.remove('met');
                    }
                }
            });

            // 计算强度
            const metRequirements = Object.values(requirements).filter(Boolean).length;
            let strength = 'weak';
            let strengthClass = 'weak';
            let strengthColor = '#ef4444';

            if (metRequirements >= 5) {
                strength = '强';
                strengthClass = 'strong';
                strengthColor = '#059669';
            } else if (metRequirements >= 4) {
                strength = '良好';
                strengthClass = 'good';
                strengthColor = '#10b981';
            } else if (metRequirements >= 3) {
                strength = '一般';
                strengthClass = 'fair';
                strengthColor = '#f59e0b';
            } else if (metRequirements >= 1) {
                strength = '弱';
                strengthClass = 'weak';
                strengthColor = '#ef4444';
            } else {
                strength = '未设置';
                strengthClass = '';
                strengthColor = '#6b7280';
            }

            // 更新强度显示
            if (strengthFill) {
                strengthFill.className = `password-strength-fill ${strengthClass}`;
            }
            if (strengthText) {
                strengthText.textContent = strength;
                strengthText.style.color = strengthColor;
            }
        }

        function checkPasswordMatch() {
            const password = passwordInput ? passwordInput.value : '';
            const confirm = confirmInput ? confirmInput.value : '';

            if (confirm.length === 0) {
                matchIndicator.classList.add('hidden');
                return;
            }

            matchIndicator.classList.remove('hidden');

            if (password === confirm) {
                matchIcon.className = 'fas fa-check-circle text-green-500';
                matchText.textContent = '密码匹配';
                matchText.className = 'text-green-600';
            } else {
                matchIcon.className = 'fas fa-times-circle text-red-500';
                matchText.textContent = '密码不匹配';
                matchText.className = 'text-red-600';
            }
        }

        // 表单提交验证
        const form = document.getElementById('password-update-form');
        if (form) {
            form.addEventListener('submit', function (e) {
                const password = passwordInput ? passwordInput.value : '';
                const confirm = confirmInput ? confirmInput.value : '';

                if (password !== confirm) {
                    e.preventDefault();
                    alert('新密码和确认密码不匹配，请检查后重试。');
                    return false;
                }

                if (password.length < 8) {
                    e.preventDefault();
                    alert('密码长度至少需要8个字符。');
                    return false;
                }

                // 显示加载状态
                const submitBtn = document.getElementById('update-password-btn');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2" aria-hidden="true"></i>更新中...';
                }
            });
        }
    });

    // 重置密码表单函数
    function resetPasswordForm() {
        // 重置强度指示器
        const strengthFill = document.getElementById('password-strength-fill');
        const strengthText = document.getElementById('password-strength-text');
        const matchIndicator = document.getElementById('password-match-indicator');

        if (strengthFill) {
            strengthFill.className = 'password-strength-fill';
        }
        if (strengthText) {
            strengthText.textContent = '未设置';
            strengthText.style.color = '#6b7280';
        }
        if (matchIndicator) {
            matchIndicator.classList.add('hidden');
        }

        // 重置要求指示器
        document.querySelectorAll('.requirement').forEach(req => {
            req.classList.remove('met');
        });

        // 重置密码显示状态
        document.querySelectorAll('.password-input').forEach(input => {
            input.type = 'password';
        });
        document.querySelectorAll('.password-toggle i').forEach(icon => {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        });
    }
</script>
