{{-- 企业级个人信息更新表单 - 增强版本，支持更多字段和验证 --}}
@php use Illuminate\Contracts\Auth\MustVerifyEmail; @endphp

<section class="enterprise-profile-form">
    {{-- 邮箱验证表单（隐藏） --}}
    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    {{-- 主要个人信息更新表单 --}}
    <form method="post" action="{{ route('profile.update') }}" class="space-y-6" id="profile-update-form">
        @csrf
        @method('patch')

        {{-- 基本信息区域 --}}
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-user mr-2 text-primary-600" aria-hidden="true"></i>
                基本信息
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{-- 姓名字段 --}}
                <div class="profile-form-group">
                    <label for="name" class="form-label required">
                        <i class="fas fa-user-tag mr-2 text-gray-400" aria-hidden="true"></i>
                        姓名
                    </label>
                    <input
                        id="name"
                        name="name"
                        type="text"
                        class="form-input @error('name') form-input-error @enderror"
                        value="{{ old('name', $user->name) }}"
                        required
                        autofocus
                        autocomplete="name"
                        placeholder="请输入您的真实姓名"
                        maxlength="50"
                    >
                    @error('name')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror
                    <p class="form-help">请输入您的真实姓名，这将显示在您的个人资料中</p>
                </div>

                {{-- 用户名字段（如果支持） --}}
                <div class="profile-form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-at mr-2 text-gray-400" aria-hidden="true"></i>
                        用户名
                    </label>
                    <input
                        id="username"
                        name="username"
                        type="text"
                        class="form-input @error('username') form-input-error @enderror"
                        value="{{ old('username', $user->username ?? '') }}"
                        autocomplete="username"
                        placeholder="设置一个唯一的用户名"
                        pattern="[a-zA-Z0-9_-]{3,20}"
                        maxlength="20"
                    >
                    @error('username')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror
                    <p class="form-help">3-20个字符，只能包含字母、数字、下划线和连字符</p>
                </div>
            </div>
        </div>

        {{-- 联系信息区域 --}}
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-address-book mr-2 text-primary-600" aria-hidden="true"></i>
                联系信息
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{-- 邮箱字段 --}}
                <div class="profile-form-group">
                    <label for="email" class="form-label required">
                        <i class="fas fa-envelope mr-2 text-gray-400" aria-hidden="true"></i>
                        邮箱地址
                    </label>
                    <div class="relative">
                        <input
                            id="email"
                            name="email"
                            type="email"
                            class="form-input @error('email') form-input-error @enderror"
                            value="{{ old('email', $user->email) }}"
                            required
                            autocomplete="email"
                            placeholder="请输入有效的邮箱地址"
                        >
                        @if ($user instanceof MustVerifyEmail && $user->hasVerifiedEmail())
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-check-circle text-green-500" title="邮箱已验证" aria-hidden="true"></i>
                            </div>
                        @endif
                    </div>
                    @error('email')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror

                    {{-- 邮箱验证状态 --}}
                    @if ($user instanceof MustVerifyEmail && ! $user->hasVerifiedEmail())
                        <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mt-0.5 mr-2"
                                   aria-hidden="true"></i>
                                <div class="flex-1">
                                    <p class="text-sm text-yellow-800 font-medium">邮箱地址未验证</p>
                                    <p class="text-sm text-yellow-700 mt-1">
                                        为了确保账户安全，请验证您的邮箱地址。
                                        <button
                                            form="send-verification"
                                            type="submit"
                                            class="font-medium text-yellow-800 underline hover:text-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 rounded"
                                        >
                                            点击重新发送验证邮件
                                        </button>
                                    </p>
                                </div>
                            </div>
                        </div>

                        @if (session('status') === 'verification-link-sent')
                            <div class="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2" aria-hidden="true"></i>
                                    <p class="text-sm text-green-800 font-medium">
                                        验证邮件已发送到您的邮箱，请查收并点击验证链接。
                                    </p>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>

                {{-- 手机号码字段 --}}
                <div class="profile-form-group">
                    <label for="phone" class="form-label">
                        <i class="fas fa-mobile-alt mr-2 text-gray-400" aria-hidden="true"></i>
                        手机号码
                    </label>
                    <input
                        id="phone"
                        name="phone"
                        type="tel"
                        class="form-input @error('phone') form-input-error @enderror"
                        value="{{ old('phone', $user->phone ?? '') }}"
                        autocomplete="tel"
                        placeholder="请输入11位手机号码"
                        pattern="[0-9]{11}"
                        maxlength="11"
                    >
                    @error('phone')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror
                    <p class="form-help">用于接收重要通知和安全验证</p>
                </div>
            </div>
        </div>

        {{-- 个人详情区域 --}}
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-id-card mr-2 text-primary-600" aria-hidden="true"></i>
                个人详情
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{-- 职位字段 --}}
                <div class="profile-form-group">
                    <label for="job_title" class="form-label">
                        <i class="fas fa-briefcase mr-2 text-gray-400" aria-hidden="true"></i>
                        职位
                    </label>
                    <input
                        id="job_title"
                        name="job_title"
                        type="text"
                        class="form-input @error('job_title') form-input-error @enderror"
                        value="{{ old('job_title', $user->job_title ?? '') }}"
                        autocomplete="organization-title"
                        placeholder="例如：产品经理、软件工程师"
                        maxlength="100"
                    >
                    @error('job_title')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror
                </div>

                {{-- 部门字段 --}}
                <div class="profile-form-group">
                    <label for="department" class="form-label">
                        <i class="fas fa-building mr-2 text-gray-400" aria-hidden="true"></i>
                        部门
                    </label>
                    <input
                        id="department"
                        name="department"
                        type="text"
                        class="form-input @error('department') form-input-error @enderror"
                        value="{{ old('department', $user->department ?? '') }}"
                        autocomplete="organization"
                        placeholder="例如：技术部、市场部"
                        maxlength="100"
                    >
                    @error('department')
                    <p class="form-error">
                        <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                        {{ $message }}
                    </p>
                    @enderror
                </div>
            </div>

            {{-- 个人简介 --}}
            <div class="profile-form-group">
                <label for="bio" class="form-label">
                    <i class="fas fa-user-edit mr-2 text-gray-400" aria-hidden="true"></i>
                    个人简介
                </label>
                <textarea
                    id="bio"
                    name="bio"
                    rows="4"
                    class="form-textarea @error('bio') form-input-error @enderror"
                    placeholder="简单介绍一下自己，让同事更好地了解您..."
                    maxlength="500"
                >{{ old('bio', $user->bio ?? '') }}</textarea>
                @error('bio')
                <p class="form-error">
                    <i class="fas fa-exclamation-circle mr-1" aria-hidden="true"></i>
                    {{ $message }}
                </p>
                @enderror
                <p class="form-help">最多500个字符，将显示在您的个人资料页面</p>
            </div>
        </div>

        {{-- 表单操作按钮 --}}
        <div class="form-actions">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2" aria-hidden="true"></i>
                        保存更改
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="this.form.reset()">
                        <i class="fas fa-undo mr-2" aria-hidden="true"></i>
                        重置
                    </button>
                </div>

                {{-- 保存成功提示 --}}
                @if (session('status') === 'profile-updated')
                    <div class="flex items-center text-success-600 animate-fade-in">
                        <i class="fas fa-check-circle mr-2" aria-hidden="true"></i>
                        <span class="text-sm font-medium">个人信息已成功更新</span>
                    </div>
                @endif
            </div>
        </div>
    </form>
</section>

{{-- 表单样式 --}}
<style>
    .enterprise-profile-form .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .enterprise-profile-form .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .enterprise-profile-form .form-section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }

    .enterprise-profile-form .form-label {
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .enterprise-profile-form .form-label.required::after {
        content: '*';
        color: #ef4444;
        margin-left: 0.25rem;
    }

    .enterprise-profile-form .form-input,
    .enterprise-profile-form .form-textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background-color: #ffffff;
    }

    .enterprise-profile-form .form-input:focus,
    .enterprise-profile-form .form-textarea:focus {
        outline: none;
        border-color: #5247ef;
        box-shadow: 0 0 0 3px rgba(82, 71, 239, 0.1);
    }

    .enterprise-profile-form .form-input-error {
        border-color: #ef4444;
        background-color: #fef2f2;
    }

    .enterprise-profile-form .form-error {
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }

    .enterprise-profile-form .form-help {
        color: #6b7280;
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    .enterprise-profile-form .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e5e7eb;
    }

    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fade-in 0.3s ease-out;
    }
</style>
