<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\SmsAuthController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\Auth\WechatAuthController;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::get('register', [RegisteredUserController::class, 'create'])
         ->name('register');

    Route::post('register', [RegisteredUserController::class, 'store']);

    Route::get('login', [AuthenticatedSessionController::class, 'create'])
         ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
         ->name('password.request');

    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
         ->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
         ->name('password.reset');

    Route::post('reset-password', [NewPasswordController::class, 'store'])
         ->name('password.store');
});

Route::middleware('auth')->group(function () {
    Route::get('verify-email', EmailVerificationPromptController::class)
         ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
         ->middleware(['signed', 'throttle:6,1'])
         ->name('verification.verify');

    Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
         ->middleware('throttle:6,1')
         ->name('verification.send');

    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
         ->name('password.confirm');

    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::put('password', [PasswordController::class, 'update'])->name('password.update');

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
         ->name('logout');
});

/*
|--------------------------------------------------------------------------
| 手机号短信验证码认证路由
|--------------------------------------------------------------------------
|
| 处理基于手机号短信验证码的用户认证功能
|
*/

Route::middleware('guest')->group(function () {
    // 手机号登录页面
    Route::get('sms-login', [SmsAuthController::class, 'showLoginForm'])
         ->name('sms.login');

    // 手机号注册页面
    Route::get('sms-register', [SmsAuthController::class, 'showRegisterForm'])
         ->name('sms.register');

    // 发送短信验证码
    Route::post('sms/send-code', [SmsAuthController::class, 'sendSmsCode'])
         ->middleware('throttle:10,1') // 每分钟最多10次
         ->name('sms.send-code');

    // 手机号验证码登录
    Route::post('sms-login', [SmsAuthController::class, 'login'])
         ->name('sms.login.submit');

    // 手机号验证码注册
    Route::post('sms-register', [SmsAuthController::class, 'register'])
         ->name('sms.register.submit');
});

/*
|--------------------------------------------------------------------------
| 微信公众号扫码认证路由
|--------------------------------------------------------------------------
|
| 处理微信公众号扫码登录注册功能
|
*/

Route::middleware('guest')->group(function () {
    // 微信登录页面
    Route::get('wechat-login', [WechatAuthController::class, 'showLoginForm'])
         ->name('wechat.login');

    // 生成微信登录二维码
    Route::post('wechat/generate-qrcode', [WechatAuthController::class, 'generateQrCode'])
         ->middleware('throttle:20,1') // 每分钟最多20次
         ->name('wechat.generate-qrcode');

    // 查询扫码状态
    Route::post('wechat/check-qrcode-status', [WechatAuthController::class, 'checkQrCodeStatus'])
         ->middleware('throttle:60,1') // 每分钟最多60次（用于轮询）
         ->name('wechat.check-qrcode-status');
});

// 微信授权回调（微信服务器调用，不需要guest中间件）
Route::any('wechat/callback', [WechatAuthController::class, 'handleCallback'])
     ->name('wechat.callback');

/*
|--------------------------------------------------------------------------
| 需要登录的认证相关路由
|--------------------------------------------------------------------------
|
| 需要用户已登录才能访问的认证功能
|
*/

Route::middleware('auth')->group(function () {
    // 绑定手机号
    Route::post('sms/bind-mobile', [SmsAuthController::class, 'bindMobile'])
         ->name('sms.bind-mobile');

    // 解绑微信账号
    Route::post('wechat/unbind', [WechatAuthController::class, 'unbindWechat'])
         ->name('wechat.unbind');
});
