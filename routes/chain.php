<?php

use App\Http\Middleware\RouteResponseTypeMiddleware;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 链式管理模块路由 - Chain Management Routes
|--------------------------------------------------------------------------
|
| 这里定义了链式管理模块的所有路由，包括：
| - 商店管理
| - Pin 管理
| - Rice 管理及批量操作
| - 零售和备件管理
| - 同步管理
|
| 所有路由都需要认证和特定的响应类型处理
|
*/

// ============================================================================
// 链式管理模块路由组 - Chain Management Routes Group
// ============================================================================

Route::middleware([RouteResponseTypeMiddleware::class, 'auth'])->prefix('chain')->name('chain.')->group(function () {
    //
    Route::resource('dashboard', App\Http\Controllers\Chain\DashboardController::class);
    // ========================================================================
    // 商店管理 - Shop Management
    // ========================================================================
    Route::resource('shop', App\Http\Controllers\Chain\ShopController::class);

    // ========================================================================
    // Pin 管理 - Pin Management
    // ========================================================================
    Route::resource('pin', App\Http\Controllers\Chain\PinController::class);

    // ========================================================================
    // Rice 管理及批量操作 - Rice Management & Batch Operations
    // ========================================================================

    // Rice 基础 CRUD 操作
    Route::resource('rice', App\Http\Controllers\Chain\RiceController::class);

    // Rice 批量操作路由
    Route::post('rice/batch_ready', [App\Http\Controllers\Chain\RiceController::class, 'batchReady'])
         ->name('rice.batch_ready');

    Route::post('rice/batch_publish', [App\Http\Controllers\Chain\RiceController::class, 'batchPublish'])
         ->name('rice.batch_publish');

    Route::post('rice/batch_preview', [App\Http\Controllers\Chain\RiceController::class, 'batchPreview'])
         ->name('rice.batch_preview');

    Route::post('rice/batch_edit', [App\Http\Controllers\Chain\RiceController::class, 'batchEdit'])
         ->name('rice.batch_edit');

    Route::post('rice/batch_match', [App\Http\Controllers\Chain\RiceController::class, 'batchMatch'])
         ->name('rice.batch_match');

    // ========================================================================
    // 零售和备件管理 - Retail & Spare Management
    // ========================================================================

    // 零售管理
    Route::resource('retail', App\Http\Controllers\Chain\RetailController::class);

    // 备件管理
    Route::resource('spare', App\Http\Controllers\Chain\SpareController::class);

    // ========================================================================
    // 同步管理 - Sync Management
    // ========================================================================

    // 同步管理
    Route::resource('sync', App\Http\Controllers\Chain\SyncController::class);
});
