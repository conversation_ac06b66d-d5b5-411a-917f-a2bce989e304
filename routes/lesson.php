<?php

use App\Http\Middleware\RouteResponseTypeMiddleware;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 课程管理模块路由 - Lesson Management Routes
|--------------------------------------------------------------------------
|
| 这里定义了课程管理模块的所有路由，包括：
| - 课程首页管理
| - 订单管理（普通订单、自动订单、异常订单）
| - 分类和主题管理
| - 产品管理（产品、SKU、草稿）
| - 租赁和格子管理
| - 信息管理
| - 客户管理
| - 关键词管理
|
| 所有路由都需要认证和特定的响应类型处理
|
*/

// ============================================================================
// 课程管理模块路由组 - Lesson Management Routes Group
// ============================================================================

Route::middleware([RouteResponseTypeMiddleware::class, 'auth'])->prefix('lesson')->name('lesson.')->group(function () {

    // ========================================================================
    // 课程首页管理 - Lesson Home Management
    // ========================================================================
    Route::resource('home', App\Http\Controllers\Lesson\Home\IndexController::class);
    //
    Route::resource('dashboard', App\Http\Controllers\Lesson\Home\DashboardController::class);
    // ========================================================================
    // 订单管理 - Order Management
    // ========================================================================

    // 普通订单管理
    Route::resource('order', App\Http\Controllers\Lesson\Order\OrderController::class);
    // 自动订单管理
    Route::resource('order_auto', App\Http\Controllers\Lesson\Order\OrderAutoController::class);
    // 异常订单管理
    Route::resource('order_aberrant', App\Http\Controllers\Lesson\Order\OrderAberrantController::class);

    // ========================================================================
    // 分类和主题管理 - Category & Theme Management
    // ========================================================================

    // 分类管理
    Route::resource('category', App\Http\Controllers\Lesson\Category\CategoryController::class);
    // 主题管理
    Route::resource('theme', App\Http\Controllers\Lesson\Theme\ThemeController::class);

    // ========================================================================
    // 产品管理 - Product Management
    // ========================================================================

    // 产品管理
    Route::resource('product', App\Http\Controllers\Lesson\Product\ProductController::class);
    // 产品 SKU 管理
    Route::resource('product_sku', App\Http\Controllers\Lesson\Product\ProductSkuController::class);
    // 产品草稿管理
    Route::resource('product_draft', App\Http\Controllers\Lesson\ProductDraft\ProductDraftController::class);

    // ========================================================================
    // 租赁和格子管理 - Rent & Lattice Management
    // ========================================================================

    // 租赁管理
    Route::resource('rent', App\Http\Controllers\Lesson\Rent\RentController::class);
    // 格子管理
    Route::resource('lattice', App\Http\Controllers\Lesson\Lattice\LatticeController::class);
    // 商店管理
    Route::resource('store', App\Http\Controllers\Lesson\Store\StoreController::class);

    // ========================================================================
    // 信息管理 - Information Management
    // ========================================================================

    // 信息管理
    Route::resource('info', App\Http\Controllers\Lesson\Info\InfoController::class);
    // 信息审核管理
    Route::resource('info_check', App\Http\Controllers\Lesson\Info\InfoCheckController::class);

    // ========================================================================
    // 客户管理 - Customer Management
    // ========================================================================

    // 客户管理
    Route::resource('customer', App\Http\Controllers\Lesson\Customer\CustomerController::class);
    // 客户联系人管理
    Route::resource('customer_contact', App\Http\Controllers\Lesson\Customer\CustomerContactController::class);

    // ========================================================================
    // 关键词管理 - Keyword Management
    // ========================================================================

    // 关键词管理
    Route::resource('keyword', App\Http\Controllers\Lesson\Keyword\KeywordController::class);
});
