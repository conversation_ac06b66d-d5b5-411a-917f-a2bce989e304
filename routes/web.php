<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes - WorkHub 企业工作协作平台
|--------------------------------------------------------------------------
|
| 这里注册应用程序的所有 Web 路由。这些路由由 RouteServiceProvider 加载，
| 并且都会被分配到 "web" 中间件组。
|
*/

// ============================================================================
// 首页和欢迎页面路由 - Home & Welcome Routes
// ============================================================================
//
Route::middleware([\App\Http\Middleware\RouteResponseTypeMiddleware::class])->group(function () {
    //
    Route::resource('/', App\Http\Controllers\Desktop\IndexController::class);
    Route::resource('/shop', App\Http\Controllers\Desktop\ShopController::class);
    Route::resource('/pin', App\Http\Controllers\Desktop\PinController::class);
    //
    Route::resource('/store', App\Http\Controllers\Desktop\StoreController::class);
    Route::resource('/rice', App\Http\Controllers\Desktop\RiceController::class);
    //
    Route::resource('/lattice', App\Http\Controllers\Desktop\LatticeController::class);
    Route::resource('/product', App\Http\Controllers\Desktop\ProductController::class);
});
//
//Route::get('/', function () {
//    return view('admin', [
//        'title'           => '首页',
//        'description'     => '现代化的企业工作协作平台，提供高效的团队协作、项目管理、文档管理和业务流程优化解决方案，助力企业数字化转型',
//        'keywords'        => '企业工作平台,团队协作,项目管理,文档管理,业务流程,数字化转型,企业办公,工作效率',
//        'pageTitle'       => 'WorkHub - 现代化企业工作协作平台',
//        'pageDescription' => '专为现代企业打造的一体化工作协作平台'
//    ]);
//})->name('home');

Route::get('/welcome', function () {
    return view('welcome');
})->name('welcome');

// ============================================================================
// 管理后台路由 - Admin Routes
// ============================================================================

Route::get('/admin', function () {
    return view('admin');
})->name('admin');

// ============================================================================
// 核心业务功能路由 - Core Business Routes
// ============================================================================

// 市场模块 - Market Module
Route::get('/market', function () {
    return view('admin');
})->name('market');

// 工作流模块 - Workflow Module
Route::get('/flow', function () {
    return view('admin');
})->name('flow');

// AI 智能助手模块 - AI Assistant Module
Route::get('/ai', function () {
    return view('ai');
})->name('ai');

// 商业智能模块 - Business Intelligence Module
Route::get('/bi', function () {
    return view('bi');
})->name('bi');

// 持续集成模块 - Continuous Integration Module
Route::get('/ci', function () {
    return view('ci');
})->name('ci');

// 课程模块 - Lesson Module
Route::get('/lesson', function () {
    return view('lesson', [
        'title'       => '课程管理',
        'description' => '课程管理系统首页',
        'menuActive'  => 'lesson'
    ]);
})->name('lesson');

// ============================================================================
// 搜索和发现路由 - Search & Discovery Routes
// ============================================================================

Route::get('/search', function () {
    return view('welcome');
})->name('search');

Route::get('/disclaimer', function () {
    return view('welcome');
})->name('disclaimer');

Route::get('/certificates', function () {
    return view('welcome');
})->name('certificates');

// ============================================================================
// 帮助和服务中心路由 - Help & Service Center Routes
// ============================================================================

// 帮助中心 - Help Center
Route::get('/help/center', function () {
    return view('simple-test');
})->name('help.center');

// 客户服务 - Customer Service
Route::get('/customer/service', function () {
    return view('simple-test');
})->name('customer.service');

// 商户服务 - Merchant Service
Route::get('/merchant/service', function () {
    return view('simple-test');
})->name('merchant.service');

// ============================================================================
// 消息和通知中心路由 - Message & Notification Center Routes
// ============================================================================

// 消息中心 - Message Center
Route::get('/message/center', function () {
    return view('simple-test');
})->name('message.center');

// 反馈中心 - Feedback Center
Route::get('/feedback/center', function () {
    return view('simple-test');
})->name('feedback.center');

// 报告中心 - Report Center
Route::get('/report/center', function () {
    return view('simple-test');
})->name('report.center');

// 通知中心 - Notifications Center
Route::get('/notifications/index', function () {
    return view('simple-test');
})->name('notifications.index');

// ============================================================================
// 核心工作模块路由 - Core Work Module Routes
// ============================================================================

// 项目管理 - Project Management
Route::get('/projects/index', function () {
    return view('simple-test');
})->name('projects.index');

// 团队管理 - Team Management
Route::get('/teams/index', function () {
    return view('simple-test');
})->name('teams.index');

// 文档管理 - Document Management
Route::get('/documents/index', function () {
    return view('simple-test');
})->name('documents.index');

// ============================================================================
// API 和开发者路由 - API & Developer Routes
// ============================================================================

// API 首页 - API Index
Route::get('/api/index', function () {
    return view('simple-test');
})->name('api.index');

// API 文档 - API Documentation
Route::get('/api/docs', function () {
    return view('simple-test');
})->name('api.docs');

// ============================================================================
// 数据分析路由 - Analytics Routes
// ============================================================================

// 数据分析中心 - Analytics Center
Route::get('/analytics/index', function () {
    return view('simple-test');
})->name('analytics.index');

// ============================================================================
// 公司信息和法律页面路由 - Company Info & Legal Pages Routes
// ============================================================================

// 关于我们 - About Us
Route::get('/about/us', function () {
    return view('simple-test');
})->name('about.us');

// 服务条款 - Terms of Service
Route::get('/terms/service', function () {
    return view('simple-test');
})->name('terms.service');

// 隐私政策 - Privacy Policy
Route::get('/privacy/policy', function () {
    return view('simple-test');
})->name('privacy.policy');

// 联系我们 - Contact Us
Route::get('/contact/us', function () {
    return view('simple-test');
})->name('contact.us');

// ============================================================================
// 需要认证的路由组 - Authenticated Routes Group
// ============================================================================

Route::middleware(['auth', 'verified'])->group(function () {
    // 仪表板 - Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // 样式指南 - Style Guide (仅认证用户可访问)
    Route::get('/style-guide', function () {
        return view('components.style-guide');
    })->name('style.guide');
});

// ============================================================================
// 开发和测试路由 - Development & Testing Routes
// ============================================================================

// Vue + Blade 集成示例 - Vue + Blade Integration Example
Route::get('/example', function () {
    return view('example', [
        'title'       => 'Vue + Blade 集成示例',
        'description' => '展示 Vue.js 与 Laravel Blade 的无缝集成',
        'breadcrumbs' => [
            ['title' => '示例页面', 'url' => '#', 'icon' => 'fas fa-code'],
            ['title' => 'Vue 集成', 'icon' => 'fab fa-vuejs']
        ]
    ]);
})->name('example');

// 布局测试页面 - Layout Testing Page
Route::get('/test-layout', function () {
    return view('test-layout', [
        'title'       => '布局测试页面',
        'description' => '测试 Blade 布局系统功能',
        'breadcrumbs' => [
            ['title' => '测试页面', 'url' => '#', 'icon' => 'fas fa-flask']
        ]
    ]);
})->name('test.layout');

// 简单测试页面 - Simple Test Page
Route::get('/simple-test', function () {
    return view('simple-test');
})->name('simple.test');

// 基础测试页面 - Basic Test Page
Route::get('/basic-test', function () {
    return view('basic-test');
})->name('basic.test');

// ============================================================================
// API 工具路由 - API Utility Routes
// ============================================================================

// CSRF 令牌刷新路由 - CSRF Token Refresh Route
Route::get('/csrf-token', function () {
    return response()->json([
                                'csrf_token' => csrf_token()
                            ]);
})->name('csrf.token');

// ============================================================================
// 用户资料管理路由 - User Profile Management Routes
// ============================================================================

Route::middleware('auth')->group(function () {
    // 编辑用户资料 - Edit User Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');

    // 更新用户资料 - Update User Profile
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // 删除用户账户 - Delete User Account
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// ============================================================================
// 认证路由 - Authentication Routes
// ============================================================================

// 引入认证路由文件 - Include Authentication Routes
require __DIR__ . '/auth.php';
