<?php

/**
 * Laravel - 一个面向 Web 艺术家的 PHP 框架
 * @package  Laravel
 * <AUTHOR> <<EMAIL>>
 */

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH)
);

// 此文件允许我们从内置的 PHP Web 服务器中模拟 Apache 的 "mod_rewrite" 功能。
// 这为在没有安装“真实”Web服务器软件的情况下测试 Laravel 应用提供了一种方便的方式。
if ($uri !== '/' && file_exists(__DIR__ . '/public' . $uri)) {
    return false;
}

require_once __DIR__ . '/public/index.php';
