import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.vue',
        './resources/js/**/*.js',
    ],

    theme: {
        extend: {
            // 字体系统 - 企业级设计
            fontFamily: {
                sans: ['Inter', 'Figtree', ...defaultTheme.fontFamily.sans],
            },
            fontSize: {
                'xs': ['0.75rem', { lineHeight: '1rem' }],     // 12px - 基础字体大小
                'sm': ['0.875rem', { lineHeight: '1.25rem' }], // 14px
                'base': ['1rem', { lineHeight: '1.5rem' }],    // 16px
                'lg': ['1.125rem', { lineHeight: '1.75rem' }], // 18px
                'xl': ['1.25rem', { lineHeight: '1.75rem' }],  // 20px
                '2xl': ['1.5rem', { lineHeight: '2rem' }],     // 24px
                '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
                '4xl': ['2.25rem', { lineHeight: '2.5rem' }],  // 36px
            },

            // 企业级最大宽度系统 - 1760px 最大宽度，640px 最小宽度
            maxWidth: {
                'container': '1760px',  // 企业级最大宽度
                '8xl': '1760px',
                '9xl': '1920px',
            },
            minWidth: {
                'mobile': '640px',      // 最小宽度支持
                'desktop': '1200px',    // 桌面最小宽度
            },

            // 响应式断点系统 - 企业级设计
            screens: {
                'xs': '640px',    // 大手机
                'sm': '768px',    // 平板竖屏
                'md': '1024px',   // 平板横屏
                'lg': '1200px',   // 小桌面
                'xl': '1440px',   // 标准桌面
                '2xl': '1760px',  // 企业级大桌面
                '3xl': '1920px',  // 超大桌面
            },

            // 间距系统
            spacing: {
                '18': '4.5rem',   // 72px
                '88': '22rem',    // 352px
                '128': '32rem',   // 512px
                '144': '36rem',   // 576px
            },

            // 企业级色彩系统 - 根据用户偏好设置
            colors: {
                // 主色调 - #5247ef
                primary: {
                    50: '#f0f0ff',
                    100: '#e6e3ff',
                    200: '#d0ccff',
                    300: '#b3adff',
                    400: '#9489ff',
                    500: '#5247ef',  // 主色调
                    600: '#4a3dd6',
                    700: '#3d32b8',
                    800: '#322899',
                    900: '#2a217a',
                    950: '#1a1454',
                },

                // 次要色调 - #5247ef
                secondary: {
                    50: '#f3f2ff',
                    100: '#e9e7ff',
                    200: '#d6d2ff',
                    300: '#b8b0ff',
                    400: '#9489ff',
                    500: '#5247ef',  // 次要色调
                    600: '#6b5ae6',
                    700: '#5d4bd2',
                    800: '#4e3db0',
                    900: '#42358f',
                    950: '#271f61',
                },

                // 成功色 - #04c717
                success: {
                    50: '#f0fdf1',
                    100: '#dcfce0',
                    200: '#bbf7c2',
                    300: '#86efac',
                    400: '#4ade80',
                    500: '#04c717',  // 成功色
                    600: '#16a34a',
                    700: '#15803d',
                    800: '#166534',
                    900: '#14532d',
                    950: '#052e16',
                },

                // 警告色 - #FF5000
                warning: {
                    50: '#fff7ed',
                    100: '#ffedd5',
                    200: '#fed7aa',
                    300: '#fdba74',
                    400: '#fb923c',
                    500: '#FF5000',  // 警告色
                    600: '#ea580c',
                    700: '#c2410c',
                    800: '#9a3412',
                    900: '#7c2d12',
                    950: '#431407',
                },

                // 危险色 - #FF0036
                danger: {
                    50: '#fef2f2',
                    100: '#fee2e2',
                    200: '#fecaca',
                    300: '#fca5a5',
                    400: '#f87171',
                    500: '#FF0036',  // 危险色
                    600: '#dc2626',
                    700: '#b91c1c',
                    800: '#991b1b',
                    900: '#7f1d1d',
                    950: '#450a0a',
                },

                // 灰色系统 - #97a6ba 基础
                gray: {
                    50: '#f9fafb',
                    100: '#f3f4f6',
                    200: '#e5e7eb',
                    300: '#d1d5db',
                    400: '#97a6ba',  // 用户偏好的灰色
                    500: '#6b7280',
                    600: '#4b5563',
                    700: '#374151',
                    800: '#1f2937',
                    900: '#111827',
                    950: '#030712',
                },
            },
        },
    },

    plugins: [forms],
};
