<?php

namespace Tests\Feature\Auth;

use App\Models\UserCenter\User;
use App\Services\Sms\SmsCodeService;
use App\Services\Sms\SmsService;
use App\Services\Wechat\WechatQrCodeService;
use App\Services\Wechat\WechatService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

/**
 * 短信验证码和微信扫码认证测试类
 * 
 * 测试新增的手机号短信验证码登录注册和微信公众号扫码登录注册功能
 * 
 * @package Tests\Feature\Auth
 * <AUTHOR> Core Team
 * @since 2025-01-22
 */
class SmsAndWechatAuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 设置测试配置
        config(['sms.default' => 'mock']);
        config(['wechat.official_account.app_id' => 'test_app_id']);
        config(['wechat.official_account.app_secret' => 'test_app_secret']);
        config(['wechat.official_account.token' => 'test_token']);
    }

    /**
     * 测试短信验证码发送
     */
    public function test_sms_code_can_be_sent(): void
    {
        $response = $this->postJson('/sms/send-code', [
            'mobile' => '***********',
            'type' => 'login'
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => '验证码已发送，请注意查收'
                 ]);
    }

    /**
     * 测试短信验证码发送频率限制
     */
    public function test_sms_code_rate_limiting(): void
    {
        $mobile = '***********';
        
        // 第一次发送应该成功
        $response = $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'login'
        ]);
        $response->assertStatus(200);

        // 立即再次发送应该被限制
        $response = $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'login'
        ]);
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['mobile']);
    }

    /**
     * 测试手机号格式验证
     */
    public function test_mobile_number_validation(): void
    {
        // 测试无效手机号
        $response = $this->postJson('/sms/send-code', [
            'mobile' => '12345678901',
            'type' => 'login'
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['mobile']);

        // 测试有效手机号
        $response = $this->postJson('/sms/send-code', [
            'mobile' => '***********',
            'type' => 'login'
        ]);

        $response->assertStatus(200);
    }

    /**
     * 测试短信验证码注册
     */
    public function test_sms_code_registration(): void
    {
        $mobile = '13800138002';
        
        // 先发送验证码
        $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'register'
        ]);

        // 模拟验证码（在测试环境中）
        $smsCodeService = app(SmsCodeService::class);
        $code = $smsCodeService->generateCode($mobile, 'register');

        // 使用验证码注册
        $response = $this->postJson('/sms-register', [
            'mobile' => $mobile,
            'code' => $code,
            'name' => 'Test User'
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => '注册成功，欢迎加入！'
                 ]);

        // 验证用户已创建
        $this->assertDatabaseHas('users', [
            'mobile' => $mobile,
            'name' => 'Test User'
        ]);
    }

    /**
     * 测试短信验证码登录
     */
    public function test_sms_code_login(): void
    {
        $mobile = '13800138003';
        
        // 创建测试用户
        $user = User::factory()->create([
            'mobile' => $mobile,
            'mobile_verified_at' => now()
        ]);

        // 发送验证码
        $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'login'
        ]);

        // 生成验证码
        $smsCodeService = app(SmsCodeService::class);
        $code = $smsCodeService->generateCode($mobile, 'login');

        // 使用验证码登录
        $response = $this->postJson('/sms-login', [
            'mobile' => $mobile,
            'code' => $code,
            'remember' => false
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => '登录成功，欢迎回来！'
                 ]);

        $this->assertAuthenticated();
    }

    /**
     * 测试错误验证码
     */
    public function test_invalid_sms_code(): void
    {
        $mobile = '13800138004';
        
        // 创建测试用户
        User::factory()->create([
            'mobile' => $mobile,
            'mobile_verified_at' => now()
        ]);

        // 尝试使用错误验证码登录
        $response = $this->postJson('/sms-login', [
            'mobile' => $mobile,
            'code' => '000000',
            'remember' => false
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['code']);

        $this->assertGuest();
    }

    /**
     * 测试微信二维码生成
     */
    public function test_wechat_qrcode_generation(): void
    {
        $response = $this->postJson('/wechat/generate-qrcode', [
            'type' => 'login',
            'redirect_url' => '/dashboard'
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'qr_code_url',
                         'scene_str',
                         'expires_in',
                         'polling_interval'
                     ]
                 ]);
    }

    /**
     * 测试微信扫码状态查询
     */
    public function test_wechat_qrcode_status_check(): void
    {
        $sceneStr = 'login_' . time() . '_test123';
        
        // 查询不存在的场景值
        $response = $this->postJson('/wechat/check-qrcode-status', [
            'scene_str' => $sceneStr
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => false,
                     'status' => 'expired'
                 ]);
    }

    /**
     * 测试SmsCodeService验证码生成
     */
    public function test_sms_code_service_generate_code(): void
    {
        $smsCodeService = app(SmsCodeService::class);
        $mobile = '13800138005';
        
        $code = $smsCodeService->generateCode($mobile, 'login');
        
        $this->assertIsString($code);
        $this->assertEquals(6, strlen($code));
        $this->assertTrue(ctype_digit($code));
    }

    /**
     * 测试SmsCodeService验证码验证
     */
    public function test_sms_code_service_verify_code(): void
    {
        $smsCodeService = app(SmsCodeService::class);
        $mobile = '13800138006';
        
        // 生成验证码
        $code = $smsCodeService->generateCode($mobile, 'login');
        
        // 验证正确的验证码
        $this->assertTrue($smsCodeService->verifyCode($mobile, $code, 'login'));
        
        // 验证错误的验证码
        $this->assertFalse($smsCodeService->verifyCode($mobile, '000000', 'login'));
    }

    /**
     * 测试SmsCodeService验证码过期
     */
    public function test_sms_code_service_code_expiry(): void
    {
        $smsCodeService = app(SmsCodeService::class);
        $mobile = '13800138007';
        
        // 生成验证码
        $code = $smsCodeService->generateCode($mobile, 'login');
        
        // 检查验证码是否有效
        $this->assertTrue($smsCodeService->hasValidCode($mobile, 'login'));
        
        // 获取剩余时间
        $remaining = $smsCodeService->getRemainingSeconds($mobile, 'login');
        $this->assertGreaterThan(0, $remaining);
    }

    /**
     * 测试WechatQrCodeService二维码生成
     */
    public function test_wechat_qrcode_service_create_temp_qrcode(): void
    {
        $qrCodeService = app(WechatQrCodeService::class);
        $sceneStr = 'test_scene_' . time();
        
        // 由于需要真实的微信API，这里主要测试方法调用
        $this->assertTrue(method_exists($qrCodeService, 'createTempQrCode'));
        $this->assertTrue(method_exists($qrCodeService, 'createPermanentQrCode'));
    }

    /**
     * 测试WechatService配置检查
     */
    public function test_wechat_service_config_check(): void
    {
        $wechatService = app(WechatService::class);
        
        $configCheck = $wechatService->checkConfig();
        
        $this->assertIsArray($configCheck);
        $this->assertArrayHasKey('valid', $configCheck);
        $this->assertArrayHasKey('message', $configCheck);
    }

    /**
     * 测试手机号绑定功能
     */
    public function test_mobile_binding(): void
    {
        // 创建已登录用户
        $user = User::factory()->create();
        $this->actingAs($user);
        
        $mobile = '13800138008';
        
        // 发送绑定验证码
        $response = $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'bind'
        ]);
        
        $response->assertStatus(200);
        
        // 生成验证码
        $smsCodeService = app(SmsCodeService::class);
        $code = $smsCodeService->generateCode($mobile, 'bind');
        
        // 绑定手机号
        $response = $this->postJson('/sms/bind-mobile', [
            'mobile' => $mobile,
            'code' => $code
        ]);
        
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => '手机号绑定成功'
                 ]);
        
        // 验证用户手机号已更新
        $user->refresh();
        $this->assertEquals($mobile, $user->mobile);
    }

    /**
     * 测试重复手机号注册
     */
    public function test_duplicate_mobile_registration(): void
    {
        $mobile = '13800138009';
        
        // 创建已存在的用户
        User::factory()->create(['mobile' => $mobile]);
        
        // 尝试发送注册验证码
        $response = $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'register'
        ]);
        
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['mobile']);
    }

    /**
     * 测试未注册手机号登录
     */
    public function test_unregistered_mobile_login(): void
    {
        $mobile = '13800138010';
        
        // 尝试发送登录验证码
        $response = $this->postJson('/sms/send-code', [
            'mobile' => $mobile,
            'type' => 'login'
        ]);
        
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['mobile']);
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        // 清理缓存
        Cache::flush();
        
        parent::tearDown();
    }
}
